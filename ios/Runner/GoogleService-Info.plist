<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>591915683713-r62rld49mqugr2r34tjj2pi36l6h406t.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.591915683713-r62rld49mqugr2r34tjj2pi36l6h406t</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>591915683713-j1d2rrjal315im80tbaajrc6gguh00fm.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyCL_o8aSP1YjmuXZY46c4pvBFO66SFiUfo</string>
	<key>GCM_SENDER_ID</key>
	<string>591915683713</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.ruanjiang.inspectionPlatformt</string>
	<key>PROJECT_ID</key>
	<string>inspector-9d353</string>
	<key>STORAGE_BUCKET</key>
	<string>inspector-9d353.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:591915683713:ios:1a803e7e5f42b9180fa280</string>
</dict>
</plist>