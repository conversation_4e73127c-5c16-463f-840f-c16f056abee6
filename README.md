# inspector

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://flutter.dev/docs/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://flutter.dev/docs/cookbook)

For help getting started with Flutter, view our
[online documentation](https://flutter.dev/docs), which offers tutorials,
samples, guidance on mobile development, and a full API reference.


### android
android打包dev环境
flutter build apk --no-tree-shake-icons -t lib/main_dev.dart --flavor dev

android打包正式环境
flutter build apk --no-tree-shake-icons -t lib/main_prod.dart --flavor prod

### ios
ios打包dev环境
flutter build ipa -t lib/main_dev.dart --flavor dev

ios打包正式环境
flutter build ipa -t lib/main_prod.dart --flavor prod

### 必要命令
flutter pub global activate intl_utils 国际化库
flutter pub add json_annotation dev:build_runner dev:json_serializable 序列化库
debug 运行需添加 --no-enable-impeller
