name: inspector
version: 2.25.0+22500
publish_to: none
description: A new Flutter project.
environment:
  sdk: '>=3.4.4 <4.0.0'
  flutter: 3.27.4

fluwx:
  app_id: 'wx003a8baa47475fd4'
  debug_logging: true # Logging in debug mode.
  android:
#    interrupt_wx_request: true # Defaults to true.
#    flutter_activity: 'MainActivity' # Defaults to app's launcher
  ios:
    universal_link: https://www.inspector.ltd

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.6
  getwidget: ^6.0.0
  get: ^4.7.2
  permission_handler: ^12.0.0+1
  image_picker: ^1.1.2
  image_pickers: ^2.0.6
  shared_preferences: ^2.5.3
  connectivity_plus: ^6.1.4
  logger: ^2.5.0
  flutter_inappwebview: ^6.1.5
  dio: ^5.4.3+1
  device_info_plus: ^11.3.0
  package_info_plus: ^8.0.0
  fluttertoast: ^8.2.12
  cached_network_image: ^3.3.1
  pull_to_refresh: 2.0.0
  flutter_spinkit: ^5.2.1
  flutter_swiper_null_safety: 1.0.2
  flutter_easyloading: ^3.0.5
  #r_upgrade: 0.4.2
  flutter_pickers: 2.1.9
  date_format: ^2.0.9
  table_calendar: ^3.1.3
  file_picker: ^10.1.9
  common_utils: 2.1.0
  path_provider: ^2.1.3
  tobias: ^5.1.2
  auto_size_text: ^3.0.0
  crypto: ^3.0.6
  flutter_slidable: ^4.0.0
  url_launcher: ^6.3.1
  easy_image_viewer: ^1.5.1
  map_launcher: ^3.5.0
  flutter_keyboard_visibility: 6.0.0
  geolocator: ^12.0.0
  #video_thumbnail: ^0.5.6
  web_socket_channel: ^3.0.3
  json_annotation: ^4.9.0
  linked_scroll_controller: ^0.2.0
  flutter_widget_from_html_core: ^0.16.0
  open_file: ^3.5.10
  intl: any
  uuid: ^4.0.0
  #flutter_custom_dialog: ^1.3.0
  dotted_border: 2.1.0
  flutter_rating_bar: ^4.0.1
  rotated_corner_decoration: ^2.1.0+1
  shimmer: ^3.0.0
  nested_scroll_view_plus: ^3.0.0
  flutter_staggered_grid_view: ^0.7.0
  after_layout: ^1.2.0
  fluwx: ^5.5.4
  flutter_html: ^3.0.0-beta.2
  wechat_assets_picker: ^9.1.0
  wechat_camera_picker: ^4.2.0
  flutter_image_compress: ^2.3.0
  #image_gallery_saver: ^2.0.3
  photo_view: ^0.15.0
  extended_image: ^8.2.1
  image_editor: ^1.5.1
  signature: ^5.5.0
  event_bus: ^2.0.1
  firebase_core: ^3.1.1
  firebase_auth: ^5.1.1
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^7.0.1
  dropdown_button2: ^2.3.9
  app_installer: ^1.3.1

dev_dependencies:
  flutter_lints: ^5.0.0
  flutter_native_splash: ^2.4.4
  flutter_test:
    sdk: flutter
  # 多环境打包
  flutter_flavorizr: ^2.2.1
  build_runner: ^2.4.15
  json_serializable: ^6.9.5

flutter:
  uses-material-design: true

  generate: true

  assets:
    - assets/images/
    - assets/

  fonts:
    - family: AppIcons
      fonts:
        - asset: fonts/AppIcons.ttf

flutter_native_splash:
  color: "#FFFFFF"
  image: "assets/images/3.0x/launch_logo.png"
  android: true
  ios: true
  ios_content_mode: center
  android_gravity: center
flutter_intl:
  enabled: true

# Plugin configurations
tobias:
  url_scheme: inspectorapp
