// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;
import 'package:inspector/flavors.dart';

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAQgkeEMCqD5WZbTGuz7DD53Y4vgvb6yJk',
    appId: '1:591915683713:web:5234083d4de3d1d50fa280',
    messagingSenderId: '591915683713',
    projectId: 'inspector-9d353',
    authDomain: 'inspector-9d353.firebaseapp.com',
    storageBucket: 'inspector-9d353.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAe7tahUy3ilqCq-Re4fw-S56TQQnIAwZc',
    appId: '1:591915683713:android:2188a993d267b7360fa280',
    messagingSenderId: '591915683713',
    projectId: 'inspector-9d353',
    storageBucket: 'inspector-9d353.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCL_o8aSP1YjmuXZY46c4pvBFO66SFiUfo',
    appId: '1:591915683713:ios:1a803e7e5f42b9180fa280',
    messagingSenderId: '591915683713',
    projectId: 'inspector-9d353',
    storageBucket: 'inspector-9d353.appspot.com',
    iosBundleId: 'com.ruanjiang.inspectionPlatformt',
  );
}
