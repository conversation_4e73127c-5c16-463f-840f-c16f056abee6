// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/data/apply_info_entity.dart';
import 'package:inspector/app/data/area_list_entity.dart';
import 'package:inspector/app/data/assign_info.dart';
import 'package:inspector/app/data/baidu_ocr_entity.dart';
import 'package:inspector/app/data/bank_entity.dart';
import 'package:inspector/app/data/bill_entity.dart';
import 'package:inspector/app/data/charge_history_entity.dart';
import 'package:inspector/app/data/group_entity.dart';
import 'package:inspector/app/data/home_new_resp.dart';
import 'package:inspector/app/data/insp_order_entity.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/data/pay_model.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/data/receive_message.dart';
import 'package:inspector/app/data/user_account.dart';
import 'package:inspector/app/data/user_profile_resp.dart';
import 'package:inspector/app/data/wallet_info_entity.dart';
import 'package:inspector/app/data/withdraw_entity.dart';
import 'package:inspector/app/data/bind_pay_entity.dart';
import 'package:inspector/app/data/location_entity.dart';
import 'package:inspector/app/data/only_price_entity.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/order_input_entity.dart';
import 'package:inspector/app/data/order_list_entity.dart';
import 'package:inspector/app/data/publish_order_entity.dart';
//mydev
import 'package:inspector/app/data/conversation_entity.dart';
import 'package:inspector/app/data/report_entity.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/data/vip_price_entity.dart';
import 'package:inspector/app/data/wallet_entity.dart';
import 'package:inspector/app/modules/auth/models/saved_account_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_search/purchase_search_model.dart';

JsonConvert jsonConvert = JsonConvert();

class JsonConvert {
  T? convert<T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return asT<T>(value);
  }

  List<T?>? convertList<T>(List<dynamic>? value) {
    if (value == null) {
      return null;
    }
    try {
      return value.map((dynamic e) => asT<T>(e)).toList();
    } catch (e) {
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) => asT<T>(e)!).toList();
    } catch (e) {
      return <T>[];
    }
  }

  T? asT<T extends Object?>(dynamic value) {
    if (value is T) {
      return value;
    }
    String type = T.toString();
    try {
      String valueS = value.toString();
      if (type == "String") {
        return valueS as T;
      } else if (type == "int") {
        int? intValue = int.tryParse(valueS);
        if (intValue == null) {
          return double.tryParse(valueS)?.toInt() as T?;
        } else {
          return intValue as T;
        }
      } else if (type == "double") {
        return double.parse(valueS) as T;
      } else if (type == "DateTime") {
        return DateTime.parse(valueS) as T;
      } else if (type == "bool") {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return JsonConvert.fromJsonAsT<T>(value);
      }
    } catch (e) {
      return null;
    }
  }

  //Go back to a single instance by type
  static M? _fromJsonSingle<M>(Map<String, dynamic> json) {
    String type = M.toString();
    if (type == (AddressEntity).toString()) {
      return AddressEntity.fromJson(json) as M;
    }
    if (type == (AddressRows).toString()) {
      return AddressRows.fromJson(json) as M;
    }
    if (type == (ApplyInfoEntity).toString()) {
      return ApplyInfoEntity.fromJson(json) as M;
    }
    if (type == (AreaListEntity).toString()) {
      return AreaListEntity.fromJson(json) as M;
    }
    if (type == (BaiduOcrEntity).toString()) {
      return BaiduOcrEntity.fromJson(json) as M;
    }
    if (type == (BankEntity).toString()) {
      return BankEntity.fromJson(json) as M;
    }
    if (type == (BillEntity).toString()) {
      return BillEntity.fromJson(json) as M;
    }
    if (type == (BillCapitalModelList).toString()) {
      return BillCapitalModelList.fromJson(json) as M;
    }
    if (type == (BindPayEntity).toString()) {
      return BindPayEntity.fromJson(json) as M;
    }
    if (type == (LocationEntity).toString()) {
      return LocationEntity.fromJson(json) as M;
    }
    if (type == (LocationGeocodes).toString()) {
      return LocationGeocodes.fromJson(json) as M;
    }
    if (type == (LocationGeocodesNeighborhood).toString()) {
      return LocationGeocodesNeighborhood.fromJson(json) as M;
    }
    if (type == (LocationGeocodesBuilding).toString()) {
      return LocationGeocodesBuilding.fromJson(json) as M;
    }
    if (type == (OnlyPriceEntity).toString()) {
      return OnlyPriceEntity.fromJson(json) as M;
    }
    if (type == (OrderDetailEntity).toString()) {
      return OrderDetailEntity.fromJson(json) as M;
    }
    if (type == (AddressInfo).toString()) {
      return AddressInfo.fromJson(json) as M;
    }
    if (type == (OrderInputEntity).toString()) {
      return OrderInputEntity.fromJson(json) as M;
    }
    if (type == (OrderListEntity).toString()) {
      return OrderListEntity.fromJson(json) as M;
    }
    if (type == (OrderListRows).toString()) {
      return OrderListRows.fromJson(json) as M;
    }
    if (type == (PublishOrderEntity).toString()) {
      return PublishOrderEntity.fromJson(json) as M;
    }
    //mydev
    if (type == (PublishConvEntity).toString()) {
      return PublishConvEntity.fromJson(json) as M;
    }
    if (type == (ChatMessageEntity).toString()) {
      return ChatMessageEntity.fromJson(json) as M;
    }
    if (type == (AdminListEntity).toString()) {
      return AdminListEntity.fromJson(json) as M;
    }
    if (type == (ChatUserEntity).toString()) {
      return ChatUserEntity.fromJson(json) as M;
    }
    //mydev
    /*if (type == (RecordRows).toString()) {
      return RecordRows.fromJson(json) as M;
    }*/
    if (type == (NoticeConvRows).toString()) {
      return NoticeConvRows.fromJson(json) as M;
    }
    if (type == (PublishConvRows).toString()) {
      return PublishConvRows.fromJson(json) as M;
    }
    if (type == (PublishOrderRows).toString()) {
      return PublishOrderRows.fromJson(json) as M;
    }
    if (type == (ReportEntity).toString()) {
      return ReportEntity.fromJson(json) as M;
    }
    if (type == (ReportModel).toString()) {
      return ReportModel.fromJson(json) as M;
    }
    if (type == (InspectorReport).toString()) {
      return InspectorReport.fromJson(json) as M;
    }
    if (type == (ReportUserInfo).toString()) {
      return ReportUserInfo.fromJson(json) as M;
    }
    if (type == (UserInfoEntity).toString()) {
      return UserInfoEntity.fromJson(json) as M;
    }
    if (type == (UserProfileResp).toString()) {
      return UserProfileResp.fromJson(json) as M;
    }
    if (type == (InspectorProfile).toString()) {
      return InspectorProfile.fromJson(json) as M;
    }
    if (type == (VipPriceEntity).toString()) {
      return VipPriceEntity.fromJson(json) as M;
    }
    if (type == (WalletEntity).toString()) {
      return WalletEntity.fromJson(json) as M;
    }
    if (type == (OrderDateEntity).toString()) {
      return OrderDateEntity.fromJson(json) as M;
    }
    if (type == (WithdrawEntity).toString()) {
      return WithdrawEntity.fromJson(json) as M;
    }
    if (type == (WalletInfoEntity).toString()) {
      return WalletInfoEntity.fromJson(json) as M;
    }
    if (type == (ChargeHistoryEntity).toString()) {
      return ChargeHistoryEntity.fromJson(json) as M;
    }
    if (type == (GroupEntity).toString()) {
      return GroupEntity.fromJson(json) as M;
    }
    if (type == (GroupInfoEntity).toString()) {
      return GroupInfoEntity.fromJson(json) as M;
    }
    if (type == (GroupMemberEntity).toString()) {
      return GroupMemberEntity.fromJson(json) as M;
    }
    if (type == (ReceiveMessage).toString()) {
      return ReceiveMessage.fromJson(json) as M;
    }
    if (type == (SavedAccount).toString()) {
      return SavedAccount.fromJson(json) as M;
    }
    if (type == (PurchaseSearchHistoryModel).toString()) {
      return PurchaseSearchHistoryModel.fromJson(json) as M;
    }
    if (type == (AssignResp).toString()) {
      return AssignResp.fromJson(json) as M;
    }
    if (type == (AssignInfoEntity).toString()) {
      return AssignInfoEntity.fromJson(json) as M;
    }
    if (type == (ApplyRecord).toString()) {
      return ApplyRecord.fromJson(json) as M;
    }
    if (type == (OrderInspector).toString()) {
      return OrderInspector.fromJson(json) as M;
    }
    if (type == (UserAccountEntity).toString()) {
      return UserAccountEntity.fromJson(json) as M;
    }
    if (type == (OrderInfoEntity).toString()) {
      return OrderInfoEntity.fromJson(json) as M;
    }
    if (type == (OrderProduct).toString()) {
      return OrderProduct.fromJson(json) as M;
    }
    if (type == (ProductModel).toString()) {
      return ProductModel.fromJson(json) as M;
    }
    if (type == (PurchaseClassEntity).toString()) {
      return PurchaseClassEntity.fromJson(json) as M;
    }
    if (type == (PurchaseItemEntity).toString()) {
      return PurchaseItemEntity.fromJson(json) as M;
    }
    if (type == (PurchaseResp).toString()) {
      return PurchaseResp.fromJson(json) as M;
    }
    if (type == (PurchaseTag).toString()) {
      return PurchaseTag.fromJson(json) as M;
    }
    if (type == (PurchaseDetailResp).toString()) {
      return PurchaseDetailResp.fromJson(json) as M;
    }
    if (type == (PurchaseReplyResp).toString()) {
      return PurchaseReplyResp.fromJson(json) as M;
    }
    if (type == (PurchaseDetailEntity).toString()) {
      return PurchaseDetailEntity.fromJson(json) as M;
    }
    if (type == (PurchaseReplyEntity).toString()) {
      return PurchaseReplyEntity.fromJson(json) as M;
    }
    if (type == (PurchaseCommentResp).toString()) {
      return PurchaseCommentResp.fromJson(json) as M;
    }
    if (type == (PurchaseMyReplyResp).toString()) {
      return PurchaseMyReplyResp.fromJson(json) as M;
    }
    if (type == (PurchaseMyReplyEntity).toString()) {
      return PurchaseMyReplyEntity.fromJson(json) as M;
    }
    if (type == (PayInfoResp).toString()) {
      return PayInfoResp.fromJson(json) as M;
    }
    if (type == (PurchasePayInfoEntity).toString()) {
      return PurchasePayInfoEntity.fromJson(json) as M;
    }
    if (type == (PurchasePaidContentEntity).toString()) {
      return PurchasePaidContentEntity.fromJson(json) as M;
    }
    if (type == (PurchaseAppealEntity).toString()) {
      return PurchaseAppealEntity.fromJson(json) as M;
    }
    if (type == (PurchaseMyAppealResp).toString()) {
      return PurchaseMyAppealResp.fromJson(json) as M;
    }
    if (type == (PurchaseMyEvaluateEntity).toString()) {
      return PurchaseMyEvaluateEntity.fromJson(json) as M;
    }
    if (type == (InspOrderResp).toString()) {
      return InspOrderResp.fromJson(json) as M;
    }
    if (type == (InspOrderEntity).toString()) {
      return InspOrderEntity.fromJson(json) as M;
    }
    if (type == (AdminNote).toString()) {
      return AdminNote.fromJson(json) as M;
    }
    if (type == (AdminNoteResp).toString()) {
      return AdminNoteResp.fromJson(json) as M;
    }
    if (type == (HomeNewResp).toString()) {
      return HomeNewResp.fromJson(json) as M;
    }
    if (type == (HomeEntrance).toString()) {
      return HomeEntrance.fromJson(json) as M;
    }
    if (type == (HomeBanner).toString()) {
      return HomeBanner.fromJson(json) as M;
    }
    if (type == (HomeNotice).toString()) {
      return HomeNotice.fromJson(json) as M;
    }
    if (type == (HomeInspect).toString()) {
      return HomeInspect.fromJson(json) as M;
    }
    if (type == (HomePurchase).toString()) {
      return HomePurchase.fromJson(json) as M;
    }
    if (type == (HomeMall).toString()) {
      return HomeMall.fromJson(json) as M;
    }
    if (type == (GoodsDetailResp).toString()) {
      return GoodsDetailResp.fromJson(json) as M;
    }
    if (type == (GoodsDetailEntity).toString()) {
      return GoodsDetailEntity.fromJson(json) as M;
    }
    if (type == (SpecEntity).toString()) {
      return SpecEntity.fromJson(json) as M;
    }
    if (type == (SkuSpecKV).toString()) {
      return SkuSpecKV.fromJson(json) as M;
    }
    if (type == (SpecAttrEntity).toString()) {
      return SpecAttrEntity.fromJson(json) as M;
    }
    if (type == (SkuEntity).toString()) {
      return SkuEntity.fromJson(json) as M;
    }
    if (type == (ShopInfoEntity).toString()) {
      return ShopInfoEntity.fromJson(json) as M;
    }
    if (type == (MallAddressEntity).toString()) {
      return MallAddressEntity.fromJson(json) as M;
    }
    if (type == (MallAddressListResp).toString()) {
      return MallAddressListResp.fromJson(json) as M;
    }
    if (type == (OrderPayEntity).toString()) {
      return OrderPayEntity.fromJson(json) as M;
    }
    if (type == (MallOrderDetailEntity).toString()) {
      return MallOrderDetailEntity.fromJson(json) as M;
    }
    if (type == (OrderGoodsSkuEntity).toString()) {
      return OrderGoodsSkuEntity.fromJson(json) as M;
    }
    if (type == (OrderGoodsEntity).toString()) {
      return OrderGoodsEntity.fromJson(json) as M;
    }
    if (type == (MallOrderListResp).toString()) {
      return MallOrderListResp.fromJson(json) as M;
    }
    if (type == (PaymentResp).toString()) {
      return PaymentResp.fromJson(json) as M;
    }
    if (type == (PaymentMethod).toString()) {
      return PaymentMethod.fromJson(json) as M;
    }
    if (type == (MallHomeResp).toString()) {
      return MallHomeResp.fromJson(json) as M;
    }
    if (type == (MallHomeTab).toString()) {
      return MallHomeTab.fromJson(json) as M;
    }
    if (type == (MallHomeTabIcon).toString()) {
      return MallHomeTabIcon.fromJson(json) as M;
    }
    if (type == (OrderGoodsSnapshotEntity).toString()) {
      return OrderGoodsSnapshotEntity.fromJson(json) as M;
    }

    return null;
  }

  //list is returned by type
  static M? _getListChildType<M>(List<dynamic> data) {
    if (<AddressEntity>[] is M) {
      return data.map<AddressEntity>((e) => AddressEntity.fromJson(e)).toList() as M;
    }
    if (<AddressRows>[] is M) {
      return data.map<AddressRows>((e) => AddressRows.fromJson(e)).toList() as M;
    }
    if (<ApplyInfoEntity>[] is M) {
      return data.map<ApplyInfoEntity>((e) => ApplyInfoEntity.fromJson(e)).toList() as M;
    }
    if (<AreaListEntity>[] is M) {
      return data.map<AreaListEntity>((e) => AreaListEntity.fromJson(e)).toList() as M;
    }
    if (<BaiduOcrEntity>[] is M) {
      return data.map<BaiduOcrEntity>((e) => BaiduOcrEntity.fromJson(e)).toList() as M;
    }
    if (<BankEntity>[] is M) {
      return data.map<BankEntity>((e) => BankEntity.fromJson(e)).toList() as M;
    }
    if (<BillEntity>[] is M) {
      return data.map<BillEntity>((e) => BillEntity.fromJson(e)).toList() as M;
    }
    if (<BillCapitalModelList>[] is M) {
      return data.map<BillCapitalModelList>((e) => BillCapitalModelList.fromJson(e)).toList() as M;
    }
    if (<BindPayEntity>[] is M) {
      return data.map<BindPayEntity>((e) => BindPayEntity.fromJson(e)).toList() as M;
    }
    if (<LocationEntity>[] is M) {
      return data.map<LocationEntity>((e) => LocationEntity.fromJson(e)).toList() as M;
    }
    if (<LocationGeocodes>[] is M) {
      return data.map<LocationGeocodes>((e) => LocationGeocodes.fromJson(e)).toList() as M;
    }
    if (<LocationGeocodesNeighborhood>[] is M) {
      return data.map<LocationGeocodesNeighborhood>((e) => LocationGeocodesNeighborhood.fromJson(e)).toList() as M;
    }
    if (<LocationGeocodesBuilding>[] is M) {
      return data.map<LocationGeocodesBuilding>((e) => LocationGeocodesBuilding.fromJson(e)).toList() as M;
    }
    if (<OnlyPriceEntity>[] is M) {
      return data.map<OnlyPriceEntity>((e) => OnlyPriceEntity.fromJson(e)).toList() as M;
    }
    if (<OrderDetailEntity>[] is M) {
      return data.map<OrderDetailEntity>((e) => OrderDetailEntity.fromJson(e)).toList() as M;
    }
    if (<AddressInfo>[] is M) {
      return data.map<AddressInfo>((e) => AddressInfo.fromJson(e)).toList() as M;
    }
    if (<OrderInputEntity>[] is M) {
      return data.map<OrderInputEntity>((e) => OrderInputEntity.fromJson(e)).toList() as M;
    }
    if (<OrderListEntity>[] is M) {
      return data.map<OrderListEntity>((e) => OrderListEntity.fromJson(e)).toList() as M;
    }
    if (<OrderListRows>[] is M) {
      return data.map<OrderListRows>((e) => OrderListRows.fromJson(e)).toList() as M;
    }
    if (<PublishOrderEntity>[] is M) {
      return data.map<PublishOrderEntity>((e) => PublishOrderEntity.fromJson(e)).toList() as M;
    }
    //mydev
    if (<NoticeConvRows>[] is M) {
      return data.map<NoticeConvRows>((e) => NoticeConvRows.fromJson(e)).toList() as M;
    }
    if (<PublishConvEntity>[] is M) {
      return data.map<PublishConvEntity>((e) => PublishConvEntity.fromJson(e)).toList() as M;
    }
    if (<ChatMessageEntity>[] is M) {
      return data.map<ChatMessageEntity>((e) => ChatMessageEntity.fromJson(e)).toList() as M;
    }
    if (<AdminListEntity>[] is M) {
      return data.map<AdminListEntity>((e) => AdminListEntity.fromJson(e)).toList() as M;
    }
    if (<ChatUserEntity>[] is M) {
      return data.map<ChatUserEntity>((e) => ChatUserEntity.fromJson(e)).toList() as M;
    }
    //mydev
    /*if (<RecordRows>[] is M) {
      return data.map<RecordRows>((e) => RecordRows.fromJson(e)).toList() as M;
    }*/
    if (<PublishOrderRows>[] is M) {
      return data.map<PublishOrderRows>((e) => PublishOrderRows.fromJson(e)).toList() as M;
    }
    //mydev
    if (<PublishConvRows>[] is M) {
      return data.map<PublishConvRows>((e) => PublishConvRows.fromJson(e)).toList() as M;
    }
    if (<ReportEntity>[] is M) {
      return data.map<ReportEntity>((e) => ReportEntity.fromJson(e)).toList() as M;
    }
    if (<ReportModel>[] is M) {
      return data.map<ReportModel>((e) => ReportModel.fromJson(e)).toList() as M;
    }
    if (<InspectorReport>[] is M) {
      return data.map<InspectorReport>((e) => InspectorReport.fromJson(e)).toList() as M;
    }
    if (<UserInfoEntity>[] is M) {
      return data.map<UserInfoEntity>((e) => UserInfoEntity.fromJson(e)).toList() as M;
    }
    if (<VipPriceEntity>[] is M) {
      return data.map<VipPriceEntity>((e) => VipPriceEntity.fromJson(e)).toList() as M;
    }
    if (<WalletEntity>[] is M) {
      return data.map<WalletEntity>((e) => WalletEntity.fromJson(e)).toList() as M;
    }
    if (<OrderDateEntity>[] is M) {
      return data.map<OrderDateEntity>((e) => OrderDateEntity.fromJson(e)).toList() as M;
    }
    if (<WithdrawEntity>[] is M) {
      return data.map<WithdrawEntity>((e) => WithdrawEntity.fromJson(e)).toList() as M;
    }
    if (<ChargeHistoryEntity>[] is M) {
      return data.map<ChargeHistoryEntity>((e) => ChargeHistoryEntity.fromJson(e)).toList() as M;
    }
    if (<GroupEntity>[] is M) {
      return data.map<GroupEntity>((e) => GroupEntity.fromJson(e)).toList() as M;
    }
    if (<GroupInfoEntity>[] is M) {
      return data.map<GroupInfoEntity>((e) => GroupInfoEntity.fromJson(e)).toList() as M;
    }
    if (<GroupMemberEntity>[] is M) {
      return data.map<GroupMemberEntity>((e) => GroupMemberEntity.fromJson(e)).toList() as M;
    }
    if (<ReceiveMessage>[] is M) {
      return data.map<ReceiveMessage>((e) => ReceiveMessage.fromJson(e)).toList() as M;
    }
    if (<SavedAccount>[] is M) {
      return data.map<SavedAccount>((e) => SavedAccount.fromJson(e)).toList() as M;
    }
    if (<PurchaseSearchHistoryModel>[] is M) {
      return data.map<PurchaseSearchHistoryModel>((e) => PurchaseSearchHistoryModel.fromJson(e)).toList() as M;
    }
    if (<AssignInfoEntity>[] is M) {
      return data.map<AssignInfoEntity>((e) => AssignInfoEntity.fromJson(e)).toList() as M;
    }
    if (<OrderInfoEntity>[] is M) {
      return data.map<OrderInfoEntity>((e) => OrderInfoEntity.fromJson(e)).toList() as M;
    }
    if (<OrderProduct>[] is M) {
      return data.map<OrderProduct>((e) => OrderProduct.fromJson(e)).toList() as M;
    }
    if (<ProductModel>[] is M) {
      return data.map<ProductModel>((e) => ProductModel.fromJson(e)).toList() as M;
    }
    if (<PurchaseClassEntity>[] is M) {
      return data.map<PurchaseClassEntity>((e) => PurchaseClassEntity.fromJson(e)).toList() as M;
    }
    if (<PurchaseItemEntity>[] is M) {
      return data.map<PurchaseItemEntity>((e) => PurchaseItemEntity.fromJson(e)).toList() as M;
    }
    if (<PurchaseResp>[] is M) {
      return data.map<PurchaseResp>((e) => PurchaseResp.fromJson(e)).toList() as M;
    }
    if (<PurchaseTag>[] is M) {
      return data.map<PurchaseTag>((e) => PurchaseTag.fromJson(e)).toList() as M;
    }
    if (<PurchaseDetailResp>[] is M) {
      return data.map<PurchaseDetailResp>((e) => PurchaseDetailResp.fromJson(e)).toList() as M;
    }
    if (<PurchaseReplyResp>[] is M) {
      return data.map<PurchaseReplyResp>((e) => PurchaseReplyResp.fromJson(e)).toList() as M;
    }
    if (<PurchaseDetailEntity>[] is M) {
      return data.map<PurchaseDetailEntity>((e) => PurchaseDetailEntity.fromJson(e)).toList() as M;
    }
    if (<PurchaseReplyEntity>[] is M) {
      return data.map<PurchaseReplyEntity>((e) => PurchaseReplyEntity.fromJson(e)).toList() as M;
    }
    if (<PurchaseCommentResp>[] is M) {
      return data.map<PurchaseCommentResp>((e) => PurchaseCommentResp.fromJson(e)).toList() as M;
    }
    if (<PurchaseMyReplyResp>[] is M) {
      return data.map<PurchaseMyReplyResp>((e) => PurchaseMyReplyResp.fromJson(e)).toList() as M;
    }
    if (<PurchaseMyReplyEntity>[] is M) {
      return data.map<PurchaseMyReplyEntity>((e) => PurchaseMyReplyEntity.fromJson(e)).toList() as M;
    }
    if (<PayInfoResp>[] is M) {
      return data.map<PayInfoResp>((e) => PayInfoResp.fromJson(e)).toList() as M;
    }
    if (<PurchasePaidContentEntity>[] is M) {
      return data.map<PurchasePaidContentEntity>((e) => PurchasePaidContentEntity.fromJson(e)).toList() as M;
    }
    if (<PurchaseAppealEntity>[] is M) {
      return data.map<PurchaseAppealEntity>((e) => PurchaseAppealEntity.fromJson(e)).toList() as M;
    }
    if (<PurchaseMyAppealResp>[] is M) {
      return data.map<PurchaseMyAppealResp>((e) => PurchaseMyAppealResp.fromJson(e)).toList() as M;
    }
    if (<PurchaseMyEvaluateEntity>[] is M) {
      return data.map<PurchaseMyEvaluateEntity>((e) => PurchaseMyEvaluateEntity.fromJson(e)).toList() as M;
    }
    if (<PurchasePayInfoEntity>[] is M) {
      return data.map<PurchasePayInfoEntity>((e) => PurchasePayInfoEntity.fromJson(e)).toList() as M;
    }
    if (<InspOrderEntity>[] is M) {
      return data.map<InspOrderEntity>((e) => InspOrderEntity.fromJson(e)).toList() as M;
    }
    if (<InspOrderResp>[] is M) {
      return data.map<InspOrderResp>((e) => InspOrderResp.fromJson(e)).toList() as M;
    }
    if (<AdminNote>[] is M) {
      return data.map<AdminNote>((e) => AdminNote.fromJson(e)).toList() as M;
    }
    if (<AdminNoteResp>[] is M) {
      return data.map<AdminNoteResp>((e) => AdminNoteResp.fromJson(e)).toList() as M;
    }
    if (<HomeNewResp>[] is M) {
      return data.map<HomeNewResp>((e) => HomeNewResp.fromJson(e)).toList() as M;
    }
    if (<HomeEntrance>[] is M) {
      return data.map<HomeEntrance>((e) => HomeEntrance.fromJson(e)).toList() as M;
    }
    if (<HomeBanner>[] is M) {
      return data.map<HomeBanner>((e) => HomeBanner.fromJson(e)).toList() as M;
    }
    if (<HomeNotice>[] is M) {
      return data.map<HomeNotice>((e) => HomeNotice.fromJson(e)).toList() as M;
    }
    if (<HomeInspect>[] is M) {
      return data.map<HomeInspect>((e) => HomeInspect.fromJson(e)).toList() as M;
    }
    if (<HomePurchase>[] is M) {
      return data.map<HomePurchase>((e) => HomePurchase.fromJson(e)).toList() as M;
    }
    if (<HomeMall>[] is M) {
      return data.map<HomeMall>((e) => HomeMall.fromJson(e)).toList() as M;
    }
    if (<SpecAttrEntity>[] is M) {
      return data.map<SpecAttrEntity>((e) => SpecAttrEntity.fromJson(e)).toList() as M;
    }
    if (<SpecEntity>[] is M) {
      return data.map<SpecEntity>((e) => SpecEntity.fromJson(e)).toList() as M;
    }
    if (<GoodsDetailResp>[] is M) {
      return data.map<GoodsDetailResp>((e) => GoodsDetailResp.fromJson(e)).toList() as M;
    }
    if (<GoodsDetailEntity>[] is M) {
      return data.map<GoodsDetailEntity>((e) => GoodsDetailEntity.fromJson(e)).toList() as M;
    }
    if (<SkuEntity>[] is M) {
      return data.map<SkuEntity>((e) => SkuEntity.fromJson(e)).toList() as M;
    }
    if (<SkuSpecKV>[] is M) {
      return data.map<SkuSpecKV>((e) => SkuSpecKV.fromJson(e)).toList() as M;
    }
    if (<ShopInfoEntity>[] is M) {
      return data.map<ShopInfoEntity>((e) => ShopInfoEntity.fromJson(e)).toList() as M;
    }
    if (<MallAddressEntity>[] is M) {
      return data.map<MallAddressEntity>((e) => MallAddressEntity.fromJson(e)).toList() as M;
    }
    if (<MallAddressListResp>[] is M) {
      return data.map<MallAddressListResp>((e) => MallAddressListResp.fromJson(e)).toList() as M;
    }
    if (<OrderPayEntity>[] is M) {
      return data.map<OrderPayEntity>((e) => OrderPayEntity.fromJson(e)).toList() as M;
    }
    if (<MallOrderDetailEntity>[] is M) {
      return data.map<MallOrderDetailEntity>((e) => MallOrderDetailEntity.fromJson(e)).toList() as M;
    }
    if (<OrderGoodsSkuEntity>[] is M) {
      return data.map<OrderGoodsSkuEntity>((e) => OrderGoodsSkuEntity.fromJson(e)).toList() as M;
    }
    if (<OrderGoodsEntity>[] is M) {
      return data.map<OrderGoodsEntity>((e) => OrderGoodsEntity.fromJson(e)).toList() as M;
    }
    if (<MallOrderListResp>[] is M) {
      return data.map<MallOrderListResp>((e) => MallOrderListResp.fromJson(e)).toList() as M;
    }
    if (<OrderGoodsSnapshotEntity>[] is M) {
      return data.map<OrderGoodsSnapshotEntity>((e) => OrderGoodsSnapshotEntity.fromJson(e)).toList() as M;
    }
    if (<PaymentMethod>[] is M) {
      return data.map<PaymentMethod>((e) => PaymentMethod.fromJson(e)).toList() as M;
    }
    if (<PaymentResp>[] is M) {
      return data.map<PaymentResp>((e) => PaymentResp.fromJson(e)).toList() as M;
    }
    if (<MallHomeResp>[] is M) {
      return data.map<MallHomeResp>((e) => MallHomeResp.fromJson(e)).toList() as M;
    }
    if (<MallHomeTab>[] is M) {
      return data.map<MallHomeTab>((e) => MallHomeTab.fromJson(e)).toList() as M;
    }
    if (<MallHomeTabIcon>[] is M) {
      return data.map<MallHomeTabIcon>((e) => MallHomeTabIcon.fromJson(e)).toList() as M;
    }

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json == null) {
      return null;
    }
    if (json is List) {
      return _getListChildType<M>(json);
    } else {
      return _fromJsonSingle<M>(json as Map<String, dynamic>);
    }
  }
}
