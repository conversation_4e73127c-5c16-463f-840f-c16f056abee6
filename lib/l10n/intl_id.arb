{"@@locale": "id", "app_name": "Inspeksi Online", "search": "<PERSON><PERSON>", "shortcut_tab_name": "Navigasi Cepat", "loading": "Memuat...", "nomore": "Tidak ada konten lagi", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "more_replies": "<PERSON><PERSON><PERSON> banyak balasan", "purchase_paid_publish_information_title": "<PERSON><PERSON>h yang pelanggan bayar untuk melihatnya", "purchase_set_fee": "Tetapkan biaya menonton", "purchase_comment_paid_supplier_hint": "<PERSON><PERSON><PERSON> masukkan nama pema<PERSON>k", "purchase_comment_paid_contact_hint": "<PERSON><PERSON><PERSON> masukkan nama kontak", "purchase_comment_paid_phone_hint": "<PERSON><PERSON>an masukkan nomor telepon kontak", "purchase_comment_paid_email_hint": "<PERSON><PERSON><PERSON> ma<PERSON>kkan email kontak", "purchase_comment_paid_address_hint": "<PERSON><PERSON><PERSON> masukkan alamat pabrik", "purchase_comment_paid_other_hint": "Informasi lain (opsional)", "purchase_comment_paid_low_price_hint": "<PERSON><PERSON><PERSON> masukkan harga dasar produk (opsional)", "purchase_reply_paid_title": "<PERSON><PERSON><PERSON>", "purchase_reply_paid_desc": "(Informasi pemasok dan harga referensi produk)", "purchase_reply_paid_evaluated_people": "({people_num} orang telah mengevalua<PERSON>)", "language_setting": "Pengaturan Bahasa", "public_and": "dan", "public_publish": "Publikasi", "public_distance": "Jarak", "public_deny": "<PERSON><PERSON>", "public_see_more": "<PERSON><PERSON> lebih banyak", "general_select": "<PERSON><PERSON><PERSON>", "public_seconds_ago": "detik yang lalu", "public_minutes_ago": "menit yang lalu", "public_hours_ago": "jam yang lalu", "public_status_applied": "Telah Diajukan", "public_status_refused": "<PERSON><PERSON><PERSON>", "public_status_approved": "Disetuju<PERSON>", "public_status_canceled": "Di<PERSON><PERSON><PERSON>", "language_page_title": "Multi Bahasa", "language_chinese": "Bahasa Mandarin", "language_english": "Bahasa Inggris", "public_app_name": "Inspector", "public_send": "<PERSON><PERSON>", "public_ok": "OK", "public_price": "<PERSON><PERSON>", "public_cancel": "<PERSON><PERSON>", "public_manage": "<PERSON><PERSON><PERSON>", "public_finish": "Se<PERSON><PERSON>", "public_reset": "Reset", "public_leave_message": "Tinggalkan pesan", "public_share": "Bagikan", "login_submit": "<PERSON><PERSON><PERSON>", "login_mobile_login": "<PERSON><PERSON> dengan <PERSON>", "login_mobile_tips": "<PERSON><PERSON><PERSON> masukkan nomor ponsel", "login_email_login": "<PERSON><PERSON> dengan <PERSON>", "login_email_tips": "<PERSON><PERSON><PERSON> ma<PERSON> email", "registry_email_phone_tips": "<PERSON><PERSON><PERSON> ma<PERSON>kkan email atau nomor ponsel", "login_verify_tips": "Masukkan kode verifikasi", "login_password_tips": "<PERSON><PERSON><PERSON><PERSON> kata sandi", "login_password_login": "<PERSON><PERSON> dengan <PERSON>", "login_verify_login": "<PERSON><PERSON> dengan Kode Verifikasi", "login_register": "<PERSON><PERSON><PERSON>", "login_take_code": "Dapatkan Kode Verifikasi", "login_forget_password": "Lupa Kata Sandi", "login_agreement": "<PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> inspector.Itd'", "login_area_selected": "<PERSON><PERSON><PERSON> Negara", "tab_home": "Be<PERSON><PERSON>", "tab_order": "<PERSON><PERSON><PERSON>", "tab_shortcut": "<PERSON><PERSON><PERSON>", "tab_purchase": "Pembelian", "tab_message": "<PERSON><PERSON>", "tab_mine": "<PERSON><PERSON>", "supplement_title": "<PERSON><PERSON><PERSON> lengkapi informasi pribadi Anda terlebih dahulu", "supplement_next": "<PERSON><PERSON><PERSON><PERSON>", "home_title": "Plaza Inspeksi", "home_record": "Catatan Aplikasi", "home_newest": "Inspeksi Terbaru", "home_nearest": "Inspeksi Terdekat", "home_recommend": "Bonus Rekomendasi RMB {money}", "home_sampling": "Inspeksi Sampel", "home_word": "Laporan WORD", "home_unit": "{people} orang/{day} hari", "home_product_tip": "Produk:", "home_person_apply": "orang menga<PERSON>", "home_know_tip": "Panduan Inspeksi", "home_inspection_tip": "Biaya inspeksi default adalah harga yang disepakati, dapat diubah, biaya rendah mungkin diprioritaskan untuk penugasan", "home_reviewed": "<PERSON>a telah membaca dan akan mematuhi", "home_apply": "Aju<PERSON>", "home_apply_price": "<PERSON><PERSON><PERSON> masukkan jumlah <PERSON>", "home_apply_check": "<PERSON><PERSON><PERSON> periksa panduan inspeksi", "home_apply_tips": "<PERSON><PERSON> bukan inspektor, jika <PERSON>a memiliki pengalaman kerja inspeksi perdagangan luar negeri lebih dari 1 tahun, si<PERSON>an berikan bukti kualifikasi terkait", "home_complete_profile_tips": "Melengkapi profil inspektor dan informasi terkait lainnya dapat meningkatkan tingkat persetujuan aplikasi inspeksi", "home_apply_sure": "<PERSON><PERSON> un<PERSON> Ditin<PERSON>", "home_complete_profile_sure": "<PERSON><PERSON><PERSON><PERSON>", "home_apply_cancel": "Batalkan Aplikasi", "home_update": "Ubah", "home_navi": "Na<PERSON><PERSON><PERSON>", "mine_unauth": "Belum Di<PERSON>if<PERSON>", "mine_checking": "<PERSON><PERSON><PERSON>", "mine_check_failed": "<PERSON><PERSON><PERSON><PERSON>", "mine_vip_level": "Level VIP", "mine_credit_quota": "Batas <PERSON>", "mine_authed": "Ubah Informasi Verifikasi", "mine_authed_inspector": "Ubah Informasi Inspektor", "mine_amount": "<PERSON><PERSON><PERSON>", "mine_cash": "<PERSON><PERSON>/<PERSON><PERSON>", "mine_order": "<PERSON><PERSON><PERSON>", "mine_purchase": "<PERSON><PERSON><PERSON><PERSON>", "mine_check": "Inspek<PERSON>", "mine_address": "<PERSON><PERSON> (Pemasok)", "mine_recommend": "Rekomendasi", "mine_setting": "<PERSON><PERSON><PERSON><PERSON>", "mine_header_inspect": "Man<PERSON><PERSON>en Inspeksi", "mine_header_purchase": "<PERSON><PERSON><PERSON><PERSON>", "mine_header_other": "<PERSON><PERSON><PERSON>", "mine_inspect_mine": "Inspek<PERSON>", "mine_inspect_order": "<PERSON><PERSON><PERSON><PERSON>", "mine_inspect_history": "Riwayat Aplikasi", "mine_purchase_mine": "<PERSON><PERSON><PERSON><PERSON>", "mine_purchase_reply": "Ri<PERSON><PERSON> Balasan", "mine_purchase_appeal": "Manajemen Banding", "mine_other_recommend": "Rekomendasi", "mine_other_address": "<PERSON><PERSON> (Pemasok)", "mine_other_settings": "<PERSON><PERSON><PERSON><PERSON>", "profile_title": "Informasi Pribadi", "profile_avatar": "Avatar", "profile_name": "<PERSON><PERSON>", "profile_mobile": "<PERSON><PERSON>", "profile_country": "Negara", "profile_real_name": "<PERSON><PERSON>", "profile_city": "Kota", "profile_email": "Email", "profile_wechat": "WeChat", "profile_bind_manage": "Manajemen Pengikatan Akun", "profile_currency": "<PERSON><PERSON>", "profile_info_failed": "Pembaruan informasi gagal", "apply_title": "Aplikasi Kualifikasi Inspektor", "apply_nick": "<PERSON><PERSON>", "apply_sex": "<PERSON><PERSON>", "apply_birthday": "<PERSON><PERSON>", "apply_education": "Pendidikan", "apply_address": "<PERSON><PERSON><PERSON> Tinggal", "apply_price": "Biaya Inspeksi Minimum", "apply_shebao": "Asuransi Sosial", "apply_id_card": "Nomor KTP", "apply_file": "Edit Resume", "apply_file_tip": "<PERSON><PERSON><PERSON> masukkan informasi dasar dan pengalaman <PERSON>a", "apply_upload_file": "Unggah Resume", "apply_upload_file_failed": "<PERSON><PERSON> resume", "apply_upload_card": "Unggah Foto KTP", "apply_card_front": "Foto Depan (<PERSON><PERSON>)", "apply_card_back": "Foto Belakang (Sisi Lambang Negara)", "apply_submit": "<PERSON><PERSON>", "apply_enter": "<PERSON><PERSON><PERSON> ma<PERSON>kkan", "apply_next_tip": "<PERSON><PERSON><PERSON> kon<PERSON><PERSON><PERSON> bahwa informasi telah lengkap sebelum mengirim", "apply_auth_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>, silakan unggah foto yang benar", "apply_checking": "Menunggu peninjauan identitas", "apply_check_success": "Peninjauan identitas berhasil", "apply_check_failed": "<PERSON><PERSON><PERSON><PERSON>, silakan ubah konten dan kirim ulang", "order_title": "<PERSON><PERSON><PERSON>", "order_input": "Inspek<PERSON>", "order_output": "<PERSON>lik<PERSON><PERSON>", "order_all": "<PERSON><PERSON><PERSON>", "order_wait_pay": "<PERSON><PERSON><PERSON>", "order_cancelled": "Di<PERSON><PERSON><PERSON>", "order_status": "<PERSON> Pesanan", "order_status_unknown": "Tidak Diketahui", "order_refund_pending": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "order_cancelled_refund_pending": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "order_refund_partial": "<PERSON><PERSON><PERSON><PERSON>", "order_refund_denied": "<PERSON><PERSON><PERSON><PERSON>", "order_wait_dispatch": "<PERSON><PERSON><PERSON>", "order_ready_inspect": "Siap untuk Inspeksi", "order_need_pay": "Bayar", "order_wait": "Siap untuk Inspeksi", "order_confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order_doing": "<PERSON><PERSON>", "order_comment": "<PERSON><PERSON><PERSON>", "order_finished": "Se<PERSON><PERSON>", "order_goods_info": "Informasi Produk", "order_goods_name": "<PERSON><PERSON>", "order_goods_model": "Model Produk", "order_goods_count": "<PERSON><PERSON><PERSON>", "order_goods_unit": "Unit", "general_date": "Tanggal", "general_desc": "<PERSON><PERSON><PERSON><PERSON>", "general_amount": "<PERSON><PERSON><PERSON>", "general_others": "<PERSON><PERSON><PERSON>", "order_order_time": "<PERSON><PERSON><PERSON>", "order_order_amount": "<PERSON><PERSON><PERSON>", "order_detail_title": "<PERSON><PERSON>", "order_applying": "Telah Diajukan", "order_apply_expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order_apply_dispatched": "Telah Ditugaskan", "order_create_time": "<PERSON><PERSON><PERSON>", "order_look": "<PERSON><PERSON> Laporan Inspeksi", "order_report_next": "<PERSON><PERSON> Inspeksi", "order_detail_inspection_info": "Informasi Inspeksi", "order_inspection_status_unpaid": "<PERSON><PERSON>", "order_inspection_status_returned": "Di<PERSON><PERSON>lik<PERSON>", "order_inspection_status_waiting_start": "<PERSON><PERSON><PERSON>", "order_detail_related_info": "Informasi Sub-pesanan Terkait", "order_detail_inspection_product": "<PERSON><PERSON>", "order_detail_inspection_time": "<PERSON><PERSON><PERSON>", "order_detail_inspection_city": "Kota Inspeksi", "order_detail_inspection_factory": "Pabrik Inspeksi", "order_detail_inspection_address": "<PERSON><PERSON><PERSON>", "order_detail_inspection_person": "Kontak", "order_detail_inspection_phone": "Nomor Telepon Kontak", "order_detail_inspection_email": "<PERSON><PERSON>", "order_detail_inspection_amount": "Informasi <PERSON>", "order_detail_inspection_sample": "Sampel", "order_detail_inspection_standard": "<PERSON><PERSON><PERSON>", "order_detail_inspection_critical": "AQL", "order_detail_inspection_major": "Major", "order_detail_inspection_minor": "Minor", "order_detail_inspection_day": "<PERSON><PERSON><PERSON>", "order_detail_inspection_number": "<PERSON><PERSON><PERSON>", "order_detail_inspection_price": "<PERSON><PERSON>", "order_detail_inspection_price_apply": "<PERSON><PERSON>", "order_detail_inspection_template": "Template Inspeksi", "order_detail_inspection_file": "Lam<PERSON>ran", "order_detail_inspection_image": "Gambar", "order_detail_inspection_type": "<PERSON><PERSON>", "order_tips": "Sampel dan <PERSON>n", "order_apply": "Ajukan <PERSON>speksi", "order_cancel": "<PERSON><PERSON>", "order_canceled": "Telah Di<PERSON>alkan", "order_dispatch_accept": "<PERSON><PERSON><PERSON>", "order_dispatch_refuse": "<PERSON><PERSON>", "order_inspection": "<PERSON><PERSON>", "order_sure_cancel": "<PERSON><PERSON> membatalkan pesanan", "order_sure_refuse": "Yakin menolak pesanan ini?", "order_sure_confirm": "Yakin menerima pesanan ini?", "publish_title": "<PERSON>lik<PERSON><PERSON>", "publish_edit_title": "<PERSON>", "publish_welcome": "Selamat datang untuk memesan, harga bebas, komuni<PERSON>i bebas dengan inspektor", "publish_sampling": "Inspeksi Sampel", "publish_sampling_content": "Berlaku untuk produk yang 100% selesai, setidaknya 80% produk telah dikemas, siap untuk pengiriman. Ka<PERSON> melakukan inspeksi sampel acak berdasarkan rencana sampling internasional ANSI/ASQZ1.4(MIL-STD-105E) serta mengacu pada persyaratan khusus Anda. <PERSON><PERSON> laporan inspeksi sampel, kami akan sepenuhnya mencerminkan jumlah produk yang selesai, kond<PERSON> kemasan, dan apakah memenuhi persyaratan AQL (Acceptable Quality Level), memberi Anda pemahaman menyeluruh tentang kualitas seluruh batch produk sebelum pengiriman, menghindari risiko apa pun pada pesanan Anda", "publish_point": "<PERSON><PERSON>", "publish_sampling_point": "● Verifikasi data/sampel pelanggan\n● Verifikasi jumlah yang selesai\n● Verifikasi ukuran, gaya, warna produk\n● Pemeriksaan tampilan dan penger<PERSON>\n● Pengujian fungsi dan keamanan produk\n● Pemeriksaan label pengiriman\n● Integritas kemasan\n● Detail kemasan spesifik\n● Persyaratan khusus pelanggan", "publish_all": "<PERSON>sp<PERSON><PERSON>", "publish_all_content": "Inspeksi penuh dapat dilakukan sebelum atau sesudah pengemasan. <PERSON><PERSON><PERSON> permintaan pelanggan, setiap produk diperiksa untuk tampilan, ukuran, pen<PERSON><PERSON><PERSON>, fungsi dan keamanan, memisahkan produk yang baik dan cacat, dan melaporkan hasil inspeksi kepada pelanggan secara tepat waktu", "publish_online": "Inspeksi Online", "publish_online_content": "Inspeksi online dilakukan selama proses produksi atau sebelum semua produksi selesai dan dikemas. Ini dapat membantu Anda memastikan kualitas, fungsi, tampilan, dan elemen lain dari produk tetap konsisten dengan spesifikasi Anda selama seluruh proses produksi, juga membantu menemukan ketidak<PERSON><PERSON>ian lebih awal, se<PERSON>ga mengurangi risiko keterlambatan pengiriman dari pabrik", "publish_online_point": "● Tindak lanjut situasi produksi\n● Evaluasi lini produksi dan konfirmasi kemajuan produksi\n● Inspeksi sampel produk setengah jadi dan jadi\n● Periksa informasi kemasan dan bahan kemasan\n● Perbaikan produk cacat\n● Evaluasi waktu pengiriman", "publish_factory": "Audit Pabrik", "publish_factory_content": "Audit pabrik terutama menggunakan metode penilaian objektif, mengevaluasi dan mengaudit pabrik secara kuantitatif berdasarkan standar atau kriteria yang telah ditetapkan sebelumnya. Laporan evaluasi dibuat berdasarkan penilaian di tempat dan hasil audit komprehensif pabrik, sebagai dasar bagi pelanggan untuk menentukan apakah pabrik tersebut dapat menjadi pemasok yang memenuhi syarat", "publish_factory_point_title": "Konten Audit", "publish_factory_point": "● Gambaran umum pabrik (informasi dasar)\n● Struktur organisasi\n● Proses produksi\n● Kapasitas produksi\n● Kemampuan penelitian dan pengembangan\n● Peralatan mesin dan fasilitas", "publish_factory_review": "<PERSON><PERSON><PERSON>", "publish_factory_review_content": "● Untuk setiap item audit, pertimbangkan kepentingan relatifnya, berikan skor yang berbeda, kem<PERSON>an buat tabel penilaian kualifikasi berdasarkan formulir survei audit dan data survei lapangan", "publish_watch": "Pengawasan Pem<PERSON>tan", "publish_watch_content": "Pengawasan pemuatan kontainer terutama mencakup evaluasi kondisi kontainer, verifikasi informasi produk, penghitungan jumlah kotak yang dimuat, pemeriksaan informasi kemasan, dan pengawa<PERSON> seluruh proses pemuatan. Untuk mengurangi risiko tinggi penggantian barang setelah pemuatan, inspektor mengawasi di lokasi pemuatan untuk memastikan produk yang Anda bayar dimuat dengan aman", "publish_watch_point": "● Catat nomor kontainer dan nomor truk\n● <PERSON>iksa kontainer untuk kerusakan, <PERSON><PERSON><PERSON><PERSON>, dan bau aneh, ambil foto kontainer kosong\n● Periksa jumlah kotak yang akan dimuat dan kondisi kemasan luar, periksa secara acak beberapa kotak untuk memastikan produk yang sebenarnya dimuat\n● Awasi proses pemuatan untuk memastikan kerusakan minimal dan pemanfaatan ruang maksimal\n● Ambil foto kondisi penutupan kontainer, nomor segel kontainer, dan daftar kemasan, catat waktu keberangkatan kontainer", "publish_watch_inspection": "Inspeksi + Pengawasan Pemuatan", "publish_watch_inspection_content": "Untuk memastikan kualitas akhir dan keleng<PERSON>pan produk, sebelum siap untuk pengiriman, kami mengambil sampel secara acak dari produk jadi untuk inspeksi sampel berdasarkan rencana sampling internasional, dan memverifikasi data yang diberikan pela<PERSON>gan, serta mengawasi seluruh proses pemuatan kontainer", "publish_watch_inspection_point": "● Sebelum kontainer tiba, verifikasi data/sampel pelanggan, lakukan inspeksi sampel pada tampilan, pen<PERSON><PERSON><PERSON>, fungsi dan keamanan produk, serta kemasan, label pengiriman, dll.\n● Segera komunikasikan dengan pabrik jika ditemukan produk cacat, untuk penggantian atau pengerjaan ulang\n● Periksa kontainer untuk kerusakan, k<PERSON><PERSON><PERSON>, dan bau aneh, ambil foto kontainer kosong\n● Awasi proses pemuatan untuk memastikan kerusakan minimal dan pemanfaatan ruang maksimal\n● Ambil foto kondisi penutupan kontainer, nomor segel kontainer, dan daftar kemasan, catat waktu keberangkatan kontainer", "publish_next": "Selanjutnya", "publish_inspection_time": "<PERSON><PERSON><PERSON>", "publish_inspection_time_selected": "<PERSON><PERSON><PERSON>", "publish_inspection_time_tip": "<PERSON><PERSON><PERSON> pilih", "publish_inspection_people": "<PERSON><PERSON><PERSON>", "publish_people": "orang", "publish_day": "hari", "publish_inspection_factory": "Pabrik Inspeksi", "publish_factory_tips": "<PERSON><PERSON><PERSON><PERSON> pabrik <PERSON>i", "publish_address_book": "<PERSON><PERSON>", "publish_goods_name": "<PERSON><PERSON>", "publish_name_tips": "<PERSON><PERSON><PERSON><PERSON> satu atau dua nama representatif jika ada beberapa produk", "publish_po_tips": "<PERSON><PERSON>an masukkan nomor P.O", "publish_file_tips": "<PERSON><PERSON><PERSON>", "publish_camera": "Unggah foto", "publish_file": "Unggah file", "publish_purchase": "Publikasikan <PERSON>", "publish_inspection": "Publikasikan Pesanan Inspeksi", "publish_factory_tip": "<PERSON><PERSON><PERSON> pilih informasi alamat inspeksi terlebih dahulu", "publish_attention": "Catatan", "publish_attention_tips": "<PERSON><PERSON><PERSON> masukkan masalah apa yang perlu diperhatikan oleh inspektor", "publish_stand_price": "<PERSON><PERSON>", "publish_click_price": "Ubah", "publish_vip_price": "Harga <PERSON>", "publish_vip_tips": "<PERSON><PERSON><PERSON><PERSON> layanan pemantauan manual selama proses", "publish_total": "Total", "publish_submit": "<PERSON><PERSON>", "publish_only_price_failed": "Tidak ada izin harga tetap", "publish_price_tip": "<PERSON><PERSON>an pilih harga", "publish_date_tips": "<PERSON><PERSON>an pilih tanggal", "date_title": "Tanggal Inspeksi", "date_save": "Simpan", "address_title": "Edit Informasi Pabrik", "address_auto_tips": "<PERSON><PERSON><PERSON> tempel atau masukkan teks, klik \"Identifikasi\" untuk secara otomatis mengidentifikasi nama pabrik, nama dan nomor telepon, <PERSON><PERSON><PERSON>, dll", "address_paste": "Tempel", "address_ocr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address_name": "<PERSON><PERSON>", "address_name_tip": "<PERSON><PERSON><PERSON> masukkan informasi pabrik inspeksi", "address_person": "Kontak", "address_person_tip": "<PERSON><PERSON><PERSON> masukkan kontak", "address_mobile": "<PERSON><PERSON>", "address_mobile_tip": "<PERSON><PERSON><PERSON> masukkan nomor ponsel", "address_email": "Email", "address_email_tip": "<PERSON><PERSON><PERSON> ma<PERSON>kkan al<PERSON> email", "address_area": "Provinsi-Kota-Kabupaten", "address_area_tip": "<PERSON><PERSON><PERSON> p<PERSON>h <PERSON>-Kota-Kabupaten  〉", "address_detail": "<PERSON><PERSON><PERSON>", "address_detail_tip": "Ma<PERSON>kkan informasi jalan, nomor, dll", "address_location": "<PERSON><PERSON>", "address_save_tip": "Simpan ke Buku Alama<PERSON>", "address_clear": "Hapus", "address_submit": "<PERSON><PERSON>", "address_recent": "<PERSON><PERSON><PERSON> yang <PERSON>", "address_more": "Lebih Banyak <PERSON>", "address_list_title": "<PERSON><PERSON><PERSON><PERSON>", "address_insert": "Tam<PERSON> Alamat", "address_delete": "Hapus", "address_delete_result": "Penghapusan Gagal", "address_edit": "Edit", "address_delete_tips": "Kon<PERSON>rma<PERSON> hapus alamat?", "address_detected_paste": "Informasi alamat te<PERSON>, gunakan alamat ini?", "pay_title": "<PERSON><PERSON>", "pay_time": "Waktu Tersisa untuk Pembayaran", "pay_paypal": "PayPal", "pay_zfb": "Alipay", "pay_usd": "Akun USD", "pay_rmb": "Akun R<PERSON>", "pay_pay": "Bayar", "pay_result_success": "Pembayaran Berhasil", "pay_result_success_wait": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> pem<PERSON><PERSON> pesanan.", "pay_result_failed": "Pembayaran Gagal", "pay_keep": "Dapat dicatat", "check_title": "<PERSON><PERSON><PERSON>", "check_picture": "Foto Inspeksi", "check_file": "Surat Komitmen Integritas", "check_report": "Surat Komitmen Integritas + <PERSON><PERSON><PERSON>", "check_draft": "Laporan Draft", "check_template": "Template <PERSON><PERSON>", "check_submit": "<PERSON><PERSON>", "check_hint": "<PERSON><PERSON><PERSON> masukkan <PERSON><PERSON> gambar", "check_checking": "Laporan sedang ditinjau", "check_check_success": "<PERSON><PERSON><PERSON><PERSON> lap<PERSON> ber<PERSON>", "check_check_failed": "<PERSON><PERSON><PERSON><PERSON> gagal, silakan ubah konten dan kirim ulang", "review_title": "<PERSON><PERSON><PERSON>", "review_next": "<PERSON><PERSON><PERSON>", "contact_bnt": "Hubung<PERSON>", "review_score": "<PERSON><PERSON><PERSON>", "review_score1": "Ke<PERSON><PERSON>", "review_score2": "Tidak <PERSON>s", "review_score3": "Biasa", "review_score4": "<PERSON><PERSON><PERSON>", "review_score5": "<PERSON><PERSON>", "review_tips": "Evaluasi dari berbagai sudut untuk membantu kami lebih memahami kemampuan kerja inspektor", "review_picture": "Gambar", "review_submit": "<PERSON><PERSON>", "setting_title": "<PERSON><PERSON><PERSON><PERSON>", "setting_address": "<PERSON><PERSON><PERSON><PERSON>", "setting_clear_cache": "<PERSON><PERSON><PERSON><PERSON>", "setting_clear_success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setting_about_us": "<PERSON><PERSON><PERSON>", "setting_receive_msg": "Terima Pemberitahuan Pesan", "setting_version": "<PERSON><PERSON>", "setting_check_update": "<PERSON>ik<PERSON>", "setting_login_out": "<PERSON><PERSON><PERSON>", "setting_login_out_tips": "Yakin ingin keluar?", "setting_delete_account_tips": "Yakin ingin menghapus akun?", "setting_policy_title": "Pemberitahuan O<PERSON>", "setting_policy_sub_title": "Sebel<PERSON> melan<PERSON>, harap baca dan setujui", "setting_privacy_policy": "<PERSON><PERSON><PERSON><PERSON>", "setting_user_agreement": "<PERSON><PERSON><PERSON><PERSON>", "setting_privacy_content": "Selamat datang di Aplikasi Inspeksi Online\nKami sangat menghargai privasi dan perlindungan informasi pribadi Anda. Se<PERSON>a Anda menggunakan Aplikasi ini, kami akan mengumpulkan dan menggunakan sebagian informasi pribadi Anda\n1. <PERSON><PERSON><PERSON><PERSON> menyetujui kebijakan privasi Aplikasi, kami akan melakukan inisialisasi SDK terintegrasi, yang akan mengumpulkan alamat MAC perangkat Anda, IMSI, Android ID, alamat IP, model perangkat keras, nomor versi sistem operasi, pengidentifikasi perangkat unik (seperti IMEI), alamat perangkat keras jaringan (MAC), nomor versi perangkat lunak, metode akses jaringan, jenis, status, data kualitas jaringan, log operasi, nomor seri perangkat keras, informasi log layanan, dll. untuk memastikan statistik data normal dan kontrol keamanan Aplikasi.\n2. <PERSON><PERSON>, kami tidak akan memperole<PERSON>, be<PERSON><PERSON>, atau memberikan informasi Anda kepada pihak ketiga.\n3. <PERSON><PERSON> da<PERSON><PERSON> menga<PERSON>, memper<PERSON><PERSON>, menghapus informasi pribadi Anda, dan kami juga akan menyediakan cara untuk membatalkan dan mengajukan keluhan.", "setting_ihavereadandagreed": "<PERSON>a telah membaca dan menye<PERSON><PERSON>i", "setting_policy_tips2": "<PERSON><PERSON> baca dan pahami dengan seksama", "wallet_title": "Dompet", "wallet_bill": "<PERSON><PERSON><PERSON>", "wallet_rmb_account": "Akun R<PERSON>", "wallet_usd_account": "Akun USD", "wallet_account_heading": "<PERSON><PERSON><PERSON> dan <PERSON>", "wallet_bank": "Kartu Bank", "wallet_wechat": "WeChat", "wallet_alipay": "Alipay", "wallet_charge": "<PERSON><PERSON>", "wallet_cash": "<PERSON><PERSON>", "wallet_balance": "<PERSON><PERSON>", "wallet_default_account": "<PERSON><PERSON><PERSON>", "wallet_set_default_account": "Atur sebagai Aku<PERSON>", "bill_title": "<PERSON><PERSON><PERSON>", "bill_out": "<PERSON><PERSON><PERSON><PERSON>", "bill_in": "<PERSON><PERSON><PERSON><PERSON>", "bill_unfreeze": "Pencairan", "bill_month": "<PERSON><PERSON><PERSON>", "bill_fenxi": "<PERSON><PERSON><PERSON>", "bill_all": "<PERSON><PERSON><PERSON>", "bill_income": "<PERSON><PERSON><PERSON><PERSON>", "bill_outcome": "<PERSON><PERSON><PERSON><PERSON>", "bill_freeze": "<PERSON><PERSON><PERSON><PERSON>", "bill_withdraw": "Penarikan", "bank_title": "Kartu Bank Saya", "bank_add": "Tambah Kartu Bank", "add_bank_title": "Tambah Kartu Bank", "add_bank_name": "Nama Kartu Bank", "add_bank_branch": "Cabang Bank", "add_bank_card": "<PERSON><PERSON>", "add_bank_real_name": "<PERSON><PERSON>", "add_bank_address": "<PERSON><PERSON><PERSON>", "bind_title": "<PERSON><PERSON> {bind}", "bind_account": "<PERSON><PERSON><PERSON>", "bind_image": "<PERSON><PERSON>", "bind_name": "<PERSON><PERSON>", "bind_hint": "<PERSON><PERSON><PERSON> ma<PERSON>kkan", "charge_title": "<PERSON><PERSON>", "charge_rmb": "Isi <PERSON> RMB", "charge_usd": "Isi <PERSON>", "charge_history_title": "Riwayat Isi <PERSON>", "charge_account": "<PERSON><PERSON><PERSON>", "charge_money": "<PERSON><PERSON><PERSON>", "charge_deposit_type_title": "Met<PERSON> Is<PERSON>", "charge_deposit_type_online": "<PERSON>i <PERSON>", "charge_deposit_type_offline": "Transfer Offline", "charge_hint": "<PERSON><PERSON><PERSON> masukkan jumlah isi ulang", "charge_offline_nopic_hint": "<PERSON><PERSON><PERSON> unggah bukti isi ulang", "charge_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charge_upload_proof": "<PERSON><PERSON><PERSON> un<PERSON>ah bukti transfer", "withdraw_list_title": "Riwayat Penarikan", "withdraw_rmb": "Penarikan RMB", "withdraw_usd": "Penarikan USD", "withdraw_status_checking": "Sedang Ditinjau", "withdraw_status_approved": "<PERSON><PERSON><PERSON><PERSON>", "withdraw_status_denied": "<PERSON><PERSON><PERSON><PERSON>", "withdraw_cash_status_unfinished": "Belum Ditransfer", "withdraw_cash_status_done": "Telah Ditransfer", "withdraw_cash_status_refused": "<PERSON><PERSON><PERSON>", "cash_title": "Penarikan", "cash_account": "<PERSON><PERSON><PERSON>", "cash_money": "<PERSON><PERSON><PERSON>", "cash_invoice_money": "<PERSON><PERSON><PERSON>", "cash_invoice_money_hint": "<PERSON><PERSON><PERSON> masukkan jumlah faktur", "cash_invoice_upload": "Unggah Faktur", "cash_account_list_title": "<PERSON><PERSON><PERSON> a<PERSON>, dana penarikan akan ditransfer ke salah satu akun berikut secara acak:", "cash_hint": "<PERSON><PERSON><PERSON> masukkan jumlah penarikan", "cash_withdraw_tips1": "Anda sedang menarik ke", "cash_withdraw_tips2": ", jumlah penarikan adalah", "cash_amount": "<PERSON><PERSON><PERSON> ya<PERSON>", "cash_other": "<PERSON><PERSON><PERSON>", "cash_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location_permission": "<PERSON>lu menggunakan izin lokasi, silakan aktif<PERSON>", "location_cancel": "<PERSON><PERSON>", "location_author": "<PERSON><PERSON>", "group_title": "Anggota Grup", "unknown_error": "Kesalahan Tidak Diketahui", "data_parsing_exception": "Pengecualian Parsing Data", "edit": "Edit", "no_data": "Tidak Ada Data", "msg_locating": "<PERSON><PERSON><PERSON>", "note": "Catatan <PERSON>", "failed_to_download": "<PERSON><PERSON> pembaruan", "pick_address": "Klik untuk memasukkan alamat pabrik", "update_now": "<PERSON><PERSON><PERSON>", "message": "<PERSON><PERSON>", "view_order": "<PERSON><PERSON>", "today": "<PERSON>", "yesterday": "<PERSON><PERSON><PERSON>", "send_file": "<PERSON><PERSON>", "login_expired": "<PERSON>gin telah ked<PERSON>, silakan login kembali", "exit_group_chat_confirm": "Yakin ingin keluar dari obrolan grup?", "exit_group_chat_success": "Telah keluar dari o<PERSON>lan grup", "exit_group_chat_page_title": "Informasi Obrolan", "exit_group_chat_button_title": "<PERSON><PERSON><PERSON>", "group_chat_setting_view_more": "<PERSON><PERSON>h Banyak Anggota Grup", "group_chat_setting_name": "<PERSON><PERSON>", "group_chat_setting_owner_update": "Hanya pemilik grup yang diizinkan mengubah nama grup", "group_chat_name_page_title": "Ubah Nama Obrolan Grup", "group_chat_name_page_required": "<PERSON><PERSON><PERSON> masukkan nama obrolan grup", "group_chat_name_save": "Simpan", "group_chat_name_saved": "Nama obrolan grup telah diubah", "conversation_manage_view_please": "<PERSON><PERSON>an pilih percakapan yang ingin dioperasikan", "conversation_manage_view_list": "<PERSON><PERSON><PERSON>", "group_manage_select": "<PERSON><PERSON>an pilih grup yang ingin dioperasikan", "group_manage_list": "Daftar Grup", "please_enter": "<PERSON><PERSON><PERSON> ma<PERSON>kkan", "address_keyword": "<PERSON><PERSON><PERSON> masukkan kata kunci alamat", "inspector_min_fee": "<PERSON><PERSON><PERSON> masukkan biaya inspeksi minimum", "inspector_id_card_required": "<PERSON><PERSON><PERSON> masukkan nomor K<PERSON>", "inspector_id_card_upload": "<PERSON><PERSON><PERSON> unggah foto KTP", "inspector_id_card_upload_fail": "<PERSON>l mengunggah foto KTP, silakan unggah ulang", "inspector_revoke": "Yakin ingin mencabut kualifikasi inspektor?", "inspector_revoke_completed": "Kualifikasi inspektor telah dicabut", "male": "<PERSON><PERSON>-laki", "female": "Perempuan", "elementary": "SD", "junior": "SMP", "technical": "SMK", "senior": "SMA", "college": "Diploma", "bachelor": "S1", "master": "S2", "doctor": "S3", "yes": "Ya", "no": "Tidak", "upload_image": "Unggah Gambar", "upload_file": "Unggah File", "revoke_inspector": "<PERSON><PERSON><PERSON> Inspektor", "deposit_card": "Kartu Tabungan", "withdrawal_balance": "<PERSON><PERSON><PERSON> penarikan tidak boleh melebihi saldo akun", "failed_get_payment_info": "Gagal mendapatkan informasi pembayaran", "recommended_order": "<PERSON><PERSON><PERSON>", "withdrawal_method": "Harap sediakan setidaknya satu metode penarikan", "withdrawal_bind_alipay": "<PERSON><PERSON><PERSON> i<PERSON> te<PERSON>u", "enabled_camera": "<PERSON><PERSON>an atur untuk mengizinkan penggunaan kamera untuk mengambil foto", "valid_email_mobile": "<PERSON><PERSON><PERSON> ma<PERSON>kkan alamat email atau nomor ponsel yang valid", "apple_map": "Peta Apple", "baidu_map": "<PERSON><PERSON>", "amap": "<PERSON><PERSON>", "google_map": "Peta Google", "tencent_map": "<PERSON><PERSON>", "image_format": "Format gambar harus png, jpg, jpeg", "enable_location_service": "Perlu mengaktifkan izin lokasi", "enable_location_service_tips": "Aktifkan izin lokasi untuk mencari pesanan inspeksi di sekitar dengan tepat", "enable_permission_not_now": "<PERSON><PERSON> saja", "enable_permission_goto_setting": "<PERSON><PERSON>", "failed_location_service": "Gagal mendapatkan informasi lokasi", "turn_on_location_service": "<PERSON><PERSON>an aktifkan layanan lokasi ponsel", "no_install_map": "Anda belum menginstal peta", "camera": "<PERSON><PERSON><PERSON>", "photo_album": "Album Foto", "new_version": "Versi baru telah diluncurkan", "invalid_mail": "<PERSON>ail pengguna tidak ada", "invalid_password": "Kata sandi salah", "invalid_mobile": "Nomor ponsel tidak ada", "invalid_auth_code": "<PERSON>de verifi<PERSON>i salah", "invalid_login": "<PERSON><PERSON> gagal, silakan coba lagi", "grabbing": "Sedang Mengambil Pesanan", "hour_ago": "jam yang lalu dipublikasikan", "minute_ago": "menit yang lalu dipublikasikan", "report_type": "<PERSON><PERSON>", "fri": "Inspeksi Sampel", "fui": "", "oli": "", "fat": "", "cls": "<PERSON><PERSON><PERSON>", "fri_cls": "", "order_payment": "<PERSON><PERSON><PERSON><PERSON>", "order_refund": "<PERSON><PERSON><PERSON><PERSON>", "expend_withdrawal": "Pengeluaran - Penarikan", "incoming_refund": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "incoming_recharge": "Pemasukan - <PERSON><PERSON>", "chat_not_member": "<PERSON>a bukan lagi anggota grup, tidak dapat mengirim pesan", "admins": "Hubung<PERSON>", "theme_title": "<PERSON><PERSON>", "theme_light": "<PERSON><PERSON>", "theme_dark": "<PERSON><PERSON>", "theme_auto": "<PERSON><PERSON><PERSON>", "amount_total": "Total", "amount_quota": "Ku<PERSON>", "amount_available": "Tersedia", "amount_blocked": "<PERSON><PERSON><PERSON><PERSON>", "amount_unblocked": "<PERSON><PERSON><PERSON><PERSON>", "download": "Klik untuk Unduh", "downloading": "Sedang Mengunduh", "saved": "<PERSON><PERSON><PERSON><PERSON>", "order_number": "<PERSON><PERSON>", "order_detail_inspection_cost": "Biaya Inspeksi", "delete_account": "Hapus Akun", "delete_account_confirm": "Semua informasi tidak akan disimpan.\nYakin ingin menghapus?", "delete_account_result": "<PERSON><PERSON>n te<PERSON>", "not_exist_account": "<PERSON>kun tidak ada", "new_password": "<PERSON><PERSON>kkan kata sandi baru", "supervisor": "Pengawas", "downloadFiles": "File yang <PERSON>", "confirm_cash_withdraw": "Informasi penarikan Anda:\nKonfirmasi penarikan?", "home_search_hint_inspector": "<PERSON>i pesanan berda<PERSON> kota/nama produk", "home_search_hint_admin": "<PERSON>i pesanan berda<PERSON> kota/nama produk", "search_recent_history": "<PERSON><PERSON><PERSON>", "assign": "Tugaskan", "assigned": "Telah Ditugaskan", "approve": "<PERSON><PERSON><PERSON><PERSON>", "assign_inspector": "Tugaskan Inspektor", "unassigned": "Belum Ditugaskan", "general_all": "<PERSON><PERSON><PERSON>", "assign_search_hint": "<PERSON><PERSON><PERSON> masukkan nama panggilan/nama/email/nomor ponsel", "assign_cancel_message": "Konfirmasi membatalkan penugasan inspektor ini", "assign_inspect_times": "<PERSON><PERSON><PERSON>", "assign_leave_message_batch": "Tinggalkan Pesan Massal", "assign_price_zero_tips": "Biaya inspeksi tidak boleh 0", "assign_applied": "Telah Diajukan", "apply_time": "<PERSON><PERSON><PERSON><PERSON> pada", "assign_message": "<PERSON><PERSON>", "chat_send_message": "<PERSON><PERSON>", "chat_send_order": "<PERSON><PERSON>", "chat_panel_album": "Album", "chat_panel_camera": "<PERSON><PERSON><PERSON>", "chat_panel_file": "File", "chat_toolbar_custom_service": "<PERSON><PERSON><PERSON>", "chat_toolbar_submit_order": "<PERSON><PERSON> Inspeksi", "home_navigation": "Klik untuk Navigasi", "price_input_error_zero": "Pesanan harus antara 0 dan 1 juta", "filter_all": "<PERSON><PERSON><PERSON>", "filter_heading_order_status": "Berdasarkan Status Pesanan", "filter_heading_insp_date": "Berdasarkan Tanggal Inspeksi", "filter_heading_order_date": "Berdasarkan Tanggal Publikasi", "filter_heading_area": "Berdasarkan Wilayah", "filter_date_start": "<PERSON><PERSON>", "filter_date_end": "<PERSON><PERSON>", "filter_date_today": "<PERSON><PERSON><PERSON>", "filter_date_tomorrow": "<PERSON><PERSON><PERSON>", "filter_date_2days_later": "<PERSON><PERSON><PERSON> 2 Hari", "filter_date_3days_later": "<PERSON><PERSON><PERSON> 3 Hari", "sort_by_order_date": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "sort_by_insp_date": "Urutkan berdasarkan Tanggal Inspeksi", "sort_by_distance": "Urutkan berdasarkan Jarak", "purchase_all_replies": "<PERSON><PERSON><PERSON>", "purchase_replies_count": "bala<PERSON>", "purchase_no_more_replies": "Tidak ada balasan lagi", "purchase_save_draft_title": "Simpan sebagai draf?", "purchase_save_draft_choice": "Simpan sebagai Draf", "purchase_save_draft_quit": "<PERSON><PERSON><PERSON>", "purchase_search_hint": "<PERSON><PERSON> pesanan pem<PERSON>ian", "purchase_reply_hint": "<PERSON><PERSON> konte<PERSON>", "purchase_complaint_hint": "Memberikan informasi lebih lanjut akan membantu laporan diproses dengan cepat", "purchase_reply_paid_hint": "<PERSON><PERSON><PERSON><PERSON> konten hadiah", "purchase_edit": "<PERSON>", "purchase_publish": "<PERSON><PERSON><PERSON>", "purchase_publish_product_label": "<PERSON><PERSON>", "purchase_publish_title_label": "<PERSON><PERSON><PERSON>", "purchase_publish_content_label": "<PERSON><PERSON><PERSON><PERSON>", "purchase_publish_product_hint": "<PERSON><PERSON><PERSON> masukkan nama produk", "purchase_publish_title_hint": "Dapat mencantumkan nama model, wilayah, kuantitas, dll.", "purchase_publish_content_hint": "<PERSON><PERSON><PERSON> ma<PERSON>kkan <PERSON><PERSON> rinci", "purchase_publish_price": "<PERSON><PERSON>", "purchase_publish_choose_category": "<PERSON><PERSON><PERSON>", "purchase_publish_choose_category_hint": "<PERSON><PERSON><PERSON> pilih kategori", "purchase_paid_publish_switch": "<PERSON><PERSON>", "purchase_paid_publish_set_price": "<PERSON><PERSON>", "purchase_detail_response_all": "<PERSON><PERSON><PERSON>", "purchase_detail_response_author_only": "<PERSON><PERSON>", "purchase_detail_response_asc": "<PERSON><PERSON><PERSON>", "purchase_detail_response_desc": "<PERSON><PERSON><PERSON>", "purchase_detail_more_reply": "<PERSON><PERSON>", "purchase_detail_more_up": "<PERSON><PERSON>", "purchase_detail_more_cancel_up": "<PERSON><PERSON>", "purchase_my_posts": "<PERSON><PERSON><PERSON>", "purchase_my_replies": "<PERSON><PERSON><PERSON>", "purchase_my_appeals": "Banding", "purchase_appeal_detail": "Detail Banding", "purchase_appeal_submit": "<PERSON><PERSON><PERSON>ing", "purchase_appeal_cancel": "Batalkan Banding", "purchase_appeal_approve": "Banding Disetuju<PERSON>", "purchase_appeal_denied": "Banding <PERSON>", "purchase_paid_content_owner_tips": "<PERSON><PERSON><PERSON>", "purchase_paid_content_tips": "<PERSON><PERSON><PERSON> be<PERSON>, bayar untuk melihat", "purchase_paid_content_paid_tips": "<PERSON><PERSON><PERSON> berbayar telah dibuka", "purchase_review_leave": "<PERSON><PERSON>", "purchase_review_my_score": "<PERSON><PERSON><PERSON>", "purchase_my_replies_original_header": "<PERSON><PERSON><PERSON>", "purchase_reply_to": "<PERSON>las ke", "purchase_publish_bounty_tips": "Catatan: <PERSON><PERSON><PERSON> hadiah dapat diisi secara bebas, hadiah yang lebih tinggi dapat menarik lebih banyak inspektor untuk secara aktif memberikan informasi yang <PERSON>a but<PERSON>.", "purchase_modify_bounty": "<PERSON><PERSON> Hadiah", "purchase_bounty_money": "<PERSON><PERSON>", "purchase_evaluated_person": "orang telah men<PERSON>", "purchase_paid_content_appeal": "Banding", "purchase_comment_paid_supplier": "Pemasok", "purchase_comment_paid_contact": "Kontak", "purchase_comment_paid_phone": "Nomor Telepon", "purchase_comment_paid_email": "Email", "purchase_comment_paid_address": "<PERSON><PERSON><PERSON>", "purchase_comment_paid_other": "<PERSON><PERSON><PERSON>", "purchase_comment_paid_low_price": "<PERSON><PERSON>", "purchase_appeal_title": "<PERSON><PERSON><PERSON>", "purchase_appeal_reason": "<PERSON><PERSON><PERSON> masukkan alasan banding", "purchase_appeal_request_price": "<PERSON><PERSON><PERSON> Banding:", "purchase_appeal_request_reason": "Alasan Banding:", "purchase_post_status_draft": "Draf", "purchase_post_status_reviewing": "Sedang Ditinjau", "purchase_post_status_published": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "purchase_post_status_denied": "Tidak Disetujui", "purchase_post_publish": "Publikasikan <PERSON>ingan", "purchase_complaint_type_leading": "<PERSON><PERSON><PERSON> pilih jenis laporan", "purchase_complaint_type_1": "Konten Pornografi", "purchase_complaint_type_2": "Iklan Spam", "purchase_complaint_type_3": "Pelecehan/Serangan", "purchase_complaint_type_4": "Kegiatan Ilegal", "purchase_complaint_type_5": "Informasi Politik Tidak Akurat", "purchase_complaint_type_6": "Pelanggaran <PERSON>", "purchase_complaint_type_7": "<PERSON><PERSON><PERSON>", "shop_goods_detail_title": "Detail Produk", "mall_buy_immediate": "<PERSON><PERSON>", "mall_goods_count": "<PERSON><PERSON><PERSON>", "mall_confirm_pay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mall_order_confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mall_submit_order": "<PERSON><PERSON>", "mall_goods_price": "<PERSON><PERSON>", "mall_express_price": "<PERSON><PERSON><PERSON>", "mall_price_total": "Total:", "mall_payment": "<PERSON><PERSON>", "mall_payment_methods": "<PERSON><PERSON>", "mall_pay_succeed": "Pembayaran Berhasil", "mall_check_order_detail": "<PERSON><PERSON>", "mall_order_remark": "Catatan Pesanan", "mall_order_remark_input": "Masukkan catatan", "report_success": "<PERSON><PERSON><PERSON>", "purchase_detail_more_report": "Laporkan", "purchase_reply_paid_content_tips": "Catatan: <PERSON><PERSON> isi informasi yang benar, bala<PERSON> berbayar perlu menunggu peninjauan setelah dipublikasikan, dan hanya dapat dilihat oleh orang lain setelah ditinjau", "public_ip_address": "Lokasi IP:", "inspection_widget_suit_tips": "<PERSON><PERSON> inspeksi, harap kenakan kartu identifikasi atau seragam kerja. <PERSON><PERSON> belum punya, <PERSON><PERSON> dapat membelinya di halaman utama", "tabbar_tab_names": "{names, select, tab_home{<PERSON>rand<PERSON>} tab_shortcut{Pi<PERSON><PERSON>} tab_message{<PERSON><PERSON>} tab_mine{<PERSON><PERSON>} tab_publish{} other{}}", "@tabbar_tab_names": {"placeholders": {"names": {}}}, "order_status_desc": "{status, select, order_status_unknown{Tidak Diketahui} order_wait_pay{Menunggu Pembayaran} order_cancelled{Dibatalkan} order_cancelled_refund_pending{<PERSON><PERSON><PERSON><PERSON>, Pengembalian <PERSON> Menunggu Peninjauan} order_refund_pending{Pengembalian <PERSON> Menunggu Peninjauan} order_refund_partial{Pengembalian <PERSON>} order_refund_denied{Pengembalian <PERSON>} order_wait_dispatch{Menunggu Penugasan} order_doing{Dalam Inspeksi} order_finished{Selesai} other{}}", "@order_status_desc": {"placeholders": {"status": {}}}, "pay_type": "{type, select, pay_rmb{Akun RMB} pay_usd{Akun USD} pay_zfb{Alipay} pay_paypal{PayPal} other{}}", "@pay_type": {"placeholders": {"type": {}}}, "purchase_complaint_type": "{type, select, type_1{Konten Pornografi} type_2{Iklan Spam} type_3{Pelecehan/Serangan} type_4{Kegiatan Ilegal} type_5{Informasi Politik Tidak Akurat} type_6{Pelanggaran Hak} type_7{Lainnya} other{}}", "@purchase_complaint_type": {"placeholders": {"type": {}}}, "purchase_reply_paid_my_evaluate": "(<PERSON><PERSON><PERSON>)", "bind_now": "<PERSON><PERSON>", "cancel_register": "Batalkan <PERSON>ftaran", "register_and_bind_email0": "Daftar dan ikat {email} sebagai email utama", "register_and_bind_email1": "<PERSON><PERSON> {email} sudah terda<PERSON>, lanju<PERSON><PERSON> pengikatan", "register_and_bind_email2": "Daftar dan ikat email utama", "new_register_bind_title": "Atur email login utama dan daftar", "new_register_bind_field_account_tips": "Ikat email/nomor ponsel", "register": "<PERSON><PERSON><PERSON>", "switch_account": "Ganti Akun", "switch_account_confirm_tips": "Yakin ingin mengganti akun?", "password_must_have": "Kata sandi Anda harus memiliki:", "password_must_have_1": "Panjang 8-32 karakter", "password_must_have_2": "1 huruf kecil (a-z)", "password_must_have_3": "1 angka", "password_must_have_4": "1 simbol (seperti !@#\\$%^&*)", "password_login": "<PERSON><PERSON><PERSON><PERSON> kata sandi login", "password_login_again": "<PERSON><PERSON>kkan kata sandi baru lagi", "choose_account_to_login": "<PERSON><PERSON><PERSON> akun untuk login cepat", "finish": "Se<PERSON><PERSON>", "done": "Se<PERSON><PERSON>", "account_apple": "Login Apple", "account_google": "Login Google", "account_wechat": "<PERSON>gin <PERSON>", "account_facebook": "Login Facebook", "third_account_unbind": "Lepaskan Pengikatan", "third_account_bind": "<PERSON><PERSON>", "confirm_unbind": "Yakin ingin melepaskan pengikatan?", "inspection_requirement": "Persyaratan Inspeksi", "liveroom_entrance": "An<PERSON><PERSON> <PERSON>", "add_account": "Tambah Akun Bar<PERSON>", "ai_category_inspector": "Pemeriks<PERSON>", "ai_nothing_category": "Tidak ada kategori yang dapat dideteksi", "ai_category_name": "<PERSON><PERSON><PERSON>", "ai_quantity": "Kuantitas", "ai_packaging": "<PERSON><PERSON><PERSON>", "ai_shipping_mark": "<PERSON><PERSON>", "ai_product_style": "<PERSON><PERSON>", "ai_test": "<PERSON><PERSON><PERSON><PERSON>", "ai_craftsmanship": "<PERSON><PERSON><PERSON>", "ai_test_verification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ai_category_measure": "Pengu<PERSON><PERSON>", "ai_spare_parts": "<PERSON><PERSON>", "ai_sampling_number": "<PERSON><PERSON>", "ai_input_range_number": "<PERSON><PERSON><PERSON><PERSON> angka dalam {range}", "ai_enter_range_number": "<PERSON><PERSON><PERSON><PERSON> angka dalam {range}", "ai_selected": "<PERSON><PERSON><PERSON><PERSON>", "ai_selected_status": "<PERSON><PERSON> dipilih", "ai_order_quantity": "Kuantitas Pesanan", "ai_packaged_boxes_quantity": "Kuantitas <PERSON> (<PERSON><PERSON>)", "ai_unpackaged_boxes_quantity": "Kuantitas Kotak Tidak <PERSON> (<PERSON><PERSON>)", "ai_sample_from_packaged": "Sampling dari <PERSON>", "ai_sample_from_unpackaged": "Sampling dari <PERSON>", "ai_spare_parts_quantity": "Kuantitas Suku Cadang", "ai_sampling_packaging_number": "<PERSON><PERSON>", "ai_sampling_packaging_number_record": "<PERSON><PERSON><PERSON>", "ai_sampling_packaging_number_list": "<PERSON><PERSON><PERSON>", "ai_judgment": "<PERSON><PERSON><PERSON>", "ai_judgment_item": "<PERSON><PERSON>", "ai_standard": "<PERSON>ar", "ai_result": "<PERSON><PERSON>", "ai_conclusion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ai_overall_conclusion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ai_consistency": "<PERSON><PERSON><PERSON><PERSON> konsisten", "ai_yes": "Ya", "ai_no": "Tidak", "ai_remarks": "Catatan", "ai_recommended_test_items": "<PERSON><PERSON> yang <PERSON>", "ai_numerical": "<PERSON><PERSON>", "ai_test_item": "<PERSON><PERSON>", "ai_add_all": "Tambah Semua", "ai_add_plus": "+ Tambah", "ai_add": "Tambah", "ai_confirm_delete": "<PERSON><PERSON><PERSON><PERSON>a yakin akan menghapus {name}?", "ai_enter_test_item": "Masukkan item pemeriksaan", "ai_defect_record": "Rekam C<PERSON>t", "ai_defect_photo": "Foto Cacat", "ai_defect_description": "Deskripsi <PERSON>", "ai_defect_level": "Tingkat Cacat", "ai_found_quantity": "Kuantitas yang di<PERSON>n", "ai_handling_method": "<PERSON><PERSON>", "ai_edit": "Edit", "ai_delete": "Hapus", "ai_pick_out": "<PERSON><PERSON><PERSON>", "ai_replace": "Ganti", "ai_rework": "<PERSON><PERSON><PERSON>", "ai_edit_description": "<PERSON>", "ai_critical": "<PERSON><PERSON><PERSON>", "ai_important": "Penting", "ai_minor": "<PERSON><PERSON><PERSON>", "ai_defect_list": "<PERSON><PERSON><PERSON>", "ai_test_level": "<PERSON><PERSON><PERSON>", "ai_sampling_sample": "Sam<PERSON> Sampling", "ai_sampling_level": "<PERSON><PERSON><PERSON>", "ai_additional_information": "Informasi <PERSON>", "ai_inspection_record": "Reka<PERSON>", "ai_sample_count": "<PERSON><PERSON><PERSON>", "ai_maximum_allowable_value": "<PERSON><PERSON> yang <PERSON>", "ai_test_item_name": "<PERSON><PERSON>", "ai_test_result": "<PERSON><PERSON>", "ai_basic_information": "Informasi <PERSON>", "ai_new_test_item": "<PERSON><PERSON>", "ai_test_project": "<PERSON><PERSON>k <PERSON>", "ai_measurement_project": "Proyek <PERSON>", "ai_measure_need_num": "<PERSON><PERSON><PERSON><PERSON>", "ai_measurement_unit": "Unit Pengukuran", "ai_measurement_method": "<PERSON><PERSON>", "ai_measurement_record": "Catatan Pengukuran", "ai_measured": "<PERSON><PERSON>", "ai_unit_of_measurement": "Unit Pengukuran", "ai_measured_value": "<PERSON><PERSON>", "ai_product_number": "Nomor Produk", "ai_number": "Nomor", "ai_new_measurement_item": "<PERSON><PERSON>", "ai_length_width_height": "Panjang, Lebar, Tinggi", "ai_dimensions_length": "Panjang", "ai_dimensions_width": "<PERSON><PERSON>", "ai_dimensions_height": "Tingg<PERSON>", "ai_length_width": "Panjang & Lebar", "ai_other": "<PERSON><PERSON><PERSON>", "ai_allowable_error": "<PERSON><PERSON><PERSON><PERSON>", "ai_report_summary": "<PERSON><PERSON><PERSON>", "ai_special_note": "<PERSON><PERSON><PERSON>", "ai_overall_conclusion_2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ai_summary": "<PERSON><PERSON><PERSON>", "ai_category_name_table": "<PERSON><PERSON><PERSON>", "ai_compliance": "<PERSON><PERSON><PERSON><PERSON>", "ai_remarks_2": "Keterangan", "ai_defect_summary": "<PERSON><PERSON><PERSON>", "ai_no_guidance_instructions": "Tidak Ada Petunjuk Panduan", "ai_no_standard_instructions": "Tidak Ada Petunjuk Standar", "ai_please_fill_in": "<PERSON><PERSON><PERSON>", "ai_please_supplement_level_or_sample": "<PERSON><PERSON><PERSON> {level} atau {sample}", "ai_please_add": "<PERSON><PERSON><PERSON>", "ai_please_input": "<PERSON><PERSON><PERSON>", "ai_please_select": "<PERSON><PERSON><PERSON>", "ai_name_not_filled": "{name} <PERSON><PERSON>", "ai_addition_successful": "<PERSON><PERSON><PERSON><PERSON>", "ai_confirm_action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ai_cancel_action": "<PERSON><PERSON>", "ai_submit": "<PERSON><PERSON>", "ai_next_item": "<PERSON><PERSON>", "ai_complete": "Se<PERSON><PERSON>", "ai_change_description": "Ubah Deskripsi", "ai_action_guidance_instructions": "Petunjuk Operasi", "ai_action_standard_instructions": "Standar Industri", "ai_add_description": "Tambahkan Deskripsi", "ai_change_description_note": "Perhatian: <PERSON><PERSON><PERSON> adalah cacat yang telah di<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, data sejarah juga akan menggunakan deskripsi baru!", "ai_packing_completion_rate": "<PERSON><PERSON>", "ai_unprocessed_quantity": "<PERSON><PERSON><PERSON> Diproses", "ai_sample_level_type_0": "Level I", "ai_sample_level_type_1": "Level II", "ai_sample_level_type_2": "Level III", "ai_sample_level_type_s1": "S-1", "ai_sample_level_type_s2": "S-2", "ai_sample_level_type_s3": "S-3", "ai_sample_level_type_s4": "S-4", "ai_sample_level_type_nothing": "Tidak Ada", "ai_inspection_image": "Gambar", "ai_photo_confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ai_add_product_ask_save": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menyimpan perubahan ini?", "ai_add_product_save": "Simpan", "ai_add_product_edit_model": "Edit Model", "ai_add_product_model_name": "<PERSON><PERSON>", "ai_add_product_input_model": "<PERSON><PERSON><PERSON><PERSON>", "ai_add_product_num": "<PERSON><PERSON><PERSON>", "ai_add_product_input_num": "<PERSON><PERSON><PERSON><PERSON> j<PERSON> model", "ai_add_product_unit": "Satuan", "ai_add_product_ask_delete": "<PERSON><PERSON><PERSON><PERSON> perlu hapus model ini?", "ai_add_product_edit_product": "Edit produk", "ai_add_product_product_name": "<PERSON><PERSON> produk", "ai_add_product_model": "Model", "ai_add_product_input_product_name": "<PERSON><PERSON><PERSON><PERSON> nama produk", "ai_add_product_new_model": "Tambah model baru", "ai_add_product_ask_product": "<PERSON><PERSON><PERSON>h perlu hapus produk ini dan semua modelnya?", "ai_add_product_picture_lost": "<PERSON><PERSON><PERSON>", "ai_add_product_lost_des": "Saat ini {lostStr} hilang, silakan lengkapi informasi sebelum dilakukan pemeriksaan kembali", "ai_add_product_model_full": "Nama model produk", "ai_add_product_model_title": "Model produk", "ai_add_product_new": "Tambah produk", "ai_default_config_des": "Produk saat ini tidak memiliki template pemeriks<PERSON>, <PERSON><PERSON> dapat memilih template di bawah atau menelepon (+86) untuk konfigurasi template.", "ai_default_config_category_all": "<PERSON><PERSON><PERSON> (Semua)", "ai_default_config_select_template": "<PERSON><PERSON><PERSON>", "ai_default_config_template_selection": "<PERSON><PERSON><PERSON><PERSON>", "ai_default_config_search_template": "<PERSON><PERSON>", "ai_default_config_classify": "<PERSON><PERSON><PERSON>", "ai_default_config_preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ai_default_config_use": "Terapkan", "ai_default_config_current_use_button": "Terapkan ke Produk Ini", "ai_default_config_more_use_button": "Terapkan ke Lebih Banyak Produk", "ai_default_config_product_list": "Daftar Produk", "ai_default_config_use_waring": "Catatan: 【Mod】Produk telah memuat template; 【Ops】Operasi telah konfigurasi template. Jika memuat template baru akan menggantikan data sebelumnya.", "ai_default_config_tag_default": "Ops", "ai_default_config_tag_manual": "Mod", "ai_default_config_load_progress": "Proses Pemuatan", "ai_default_config_template_progress": "Template dimuat seles<PERSON> {name}.", "ai_default_config_template_fail_count": "{name} gagal, klik untuk mencoba lagi", "ai_default_config_load": "Muat", "ai_default_config_success": "<PERSON><PERSON><PERSON>", "ai_default_config_fail": "Gaga<PERSON>", "ai_default_config_product_edit": "编辑产品", "ai_wait": "<PERSON><PERSON><PERSON>", "ai_product_info": "Informasi Produk", "ai_product_category": "<PERSON><PERSON><PERSON>", "ai_product_unit": "<PERSON><PERSON><PERSON>", "ai_product_full": "Informasi <PERSON>", "ai_product_full_tip": "Informasi produk dalam pesanan tidak lengkap. Harap lengkapi sesuai informasi aktual produk di lokasi inspeksi.", "ai_each_box": "<PERSON>", "ai_simple_count": "<PERSON><PERSON><PERSON>", "ai_simple_level": "<PERSON><PERSON>", "ai_simple_num": "<PERSON><PERSON><PERSON>", "ai_simple_no": "Nomor Kotak Sampling", "ai_simple_result": "<PERSON><PERSON>", "ai_package_num_done": "Kotak yang Sudah Dikemas", "ai_simple_project": "<PERSON><PERSON>", "ai_simple_project_manage": "<PERSON><PERSON><PERSON>", "ai_simple_project_edit": "<PERSON> <PERSON><PERSON>", "ai_simple_project_recmend": "Rekomendasi Cerdas", "ai_simple_project_input": "Catatan Inspeksi", "ai_simple_help": "Bantuan", "ai_simple_project_record": "<PERSON><PERSON><PERSON>", "ai_simple_require": "Persyaratan <PERSON>nggan", "ai_simple_record": "Rekam", "ai_simple_dsec": "<PERSON><PERSON><PERSON><PERSON>", "ai_simple_before": "<PERSON><PERSON>", "ai_simple_add": "Tambahkan Grup", "ai_simple_add_desc": "Tambahkan deskripsi untuk foto", "ai_simple_add_citations": "Ku<PERSON><PERSON>", "ai_no_more": "Tidak ada data lagi", "ai_wrong_tip": "<PERSON><PERSON><PERSON> tidak boleh mele<PERSON>hi total", "ai_defect_records": "Catatan Cacat", "ai_check_require": "<PERSON><PERSON><PERSON><PERSON>", "ai_find_defect": "Temu<PERSON>", "ai_defect_question": "Masalah Cacat", "ai_modify_level": "Ubah Tingkat Sampling", "ai_defect_quick": "Tambahkan Cacat Proses dengan Cepat", "ai_defect_self": "Nama C<PERSON> Ku<PERSON>", "ai_defect_record_list": "Daftar Catatan Cacat", "ai_measure_require": "<PERSON>sy<PERSON><PERSON>", "ai_measurement_item": "<PERSON><PERSON>", "ai_measurement_error": "<PERSON><PERSON><PERSON>", "ai_measurement_standard": "<PERSON><PERSON>", "ai_measurement_value_standard": "<PERSON><PERSON>", "ai_measurement_camera": "Foto Pengukuran", "ai_measurement_add": "Tambahkan Standar Pengukuran dengan Cepat", "ai_product_first": "<PERSON><PERSON><PERSON>", "ai_product_report": "<PERSON><PERSON><PERSON> La<PERSON>", "ai_product_report_tip": "<PERSON><PERSON><PERSON> pilih gambar utama produk", "ai_product_report_special": "<PERSON><PERSON><PERSON><PERSON> konten yang perlu perhatian khusus", "ai_product_report_sign": "<PERSON><PERSON>", "ai_product_report_sign_done": "<PERSON><PERSON>", "ai_defect_names": "<PERSON><PERSON>", "ai_input_tip": "<PERSON><PERSON><PERSON><PERSON> nama", "ai_add_measure_tip": "Harap tambahkan standar pengukuran terlebih dahulu", "ai_wrong_num": "<PERSON><PERSON><PERSON>", "ai_wrong_name": "<PERSON><PERSON><PERSON><PERSON> nama produk", "ai_wrong_sample_num": "<PERSON><PERSON><PERSON> boleh mele<PERSON>hi jumlah sampel", "ai_per_box": "<PERSON><PERSON><PERSON> per <PERSON>", "ai_wrong_sample_num_cal": "Sampel yang dikemas + sampel yang belum dikemas harus sama dengan jumlah sampel", "ai_sure_delete": "Yakin ingin menghapus?", "ai_choose_tip": "<PERSON><PERSON><PERSON> metode dan jumlah penanganan cacat", "ai_weight": "<PERSON><PERSON>", "sampling_plan": "<PERSON><PERSON><PERSON>", "single": "<PERSON><PERSON><PERSON>", "normal": "Normal", "po_number": "Nomor PO", "product_quantity": "<PERSON><PERSON><PERSON>", "customer_name": "<PERSON><PERSON>", "supplier_name": "<PERSON><PERSON>", "inspection_date": "Tanggal Inspeksi", "arrival_time": "<PERSON><PERSON><PERSON>", "completion_time": "<PERSON><PERSON><PERSON>", "inspection_address": "<PERSON><PERSON><PERSON>", "inspector": "Inspektor"}