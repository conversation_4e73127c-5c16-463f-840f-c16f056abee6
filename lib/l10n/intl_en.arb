{"@@locale": "en", "app_name": "Inspector Online", "search": "Search", "shortcut_tab_name": "Shortcuts", "loading": "Loading...", "nomore": "No more items", "confirm": "Confirm", "more_replies": "More Replies", "purchase_paid_publish_information_title": "Set viewing fee", "purchase_set_fee": "设置查看费用", "purchase_comment_paid_supplier_hint": "Please input supplier name", "purchase_comment_paid_contact_hint": "Please input contact name", "purchase_comment_paid_phone_hint": "Please input phone number", "purchase_comment_paid_email_hint": "Please input email address", "purchase_comment_paid_address_hint": "Please input factory address", "purchase_comment_paid_other_hint": "Other information (Optional)", "purchase_comment_paid_low_price_hint": "Please input low price (Optional)", "purchase_reply_paid_title": "Paid Content", "purchase_reply_paid_desc": " (Supplier and price informations)", "purchase_reply_paid_evaluated_people": "({people_num} evaluated)", "language_setting": "Language Setting", "public_and": "and", "public_publish": "publish", "public_distance": "distance", "public_deny": "deny", "public_see_more": "See more", "general_select": "select", "language_page_title": "Languages", "language_chinese": "Chinese", "language_english": "English", "public_seconds_ago": "seconds ago", "public_minutes_ago": "minutes ago", "public_hours_ago": "hours ago", "public_status_applied": "Applied", "public_status_refused": "Denied", "public_status_approved": "Approved", "public_status_canceled": "Canceled", "public_app_name": "Inspector", "public_send": "Send", "public_ok": "Ok", "public_price": "Price", "public_cancel": "Cancel", "public_manage": "Manage", "public_finish": "Finish", "public_reset": "Reset", "public_leave_message": "Message", "public_share": "Share", "login_submit": "<PERSON><PERSON>", "login_mobile_login": "Mobile", "login_mobile_tips": "Please enter mobile", "login_email_login": "Email", "login_email_tips": "Please enter email", "registry_email_phone_tips": "Please enter email or phone number", "login_verify_tips": "Please enter verification code", "login_password_tips": "Please enter password", "login_password_login": "Login with password", "login_verify_login": "Login with verification code", "login_register": "Create account", "login_take_code": "Send", "login_forget_password": "Forgot password", "login_agreement": "I agree to Inspector.Itd Agreement", "login_area_selected": "Select Country", "tab_home": "Home", "tab_order": "Order", "tab_shortcut": "Shortcut", "tab_purchase": "Purchase", "tab_message": "Message", "tab_mine": "Me", "supplement_title": "Please complete your personal information first", "supplement_next": "Go", "home_title": "Inspection Square", "home_record": "Records", "home_newest": "Latest", "home_nearest": "Nearest", "home_recommend": "Referral Reward RMB{money}\\$", "home_sampling": "Hypothesis Testing", "home_word": "WORD REPORT", "home_unit": "{people} person/{day} day", "home_product_tip": "Product:", "home_person_apply": " Applied", "home_know_tip": "Terms & Privacy Policy.", "home_inspection_tip": "The inspection fee defaults to the merchant\"s pricing,can be modified,low cost may be given priority for assignment.", "home_reviewed": "I agree to our ", "home_apply": "Apply", "home_apply_price": "Please enter the amount", "home_apply_check": "Please refer to the inspection notice", "home_apply_tips": "you are not a certified inspector, but you have more than 1 year of experience in foreign trade inspection, please provide relevant qualification proofs.", "home_complete_profile_tips": "Completing relevant information for the inspector can increase the application success rate", "home_apply_sure": "Submit", "home_complete_profile_sure": "Go", "home_apply_cancel": "Cancel", "home_update": "Edit", "home_navi": "", "mine_unauth": "Not certified", "mine_checking": "Pending Approval", "mine_check_failed": "<PERSON><PERSON><PERSON><PERSON> Rejected", "mine_vip_level": "VIP Level", "mine_credit_quota": "Credit quota", "mine_authed": "Certified Inspector", "mine_authed_inspector": "Modify Certified Inspector", "mine_amount": "Balance", "mine_cash": "Recharge/Withdrawal", "mine_order": "My Orders", "mine_purchase": "My Purchase", "mine_check": "My inspections", "mine_address": "Address Book(supplier)", "mine_recommend": "Referral", "mine_setting": "Settings", "mine_header_inspect": "Inspect management", "mine_header_purchase": "Purchase management", "mine_header_other": "Others", "mine_inspect_mine": "My Inspect", "mine_inspect_order": "Order", "mine_inspect_history": "Apply history", "mine_purchase_mine": "My purchase", "mine_purchase_reply": "Replies", "mine_purchase_appeal": "Appeals", "mine_other_recommend": "Referral", "mine_other_address": "Address Book(supplier)", "mine_other_settings": "Settings", "profile_title": "Personal Information", "profile_avatar": "Avatar", "profile_name": "Nickname", "profile_mobile": "Mobile", "profile_country": "Country", "profile_real_name": "Real name", "profile_city": "City", "profile_email": "Email", "profile_wechat": "WeChat", "profile_bind_manage": "Third login management", "profile_info_failed": "Information update failed", "apply_title": "Inspector Qualification Application", "apply_nick": "Nickname", "apply_sex": "Gender", "apply_birthday": "Birthday", "apply_education": "Education Background", "apply_address": "Permanent Address", "apply_price": "<PERSON>", "apply_shebao": "Social Security", "apply_id_card": "ID number", "apply_file": "Edit Resume", "apply_file_tip": "Please enter your basic information\n and experiences", "apply_upload_file": "Upload Resume", "apply_upload_file_failed": "Upload failed", "apply_upload_card": "Upload ID Card Photos", "apply_card_front": "Front-facing Photo", "apply_card_back": "Back-facing Photo", "apply_submit": "Submit", "apply_enter": "Please enter", "apply_next_tip": "Please complete the information before submitting", "apply_auth_failed": "ID verification failed, please upload a correct photo", "apply_checking": "Pending", "apply_check_success": "Approved", "apply_check_failed": "Verification failed, please modify the content and resubmit", "order_title": "My Orders", "order_input": "Inspections", "order_output": "Orders", "order_all": "All", "order_wait_pay": "Pending payment", "order_cancelled": "Cancelled", "order_status": "Order status", "order_status_unknown": "Unknown", "order_refund_pending": "Refund pending", "order_cancelled_refund_pending": "Cancelled, refund pending", "order_refund_partial": "Partial refund", "order_refund_denied": "Refund denied", "order_wait_dispatch": "Awaiting", "order_ready_inspect": "Preparing", "order_need_pay": "Unpaid", "order_wait": "Preparing", "order_confirm": "Confirmed", "order_doing": "Inspecting", "order_comment": "Pending Evaluation", "order_finished": "Completed", "order_goods_info": "Products", "order_goods_name": "Product", "order_goods_model": "Model", "order_goods_count": "Count", "order_goods_unit": "Unit", "order_order_time": "Date", "order_order_amount": "Amount", "order_detail_title": "Detail", "order_applying": "Applied", "order_apply_expired": "Expired", "order_apply_dispatched": "Dispatched", "order_create_time": "Created At", "order_look": "View inspection report", "order_report_next": "Submit inspection report", "order_detail_inspection_info": "Inspection Information", "order_inspection_status_unpaid": "Unpaid", "order_inspection_status_returned": "Returned", "order_inspection_status_waiting_start": "Waiting start", "order_detail_related_info": "Associated sub-order", "order_detail_inspection_product": "Product", "order_detail_inspection_time": "Inspection Date", "order_detail_inspection_city": "City", "order_detail_inspection_factory": "Factory", "order_detail_inspection_address": "Address", "order_detail_inspection_person": "Contact", "order_detail_inspection_phone": "Phone", "order_detail_inspection_email": "Email", "order_detail_inspection_amount": "Price Information", "order_detail_inspection_sample": "<PERSON><PERSON>", "order_detail_inspection_standard": "Sample standard", "order_detail_inspection_critical": "AQL", "order_detail_inspection_major": "Major", "order_detail_inspection_minor": "Minor", "order_detail_inspection_day": "Days", "order_detail_inspection_number": "Number of People", "order_detail_inspection_price": "Price", "order_detail_inspection_price_apply": "Requested Price", "order_detail_inspection_template": "Templates", "order_detail_inspection_file": "Attachments", "order_detail_inspection_image": "Images", "order_detail_inspection_type": "<PERSON>vie <PERSON>", "order_tips": "Note", "order_apply": "Application for Inspecting", "order_cancel": "Cancel", "order_canceled": "Cancelled", "order_dispatch_accept": "Accept", "order_dispatch_refuse": "Refuse", "order_inspection": "Begin the Inspection", "order_sure_cancel": "Sure to cancel the order", "order_sure_refuse": "Sure to refuse this order", "order_sure_confirm": "Sure to accept this order", "publish_title": "Place an Order", "publish_edit_title": "Update Order", "publish_welcome": "Welcome to place an order, free quoting and communication", "publish_sampling": "Final Random Inspection (FRI)", "publish_sampling_content": "The inspection will be performed after 100% finished production and above 80% packing at least. The inspector will choose samples randomly according to ANSI/ASQZ1.4 (MIL-STD-105E) and your requirement accordingly,during inspection,we will check the data like appearance, workmanship, function, safety and other points required by customer. Picking out defectives and ask factory to rework or replace. The formal report will shows the entire knowledge of the cargo quality.", "publish_point": "<PERSON>", "publish_sampling_point": "● Clients requirement/Sample comparison\n● Quantity check\n● Product size/pattern/color\n● Appearance quality check\n● Functional test & Safety test\n● Shipping mark\n● Packing safety\n● Packing details\n● Special requirements", "publish_all": "Full Day Inspection (FUI)", "publish_all_content": "Full check means that we will go to the factory after the goods are 100% finished production,it can be happened before or after packing. We inspect all the products one by one, check the data like appearance, workmanship, function, safety and other points required by customer,to distinguish the pass products and defects.Picking out defectives and inform the factory to replace or rework. (If cargo quantity is large,will need to add inspectors or days,and charge will also be added)", "publish_online": "<PERSON><PERSON> (OLI)", "publish_online_content": "This inspection will be happened during production,or 30-50% production finished,to check the product quality,pattern,color,size functional,appreance etc,to ensure the whole production will be consistent with the sample and contract,also It will be helpful to find out the problem earlier,to avoid the delay of delivery.", "publish_online_point": "● Processing follow up\n● Production assessment & Production speed\n● Semi-products and finished products\n● Check the packing info and package\n● Advise improve the defective\n● Assess the delivery time", "publish_factory": "Factory Audit (FAT)", "publish_factory_content": "Factory Audits can uncover problems before production or order confirmation. You could have a clear understanding of your supplier\"s capabilities, quality control system, management and operating procedures. This enables you to select a qualified supplier with confidence.", "publish_factory_point_title": "Check points", "publish_factory_point": "● Basic profile\n● Organization Structure\n● Production Process\n● Production ability\n● R&D\n● Facilities & Machinery\n● Quality assurance system & related certificates", "publish_factory_review": "Comprehensive evaluation", "publish_factory_review_content": "● For each audit item, weigh the importance of each other, give different scores respectively, and then issue the qualification scoring table according to the audit questionnaire and field survey data", "publish_watch": "Container Loading Supervision (CLS)", "publish_watch_content": "Container loading supervision includes the checking of container condition,to ensure it is good for loading; To supervise your goods will be loaded accordingly correctly,and confirm the container been sealed safety.", "publish_watch_point": "● Record the Container No and Trunck No\n● The container should be in good condition,no damage/wet/odor\n● Pre-load goods cartons quantity,randomly open some cartons to check YOUR goods inside\n● Supervise the loading without damage goods\n● The container seal and packing list", "publish_watch_inspection": "Inspection+Loading supervision (FRI+CLS)", "publish_watch_inspection_content": "To ensure the goods’s final quality and quantity,the inspector will randomly pick samples according to ANSI/ASQZ1.4 (MIL-STD-105E) from whole goods to check quality and product pattern,and supervise the loading supervision.", "publish_watch_inspection_point": "● Products appreance/qualtiy/size measurement/functional/packing etc\n● Show the defects to the supplier,require replace or rework\n● The container should be in good condition,no damage/wet/odor\n● Supervise the loading without damage goods\n● The container seal and packing list", "publish_next": "Next", "publish_inspection_time": "Date", "publish_inspection_time_selected": "Inspection Date", "publish_inspection_time_tip": "Please Select", "publish_inspection_people": "Number", "publish_people": "People", "publish_day": "Day", "publish_inspection_factory": "Factory", "publish_factory_tips": "Please enter factory information", "publish_address_book": "Address Book", "publish_goods_name": "Product", "publish_name_tips": "Please enter product information", "publish_po_tips": "Please input P<PERSON>O", "publish_file_tips": "Attachment", "publish_camera": "Camera", "publish_file": "Upload Files", "publish_purchase": "Create purchase order", "publish_inspection": "Create inspection order", "publish_factory_tip": "Please Select Address", "publish_attention": "Attentions", "publish_attention_tips": "What critical points that you want \nthe inspector to pay attention?", "publish_stand_price": "Fixed Price", "publish_click_price": "", "publish_vip_price": "VIP Price", "publish_vip_tips": "Includes thorough personal service", "publish_total": "Total", "publish_submit": "Submit", "publish_only_price_failed": "No fixed price authorization", "publish_price_tip": "Please Select Price", "publish_date_tips": "Please Select Data", "date_title": "Inspect Date", "date_save": "Save", "address_title": "Update Factory Information", "address_auto_tips": "Input / paste the whole address, automatically identify the contact, email, phone, factory name and address", "address_paste": "Paste", "address_ocr": "Recognition", "address_name": "Factory Name", "address_name_tip": "Please enter factory information", "address_person": "Contact", "address_person_tip": "Please Enter Contact", "address_mobile": "Mobile", "address_mobile_tip": "Please enter mobile number", "address_email": "Email", "address_email_tip": "Please enter email address", "address_area": "Province/City/District", "address_area_tip": "Please select province/city/district 〉", "address_detail": "Detailed Address", "address_detail_tip": "Enter street name, house/building number and other details", "address_location": "Geolocation", "address_save_tip": "Save", "address_clear": "Clear", "address_submit": "Submit", "address_recent": "Recently Used Addresses", "address_more": "More Addresses", "address_list_title": "Address Management", "address_insert": "New Address", "address_delete": "Deleted", "address_delete_result": "Failed to delete", "address_edit": "Edit", "address_delete_tips": "Are you sure to delete?", "address_detected_paste": "Would you like to paste?", "pay_title": "Pay Order", "pay_time": "Remaining payment time", "pay_paypal": "PayPal", "pay_zfb": "AliPay", "pay_usd": "USD Account", "pay_rmb": "RMB Account", "pay_pay": "Pay", "pay_result_success": "Payment Successful", "pay_result_success_wait": "Payment successful, waiting for handling.", "pay_result_failed": "Payment Failed", "pay_keep": "Can be Record", "check_title": "Inspection Report", "check_picture": "Inspection Photos", "check_file": "Letter of Integrity Commitment", "check_report": "Inspection Result(manual)", "check_draft": "Draft", "check_template": "Report Template", "check_submit": "Submit", "check_hint": "Please enter image description", "check_checking": "Report Being Reviewed", "check_check_success": "Report Approved", "check_check_failed": "Report review failed, please revise the content and resubmit", "review_title": "Service Rating", "review_next": "Rating", "contact_bnt": "Chat Now", "review_score": "Service Quality", "review_score1": "Disappointed", "review_score2": "Unsatisfied", "review_score3": "Average", "review_score4": "Satisfied", "review_score5": "Surprised", "review_tips": "Evaluate from multiple perspectives to help us further understand the inspector\"s work abilities", "review_picture": "Image", "review_submit": "Submit", "setting_title": "Settings", "setting_address": "Address Management", "setting_clear_cache": "<PERSON>ache", "setting_clear_success": "Successfully Cleared", "setting_about_us": "About Us", "setting_receive_msg": "Message Notification", "setting_version": "Version", "setting_check_update": "Check update", "setting_login_out": "Logout", "setting_login_out_tips": "Are you sure to logout？", "setting_delete_account_tips": "Are you sure to delete your account？", "setting_policy_title": "Privacy policy tips", "setting_policy_sub_title": "Please read and agree before next step", "setting_privacy_policy": "Privacy Policy", "setting_user_agreement": "User Agreement", "setting_privacy_content": "Welcome to the Inspection Online App\nWe attach great importance to your privacy and personal information protection. When you use this App, we will collect and use some of your personal information\n1. After you agree to the App Privacy Policy , we will initialize the integrated SDK and collect your device MAC address, IMSI, Android ID, IP address, hardware model, operating system version number, unique device identifier (IMEI, etc.), network device hardware address (MAC) , software version number, network access method, type, status, network quality data, operation logs, hardware serial numbers, service log information, etc. to ensure normal data statistics and security risk control of the App. \n2. We will not obtain, share or provide your information to third parties without your consent. \n3. You can access, correct, and delete your personal information, and we will also provide ways to cancel and complain. \n", "setting_ihavereadandagreed": "I have read and agreed ", "setting_policy_tips2": "Please read carefully and understand ", "wallet_title": "Wallet", "wallet_bill": "Bill", "wallet_rmb_account": "RMB Account", "wallet_usd_account": "USD Account", "wallet_account_heading": "Account and settings", "wallet_bank": "Bank Card", "wallet_wechat": "WeChat", "wallet_alipay": "Alipay", "wallet_charge": "Recharge", "wallet_cash": "<PERSON><PERSON><PERSON>", "wallet_balance": "Balance", "wallet_default_account": "Default account", "wallet_set_default_account": "Set as default account", "bill_title": "Bill", "bill_out": "Expend", "bill_in": "Incoming", "bill_month": "Month", "bill_fenxi": "Operations Flow", "bill_unfreeze": "Unfreeze", "bill_all": "All", "bill_income": "Income", "bill_outcome": "Outcome", "bill_freeze": "Freeze", "bill_withdraw": "Withdraw", "bank_title": "My Bank Card", "bank_add": "New Bank Card", "add_bank_title": "New Bank Card", "add_bank_name": "Bank Name", "add_bank_branch": "Bank of Deposit", "add_bank_card": "Card Number", "add_bank_real_name": "Name", "add_bank_address": "Bank Address", "bind_title": "Binding {bind}", "bind_account": "Account", "bind_image": "Collection Code", "bind_name": "Name", "bind_hint": "Please enter", "charge_title": "Recharge", "charge_account": "Account", "charge_money": "Amount", "charge_deposit_type_title": "Charge Type", "charge_deposit_type_online": "Online Charge", "charge_deposit_type_offline": "Offline Charge", "charge_offline_nopic_hint": "Please upload your proof", "charge_upload_proof": "Please upload your proof", "withdraw_list_title": "Withdraw History", "withdraw_rmb": "Withdraw in RMB", "withdraw_usd": "Withdraw in USD", "withdraw_status_checking": "Withdrawing", "withdraw_status_approved": "Approved", "withdraw_status_denied": "Denied", "withdraw_cash_status_unfinished": "Unfinished", "withdraw_cash_status_done": "Finished", "withdraw_cash_status_refused": "Refused", "charge_hint": "Please enter amount", "charge_submit": "Submit", "charge_rmb": "Charge in RMB", "charge_usd": "Charge in USD", "charge_history_title": "Charge History", "cash_title": "<PERSON><PERSON><PERSON>", "cash_account": "Please select account", "cash_money": "Amount", "cash_invoice_money": "Invoice Amount", "cash_invoice_money_hint": "Please input invoice amount", "cash_invoice_upload": "Upload invoice", "cash_account_list_title": "Money will be deposit to these accounts randomly", "cash_hint": "Please enter amount", "cash_withdraw_tips1": "You are withdrawing to ", "cash_withdraw_tips2": ", amount is ", "cash_amount": "Receipt Amount", "cash_other": "Fee", "cash_submit": "Submit", "location_permission": "Please enable location permission", "location_cancel": "Cancel", "location_author": "Authorization", "group_title": "Group Members", "unknown_error": "unknown error", "data_parsing_exception": "Data parsing exception", "edit": "Edit", "no_data": "No Data", "note": "Note", "msg_locating": "Locating", "failed_to_download": "Failed to download", "pick_address": "Click to input address of factory", "update_now": "Update Now", "message": "Message", "view_order": "View Order", "today": "Today", "yesterday": "Yesterday", "send_file": "Send File", "login_expired": "Senssion expired,please login again", "exit_group_chat_confirm": "Are you sure to exit group chat?", "exit_group_chat_success": "Exit Successfully", "exit_group_chat_page_title": "Group Chat Information", "exit_group_chat_button_title": "Exit Group Chat", "group_chat_setting_view_more": "View More", "group_chat_setting_name": "Group Chat Name", "group_chat_setting_owner_update": "Only the owner is allowed to modify the group name.", "group_chat_name_page_title": "Update Group Chat Name", "group_chat_name_page_required": "Please enter group chat name", "group_chat_name_save": "Save", "group_chat_name_saved": "Saved Successfully", "conversation_manage_view_please": "Please select the session that needs to be operated", "conversation_manage_view_list": "Conversation List", "group_manage_select": "Please select the group that needs to be operated", "group_manage_list": "Group List", "please_enter": "Pleas enter ", "address_keyword": "Please enter address keywords", "inspector_min_fee": "Please enter the minimum inspection fee", "inspector_id_card_required": "Please enter ID card number", "inspector_id_card_upload": "Please upload ID images", "inspector_id_card_upload_fail": "Failed to upload, please again", "inspector_revoke": "Are you sure to revoke the qualification of the inspector?", "inspector_revoke_completed": "Completed to revoke", "male": "Male", "female": "Female", "elementary": "Elementary", "junior": "Junior High", "technical": "Technical", "senior": "Senior", "college": "college", "bachelor": "Bachelor", "master": "Master", "doctor": "Doctor", "yes": "Yes", "no": "No", "upload_image": "Upload Image", "upload_file": "Upload File", "revoke_inspector": "Revoke", "deposit_card": "Deposit Card", "withdrawal_balance": "The withdrawal amount cannot exceed the account balance.", "failed_get_payment_info": "Failed to obtain payment information.", "recommended_order": "Recommended for Ordering", "withdrawal_method": "Please provide at least one withdrawal method.", "withdrawal_bind_alipay": "Please bind alipay first", "enabled_camera": "Please enable camera access for taking photos", "valid_email_mobile": "Please enter a valid email address or phone number", "apple_map": "Apple Maps", "baidu_map": "Baidu Maps", "amap": "Amap", "google_map": "Google Maps", "tencent_map": "Tencent Maps", "image_format": "The image format must be png, jpg, or jpeg", "enable_location_service": "Please allow the app to use your phone\"s location service.", "enable_location_service_tips": "Enable location permission allows more accurate recommendation of orders", "enable_permission_not_now": "Not now", "enable_permission_goto_setting": "Go to settings", "failed_location_service": "The acquisition of location information has encountered an error.", "turn_on_location_service": "Please turn on the location service on your phone.", "no_install_map": "You haven\"t install the map", "camera": "Camera", "photo_album": "Photo Album", "new_version": " is newly launched", "invalid_mail": "Invalid email", "invalid_password": "Invalid pasword", "invalid_mobile": "Invalid mobile number", "invalid_auth_code": "Invalid auth code", "invalid_login": "Failed to login,please again", "grabbing": "Grabbing", "hour_ago": "h ago", "minute_ago": "m ago", "report_type": "Report Type", "fri": "FRI", "fui": "FUI", "oli": "OLI", "fat": "FAT", "cls": "CLS", "fri_cls": "FRI+CLS", "order_payment": "Order Payment", "order_refund": "Order Refund", "expend_withdrawal": "Expend-WithDrawal", "incoming_refund": "Incoming-Refund", "incoming_recharge": "Incoming-Recharge", "chat_not_member": "You are no longer a member of the group and cannot send messages", "admins": "Admins", "theme_title": "Theme", "theme_light": "Light Mode", "theme_dark": "Dark Mode", "theme_auto": "Follow System", "amount_total": "Total", "amount_available": "Available", "amount_blocked": "Blocked", "download": "download", "downloading": " downloading", "saved": "Save completed", "order_number": "Order Number", "order_detail_inspection_cost": "Cost", "delete_account": "Delete Account", "delete_account_confirm": "All information will be deleted。\nAre you sure?", "delete_account_result": "Account has been deleted.", "not_exist_account": "Invalid account.", "new_password": "Please enter new password", "supervisor": "Supervisor", "downloadFiles": "Downloaded Files", "home_search_hint_inspector": "Search orders by area or product", "home_search_hint_admin": "Search orders by area or product", "search_recent_history": "Recently searched", "assign": "assign", "assigned": "assigned", "approve": "approve", "assign_inspector": "Assign Inspector", "unassigned": "Unassigned", "general_all": "All", "general_date": "Date", "general_desc": "Description", "general_amount": "Amount", "assign_search_hint": "Please input nickname/name/email/phone", "assign_cancel_message": "Confirm cancel the assignment", "assign_inspect_times": "Inspected times", "assign_leave_message_batch": "Leave message", "assign_price_zero_tips": "Inspection fee cannot be 0", "assign_applied": "Applied", "is_auth_forbidden": "Forbidden", "apply_time": "Applied at", "assign_message": "Message", "chat_send_message": "Send message", "chat_send_order": "Send order", "chat_panel_album": "Album", "chat_panel_camera": "Camera", "chat_panel_file": "File", "chat_toolbar_custom_service": "Custom service", "chat_toolbar_submit_order": "Submit an order", "home_navigation": "navigation", "price_input_error_zero": "Order price should between 0 and 1 million", "filter_all": "All filters", "filter_heading_order_status": "Order status", "filter_heading_insp_date": "Inspection date", "filter_heading_order_date": "Order date", "filter_heading_area": "Province/City/Area", "filter_date_start": "Start date", "filter_date_end": "End date", "filter_date_today": "Today", "filter_date_tomorrow": "Tomorrow", "filter_date_2days_later": "Within 2 days", "filter_date_3days_later": "Within 3 days", "sort_by_order_date": "Sort by order date", "sort_by_insp_date": "Sort by inspection date", "sort_by_distance": "Sort by distance", "purchase_all_replies": "All replies", "purchase_replies_count": " replies", "purchase_no_more_replies": "No more replies", "purchase_save_draft_title": "Do you need to save draft?", "purchase_save_draft_choice": "Save as draft", "purchase_save_draft_quit": "Quit", "purchase_search_hint": "Search purchase orders", "purchase_reply_hint": "Input your reply", "purchase_reply_reason_hint": "Reason why recommend", "purchase_complaint_hint": "More information helps to handle more quickly", "purchase_reply_paid_hint": "Input your valuable reply", "purchase_edit": "Edit post", "purchase_publish": "Publish purchase post", "purchase_publish_product_label": "Product name", "purchase_publish_title_label": "<PERSON>itle", "purchase_publish_quantity": "Quantity", "purchase_publish_content_label": "Description", "purchase_publish_product_hint": "Please input product name", "purchase_publish_title_hint": "Please input model, area or other information", "end_date": "End date", "purchase_area": "Area", "purchase_permission_author_only": "Only author of post can see replies", "purchase_publish_quantity_hint": "Please input quantity", "purchase_publish_content_hint": "Please input description", "purchase_publish_price": "Purchase price", "purchase_publish_choose_category": "Choose category", "purchase_publish_choose_category_hint": "Please choose a category", "purchase_paid_publish_switch": "Paid content", "purchase_paid_publish_set_price": "Set price", "purchase_detail_response_all": "All", "purchase_detail_response_author_only": "Author only", "purchase_detail_response_asc": "Asc", "purchase_detail_response_desc": "Desc", "purchase_detail_more_reply": "Reply", "purchase_detail_more_up": "Up", "purchase_detail_more_cancel_up": "Cancel up", "purchase_my_posts": "My Posts", "purchase_my_replies": "My Replies", "purchase_my_appeals": "My Appeals", "purchase_appeal_detail": "Appeal details", "purchase_appeal_submit": "Submit appeal", "purchase_appeal_cancel": "Cancel appeal", "purchase_appeal_approve": "Agreed appeal", "purchase_appeal_denied": "Denied appeal", "purchase_paid_content_owner_tips": "Paid content", "purchase_paid_content_tips": "Paid content, view after pay", "purchase_paid_content_paid_tips": "Paid content is unlocked", "purchase_review_leave": "Leave a review", "purchase_review_my_score": "My review", "purchase_my_replies_original_header": "In post", "purchase_publish_bounty_tips": "Tips: bounty is optional, higher bounty will get more attention.", "purchase_reply_to": "Reply to", "purchase_modify_bounty": "Modify bounty", "purchase_bounty_money": "Bounty", "purchase_evaluated_person": "provider ratings", "purchase_comment_paid_supplier": "Supplier", "purchase_comment_paid_contact": "Contact", "purchase_comment_paid_phone": "Phone", "purchase_comment_paid_email": "Email", "purchase_comment_paid_address": "Factory address", "purchase_comment_paid_other": "Other", "purchase_comment_paid_low_price": "Low price", "purchase_appeal_title": "Refund request", "purchase_appeal_reason": "Please describe your reason", "purchase_appeal_request_price": "Refund: ", "purchase_appeal_request_reason": "Reason: ", "purchase_post_status_draft": "Draft", "purchase_post_status_reviewing": "Reviewing", "purchase_post_status_published": "Published", "purchase_post_status_denied": "Denied", "purchase_post_publish": "Publish post", "purchase_complaint_type_leading": "Please choose type", "purchase_complaint_type_1": "Sexualization", "purchase_complaint_type_2": "Spam", "purchase_complaint_type_3": "Abuse or thretening violence", "purchase_complaint_type_4": "Breaks rules or laws", "purchase_complaint_type_5": "False information", "purchase_complaint_type_6": "Copyright violation", "purchase_complaint_type_7": "Other", "shop_goods_detail_title": "Product Details", "mall_buy_immediate": "Order Now", "mall_goods_count": "Qty", "mall_confirm_pay": "Confirm Pay", "mall_order_confirm": "Order Confirm", "mall_submit_order": "Submit Order", "mall_goods_price": "Goods price", "mall_express_price": "Express price", "mall_price_total": "Total: ", "mall_payment": "Payment", "mall_payment_methods": "Payment methods", "mall_pay_succeed": "Payment succeed", "mall_check_order_detail": "View order details", "mall_order_remark": "Remark", "mall_order_remark_input": "Input remark", "purchase_detail_more_report": "Report", "purchase_reply_paid_content_tips": "Tips: please input correct infomations, after reviewed by admin, your reply can be viewed by others.", "public_ip_address": "IP address: ", "inspection_widget_suit_tips": "Wear suit, or you can buy now if you don\"t have it", "purchase_paid_content_appeal": "appeal", "report_success": "Report success", "tabbar_tab_names": "{names, select, tab_home{Home} tab_shortcut{Shortcut} tab_message{Message} tab_mine{Me} tab_publish{} other{}}", "@tabbar_tab_names": {"placeholders": {"names": {}}}, "order_status_desc": "{status, select, order_status_unknown{Unknown} order_wait_pay{Pending payment} order_cancelled{Cancelled} order_cancelled_refund_pending{Cancelled, refund pending} order_refund_pending{Refund pending} order_refund_partial{Partial refund} order_refund_denied{Refund denied} order_wait_dispatch{Awaiting} order_doing{Inspecting} order_finished{Completed} other{}}", "@order_status_desc": {"placeholders": {"status": {}}}, "pay_type": "{type, select, pay_rmb{RMB Account} pay_usd{USD Account} pay_zfb{AliPay} pay_paypal{PayPal}  other{}}", "@pay_type": {"placeholders": {"type": {}}}, "purchase_complaint_type": "{type, select, type_1{Sexualization} type_2{Spam} type_3{Abuse or thretening violence} type_4{Breaks rules or laws} type_5{False information} type_6{Copyright violation} type_7{Other}  other{}}", "@purchase_complaint_type": {"placeholders": {"type": {}}}, "purchase_reply_paid_my_evaluate": "(My Review)", "bind_now": "Bind now", "cancel_register": "Cancel register", "register_and_bind_email0": "Register and bind {email} as primary login email.", "register_and_bind_email1": "You have registered with {email} already, bind and continue.", "register_and_bind_email2": "Register and bind an email as your primary login email.", "new_register_bind_title": "Register & Bind", "new_register_bind_field_account_tips": "Bind email/phone number", "register": "Register", "switch_account": "Switch account", "switch_account_confirm_tips": "Are you sure to switch account?", "password_must_have": "Your password must contain:", "password_must_have_1": "Length 8-32 characters", "password_must_have_2": "1 lowercase letter (a-z)", "password_must_have_3": "1 number", "password_must_have_4": "1 symbol (e.g., !@#$%^&*)", "password_login": "Login Password", "password_login_again": "Re-enter Password", "choose_account_to_login": "Choose account to login quickly", "finish": "Finish", "done": "Done", "account_apple": "Apple Account", "account_google": "Google Account", "account_wechat": "<PERSON><PERSON><PERSON> Account", "account_facebook": "Facebook Account", "third_account_unbind": "Unbind", "third_account_bind": "Bind", "confirm_unbind": "Are you confirm to unbind?", "inspection_requirement": "Inspection Requirement", "liveroom_entrance": "Live Rooms", "add_account": "Add account", "message_order": "Orders", "message_email": "Email", "message_wallet": "Wallet", "message_user": "User info", "salesman": "Salesman", "public_continue": "Continue", "camera_permission_tips": "In order to facilitate you to upload pictures, files or photos when using the App, you need to allow the use of camera and storage permissions.", "ai_category_inspector": "inspector", "ai_nothing_category": "Nothing Inspection Category", "ai_category_name": "Inspection Category", "ai_quantity": "Quantity", "ai_packaging": "Packaging", "ai_shipping_mark": "Shipping Mark", "ai_product_style": "Product Style", "ai_test": "Test", "ai_craftsmanship": "Craftsmanship", "ai_test_verification": "Test Verification", "ai_category_measure": "Measure", "ai_spare_parts": "Spare Parts", "ai_sampling_number": "Sampling Number", "ai_input_range_number": "Enter a number within {range}", "ai_enter_range_number": "Please enter a number within {range}", "ai_selected": "Selected", "ai_selected_status": "Selected Status", "ai_order_quantity": "Order Quantity", "ai_packaged_boxes_quantity": "Quantity of Packaged Boxes (Finished Products)", "ai_unpackaged_boxes_quantity": "Quantity of Unpackaged Boxes (Finished Products)", "ai_sample_from_packaged": "Sample from Packaged", "ai_sample_from_unpackaged": "Sample from Unpackaged", "ai_spare_parts_quantity": "Quantity of Spare Parts", "ai_sampling_packaging_number": "Sampling Packaging Number", "ai_sampling_packaging_number_record": "Sampling Packaging Number Record", "ai_sampling_packaging_number_list": "Sampling Packaging Number List", "ai_judgment": "Judgment", "ai_judgment_item": "Judgment Item", "ai_standard": "Standard", "ai_result": "Result", "ai_conclusion": "Conclusion", "ai_overall_conclusion": "Overall Conclusion", "ai_consistency": "Consistency", "ai_yes": "Yes", "ai_no": "No", "ai_remarks": "Remarks", "ai_numerical": "Numerical", "ai_recommended_test_items": "Recommended Test Items", "ai_test_item": "Test Item", "ai_add_all": "Fast Add", "ai_add_plus": "+ Add", "ai_add": "Add", "ai_confirm_delete": "Are you sure you want to delete {name}?", "ai_enter_test_item": "Please enter a test item", "ai_defect_record": "Defect Record", "ai_defect_photo": "Defect Photo", "ai_defect_description": "Defect Description", "ai_defect_level": "Defect Level", "ai_found_quantity": "Quantity Found", "ai_handling_method": "Handling Method", "ai_edit": "Edit", "ai_delete": "Delete", "ai_pick_out": "Pick Out", "ai_replace": "Replace", "ai_rework": "Rework", "ai_edit_description": "Edit Description", "ai_critical": "Critical", "ai_important": "Important", "ai_minor": "Minor", "ai_defect_list": "Defect List", "ai_test_level": "Test Level", "ai_sampling_sample": "Sampling Sample", "ai_sampling_level": "Sampling Level", "ai_additional_information": "Additional Information", "ai_inspection_record": "Inspection Record", "ai_sample_count": "Sample Count", "ai_maximum_allowable_value": "Maximum Allowable Value", "ai_test_item_name": "Test Item Name", "ai_test_result": "Test Result", "ai_basic_information": "Basic Information", "ai_new_test_item": "New Test Item", "ai_test_project": "Test Project", "ai_measurement_project": "Measurement Project", "ai_measure_need_num": "Measure Need Num", "ai_measurement_unit": "Measurement Unit", "ai_measurement_method": "Measurement Method", "ai_measurement_record": "Measurement Record", "ai_measured": "Measured", "ai_unit_of_measurement": "Unit of Measurement", "ai_measured_value": "Measured Value", "ai_product_number": "Product Number", "ai_number": "Number", "ai_new_measurement_item": "New Measurement Item", "ai_length_width_height": "Length, Width, Height", "ai_dimensions_length": "Length", "ai_dimensions_width": "<PERSON><PERSON><PERSON>", "ai_dimensions_height": "Height", "ai_length_width": "Length, Width", "ai_other": "Other", "ai_allowable_error": "Allowable Error", "ai_report_summary": "Report Summary", "ai_special_note": "Special Note", "ai_overall_conclusion_2": "Overall Conclusion", "ai_summary": "Summary", "ai_category_name_table": "Inspection Category", "ai_compliance": "Compliance Status", "ai_remarks_2": "Remarks", "ai_defect_summary": "Defect Summary", "ai_no_guidance_instructions": "No Guidance Instructions Available", "ai_no_standard_instructions": "No Standard Instructions Available", "ai_please_fill_in": "Please Fill In", "ai_please_supplement_level_or_sample": "Please Supplement {level} or {sample}", "ai_please_add": "Please Add", "ai_please_input": "Please Input", "ai_please_select": "Please Select", "ai_name_not_filled": "{name} Not Filled", "ai_addition_successful": "Addition Successful", "ai_confirm_action": "Confirm", "ai_cancel_action": "Cancel", "ai_submit": "Submit", "ai_next_item": "Next Item", "ai_complete": "Complete", "ai_change_description": "Change Description", "ai_action_guidance_instructions": "Guidance Instructions", "ai_action_standard_instructions": "Standard Instructions", "ai_add_description": "Add Description", "ai_change_description_note": "Attention: The following are the discovered defects. If modified, the historical data will also use the new description!", "ai_packing_completion_rate": "Packing Completion Rate", "ai_unprocessed_quantity": "Unprocessed Quantity", "ai_sample_level_type_0": "I Level", "ai_sample_level_type_1": "II Level", "ai_sample_level_type_2": "III Level", "ai_sample_level_type_s1": "S-1", "ai_sample_level_type_s2": "S-2", "ai_sample_level_type_s3": "S-3", "ai_sample_level_type_s4": "S-4", "ai_sample_level_type_nothing": "Custom", "ai_inspection_image": "Picture", "ai_photo_confirm": "Confirm", "ai_add_product_ask_save": "Do you need to save this edit?", "ai_add_product_save": "Save", "ai_add_product_edit_model": "Edit Model", "ai_add_product_model_name": "Model Name", "ai_add_product_input_model": "Enter Model Name", "ai_add_product_num": "Quantity", "ai_add_product_input_num": "Enter Quantity", "ai_add_product_unit": "Unit", "ai_add_product_ask_delete": "Do you want to delete this model?", "ai_add_product_edit_product": "Edit Product", "ai_add_product_product_name": "Product Name", "ai_add_product_model": "Model", "ai_add_product_input_product_name": "Enter Product Name", "ai_add_product_new_model": "Add New Model", "ai_add_product_ask_product": "Do you want to delete this product and all its models?", "ai_add_product_picture_lost": "Missing Picture", "ai_add_product_lost_des": "Currently missing {lostStr}. Please complete the information before inspection.", "ai_add_product_model_full": "Full Product Model Name", "ai_add_product_model_title": "Product Model", "ai_add_product_new": "Add Product", "ai_model_unit_piece": "piece", "ai_model_unit_only": "piece", "ai_model_unit_item": "piece", "ai_model_unit_pair": "pair", "ai_model_unit_set": "set", "ai_model_unit_dozen": "dozen", "ai_model_unit_roll": "roll", "ai_model_unit_vehicle": "vehicle", "ai_model_unit_head": "head", "ai_model_unit_bag": "bag", "ai_model_unit_box": "box", "ai_model_unit_pack": "pack", "ai_model_unit_yard": "yard", "ai_model_unit_meter": "meter", "ai_model_unit_kilogram": "kilogram", "ai_model_unit_metric_ton": "metric ton", "ai_model_unit_liter": "liter", "ai_model_unit_gallon": "gallon", "ai_model_unit_other": "other", "ai_default_config_des": "There are currently no detection templates for the product. You can choose the template below or call (+86) to configure the template.", "ai_default_config_category_all": "Category (All)", "ai_default_config_select_template": "Please select a template", "ai_default_config_template_selection": "Template Selection", "ai_default_config_search_template": "Search Template", "ai_default_config_classify": "classify", "ai_default_config_preview": "Preview", "ai_default_config_use": "Use", "ai_default_config_current_use_button": "Apply to This Product", "ai_default_config_more_use_button": "Apply to More Products", "ai_default_config_product_list": "Product List", "ai_default_config_use_warning": "Note: 【Mod】Product template already loaded; 【Ops】Template already configured by operations. Loading a new template will overwrite previous data.", "ai_default_config_tag_default": "Ops", "ai_default_config_tag_manual": "Mod", "ai_default_config_load_progress": "Loading Progress", "ai_default_config_template_progress": "Template loaded successfully for {name}.", "ai_default_config_template_fail_count": "Failed for {name}. Click to retry.", "ai_default_config_load": "Load", "ai_default_config_success": "Success", "ai_default_config_fail": "Failure", "ai_default_config_template": "template", "ai_add_model_count_warning": "The quantity must be greater than 0", "ai_default_config_product_edit": "product edit", "ai_wait": "wait", "ai_product_info": "Product information", "ai_product_category": "Product classification", "ai_product_unit": "Product unit", "ai_package_num_done": "Number of packed cases", "ai_product_full": "Supplementary information", "ai_product_full_tip": "The product information in the order is incomplete. Please supplement it according to the actual product information in the inspection site", "ai_each_box": "Each box", "ai_simple_count": "Number of samples", "ai_simple_level": "Sampling standard", "ai_simple_num": "Sampling quantity", "ai_simple_no": "Sample box number", "ai_simple_result": "Result determination", "ai_simple_project": "Check item", "ai_simple_project_manage": "Management check item", "ai_simple_project_edit": "Edit check item", "ai_simple_project_recmend": "Intelligent recommendation", "ai_simple_project_input": "Fill in the inspection record", "ai_simple_help": "Help", "ai_simple_project_record": "Inspection record", "ai_simple_require": "Customer requirement", "ai_simple_record": "Records", "ai_simple_dsec": "Description", "ai_simple_before": "previous", "ai_simple_add": "Add a set", "ai_simple_add_desc": "Add a description to the photo", "ai_simple_add_citations": "citations", "ai_no_more": "No more data", "ai_wrong_tip": "The quantity cannot be greater than the total", "ai_defect_records": "Defect record", "ai_check_require": "Sampling requirement", "ai_find_defect": "Find defect", "ai_defect_question": "Defect problem", "ai_modify_level": "Modified sampling grade", "ai_defect_quick": "Add process defects quickly", "ai_defect_self": "Custom defect name", "ai_defect_record_list": "Defect record list", "ai_measure_require": "Measurement requirement", "ai_measurement_item": "Measurement item", "ai_measurement_error": "error", "ai_measurement_standard": "Measuring standard", "ai_measurement_value_standard": "Standard value", "ai_measurement_camera": "Photograph", "ai_measurement_add": "Add measurement standards quickly", "ai_product_first": "cover", "ai_product_report": "Generate report", "ai_product_report_tip": "Please select the first product image", "ai_product_report_special": "Please enter anything that requires special attention", "ai_product_report_sign": "signature", "ai_product_report_sign_done": "Signature complete", "ai_defect_names": "Defect name", "ai_input_tip": "Please enter name", "ai_add_measure_tip": "Please add the measurement standard first", "ai_wrong_num": "Quantity error", "ai_wrong_name": "Please enter the product name", "ai_wrong_sample_num": "Cannot be greater than the number of samples", "ai_per_box": "Quantity per carton", "ai_wrong_sample_num_cal": "Packed sampling + unpacked sampling must equal the number of samples sampled", "ai_sure_delete": "Are you sure to delete it?", "ai_choose_tip": "Please select the defect handling method and the number", "ai_weight": "weight", "sampling_plan": "Sampling plan", "single": "Single", "normal": "Normal", "summarize": "Summarize", "po_number": "PO Number", "product_quantity": "Product Quantity", "customer_name": "Customer Name", "supplier_name": "Supplier Name", "inspection_date": "Inspection Date", "arrival_time": "Arrival Time", "completion_time": "Completion Time", "inspection_address": "Inspection Address", "inspector": "Inspector", "inspection_report_note": "This inspection report is for reference only. Final approval is subject to customer confirmation.", "remark_toast": "Please fill in the remarks first", "process_appearance_judgment": "Process appearance judgment", "test_validation_judgment": "Test validation judgment", "check_save": "Are you sure you want to save {name}?", "select_template_config_tip": "If you need to configure the inspection template, please contact your order follower"}