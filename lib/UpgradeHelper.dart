import 'dart:io';

import 'package:get/get.dart';
import 'package:inspector/app/config/api.dart';
import 'package:inspector/app/modules/init/views/update_view.dart';
import 'package:inspector/app/modules/store/privacy_store.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/privacy_helper.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';

class UpgradeHelper {
  static void alertUpdate({bool needToast = false}) {
    if (PrivacyStore.to.isPrivacyAgreed() && Platform.isAndroid) {
      _checkUpgrade((dynamic data) {
        String version = data['versionName'];
        if (version != GlobalConst.versionName) {
          Get.dialog(UpdateAppView(data));
        } else if (needToast == true) {
          showToast('已是最新版本');
        }
      });
    }
  }

  static Future<void> _checkUpgrade(DynamicCallback upgradeAction) async {
    var value = await PublicProvider.request(
      path: Api.updateApp,
      params: {'osVersion': Platform.isAndroid ? 'android' : 'ios'},
      isPost: false,
    );
    if (value.isSuccess) {
      if (Platform.isAndroid) {
        upgradeAction(value.data);
      }
    }
    return Future.value();
  }
}
