import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/home/<USER>/home_list_controller.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/widgets/search_filter/filter_panel_widget.dart';
import 'package:inspector/app/widgets/search_filter/filter_panel_controller.dart';
import 'package:inspector/app/widgets/search_filter/sort_panel_widget.dart';
import 'package:inspector/app/widgets/text.dart';

import '../../../generated/l10n.dart';

class SearchFilterWidget extends GetView<HomeListController> implements PreferredSizeWidget {
  const SearchFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    Widget input = Builder(builder: (context) {
      return Row(
        children: [
          Expanded(
            child: Text<PERSON><PERSON>(
              controller: controller.searchEditController,
              focusNode: controller.searchFocusNode,
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.only(left: 15),
                suffixIconConstraints: const BoxConstraints(minWidth: 24, maxWidth: 60, maxHeight: 46, minHeight: 46),
                suffixIcon: Obx(() {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Visibility(
                        visible: controller.showClear.value,
                        child: Row(
                          children: [
                            GestureDetector(
                              onTap: () {
                                controller.searchEditController.text = '';
                                controller.showClear.value = false;
                              },
                              child: Padding(
                                  padding: const EdgeInsets.all(4),
                                  child: Icon(Icons.cancel_rounded, size: 16, color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999)),
                            ),
                          ],
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          controller.searchQuery.value = controller.searchEditController.text;
                          controller.searchFocusNode.unfocus();
                          controller.fetchOrderList(false);
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(2.0),
                          child: Icon(
                            Icons.search_sharp,
                            size: 20,
                            color: context.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000,
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 12,
                      )
                    ],
                  );
                }),
                fillColor: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                filled: true,
                isDense: true,
                hintText:
                    GlobalConst.userModel?.role == UserRole.admin ? S.of(Get.context!).home_search_hint_admin : S.of(Get.context!).home_search_hint_inspector,
                hintStyle: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
                labelStyle: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000, width: 2),
                  borderRadius: BorderRadius.circular(20),
                ),
                focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000, width: 2),
                    borderRadius: BorderRadius.circular(20)),
                constraints: const BoxConstraints(maxHeight: 40),
              ),
              keyboardType: TextInputType.text,
              textInputAction: TextInputAction.search,
              onSubmitted: (value) {
                controller.searchQuery.value = value;
                controller.searchFocusNode.unfocus();
                controller.fetchOrderList(false);
              },
              onChanged: (val) {
                controller.showClear.value = val.isNotEmpty;
              },
            ),
          ),
          GestureDetector(
            onTap: () {
              Get.bottomSheet(
                BottomSheet(
                    onClosing: () {},
                    constraints: const BoxConstraints(maxHeight: 3 * SORT_HEIGHT + 28, minHeight: 3 * SORT_HEIGHT + 28),
                    builder: (context) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 16.0),
                        child: SortPanelWidget(tag: tag),
                      );
                    }),
                shape: RoundedRectangleBorder(
                  side: BorderSide(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
                ),
                clipBehavior: Clip.antiAliasWithSaveLayer,
                // isScrollControlled: true,
              );
            },
            child: const Icon(Icons.swap_vert_rounded, size: 24),
          ),
          GestureDetector(
            onTap: () {
              Get.bottomSheet(
                BottomSheet(
                    onClosing: () {},
                    constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 4 / 5, minHeight: MediaQuery.of(context).size.height * 2 / 5),
                    builder: (context) {
                      return FilterPanelWidget(controller.selectedStatus, controller.selectedAreas, controller.orderDate.value, controller.inspDate.value,
                          tag: tag);
                    }),
                shape: RoundedRectangleBorder(
                  side: BorderSide(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
                ),
                clipBehavior: Clip.antiAliasWithSaveLayer,
                isScrollControlled: true,
              ).then((value) {
                if (value is Map) {
                  List<OrderStateFilter> status = value['status'];
                  List<AreaFilter> area = value['area'];
                  DateFilter orderDate = value['orderDate'];
                  DateFilter inspDate = value['inspDate'];
                  controller.updateFilters(status, area, orderDate, inspDate);
                }
              });
            },
            child: Obx(() {
              var filterCount = controller.getFilterCount();
              return Badge(
                label: Text('$filterCount'),
                isLabelVisible: filterCount > 0,
                child: const Icon(
                  Icons.filter_alt_outlined,
                  size: 24,
                ),
              );
            }),
          )
        ],
      );
    });

    return Container(
      margin: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 6.0),
      child: input,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(46);
}
