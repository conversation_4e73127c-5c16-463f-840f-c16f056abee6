import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/order_list_entity.dart';
import 'package:inspector/app/tools/extensions.dart';

import '../../../generated/l10n.dart';

class FilterPanelController extends GetxController {
  late List<DateFilter> presetOrderDateFilters;
  late List<DateFilter> presetInspDateFilters;
  late List<OrderStateFilter> presetOrderStateFilters;

  final selectedStatus = RxList<OrderStateFilter>();
  final selectedAreas = RxList<AreaFilter>();
  final orderDate = DateFilter('OrderRange').obs;
  final inspDate = DateFilter('InspRange').obs;

  FilterPanelController();

  @override
  void onInit() {
    presetOrderStateFilters = buildOrderStatusFilters();
    presetOrderDateFilters = buildOrderDateFilters();
    presetInspDateFilters = buildInspDateFilters();
  }

  void copyTmpFilters(List<OrderStateFilter> status, List<AreaFilter> areas, DateFilter order, DateFilter insp) {
    selectedStatus.value = List.from(status);
    selectedAreas.value = List.from(areas);
    orderDate.value.fromDate = order.fromDate;
    orderDate.value.toDate = order.toDate;
    inspDate.value.fromDate = insp.fromDate;
    inspDate.value.toDate = insp.toDate;
  }

  void clearFilters() {
    selectedStatus.value = [];
    selectedAreas.value = [];
    orderDate.value.fromDate = null;
    orderDate.value.toDate = null;
    orderDate.refresh();
    inspDate.value.fromDate = null;
    inspDate.value.toDate = null;
    inspDate.refresh();
  }

  List<OrderStateFilter> buildOrderStatusFilters() {
    presetOrderStateFilters = [];
    /**
     *
        S.of(Get.context!).order_all,
        S.of(Get.context!).order_wait_dispatch,
        S.of(Get.context!).order_ready_inspect,
        S.of(Get.context!).order_wait_pay,
        S.of(Get.context!).order_cancelled,
        S.of(Get.context!).order_finished
        if (selectedIndex.value == 1) {
        //待派单
        type = 1;
        } else if (selectedIndex.value == 2) {
        //准备验货
        type = 3;
        } else if (selectedIndex.value == 3) {
        //验货中
        type = 4;
        } else if (selectedIndex.value == 4) {
        //待支付
        type = 0;
        } else if (selectedIndex.value == 5) {
        //取消
        type = -1;
        } else if (selectedIndex.value == 6) {
        //已完成
        type = 5;
        }
     */
    presetOrderStateFilters.add(OrderStateFilter(1, S.of(Get.context!).order_wait_dispatch));
    presetOrderStateFilters.add(OrderStateFilter(3, S.of(Get.context!).order_ready_inspect));
    presetOrderStateFilters.add(OrderStateFilter(0, S.of(Get.context!).order_wait_pay));
    presetOrderStateFilters.add(OrderStateFilter(-1, S.of(Get.context!).order_cancelled));
    presetOrderStateFilters.add(OrderStateFilter(5, S.of(Get.context!).order_finished));
    return presetOrderStateFilters;
  }

  List<DateFilter> buildInspDateFilters() {
    return [
      DateFilter(S.of(Get.context!).filter_date_today, fromDate: DateTime.now().today(), toDate: DateTime.now().today()),
      DateFilter(S.of(Get.context!).filter_date_tomorrow, fromDate: DateTime.now().tomorrow(), toDate: DateTime.now().tomorrow()),
      DateFilter(S.of(Get.context!).filter_date_2days_later, fromDate: DateTime.now(), toDate: DateTime.now().tomorrow()),
      DateFilter(S.of(Get.context!).filter_date_3days_later, fromDate: DateTime.now(), toDate: DateTime.now().tomorrow().tomorrow()),
    ];
  }

  List<DateFilter> buildOrderDateFilters() {
    return [
      DateFilter(S.of(Get.context!).filter_date_today, fromDate: DateTime.now().today(), toDate: DateTime.now().today()),
      DateFilter(S.of(Get.context!).filter_date_tomorrow, fromDate: DateTime.now().today().tomorrow(), toDate: DateTime.now().today().tomorrow()),
      DateFilter(S.of(Get.context!).filter_date_2days_later, fromDate: DateTime.now().today(), toDate: DateTime.now().today().tomorrow()),
      DateFilter(S.of(Get.context!).filter_date_3days_later, fromDate: DateTime.now().today(), toDate: DateTime.now().today().tomorrow().tomorrow()),
    ];
  }

  void addNewArea(province, city) {
    AreaFilter newFilter = AreaFilter(province, city);
    selectedAreas.removeWhere((element) {
      return newFilter.isSame(element) || newFilter.belongTo(element) || element.belongTo(newFilter);
    });
    selectedAreas.insert(0, newFilter);
  }

  void changeSelection(OrderStateFilter filter) {
    if (selectedStatus.contains(filter)) {
      selectedStatus.remove(filter);
    } else {
      selectedStatus.add(filter);
    }
  }

  bool isStatusSelected(OrderStateFilter filter) {
    return selectedStatus.contains(filter);
  }
}

class _FilterItem {
  _FilterItem();
}

class OrderStateFilter extends _FilterItem {
  final int status;
  final String name;

  OrderStateFilter(this.status, this.name);

  @override
  int get hashCode => status;

  @override
  bool operator ==(Object other) => other is OrderStateFilter && other.runtimeType == runtimeType && other.status == status;
}

class AreaFilter extends _FilterItem {
  final String province;
  final String city;

  AreaFilter(this.province, this.city);

  String areaName() {
    String name = '';
    if (province != null) {
      name = '$province';
    }
    if (city != null) {
      name = '$name $city';
    } else {
      return name;
    }
    return name;
  }

  bool isSame(AreaFilter another) {
    return province == another.province && city == another.city;
  }

  bool belongTo(AreaFilter another) {
    if (province != another.province) {
      return false;
    }
    if (city != another.city) {
      return another.city == null;
    }
    return true;
  }
}

class DateFilter extends _FilterItem {
  final String name;
  DateTime? fromDate;
  DateTime? toDate;

  DateFilter(this.name, {this.fromDate, this.toDate});

  String fromDateStr() {
    return fromDate == null ? S.of(Get.context!).filter_date_start : '${fromDate!.year}-${fromDate!.month}-${fromDate!.day}';
  }

  String toDateStr() {
    return toDate == null ? S.of(Get.context!).filter_date_end : '${toDate!.year}-${toDate!.month}-${toDate!.day}';
  }
}
