import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/home/<USER>/home_list_controller.dart';
import 'package:inspector/app/widgets/text.dart';

import '../../../generated/l10n.dart';

const SORT_HEIGHT = 45.0;

class SortPanelWidget extends GetView<HomeListController> {
  @override
  final tag;

  const SortPanelWidget({super.key, this.tag});

  @override
  HomeListController get controller {
    return Get.find<HomeListController>(tag: tag);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListView.builder(
          shrinkWrap: true,
          itemBuilder: (context, position) {
            if (position == 0) {
              return GestureDetector(
                onTap: () {
                  controller.sortBy.value = 0;
                  controller.updateSort();
                  Get.back();
                },
                child: SizedBox(
                    height: SORT_HEIGHT,
                    child: Stack(
                      children: [
                        Center(
                          child: Row(
                            children: [
                              const SizedBox(
                                width: 12,
                              ),
                              Expanded(
                                child: ThemeText(
                                  S.of(Get.context!).sort_by_order_date,
                                  style: MFont.semi_Bold16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(
                                width: 12,
                              ),
                            ],
                          ),
                        ),
                        Visibility(
                            visible: controller.sortBy.value == 0,
                            child: const Center(
                              child: Row(
                                children: [
                                  Spacer(),
                                  Icon(Icons.check_outlined, size: 20, color: MColor.skin),
                                  SizedBox(
                                    width: 12,
                                  )
                                ],
                              ),
                            )),
                      ],
                    )),
              );
            } else if (position == 1) {
              return GestureDetector(
                onTap: () {
                  controller.sortBy.value = 2;
                  controller.updateSort();
                  Get.back();
                },
                child: SizedBox(
                    height: SORT_HEIGHT,
                    child: Stack(
                      children: [
                        Center(
                          child: Row(
                            children: [
                              const SizedBox(
                                width: 12,
                              ),
                              Expanded(
                                child: ThemeText(
                                  S.of(Get.context!).sort_by_insp_date,
                                  style: MFont.semi_Bold16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(
                                width: 12,
                              ),
                            ],
                          ),
                        ),
                        Visibility(
                            visible: controller.sortBy.value == 2,
                            child: const Center(
                              child: Row(
                                children: [
                                  Spacer(),
                                  Icon(Icons.check_outlined, size: 20, color: MColor.skin),
                                  SizedBox(
                                    width: 12,
                                  )
                                ],
                              ),
                            )),
                      ],
                    )),
              );
            } else {
              return GestureDetector(
                onTap: () {
                  controller.requestPermission();
                  controller.sortBy.value = 1;
                  controller.updateSort();
                  Get.back();
                },
                child: SizedBox(
                    height: SORT_HEIGHT,
                    child: Stack(
                      children: [
                        Center(
                          child: Row(
                            children: [
                              const SizedBox(
                                width: 12,
                              ),
                              Expanded(
                                child: ThemeText(
                                  S.of(Get.context!).sort_by_distance,
                                  style: MFont.semi_Bold16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(
                                width: 12,
                              ),
                            ],
                          ),
                        ),
                        Visibility(
                            visible: controller.sortBy.value == 1,
                            child: const Center(
                              child: Row(
                                children: [
                                  Spacer(),
                                  Icon(Icons.check_outlined, size: 20, color: MColor.skin),
                                  SizedBox(
                                    width: 12,
                                  )
                                ],
                              ),
                            )),
                      ],
                    )),
              );
            }
          },
          itemCount: 3,
        ),
        const SizedBox(
          height: 12,
        )
      ],
    );
  }
}
