import 'package:flutter/material.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_pickers/time_picker/model/pduration.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/address/address_manager.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/widgets/search_filter/filter_panel_controller.dart';
import 'package:inspector/app/widgets/text.dart';

import 'package:inspector/app/tools/extensions.dart';

import '../../../generated/l10n.dart';

typedef DateTimeCallback = void Function(DateTime? dateTime);

class FilterPanelWidget extends GetView<FilterPanelController> {
  @override
  final tag;

  FilterPanelWidget(orderStatusFilters, areaFilters, orderDate, inspDate, {super.key, this.tag}) {
    FilterPanelController con = Get.put(FilterPanelController(), tag: tag);
    con.copyTmpFilters(orderStatusFilters, areaFilters, orderDate, inspDate);
    logger.i('FilterPanelWidget create $tag');
  }

  @override
  FilterPanelController get controller {
    return Get.find<FilterPanelController>(tag: tag);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
          color: Colors.white),
      padding: const EdgeInsets.only(left: 12, right: 12, top: 16, bottom: 16),
      child: Column(
        children: [
          ThemeText(
            S.of(Get.context!).filter_all,
            style: MFont.semi_Bold16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            textAlign: TextAlign.center,
          ),
          const SizedBox(
            height: 16,
          ),
          Expanded(
            child: ListView.separated(
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return getItemView(index);
              },
              separatorBuilder: (context, index) => const SizedBox(height: 10),
              itemCount: itemCount,
            ),
          ),
          _nextButton
        ],
      ),
    );
  }

  int get itemCount {
    if (GlobalConst.userModel?.role == UserRole.admin) {
      return 4;
    } else {
      return 3;
    }
  }

  Widget getItemView(int position) {
    if (GlobalConst.userModel?.role == UserRole.admin) {
      if (position == 0) {
        return _orderStatusRow;
      } else if (position == 1) {
        return _inspDateRow;
      } else if (position == 2) {
        return _orderDateRow;
      } else {
        return _areaRow;
      }
    } else {
      if (position == 0) {
        return _inspDateRow;
      } else if (position == 1) {
        return _orderDateRow;
      } else {
        return _areaRow;
      }
    }
  }

  Widget get _orderStatusRow {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: Text(
              S.of(Get.context!).filter_heading_order_status,
              style: MFont.semi_Bold16.apply(color: Get.context!.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            )),
            const SizedBox(
              width: 12,
            ),
          ],
        ),
        const SizedBox(
          height: 12,
        ),
        Align(
          alignment: Alignment.topLeft,
          child: Wrap(
            spacing: 8.0,
            runSpacing: 8.0,
            children: [
              for (OrderStateFilter filter in controller.presetOrderStateFilters) ...{
                GestureDetector(
                  onTap: () {
                    controller.changeSelection(filter);
                  },
                  child: Obx(() {
                    bool isSelected = controller.isStatusSelected(filter);
                    return Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: isSelected
                            ? (Get.context!.isDarkMode ? DarkColor.xFFFEE0D0 : MColor.xFFFEE0D0)
                            : (Get.context!.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                      child: Text(
                        filter.name,
                        style: MFont.regular14.apply(color: Get.context!.isDarkMode ? DarkColor.xFF2A2A2A : MColor.xFF2A2A2A),
                      ),
                    );
                  }),
                )
              }
            ],
          ),
        )
      ],
    );
  }

  Widget get _orderDateRow {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: Text(
              S.of(Get.context!).filter_heading_order_date,
              style: MFont.semi_Bold16.apply(color: Get.context!.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            )),
            const SizedBox(
              width: 12,
            ),
          ],
        ),
        const SizedBox(
          height: 12,
        ),
        Obx(() {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                  child: _dateRange(controller.orderDate.value, true, (dateTime) {
                controller.orderDate.value.fromDate = dateTime;
                controller.orderDate.refresh();
              })),
              SizedBox(
                height: 2,
                width: 32,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: Container(
                    decoration: BoxDecoration(
                        border: Border.all(
                      color: Get.context!.isDarkMode ? DarkColor.xFFF6F6F6 : MColor.xFFF6F6F6,
                    )),
                  ),
                ),
              ),
              Expanded(
                  child: _dateRange(controller.orderDate.value, false, (dateTime) {
                controller.orderDate.value.toDate = dateTime;
                controller.orderDate.refresh();
              })),
            ],
          );
        }),
        const SizedBox(
          height: 12,
        ),
        Align(
          alignment: Alignment.topLeft,
          child: Wrap(
            spacing: 8.0,
            runSpacing: 8.0,
            children: [
              for (DateFilter dateFilter in controller.presetOrderDateFilters) ...{
                Obx(() {
                  bool isSelected = (dateFilter.fromDate?.isSameDay(controller.orderDate.value.fromDate) ?? false) &&
                      (dateFilter.toDate?.isSameDay(controller.orderDate.value.toDate) ?? false);

                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      color: isSelected
                          ? (Get.context!.isDarkMode ? DarkColor.xFFFEE0D0 : MColor.xFFFEE0D0)
                          : (Get.context!.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    child: GestureDetector(
                      onTap: () {
                        if (isSelected) {
                          controller.orderDate.value.fromDate = null;
                          controller.orderDate.value.toDate = null;
                        } else {
                          controller.orderDate.value.fromDate = dateFilter.fromDate;
                          controller.orderDate.value.toDate = dateFilter.toDate;
                        }
                        controller.orderDate.refresh();
                      },
                      child: Text(
                        dateFilter.name,
                        style: MFont.regular14.apply(color: Get.context!.isDarkMode ? DarkColor.xFF2A2A2A : MColor.xFF2A2A2A),
                      ),
                    ),
                  );
                })
              }
            ],
          ),
        ),
      ],
    );
  }

  Widget get _inspDateRow {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: Text(
              S.of(Get.context!).filter_heading_insp_date,
              style: MFont.semi_Bold16.apply(color: Get.context!.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            )),
            const SizedBox(
              width: 12,
            ),
          ],
        ),
        const SizedBox(
          height: 12,
        ),
        Obx(() {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                  child: _dateRange(controller.inspDate.value, true, (dateTime) {
                controller.inspDate.value.fromDate = dateTime;
                controller.inspDate.refresh();
              })),
              SizedBox(
                height: 2,
                width: 32,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: Container(
                    decoration: BoxDecoration(
                        border: Border.all(
                      color: Get.context!.isDarkMode ? DarkColor.xFFF6F6F6 : MColor.xFFF6F6F6,
                    )),
                  ),
                ),
              ),
              Expanded(
                  child: _dateRange(controller.inspDate.value, false, (dateTime) {
                controller.inspDate.value.toDate = dateTime;
                controller.inspDate.refresh();
              })),
            ],
          );
        }),
        const SizedBox(
          height: 12,
        ),
        Align(
          alignment: Alignment.topLeft,
          child: Wrap(
            spacing: 8.0,
            runSpacing: 8.0,
            children: [
              for (DateFilter dateFilter in controller.presetInspDateFilters) ...{
                Obx(() {
                  bool isSelected = (dateFilter.fromDate?.isSameDay(controller.inspDate.value.fromDate) ?? false) &&
                      (dateFilter.toDate?.isSameDay(controller.inspDate.value.toDate) ?? false);
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      color: isSelected
                          ? (Get.context!.isDarkMode ? DarkColor.xFFFEE0D0 : MColor.xFFFEE0D0)
                          : (Get.context!.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    child: GestureDetector(
                      onTap: () {
                        if (isSelected) {
                          controller.inspDate.value.fromDate = null;
                          controller.inspDate.value.toDate = null;
                        } else {
                          controller.inspDate.value.fromDate = dateFilter.fromDate;
                          controller.inspDate.value.toDate = dateFilter.toDate;
                        }
                        controller.inspDate.refresh();
                      },
                      child: ThemeText(
                        dateFilter.name,
                        style: MFont.regular14.apply(color: Get.context!.isDarkMode ? DarkColor.xFF2A2A2A : MColor.xFF2A2A2A),
                      ),
                    ),
                  );
                }),
              }
            ],
          ),
        ),
      ],
    );
  }

  Widget get _areaRow {
    return Obx(() {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                  child: Text(
                S.of(Get.context!).filter_heading_area,
                style: MFont.semi_Bold16.apply(color: Get.context!.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              )),
              const SizedBox(
                width: 12,
              ),
            ],
          ),
          const SizedBox(
            height: 12,
          ),
          Align(
            alignment: Alignment.topLeft,
            child: Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                for (int i = 0; i < controller.selectedAreas.length + 1; i++) ...{
                  if (i == controller.selectedAreas.length) ...{
                    GestureDetector(
                      onTap: () {
                        Pickers.showMultiLinkPicker(
                          Get.context!,
                          data: AddressManager.toCityMap(),
                          columeNum: 2,
                          selectData: ['', ''],
                          pickerStyle: PickerStyle(
                            // showTitleBar: true,
                            commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
                            cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
                            itemOverlay: Builder(builder: (context) {
                              return Container(
                                decoration: BoxDecoration(
                                  border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
                                ),
                              );
                            }),
                          ),
                          // initTown: '',
                          // addAllItem: false,
                          onConfirm: (List p, List i) {
                            var province = p[0];
                            var city = p[1];
                            controller.addNewArea(province, city);
                          },
                        );
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: Get.context!.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 8),
                        child: const Icon(
                          Icons.add_location_alt_outlined,
                          size: 16,
                        ),
                      ),
                    ),
                  } else ...{
                    GestureDetector(
                      onTap: () {
                        controller.selectedAreas.removeAt(i);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: Get.context!.isDarkMode ? DarkColor.xFFFEE0D0 : MColor.xFFFEE0D0,
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              controller.selectedAreas[i].areaName(),
                              style: MFont.regular14.apply(color: Get.context!.isDarkMode ? DarkColor.xFF2A2A2A : MColor.xFF2A2A2A),
                            ),
                            const SizedBox(
                              width: 3,
                            ),
                            const Icon(
                              Icons.wrong_location_outlined,
                              size: 16,
                            )
                          ],
                        ),
                      ),
                    )
                  }
                }
              ],
            ),
          ),
          const SizedBox(
            height: 12,
          ),
        ],
      );
    });
  }

  Widget get _nextButton {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Expanded(
          child: TextButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(const RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(25), bottomLeft: Radius.circular(25)),
                )),
                backgroundColor: MaterialStateProperty.all(MColor.xFFD9A179),
                minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
              ),
              onPressed: () {
                controller.clearFilters();
              },
              child: Text(
                S.of(Get.context!).public_reset,
                style: MFont.medium18.apply(color: Colors.white),
              )),
        ),
        Expanded(
          child: TextButton(
            style: ButtonStyle(
              shape: MaterialStateProperty.all(const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topRight: Radius.circular(25), bottomRight: Radius.circular(25)),
              )),
              backgroundColor: MaterialStateProperty.all(MColor.xFFE95332),
              minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
            ),
            onPressed: () {
              Get.back(result: {
                'status': controller.selectedStatus,
                'area': controller.selectedAreas,
                'orderDate': controller.orderDate.value,
                'inspDate': controller.inspDate.value
              });
            },
            child: Text(
              S.of(Get.context!).public_finish,
              style: MFont.medium18.apply(color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _dateRange(DateFilter dateFilter, bool isFrom, DateTimeCallback callback) {
    var minDate = GlobalConst.userModel?.role == UserRole.admin ? PDuration.parse(DateTime.now().lastYear()) : PDuration.parse(DateTime.now());
    var maxDate = PDuration.parse(DateTime.now().nextYear());
    bool isSelected = isFrom ? (dateFilter.fromDate != null) : (dateFilter.toDate != null);
    var bgColor =
        isSelected ? (Get.context!.isDarkMode ? DarkColor.xFFFEE0D0 : MColor.xFFFEE0D0) : (Get.context!.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7);
    if (isFrom) {
      maxDate = dateFilter.toDate == null ? maxDate : PDuration.parse(dateFilter.toDate!);
    } else {
      minDate = dateFilter.fromDate == null ? minDate : PDuration.parse(dateFilter.fromDate!);
    }
    return GestureDetector(
      onTap: () {
        Pickers.showDatePicker(
          Get.context!,
          minDate: minDate,
          maxDate: maxDate,
          pickerStyle: PickerStyle(
            commitButton: Container(
                padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge),
                child: Text(S.of(Get.context!).public_ok,
                    textAlign: TextAlign.center, style: MFont.regular14.apply(color: Get.context!.isDarkMode ? DarkColor.xFF2A2A2A : MColor.xFF2A2A2A))),
            cancelButton: Container(
                padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge),
                child: Text(S.of(Get.context!).public_cancel,
                    textAlign: TextAlign.center, style: MFont.regular14.apply(color: Get.context!.isDarkMode ? DarkColor.xFF2A2A2A : MColor.xFF2A2A2A))),
            pickerItemHeight: 30,
            textSize: 15,
          ),
          onConfirm: (value) {
            callback(DateTime(value.year!, value.month!, value.day!));
          },
        );
      },
      child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            color: bgColor,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: ThemeText(
                  isFrom ? dateFilter.fromDateStr() : dateFilter.toDateStr(),
                  textAlign: TextAlign.center,
                  style: MFont.regular14.apply(color: Get.context!.isDarkMode ? DarkColor.xFF2A2A2A : MColor.xFF2A2A2A),
                ),
              ),
              if (isFrom ? dateFilter.fromDate != null : dateFilter.toDate != null) ...{
                GestureDetector(
                  onTap: () {
                    callback(null);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: Icon(Icons.close_outlined, size: 16, color: Get.context!.isDarkMode ? DarkColor.xFF2A2A2A : MColor.xFF2A2A2A),
                  ),
                )
              }
            ],
          )),
    );
  }
}
