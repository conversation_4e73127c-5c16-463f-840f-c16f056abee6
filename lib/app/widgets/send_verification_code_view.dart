import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../generated/l10n.dart';
import '../config/design.dart';

class SendVerificationCodeView extends StatefulWidget {
  final bool isEmail;
  final String? email;
  final String? phone;
  final String? areaCode;
  final bool immediately;
  const SendVerificationCodeView(this.isEmail, this.email, this.phone, this.areaCode, this.immediately, {super.key});

  @override
  State<StatefulWidget> createState() {
    return _SendVerificationCodeState();
  }
}

class _SendVerificationCodeState extends State<SendVerificationCodeView> {
  //data
  int time = 61;
  Timer? _timer;

  @override
  void initState() {
    if (widget.immediately) {
      _takeCode();
    }
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _timer = null;
    super.dispose();
  }

  void _beginTimer() {
    _timer?.cancel();
    _timer = null;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (time == 0) {
        _timer?.cancel();
        _timer = null;
        setState(() {
          time = 61;
        });

        return;
      }
      setState(() {
        time = time - 1;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return TextButton(
      style: ButtonStyle(
        textStyle: WidgetStateProperty.all(MFont.regular13),
        foregroundColor: WidgetStateProperty.all(MColor.xFF333333),
        minimumSize: WidgetStateProperty.all(const Size(10, 20)),
        visualDensity: VisualDensity.compact,
      ),
      onPressed: () {
        _takeCode();
      },
      child: Container(
        padding: const EdgeInsets.only(right: 17),
        child: Text(
          time == 61 ? S.of(Get.context!).login_take_code : '$time s',
          style: MFont.regular14.apply(color: MColor.skin),
          textAlign: TextAlign.right,
        ),
      ),
    );
  }

  void _takeCode() {
    if (time != 61) {
      return;
    }
    Get.showOverlay(
      asyncFunction: () => Future.value(),
      loadingWidget: loadingWidget,
    );
    _beginTimer();
    MineProvider().takeCode(widget.isEmail, widget.isEmail ? widget.email! : widget.phone!, widget.isEmail ? null : widget.areaCode).then((value) {
      if (!value.isSuccess) {
        _timer?.cancel();
        _timer = null;
        setState(() {
          time = 61;
        });
      }
      showToast(value.message ?? '');
    });
  }
}
