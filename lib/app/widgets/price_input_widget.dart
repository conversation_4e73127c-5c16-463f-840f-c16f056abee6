import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../generated/l10n.dart';

class PriceInputWidget extends StatefulWidget {
  final numberKeys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '.', '0', 'KB'];
  final priceEditor = TextEditingController();
  final focusNode = FocusNode();

  String initialPrice = '0.00';
  String labelText = '';
  bool isUSD = false;
  double huilv = 1;

  PriceInputWidget(this.initialPrice, this.labelText, this.isUSD, this.huilv, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _PriceInputState();
  }
}

class _PriceInputState extends State<PriceInputWidget> {
  @override
  void initState() {
    super.initState();
    double initialPrice = double.tryParse(widget.initialPrice) ?? 0;
    if (initialPrice != 0) {
      widget.priceEditor.text = widget.initialPrice;
    }
    widget.focusNode.requestFocus();
  }

  @override
  void dispose() {
    widget.focusNode.dispose();
    widget.priceEditor.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;

    var totalWidth = min(size.height, size.width);

    // 数字键占5/7
    double numberTotalWidth = (totalWidth) * 5 / 7;
    // 功能键占2/7
    double funcKeyWidth = (totalWidth) * 2 / 7;
    double funcKeyRatio = 0.9;
    double funcKeyHeight = funcKeyWidth / funcKeyRatio;
    double funcKeyTotalHeight = funcKeyHeight * 2;

    double numberKeyHeight = funcKeyTotalHeight / 4;
    double numberKeyWidth = numberTotalWidth / 3;
    double numberKeyRatio = numberKeyWidth / numberKeyHeight;

    return Container(
      decoration: const BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            height: 16,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(
                width: 20,
              ),
              Center(
                  child: Text(
                widget.labelText,
                style: MFont.semi_Bold16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                strutStyle: const StrutStyle(
                  forceStrutHeight: true,
                  leading: 0.5,
                ),
              )),
              const SizedBox(
                width: 8,
              ),
              const Spacer(),
              IntrinsicWidth(
                child: TextField(
                    strutStyle: const StrutStyle(
                      forceStrutHeight: true,
                      leading: 0.5,
                    ),
                    focusNode: widget.focusNode,
                    style: const TextStyle(fontSize: 24, fontWeight: FontWeight.w600, color: MColor.xFFE95332, height: 1),
                    controller: widget.priceEditor,
                    textAlign: TextAlign.end,
                    textAlignVertical: TextAlignVertical.center,
                    showCursor: false,
                    readOnly: true,
                    cursorHeight: 18,
                    cursorColor: context.isDarkMode ? DarkColor.xFFE95332 : MColor.xFFE95332,
                    // inputFormatters: [FilteringTextInputFormatter(RegExp('^([1-9]\\d{0,9}|0)(\\.\\d{1,2})?\$'), allow: true)],
                    decoration: InputDecoration(
                        filled: true,
                        fillColor: MColor.xFFFFFFFF,
                        isDense: true,
                        hintText: '0.00',
                        hintStyle: const TextStyle(fontSize: 24, fontWeight: FontWeight.w600, color: DarkColor.xFF3D3D3D, height: 1),
                        contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                        // prefix: Text('\$'),
                        prefix: SizedBox(
                          child: Builder(builder: (context) {
                            bool isEmptyInput = widget.priceEditor.text.isEmpty;
                            return widget.isUSD
                                ? Padding(
                                    padding: const EdgeInsets.only(right: 2),
                                    child: Text(
                                      '\$',
                                      style: TextStyle(
                                          fontSize: 16, fontWeight: FontWeight.w600, color: isEmptyInput ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D, height: 1),
                                      textAlign: TextAlign.center,
                                    ),
                                  )
                                : Padding(
                                    padding: const EdgeInsets.only(right: 2),
                                    child: Text(
                                      '¥',
                                      style: TextStyle(
                                          fontSize: 16, fontWeight: FontWeight.w600, color: isEmptyInput ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D, height: 1),
                                    ),
                                  );
                          }),
                        ),
                        // constraints: const BoxConstraints(maxHeight: 30, minHeight: 30),
                        focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: MColor.xFFA4A5A9_80),
                        ),
                        enabledBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: MColor.xFFA4A5A9_80),
                        ),
                        border: UnderlineInputBorder(borderSide: BorderSide(color: MColor.xFFA4A5A9_80)))),
              ),
              const SizedBox(
                width: 20,
              )
            ],
          ),
          const SizedBox(
            height: 8,
          ),
          Row(
            children: [
              Expanded(
                flex: 5,
                child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 3, childAspectRatio: numberKeyRatio),
                    itemBuilder: (context, position) {
                      double leftWidth = position % 3 != 0 ? 2 : 0;
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            String text = widget.priceEditor.text;
                            if (position == widget.numberKeys.length - 1) {
                              widget.isUSD = !widget.isUSD;
                              if (text.isEmpty) {
                                return;
                              }
                              double inputPrice = double.tryParse(text) ?? 0;
                              if (widget.isUSD) {
                                inputPrice = inputPrice / widget.huilv;
                              } else {
                                inputPrice = inputPrice * widget.huilv;
                              }
                              widget.priceEditor.text = inputPrice.toStringAsFixed(2);
                            } else {
                              if (text.contains('.') && position == 9) {
                                return;
                              }
                              if (text.isEmpty && position == 9) {
                                return;
                              }
                              if (text.length == 1 && text == '0' && position == 10) {
                                return;
                              }
                              if (text.contains('.') && text.lastIndexOf('.') == text.length - 3) {
                                return;
                              }
                              if (text.startsWith('0') && position != 9 && text.length == 1) {
                                text = text.substring(1, text.length);
                              }
                              String newText = '$text${widget.numberKeys[position]}';
                              double newPrice = double.tryParse(newText) ?? 0;
                              if (newPrice >= 0 && newPrice <= 1000000) {
                                widget.priceEditor.text = '$text${widget.numberKeys[position]}';
                              } else {
                                showToast(S.of(Get.context!).price_input_error_zero);
                              }
                            }
                          });
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border(
                              top: BorderSide(width: 2, color: context.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80),
                              left: BorderSide(width: leftWidth, color: context.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80),
                            ),
                          ),
                          child: Center(
                            child: Builder(builder: (context) {
                              if (position == widget.numberKeys.length - 1) {
                                return Icon(
                                  Icons.currency_exchange,
                                  size: 30,
                                  color: !widget.isUSD ? MColor.xFF3D3D3D : MColor.xFFE95332,
                                );
                              } else {
                                return Text(
                                  widget.numberKeys[position],
                                  textAlign: TextAlign.center,
                                  style: MFont.regular30.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                                );
                              }
                            }),
                          ),
                        ),
                      );
                    },
                    itemCount: 12),
              ),
              Expanded(
                flex: 2,
                child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 1, childAspectRatio: 0.9),
                    itemBuilder: (context, position) {
                      if (position == 0) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              String text = widget.priceEditor.text;
                              if (text.isNotEmpty) {
                                widget.priceEditor.text = text.substring(0, text.length - 1);
                              }
                            });
                          },
                          child: Container(
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(width: 2, color: context.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80),
                                  left: BorderSide(width: 2, color: context.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80),
                                ),
                              ),
                              child: const Center(
                                child: Icon(
                                  Icons.backspace_outlined,
                                  size: 30,
                                ),
                              )),
                        );
                      } else {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              String text = widget.priceEditor.text;
                              // if (text == '0.00') {
                              // return;
                              // } else {
                              double price = double.tryParse(text) ?? 0;
                              Get.back(result: {'price': price.toStringAsFixed(2), 'isUSD': widget.isUSD});
                              // }
                            });
                          },
                          child: Container(
                            decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(width: 2, color: context.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80),
                                  left: BorderSide(width: 2, color: context.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80),
                                ),
                                color: context.isDarkMode ? DarkColor.xFFE95332 : MColor.xFFE95332),
                            child: Center(
                              child: Text(
                                S.of(Get.context!).public_ok,
                                textAlign: TextAlign.center,
                                style: MFont.semi_Bold20.apply(color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE),
                              ),
                            ),
                          ),
                        );
                      }
                    },
                    itemCount: 2),
              )
            ],
          )
          // GridView.builder(gridDelegate: gridDelegate, itemBuilder: itemBuilder)
        ],
      ),
    );
  }
}
