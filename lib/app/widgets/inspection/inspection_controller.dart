import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/home/<USER>';
import 'package:inspector/app/modules/home/<USER>/controllers/record_controller.dart';
import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../generated/l10n.dart';

class InspectionController extends GetxController {
  TextEditingController noteController = TextEditingController();
  HomeProvider provider = HomeProvider();
  OrderProvider orderProvider = OrderProvider();
  TextEditingController priceController = TextEditingController();
  FocusNode focusNode = FocusNode();
  final isCheck = true.obs;
  final accountType = 1.obs;

  VoidCallback? onApplied;
  VoidCallback? onCancelled;

  void fetchApply(int? orderId) {
    if (!isCheck.value) {
      showToast(S.of(Get.context!).home_apply_check);
      return;
    }
    if (priceController.text.isEmpty) {
      return;
    }
    if (orderId == null) {
      return;
    }
    var price = priceController.text;

    EasyLoading.show();
    provider.takeApply(orderId, price, accountType.value, noteController.text.trim()).then((value) async {
      if (value.isSuccess) {
        Get.back();

        if (onApplied != null) {
          onApplied!();
        }
      }
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void canAction(int orderId, bool isSelf) {
    EasyLoading.show();
    orderProvider.takeOrderCancel(orderId, !isSelf).then((value) async {
      if (value.isSuccess) {
        Get.back();
        if (onCancelled != null) {
          onCancelled!();
        }
      }
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  @override
  void onClose() {
    noteController.dispose();
    focusNode.dispose();
  }
}
