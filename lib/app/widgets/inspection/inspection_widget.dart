import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/app_theme.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/tools/translation_service.dart';
import 'package:inspector/app/wallet/wallet_provider.dart';
import 'package:inspector/app/widgets/inspection/inspection_controller.dart';
import 'package:inspector/app/widgets/price_input_widget.dart';
import 'package:inspector/generated/assets.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../generated/l10n.dart';
import '../../modules/store/setting_store.dart';
import '../../tools/storage_util.dart';

class InspectionWidget extends GetView<InspectionController> {
  @override
  final tag;

  final int orderID;
  final bool isFirst;
  final double initialPrice;
  final String initialRemark;
  final dynamic liverooms;

  InspectionWidget(this.orderID, this.isFirst, this.initialPrice, this.initialRemark, this.liverooms, VoidCallback onApplied, VoidCallback onCancelled,
      {Key? key, this.tag})
      : super(key: key) {
    if (controller.priceController.text.isEmpty) {
      controller.priceController.text = initialPrice.toStringAsFixed(2);
    }
    controller.noteController.text = initialRemark;
    controller.onApplied = onApplied;
    controller.onCancelled = onCancelled;
    // FocusScope.of(Get.context!).requestFocus(controller.focusNode);
  }

  @override
  InspectionController get controller {
    return GetInstance().putOrFind(() => InspectionController(), tag: tag);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: Theme.of(context).colorScheme.conatiner,
      ),
      padding: EdgeInsets.fromLTRB(15, 0, 15, 0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 46,
            child: Center(
              child: Text(
                S.of(context).inspection_requirement,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: MColor.skin),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          if (liverooms is Map<String, dynamic> && liverooms.containsKey('grab_order_tip_up')) ...{
            Row(
              children: [
                Expanded(
                  child: Text(
                    liverooms['grab_order_tip_up'],
                    style: MFont.regular14.apply(color: MColor.xFF333333),
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 12,
            ),
          },
          Row(
            children: [
              Expanded(child: _textField),
            ],
          ),
          const SizedBox(height: 12),
          Row(children: [Expanded(child: _txtNote)]),
          const SizedBox(height: 12),
          if (liverooms is Map<String, dynamic> && liverooms.containsKey('grab_order_tip_down')) ...{
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppIcons.icon(
                  0xe62b,
                  size: 20,
                  color: MColor.skin,
                ),
                const SizedBox(
                  width: 4,
                ),
                Expanded(
                    child: Text(
                  liverooms['grab_order_tip_down'],
                  // 'style: TextStyle(fontSize: 16, color: MColor.xFF000000, fontWeight: FontWeight.w500)',
                  style: TextStyle(fontSize: 16, color: MColor.xFF000000, fontWeight: FontWeight.w500),
                  textAlign: TextAlign.start,
                )),
              ],
            ),
            const SizedBox(height: 12),
          },
          if (liverooms is Map<String, dynamic> &&
              liverooms.containsKey('grab_order_entrance') &&
              liverooms['grab_order_entrance'] is List &&
              liverooms['grab_order_entrance'].isNotEmpty) ...{
            Row(
              children: [
                Expanded(
                  child: Text(
                    S.of(context).liveroom_entrance,
                    textAlign: TextAlign.start,
                    style: TextStyle(fontSize: 14, color: MColor.xFF333333),
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 12,
            ),
            Row(
              children: [
                const SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    alignment: WrapAlignment.center,
                    runAlignment: WrapAlignment.center,
                    spacing: 12.0,
                    runSpacing: 12.0,
                    children: [
                      for (var liveroom in liverooms['grab_order_entrance']) ...{
                        GestureDetector(
                          onTap: () {
                            if (Uri.parse(liveroom['url']).isAbsolute) {
                              // goto download
                              launchUrlString(liveroom['url'], mode: LaunchMode.externalApplication);
                            }
                          },
                          child: Column(
                            children: [
                              CachedNetworkImage(
                                imageUrl: liveroom['icon'],
                                width: 36,
                                height: 36,
                                fit: BoxFit.cover,
                                placeholder: (ctx, e) {
                                  return Container(
                                    decoration: const BoxDecoration(
                                      color: MColor.xFFE5E5E5,
                                    ),
                                  );
                                },
                                errorWidget: (ctx, e, x) {
                                  return Container(
                                    decoration: const BoxDecoration(
                                      color: MColor.xFFE5E5E5,
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(
                                height: 6,
                              ),
                              Text(
                                liveroom['name'],
                                style: TextStyle(
                                  fontSize: 14,
                                  color: MColor.xFF000000,
                                ),
                              )
                            ],
                          ),
                        )
                      }
                    ],
                  ),
                ),
                const SizedBox(
                  width: 15,
                ),
              ],
            ),
            const SizedBox(
              height: 12,
            ),
          },
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  controller.isCheck.value = !controller.isCheck.value;
                },
                child: Obx(() {
                  return Icon(controller.isCheck.value ? Icons.check_box_outlined : Icons.check_box_outline_blank_outlined, size: 16, color: MColor.skin);
                }),
              ),
              const SizedBox(width: 5),
              Text(
                S.of(Get.context!).home_reviewed,
                style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666),
              ),
              GestureDetector(
                onTap: () {
                  Get.toNamed(
                    Routes.WEB,
                    parameters: {
                      'url': '${Server.web}/privacy/about_inspection.html',
                      'title'
                          '': S.of(Get.context!).home_know_tip
                    },
                  );
                },
                child: Text(
                  S.of(Get.context!).home_know_tip,
                  style: MFont.regular13.apply(color: MColor.skin),
                ),
              ),
            ],
          ),
          const SizedBox(height: 17),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    controller.fetchApply(orderID);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 7),
                    decoration:
                        BoxDecoration(gradient: const LinearGradient(colors: [MColor.skin, MColor.xFFFB6668]), borderRadius: BorderRadius.circular(19.5)),
                    child: Center(
                      child: Text(
                        isFirst ? S.of(Get.context!).home_apply : S.of(Get.context!).home_update,
                        style: MFont.regular15.apply(color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ),
              if (!isFirst) ...{
                const SizedBox(width: 10),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      controller.canAction(orderID, false);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 7),
                      decoration:
                          BoxDecoration(gradient: const LinearGradient(colors: [MColor.xFFFB6668, MColor.skin]), borderRadius: BorderRadius.circular(19.5)),
                      child: Center(
                        child: Text(
                          S.of(Get.context!).home_apply_cancel,
                          style: MFont.regular15.apply(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                ),
              },
            ],
          ),
          const SizedBox(height: 25),
        ],
      ),
    );
  }

  Widget get _textField {
    return Builder(builder: (context) {
      return TextField(
        controller: controller.priceController,
        keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: false),
        inputFormatters: [PrecisionLimitFormatter(2)],
        showCursor: true,
        readOnly: true,
        onTap: () {
          Get.bottomSheet(
            SafeArea(
              // and this
              child: PriceInputWidget(controller.priceController.text, S.of(Get.context!).publish_total, controller.accountType.value == 2,
                  WalletProvider.instance.getExchangeRate()),
            ),
            isScrollControlled: true,
            ignoreSafeArea: false, // add this
          ).then((value) {
            if (value is Map) {
              controller.accountType.value = value['isUSD'] ? 2 : 1;
              controller.priceController.value = TextEditingValue(
                  text: value['price'], selection: TextSelection.fromPosition(TextPosition(affinity: TextAffinity.downstream, offset: value['price'].length)));
              // controller.focusNode.requestFocus();
            }
          });
        },
        cursorColor: MColor.skin,
        style: MFont.semi_Bold22.apply(color: Colors.black),
        decoration: InputDecoration(
          hintText: S.of(Get.context!).home_apply_price,
          hintStyle: MFont.regular14.apply(color: MColor.xFFA6A6A6),
          filled: true,
          isDense: false,
          prefixIconConstraints: BoxConstraints(
            minWidth: 40,
            minHeight: 40,
          ),
          prefixIcon: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              var price = double.tryParse(controller.priceController.text) ?? 0.0;
              if (controller.accountType.value == 1) {
                controller.accountType.value = 2;
                price = price / WalletProvider.instance.getExchangeRate();
              } else {
                controller.accountType.value = 1;
                price = price * WalletProvider.instance.getExchangeRate();
              }
              controller.priceController.text = price.toStringAsFixed(2);
              StorageUtil.setInt(Constant.accountType, controller.accountType.value);
            },
            child: SizedBox(
              height: 40,
              width: 40,
              child: Center(
                child: Obx(
                  () => Text(
                    controller.accountType.value == 1 ? '¥' : '\$',
                    style: MFont.semi_Bold22.apply(color: MColor.xFF000000),
                  ),
                ),
              ),
            ),
          ),
          contentPadding: EdgeInsets.zero,
          fillColor: MColor.xFFFFFFFF,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.skin),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
        ),
      );
    });
  }

  Widget get _txtNote {
    return Builder(builder: (context) {
      return TextField(
        controller: controller.noteController,
        focusNode: controller.focusNode,
        textAlign: TextAlign.left,
        maxLength: 200,
        maxLines: 1,
        decoration: InputDecoration(
          hintText: S.of(Get.context!).note,
          hintStyle: MFont.regular14.apply(color: MColor.xFFA6A6A6),
          filled: true,
          isDense: false,
          counterText: '',
          contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 0),
          fillColor: MColor.xFFFFFFFF,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.skin),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
        ),
      );
    });
  }
}
