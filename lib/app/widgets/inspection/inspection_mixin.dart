import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/home/<USER>/home_list_controller.dart';
import 'package:inspector/app/modules/home/<USER>';
import 'package:inspector/app/modules/home/<USER>/controllers/order_detail_controller.dart';
import 'package:inspector/app/modules/home/<USER>/controllers/record_controller.dart';
import 'package:inspector/app/modules/mine/controllers/mine_controller.dart';
import 'package:inspector/app/modules/new_home/home_new_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/widgets/inspection/inspection_widget.dart';

import '../../../generated/l10n.dart';

mixin InspectionMixin {
  final _homeProvider = HomeProvider();

  void fetchPrice(int? orderId, bool isFirst) {
    if (orderId == null) {
      return;
    }
    // if (GlobalConst.userModel?.role != UserRole.inspector) {
    //   showCustomDialog(S.of(Get.context!).home_apply_tips, okTitle: S.of(Get.context!).home_apply_sure, onConfirm: () {
    //     Get.toNamed(Routes.APPLY);
    //   }, cancel: true);
    //   return;
    // }
    EasyLoading.show();
    _homeProvider.takePrice(orderId).then((value) async {
      if (value.isSuccess) {
        var price = double.tryParse(value.data['price']) ?? 0;
        var remark = value.data['mark'] ?? '';

        await EasyLoading.dismiss();
        await Get.bottomSheet(
          persistent: false,
          isScrollControlled: true,
          ignoreSafeArea: false,
          InspectionWidget(
            orderId,
            isFirst,
            price,
            remark,
            value.data['live_room'],
            () {
              if (Get.isRegistered<HomeListController>()) {
                Get.find<HomeListController>().updateApply(orderId, true);
              }
              if (Get.isRegistered<OrderDetailController>()) {
                Get.find<OrderDetailController>().updateApply(true);
              }
              if (Get.isRegistered<HomeNewController>()) {
                Get.find<HomeNewController>().updateApply(orderId, true);
              }
            },
            () {
              if (Get.isRegistered<HomeListController>()) {
                Get.find<HomeListController>().updateApply(orderId, false);
              }
              if (Get.isRegistered<HomeNewController>()) {
                Get.find<HomeNewController>().updateApply(orderId, false);
              }
              if (Get.isRegistered<OrderDetailController>()) {
                Get.find<OrderDetailController>().updateApply(false);
              }
              if (Get.isRegistered<RecordController>()) {
                Get.find<RecordController>().refreshAction();
              }
            },
            tag: orderId.toString(),
          ),
        ).then((value) {
          logger.i('then');
        });
      } else if (value.code == 20005) {
        showCustomDialog(value.message ?? '', okTitle: S.of(Get.context!).home_apply_sure, onConfirm: () {
          Get.toNamed(Routes.APPLY);
        }, cancel: true);
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }
}
