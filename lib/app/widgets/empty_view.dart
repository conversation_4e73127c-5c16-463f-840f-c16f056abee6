import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';

import '../../generated/l10n.dart';

class EmptyView extends StatelessWidget {
  const EmptyView({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: Get.height / 2,
      child: <PERSON><PERSON>(
        children: [
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.hourglass_empty,
                  size: 40,
                  color: MColor.xFF999999,
                ),
                const SizedBox(height: 10),
                Text(S.of(Get.context!).no_data, style: MFont.regular15.apply(color: Get.context!.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
