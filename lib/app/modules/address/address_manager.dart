import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/modules/store/setting_store.dart';
import 'package:inspector/app/tools/global_const.dart';

import '../../tools/storage_util.dart';

class AddressManager {
  static Map<String, dynamic> toCityMap() {
    bool isChinese = SettingStore.to.getCurrentLocale().languageCode == 'zh';
    Map<String, dynamic> country = <String, dynamic>{};
    for (var province in GlobalConst.chinaAreas) {
      List<String> cities = [];
      for (var city in province.cities) {
        cities.add(isChinese ? city.areaName : city.pinyin);
      }
      if (isChinese) {
        country[province.areaName] = cities;
      } else {
        country[province.pinyin] = cities;
      }
    }
    return country;
  }

  static Map<String, dynamic> toMapJson({bool? forceChinese}) {
    bool isChinese = SettingStore.to.getCurrentLocale().languageCode == 'zh';
    Map<String, dynamic> conunty = <String, dynamic>{};
    for (var province in GlobalConst.chinaAreas) {
      Map<String, dynamic> cities = <String, dynamic>{};
      for (var city in province.cities) {
        List<String> areas = [];
        if (city.areas != null) {
          city.areas?.forEach((area) {
            if (isChinese) {
              areas.add(area.areaName);
            } else {
              areas.add(area.pinyin);
            }
          });
        } else {
          if (isChinese) {
            areas.add('-');
          } else {
            areas.add('-');
          }
        }
        if (isChinese) {
          cities[city.areaName] = areas;
        } else {
          cities[city.pinyin] = areas;
        }
      }
      if (isChinese) {
        conunty[province.areaName] = cities;
      } else {
        conunty[province.pinyin] = cities;
      }
    }
    return conunty;
  }
}
