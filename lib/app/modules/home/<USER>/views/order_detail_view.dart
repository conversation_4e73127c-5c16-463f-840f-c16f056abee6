import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/ai/photo_selected/image_browse_page.dart';
import 'package:inspector/app/modules/home/<USER>/controllers/order_detail_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/app_theme.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';
import '../../../../../generated/l10n.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

class OrderDetailView extends GetView<OrderDetailController> {
  const OrderDetailView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        Get.back(result: controller.isChange);
        return Future.value(false);
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(S.of(Get.context!).order_detail_title),
          centerTitle: true,
          actions: [
            Obx(() {
              if (controller.detailEntity.value.canEdit ?? false) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    controller.detailEntity.value.orderId = controller.orderId;
                    Get.toNamed(Routes.ORDER_EDIT, arguments: {'editable': true, 'orderId': controller.orderId})?.then((value) {
                      controller.fetchOrderDetail();
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    alignment: Alignment.center,
                    child: Text(
                      S.of(Get.context!).edit,
                      style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                    ),
                  ),
                );
              }
              return Container();
            }),
          ],
        ),
        body: Stack(
          children: [
            SizedBox(
              child: Obx(() {
                return Column(
                  children: [
                    Expanded(
                      child: ListView(
                        children: [
                          _baseInfoView,
                          _relatedInfoView,
                          _otherInfoView,
                        ],
                      ),
                    ),
                    _bottomView,
                    const SizedBox(height: 30),
                  ],
                );
              }),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  controller.fetchImGroup();
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(color: MColor.skin, width: 2),
                    color: Colors.white.withOpacity(0.6),
                  ),
                  width: 40,
                  height: 40,
                  margin: const EdgeInsets.only(right: 16),
                  child: const Center(
                    child: Icon(
                      Icons.group_add_sharp,
                      size: 20,
                      color: MColor.skin,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget get _relatedInfoView {
    OrderDetailEntity model = controller.detailEntity.value;
    if (model.timeBeans != null && model.timeBeans!.isNotEmpty) {
      return Column(
        children: [
          _cardView([
            _titleView(Assets.productNote, S.of(Get.context!).order_detail_related_info),
            for (OrderDateEntity entity in model.timeBeans!) ...{
              _relatedDateView(entity),
            }
          ])
        ],
      );
    } else {
      return Container();
    }
  }

  Widget _relatedDateView(OrderDateEntity model, {MainAxisAlignment alignment = MainAxisAlignment.spaceEvenly}) {
    return Builder(builder: (context) {
      return Container(
          margin: const EdgeInsets.only(left: 12, right: 12, top: 10),
          child: Row(
            mainAxisAlignment: alignment,
            children: [
              Text(
                model.date ?? '-',
                style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              ),
              Container(color: context.isDarkMode ? DarkColor.xFFDDDDDD : MColor.xFFDDDDDD, width: 1, height: 24),
              Text(
                S.of(Get.context!).home_unit('${model.inspectNum ?? 0}', '1'),
                style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              ),
            ],
          ));
    });
  }

  Widget get _baseInfoView {
    OrderDetailEntity model = controller.detailEntity.value;
    var address = model.provinceCity ?? '';
    address += model.address ?? '';
    String priceText = '${model.userAccount == 2 ? '\$' : '¥'}${model.price}';

    // if (model.inspectors != null && model.inspectors!.isNotEmpty) {
    //   priceText = (model.inspectors!.first.accountType?.symbol ?? '') + (model.inspectors!.first.cost?.toStringAsFixed(2) ?? '');
    // }
    // var isSelf = model.isSelf ?? false;

    // 待验货 3  取消验货
    // 验货中（正在验货，待提交报告） 6  验货报告提交
    // 已完成  4    查看验货报告
    // var otherStatus = controller.detailEntity.value.status ?? 0;
    //可以打电话
    //var canCall = (otherStatus == 3 || otherStatus == 4 || otherStatus == 6) && !isSelf;
    //是否已申请验货
    // final isApply = controller.detailEntity.value.isApply ?? false;

    return Column(
      children: [
        _cardView(
          [
            _titleView(Assets.productNote, S.of(Get.context!).order_detail_inspection_info),
            _infoView(S.of(Get.context!).order_number, model.orderNo ?? ''),
            _infoView(S.of(Get.context!).order_create_time, model.createdTime ?? ''),
            _infoView(S.of(Get.context!).order_detail_inspection_time, model.inspectTime ?? ''),
            Visibility(
              visible: !controller.isApply,
              child: Row(
                children: [
                  Expanded(child: _infoView(S.of(Get.context!).order_detail_inspection_factory, model.factoryName ?? '')),
                ],
              ),
            ),
            _infoView(S.of(Get.context!).order_detail_inspection_address, address.replaceAll(' ', '').replaceAll('市辖区', '')),
            if (model.addressInfo?.phone?.isNotEmpty == true)
              _infoView(S.of(Get.context!).order_detail_inspection_phone, model.addressInfo?.phone ?? '', canAction: true, icon: Icons.call, action: () {
                launchUrlString('tel:${model.addressInfo?.phone}');
              }),
            Visibility(
              visible: !controller.isApply,
              child: _infoView(S.of(Get.context!).order_detail_inspection_person, model.name ?? ''),
            ),
            Visibility(
              visible: !controller.isApply,
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  if ((model.phone ?? '').isNotEmpty) {
                    launchUrlString('tel:${model.phone}');
                  }
                },
                child: _infoView(S.of(Get.context!).order_detail_inspection_phone, model.phone ?? '', canAction: true, action: () {
                  launchUrlString('tel:${model.phone}');
                }),
              ),
            ),
            Visibility(
              visible: !controller.isApply,
              child: _infoView(S.of(Get.context!).order_detail_inspection_email, model.email ?? ''),
            ),
            Visibility(
              visible: controller.isApply,
              child: _infoView(S.of(Get.context!).order_detail_inspection_day, '1${S.of(Get.context!).publish_day}'),
            ),
            Visibility(
              visible: controller.isApply,
              child: _infoView(S.of(Get.context!).order_detail_inspection_number, '${model.inspectNum ?? 0}${S.of(Get.context!).publish_people}'),
            ),
            if (model.supervisor != null && !controller.isApply)
              _infoView(S.of(Get.context!).supervisor, model.supervisor?.name ?? '', canAction: true, icon: Icons.chat, action: () {
                int? imUserId = controller.detailEntity.value.supervisor?.imUserId;
                if (imUserId != null) {
                  controller.createConv(imUserId);
                }
              }),
            if (model.salesmanInfo != null && model.salesmanInfo is Map<String, dynamic> && (model.salesmanInfo['name']?.isNotEmpty == true))
              _infoView(S.of(Get.context!).salesman, model.salesmanInfo['name']),
          ],
        ),
        Visibility(
          visible: !controller.isApply,
          child: _cardView(
            [
              _titleView(Assets.productFactory, S.of(Get.context!).order_detail_inspection_amount),
              _infoView(S.of(Get.context!).order_detail_inspection_day, '1${S.of(Get.context!).publish_day}'),
              _infoView(S.of(Get.context!).order_detail_inspection_number, '${model.inspectNum ?? 0}${S.of(Get.context!).publish_people}'),
              if (GlobalConst.userModel?.role != UserRole.customer) _infoView(S.of(Get.context!).order_detail_inspection_cost, priceText),
              _infoView(S.of(Get.context!).order_detail_inspection_type, Helper.parseOrderType(model.type ?? 0)),
            ],
          ),
        ),
        Visibility(
            visible: controller.detailEntity.value.orderProducts?.isNotEmpty ?? false,
            child: Obx(() {
              return _cardView([
                _titleView(Assets.productInfo,  S.of(Get.context!).order_goods_info,
                    other: GestureDetector(
                        onTap: () => controller.editProduct(),
                        child: Text(S.current.ai_add_product_new,
                            style: MFont.regular13.apply(
                              color: MColor.xFF0081E7,
                            )))),
                if (controller.detailEntity.value.orderProducts != null &&
                    controller.detailEntity.value.orderProducts!.isNotEmpty) ...{
                  _productListView(controller.detailEntity.value.orderProducts!)
                }
              ]);
            })),
        _cardView([
          _titleView(Assets.productTips, S.of(Get.context!).order_tips),
          Column(
            children: [
              if (model.sample != null) ...{
                _infoView(S.of(Get.context!).order_detail_inspection_sample, Helper.parseSampleType(model.sample ?? 0)),
              },
              if (model.standard != null) ...{
                _infoView(S.of(Get.context!).order_detail_inspection_standard, '${model.standard}'),
              },
              if (model.critical != null) ...{
                _infoView(S.of(Get.context!).order_detail_inspection_critical, '${model.critical}'),
              },
              if (model.major != null) ...{
                _infoView(S.of(Get.context!).order_detail_inspection_major, model.major ?? ''),
              },
              if (model.minor != null) ...{
                _infoView(S.of(Get.context!).order_detail_inspection_minor, model.minor ?? ''),
              },
              if (model.poNum != null) ...{
                _infoView('P.O', '${model.poNum}'),
              },
              if (model.remark != null) ...{
                _infoView(S.of(Get.context!).publish_attention, '${model.remark}'),
              },
            ],
          ),
        ]),
      ],
    );
  }

  Widget get _otherInfoView {
    OrderDetailEntity model = controller.detailEntity.value;

    return Builder(builder: (context) {
      return Column(
        children: [
          if (model.file.isNotEmpty) ...{
            _cardView([
              _titleView(Assets.productNote, S.of(Get.context!).order_detail_inspection_file),
              for (String url in model.file) ...{
                GestureDetector(
                  onTap: () {
                    launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
                  },
                  child: _fileView(url),
                ),
              },
            ]),
          },
          if (model.template.isNotEmpty) ...{
            _cardView([
              _titleView(Assets.productNote, S.of(Get.context!).order_detail_inspection_template),
              for (String url in model.template) ...{
                GestureDetector(
                  onTap: () {
                    launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
                  },
                  child: _fileView(url),
                ),
              },
            ]),
          },
        ],
      );
    });
  }

  Widget get _bottomView {
    return Obx(() {
      // 如果是自己下的订单
      if (controller.detailEntity.value.isSelf ?? false) {
        return _bottomButtonAsOwner();
      } else if (GlobalConst.userModel?.role == UserRole.inspector) {
        return _bottomButtonAsInspector();
      } else {
        return const SizedBox();
      }
    });
  }

  Widget _bottomButtonAsOwner() {
    if (controller.detailEntity.value.statusType == null) return const SizedBox();
    var statusType = controller.detailEntity.value.statusType;
    switch (statusType) {
      //订单已取消
      case 1:
      case 2:
      case 3:
      case 4:
        break;
      case 5:
        //待派单
        return Row(
          children: [
            Expanded(
                child: _textButton(S.of(Get.context!).order_cancel, action: () {
              controller.fetchCancel();
            })),
          ],
        );
      case 6:
        //订单结束
        return Row(
          children: [
            if ((controller.detailEntity.value.reportStatus ?? 0) != 0)
              Expanded(
                child: _textButton(S.of(Get.context!).order_look, action: () {
                  Get.toNamed(Routes.CHECK, arguments: {'orderId': controller.orderId, 'isInspector': false});
                }),
              ),
            if (controller.detailEntity.value.commentStatus == 1) //未评价
              Expanded(
                  child: _textButton(S.of(Get.context!).review_next, action: () {
                Get.toNamed(Routes.REVIEW, arguments: controller.orderId)?.then((value) {
                  controller.fetchOrderDetail();
                });
              })),
          ],
        );

      case 7:
        //验货中
        break;
      default:
        //待支付
        String inspectTime = controller.detailEntity.value.inspectTime ?? '';
        DateTime? inspDay = DateTime.tryParse(inspectTime);
        DateTime today = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
        if (inspDay != null && !today.isAfter(inspDay)) {
          return Row(
            children: [
              Expanded(child: _textButton(S.of(Get.context!).order_cancel, action: controller.fetchCancel)),
              Expanded(child: _textButton(S.of(Get.context!).order_need_pay, action: controller.payAction)),
            ],
          );
        }
    }
    return const SizedBox();
  }

  Widget _bottomButtonAsInspector() {
    //是否已申请验货
    var isApply = controller.detailEntity.value.isApply ?? false;
    var statusType = controller.detailEntity.value.statusType ?? -1;
    int insStatus = controller.detailEntity.value.insStatus ?? -1;
    if (statusType == 5) {
      //待派单，验货员还看不到这个订单，显示取消申请和修改申请
      if (isApply) {
        return Row(
          children: [
            Expanded(
              child: _textButton(S.of(Get.context!).home_update, action: () {
                controller.fetchPrice(controller.orderId, false);
              }),
            ),
            Expanded(
              child: _textButton(S.of(Get.context!).home_apply_cancel, action: () {
                controller.fetchPrice(controller.orderId, false);
              }),
            )
          ],
        );
      } else {
        return _textButton(S.of(Get.context!).order_apply, action: () {
          controller.fetchPrice(controller.orderId, true);
        });
      }
    } else {
      Widget nextButton = const SizedBox();
      logger.i('insStatus $insStatus');
      //todo 此处强行改为4 上线时需注释
      insStatus = 4;
      switch (insStatus) {
        //待派单
        case 1:
          nextButton = isApply
              ? _textButton(S.of(Get.context!).order_applying, action: () {
                  controller.fetchPrice(controller.orderId ?? 0, false);
                })
              : _textButton(S.of(Get.context!).order_apply, action: () {
                  controller.fetchPrice(controller.orderId ?? 0, true);
                });
          break;
        case 2:
          //派单确认
          nextButton = Row(
            children: [
              Expanded(
                  child: _textButton(S.of(Get.context!).order_dispatch_accept, action: () {
                showCustomDialog(S.of(Get.context!).order_sure_confirm, onConfirm: () {
                  controller.confirmOrder();
                }, cancel: true);
              })),
              Expanded(
                  child: _textButton(S.of(Get.context!).order_dispatch_refuse, action: () {
                showCustomDialog(S.of(Get.context!).order_sure_refuse, onConfirm: () {
                  controller.confirmRefuse();
                }, cancel: true);
              })),
            ],
          );
          break;
        //准备验货
        case 3:
          // => 开始验货
          break;
        //验货中
        case 4:
          // => 提交验货报告
          nextButton = _textButton(S.current.ai_category_inspector, action:controller.toAi);
          break;
        //已完成
        case 5:
          // => 验货报告
          nextButton = _textButton(S.of(Get.context!).order_look, action: () {
            Get.toNamed(Routes.CHECK, arguments: {'orderId': controller.orderId, 'isInspector': true});
          });
          break;
        case 8:
          nextButton = _textButton(S.of(Get.context!).order_inspection, action: controller.beginInspect);
          break;
      }

      return nextButton;
    }
  }

  Widget _cardView(List<Widget> itemViews) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(Get.context!).colorScheme.conatiner,
        borderRadius: BorderRadius.circular(6),
      ),
      margin: const EdgeInsets.only(left: 16, right: 16, top: 10),
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: itemViews,
      ),
    );
  }

  Widget _titleView(String icon, String text, {Widget? other}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      color: Theme.of(Get.context!).colorScheme.conatiner,
      child: Row(
        children: [
          Image.asset(
            icon,
            width: 20,
            height: 20,
          ),
          Expanded(
            child: Text(
              text,
              style: MFont.medium15,
            ),
          ),
          // if (other != null) other
        ],
      ),
    );
  }

  Widget _productView(OrderProduct orderProduct) {
    return Builder(builder: (context) {
      return Container(
        padding: const EdgeInsets.only(left: 12, right: 12, top: 4, bottom: 4),
        child: Column(
          children: [
            SizedBox(height: 8),
            Row(
              children: [
                SizedBox(
                  width: 80,
                  child: Text(
                    S.of(Get.context!).order_goods_name,
                    style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                  ),
                ),
                Expanded(
                  child: Text(
                    orderProduct.productName ?? '-',
                    style: MFont.regular13.apply(
                      color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666,
                      heightFactor: 1.2,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // GestureDetector(
                //     onTap: () => controller.editProduct(product: orderProduct),
                //     child: Text(S.of(context).edit,
                //         style: MFont.regular13.apply(
                //           color: MColor.xFF0081E7,
                //         )))
              ],
            ),
            if (orderProduct.productModel != null && orderProduct.productModel!.isNotEmpty) ...{
              const SizedBox(
                height: 4,
              ),
              Table(
                columnWidths: const {
                  0: FlexColumnWidth(2),
                  1: FlexColumnWidth(1),
                  2: FlexColumnWidth(1),
                  3: FlexColumnWidth(1),
                },
                textBaseline: TextBaseline.alphabetic,
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                border: TableBorder.all(color: MColor.xFFA2A2A2, width: 1),
                children: [
                  TableRow(
                    children: [
                      TableCell(
                          child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6.0),
                        child: Text(S.of(Get.context!).order_goods_model, textAlign: TextAlign.center, maxLines: 1, overflow: TextOverflow.ellipsis),
                      )),
                      TableCell(
                          child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6.0),
                        child: Text(S.of(Get.context!).order_goods_count, textAlign: TextAlign.center, maxLines: 1, overflow: TextOverflow.ellipsis),
                      )),
                      TableCell(
                          child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6.0),
                        child: Text(
                          S.of(Get.context!).order_goods_unit,
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )),
                      TableCell(
                          child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6.0),
                        child: Text(
                          S.current.ai_inspection_image,
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ))
                    ],
                  ),
                  for (ProductModel? productModel in orderProduct.productModel!) ...{
                    TableRow(
                      children: [
                        TableCell(
                            child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6.0),
                          child: Text(
                            productModel?.mark ?? '-',
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )),
                        TableCell(
                            child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6.0),
                          child: Text(
                            '${productModel?.amount ?? 0}',
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )),
                        TableCell(
                            child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6.0),
                          child: Text(
                            productModel?.unit ?? '-',
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )),
                        TableCell(
                            child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6.0),
                          child: (productModel?.pic?.isEmpty ?? true)
                              ? SizedBox.shrink()
                              : GestureDetector(
                                  onTap: () {
                                    ImageBrowsePage.pushBrowse(Get.context!, productModel.pic!);
                                  },
                                  child: CachedNetworkImage(
                                    imageUrl: productModel!.pic!,
                                    width: 20,
                                    height: 20,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                        )),
                      ],
                    )
                  },
                ],
              )
            },
          ],
        ),
      );
    });
  }

  Widget _productListView(List<OrderProduct> orderProduct) {
    return ListView.separated(
        scrollDirection: Axis.vertical,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return _productView(orderProduct[index]);
        },
        separatorBuilder: (context, index) {
          return const SizedBox();
        },
        itemCount: orderProduct.length);
  }

  Widget _infoView(String title, String value, {bool canAction = false, IconData icon = Icons.call, GestureTapCallback? action}) {
    return Builder(builder: (context) {
      return Container(
        margin: const EdgeInsets.only(top: 5, left: 17, right: 17),
        color: Theme.of(Get.context!).colorScheme.conatiner,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 80,
              child: Text(
                '$title:',
                style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              ),
            ),
            !canAction
                ? Expanded(
                    child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text(
                          value,
                          style: MFont.regular13.apply(
                              color: canAction
                                  ? MColor.xFF0081E7
                                  : context.isDarkMode
                                      ? DarkColor.xFF666666
                                      : MColor.xFF666666,
                              heightFactor: 1.2),
                        ),
                      ),
                      title != S.of(Get.context!).order_detail_inspection_address || value == ''
                          ? const SizedBox()
                          : GestureDetector(
                              onTap: () {
                                var model = controller.detailEntity.value;
                                var address = (model.provinceCity ?? '') + (model.address ?? '');

                                Location.share.toNavigation(
                                  controller.detailEntity.value.lat,
                                  controller.detailEntity.value.lon,
                                  address.replaceAll(' ', '').replaceAll('市辖区', ''),
                                );
                              },
                              child: const Icon(
                                Icons.location_on_sharp,
                                color: DefaultStyle.blue,
                                size: 15,
                              ),
                            )
                    ],
                  ))
                : Expanded(
                    child: Row(
                      children: [
                        Text(
                          value,
                          style: MFont.regular13.apply(color: MColor.xFF0081E7, heightFactor: 1.2),
                        ),
                        const SizedBox(width: 4),
                        !canAction || value == ''
                            ? const SizedBox()
                            : GestureDetector(
                                onTap: action,
                                child: Padding(padding: const EdgeInsets.only(bottom: 2), child: Icon(icon, color: Colors.blue, size: 15)),
                              ),
                      ],
                    ),
                  ),
          ],
        ),
      );
    });
  }

  Widget _fileView(String fileUrl) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 5, left: 17, right: 17),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: Text(fileUrl.split('/').last.replaceAll('}', ''), style: const TextStyle(color: DefaultStyle.blue)),
      ),
    );
  }

  Widget _textButton(String title, {@required VoidCallback? action, Color? color}) {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18),
      child: TextButton(
        onPressed: () {
          action!();
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(color ?? MColor.skin),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          title,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }
}
