import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/home/<USER>/controllers/record_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/app_theme.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../../generated/l10n.dart';

class RecordView extends GetView<RecordController> {
  const RecordView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).home_record),
        centerTitle: true,
      ),
      body: Obx(() {
        return SmartRefresher(
            controller: controller.refreshController,
            onRefresh: () => controller.refreshAction(),
            onLoading: () => controller.loadMore(),
            child: ListView.builder(
              itemCount: controller.dataList.isEmpty ? 1 : controller.dataList.length,
              itemBuilder: (ctx, index) {
                if (controller.dataList.isEmpty) {
                  return SizedBox(
                    width: double.infinity,
                    height: Get.height / 2,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.hourglass_empty,
                            size: 40,
                            color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                          ),
                          const SizedBox(height: 10),
                          Text(S.of(Get.context!).no_data, style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
                        ],
                      ),
                    ),
                  );
                }
                return _itemView(index);
              },
            ));
      }),
    );
  }

  Widget _itemView(int index) {
    var model = controller.dataList[index];
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.toNamed(Routes.LIST_DETAIL, arguments: model.id, parameters: {'isApply': '1'})?.then((value) {
          controller.fetchRecordList(false);
        });
      },
      child: Builder(builder: (context) {
        return Container(
          margin: const EdgeInsets.only(left: 12, right: 12, top: 10),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.conatiner,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.only(top: 16, left: 12),
                    child: Row(
                      children: [
                        Text(
                          Helper.parseChinaArea(model.provinceCity ?? '-'),
                          style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                        ),
                        const SizedBox(width: 4),
                        Offstage(
                          offstage: model.distance == null || model.distance!.isEmpty,
                          child: RichText(
                            text: TextSpan(
                              children: [
                                WidgetSpan(
                                  child: Icon(
                                    Icons.location_on_sharp,
                                    color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383,
                                    size: 16,
                                  ),
                                ),
                                TextSpan(
                                  text: S.of(Get.context!).home_navigation,
                                  style: MFont.regular11.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Offstage(
                          offstage: model.distance == null || model.distance!.isEmpty,
                          child: Text(
                            '${model.distance}',
                            style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Offstage(
                    offstage: model.award == null,
                    child: Container(
                      decoration: const BoxDecoration(
                        image: DecorationImage(image: AssetImage(Assets.rmbIcon)),
                      ),
                      width: 133,
                      height: 27,
                      child: Center(
                        child: Text(
                          S.of(Get.context!).home_recommend(model.award ?? ''),
                          style: MFont.medium12.apply(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Visibility(
                visible: model.address != null && model.address!.isNotEmpty,
                child: Container(
                  padding: const EdgeInsets.only(left: 12, right: 12, top: 8),
                  child: Text(
                    model.address ?? '',
                    style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 13, left: 12, right: 12),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                height: 44,
                color: context.isDarkMode ? DarkColor.xFFF7F8F9 : MColor.xFFF7F8F9,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: Text(
                        model.inspectionDate ?? '-',
                        style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(color: context.isDarkMode ? DarkColor.xFFDDDDDD : MColor.xFFDDDDDD, width: 1, height: 24),
                    Expanded(
                      child: Text(
                        Helper.parseOrderType(model.type ?? 0),
                        style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(color: context.isDarkMode ? DarkColor.xFFDDDDDD : MColor.xFFDDDDDD, width: 1, height: 24),
                    Expanded(
                      child: Text(
                        S.of(Get.context!).home_unit('${model.inspNumber ?? 0}', '${model.inspDay ?? 0}'),
                        style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.only(left: 12, right: 12, top: 6, bottom: 2),
                child: Row(
                  children: [
                    Text(
                      S.of(Get.context!).home_product_tip,
                      style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                    ),
                    Expanded(
                      child: Text(
                        model.productName ?? '-',
                        style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 4),
                child: Row(
                  children: [
                    Text(
                      '${model.applyNum ?? 0}${S.of(Get.context!).home_person_apply}',
                      style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                    ),
                    const Spacer(),
                    UnconstrainedBox(
                      child: TextButton(
                        onPressed: () {
                          if (model.applyStatus == 0 || model.applyStatus == 1) {
                            controller.fetchPrice(model.id ?? 0, false);
                          }
                        },
                        style: ButtonStyle(
                          shape: MaterialStateProperty.all(const StadiumBorder()),
                          side: MaterialStateProperty.all(const BorderSide(color: MColor.skin, width: 1)),
                          visualDensity: VisualDensity.compact,
                          padding: MaterialStateProperty.all(const EdgeInsets.symmetric(horizontal: 24, vertical: 5)),
                        ),
                        child: Text(
                          Helper.parseApplyStatus(model.applyStatus ?? 0),
                          style: MFont.medium14.apply(color: MColor.skin),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        );
      }),
    );
  }
}
