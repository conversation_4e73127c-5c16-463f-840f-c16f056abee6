import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/widgets/inspection/inspection_mixin.dart';
import 'package:inspector/app/widgets/search_filter/filter_panel_controller.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/data/order_list_entity.dart';
import 'package:inspector/app/modules/home/<USER>';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/tools/extensions.dart';

import '../../../tools/storage_util.dart';

class HomeListController extends GetxController with WidgetsBindingObserver, InspectionMixin {
  RefreshController refreshController = RefreshController();
  HomeProvider provider = HomeProvider();
  TextEditingController searchEditController = TextEditingController();
  FocusNode searchFocusNode = FocusNode();

  final sortBy = 0.obs; //0 按订单日期排序 1 按距离排序 2 按验货日期排序
  int page = 1;
  final listEntity = <OrderListRows>[].obs;

  RxBool hasPermission = false.obs;

  RxBool isLocating = false.obs;
  RxBool isReqLocationPermission = false.obs;

  bool leaveWithPermission = true;
  final selectedStatus = RxList<OrderStateFilter>();
  final selectedAreas = RxList<AreaFilter>();

  final orderDate = DateFilter('OrderRange').obs;
  final inspDate = DateFilter('InspRange').obs;

  final searchQuery = RxString('');
  final showClear = false.obs;

  HomeListController() {
    logger.i('HomeListController created ');
  }

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    fetchOrderList(false);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (sortBy.value == 1) {
      if (state == AppLifecycleState.resumed) {
        if (leaveWithPermission) {
          checkPermission();
        } else {
          fetchOrderIfPermissionAllowed();
        }
      } else if (state == AppLifecycleState.paused) {
        leaveWithPermission = hasPermission.value;
      }
    }
  }

  int getFilterCount() {
    int count = (orderDate.value.fromDate == null && orderDate.value.toDate == null) ? 0 : 1;
    count += (inspDate.value.fromDate == null && inspDate.value.toDate == null) ? 0 : 1;
    count += selectedAreas.length;
    count += selectedStatus.length;
    return count;
  }

  void requestPermission() {
    logger.i('HomeList requestPermission');
    Geolocator.checkPermission().then((value) {
      logger.i('HomeList checkPermission after ${value.index} $value');
      if (value == LocationPermission.denied) {
        hasPermission.value = false;
        Location.share.position = null;
        Geolocator.requestPermission().then((value) {
          logger.i('HomeList requestPermission after ${value.index} $value');
          if (value == LocationPermission.denied) {
            Location.share.position = null;
            hasPermission.value = false;
          } else if (value == LocationPermission.deniedForever) {
            Location.share.position = null;
            hasPermission.value = false;
            Geolocator.openAppSettings().then((value) {});
          } else {
            _getCurrentPosition();
          }
        });
      } else if (value == LocationPermission.deniedForever) {
        hasPermission.value = false;
        Location.share.position = null;
        Geolocator.openAppSettings().then((value) {});
      } else {
        _getCurrentPosition();
      }
    });
  }

  void _getCurrentPosition() {
    hasPermission.value = true;
    isLocating.value = true;
    logger.i('HomeList _getCurrentPosition');
    if (Location.share.position == null) {
      Geolocator.getLastKnownPosition().then((value) {
        logger.i('HomeList getLastKnownPosition end $value');
        if (value != null) {
          Location.share.position = value;
        }
      }).catchError((err) {
        logger.i('HomeList getLastKnownPosition error $err');
      });
    }
    Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.best).then(
      (value) {
        logger.i('HomeList _getCurrentPosition end $value');
        Location.share.position = value;
        unawaited(StorageUtil.setDouble(Constant.lat, value.latitude));
        unawaited(StorageUtil.setDouble(Constant.lon, value.longitude));
        unawaited(PublicProvider.userGps(value.latitude, value.longitude));
        isLocating.value = false;
        fetchOrderList(false);
      },
    ).catchError((err) {
      logger.i('HomeList _getCurrentPosition error $err');
    });
  }

  void fetchOrderIfPermissionAllowed() {
    logger.i('HomeList fetchOrderIfPermissionAllowed');
    Geolocator.checkPermission().then((value) {
      hasPermission.value = (value != LocationPermission.denied && value != LocationPermission.deniedForever);
      logger.i('HomeList fetchOrderIfPermissionAllowed after ${hasPermission.value} $value');
      if (hasPermission.value) {
        if (Location.share.position == null) {
          isLocating.value = true;
          Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.best, timeLimit: const Duration(seconds: 30)).then(
            (value) {
              logger.i('HomeList fetchOrderIfPermissionAllowed no position then get $value');
              Location.share.position = value;
              unawaited(StorageUtil.setDouble(Constant.lat, value.latitude));
              unawaited(StorageUtil.setDouble(Constant.lon, value.longitude));
              unawaited(PublicProvider.userGps(value.latitude, value.longitude));
              isLocating.value = false;
              fetchOrderList(false);
            },
          );
        } else {
          logger.i('HomeList fetchOrderIfPermissionAllowed has position then fetch');
          fetchOrderList(false);
        }
      }
    });
  }

  void checkPermission() {
    logger.i('HomeList checkPermission');
    Geolocator.checkPermission().then((value) {
      hasPermission.value = (value != LocationPermission.denied && value != LocationPermission.deniedForever);
    });
  }

  void refreshAction() {
    fetchOrderList(false);
  }

  void refreshWithPermissionCheck() {
    if (sortBy.value != 1) {
      refreshAction();
    } else {
      fetchOrderIfPermissionAllowed();
    }
  }

  void loadMore() {
    page++;
    fetchOrderList(true);
  }

  void updateApply(int id, bool isApply) {
    listEntity.value = listEntity.map((element) {
      if (element.id == id) {
        element.applyStatus = isApply ? 1 : 0;
        var applyNum = element.applyNum ?? 0;
        element.applyNum = isApply ? applyNum + 1 : applyNum - 1;
      }
      return element;
    }).toList();
    listEntity.refresh();
  }

  void fetchOrderList(bool isMore) {
    logger.i('fetchOrderList $sortBy $hashCode');
    var lat = StorageUtil.getDouble(Constant.lat);
    var lon = StorageUtil.getDouble(Constant.lon);

    EasyLoading.show();
    var total = 0;
    if (!isMore) {
      page = 1;
    }
    List<int> status = [];
    List<String> areas = [];
    if (selectedStatus.isNotEmpty) {
      for (var element in selectedStatus) {
        status.add(element.status);
      }
    }
    if (selectedAreas.isNotEmpty) {
      for (var element in selectedAreas) {
        areas.add(element.city);
      }
    }
    provider
        .takeOrderList(
      sortBy.value,
      page: page,
      lat: lat,
      lon: lon,
      sortBy: 0,
      search: searchQuery.value,
      filterStatus: status,
      filterAreas: areas,
      filterOrderDateFrom: orderDate.value.fromDate?.millisecondsSinceEpoch,
      filterOrderDateTo: orderDate.value.toDate?.millisecondsSinceEpoch,
      filterInspDateFrom: inspDate.value.fromDate?.millisecondsSinceEpoch,
      filterInspDateTo: inspDate.value.toDate?.millisecondsSinceEpoch,
    )
        .then((value) async {
      if (value.isSuccess) {
        var models = value.data?.rows ?? [];
        total = value.data?.total ?? 0;
        if (!isMore) {
          listEntity.value = [];
        }
        var tempList = <OrderListRows>[];
        tempList.addAll(listEntity);
        tempList.addAll(models);
        connectRows(tempList);
        listEntity.clear();
        listEntity.addAll(tempList);
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
      refreshController.refreshCompleted();
      if (listEntity.isEmpty || total == listEntity.length) {
        refreshController.loadNoData();
      } else {
        refreshController.loadComplete();
      }
    });
  }

  void connectRows(List<OrderListRows> models) {
    OrderListRows? prev;
    for (int i = 0; i < models.length; i++) {
      OrderListRows current = models[i];
      if (prev == null) {
        current.connectLast = false;
      } else {
        int prevId = prev.id!;
        List<int> childrenIds = [];
        current.children?.forEach((element) {
          if (element.id != null) {
            childrenIds.add(element.id!);
          }
        });
        if (childrenIds.contains(prevId)) {
          prev.connectNext = true;
          current.connectLast = true;
        }
      }
      prev = current;
    }
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    refreshController.dispose();
    searchEditController.dispose();
    searchFocusNode.dispose();
  }

  void updateFilters(List<OrderStateFilter> status, List<AreaFilter> area, DateFilter order, DateFilter insp) {
    selectedStatus.clear();
    selectedStatus.addAll(status);
    selectedAreas.clear();
    selectedAreas.addAll(area);
    orderDate.value.fromDate = order.fromDate;
    orderDate.value.toDate = order.toDate;
    inspDate.value.fromDate = insp.fromDate;
    inspDate.value.toDate = insp.toDate;
    orderDate.refresh();
    inspDate.refresh();

    fetchOrderList(false);
  }

  void updateSort() {
    fetchOrderList(false);
  }
}
