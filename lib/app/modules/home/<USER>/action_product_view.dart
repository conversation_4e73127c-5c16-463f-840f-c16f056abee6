import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/modules/ai/photo_selected/image_browse_page.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/modules/home/<USER>/action_product_controller.dart';
import 'package:inspector/generated/l10n.dart';

class ActionProductView extends GetView<ActionProductController> {
  const ActionProductView({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissOnTap(
      child: Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          title: Text(S.of(context).ai_add_product_edit_product),
          centerTitle: true,
          actions: controller.hasProduct ? [deleteButton()] : null,
        ),
        body: ListenableBuilder(
            listenable: controller,
            builder: (context, child) {
              return Column(
                children: [
                  Expanded(
                    child: ListView(children: [
                      Padding(
                        padding: EdgeInsets.fromLTRB(15, 20, 15, 10),
                        child:
                            Text(S.of(context).ai_add_product_product_name, style: MFont.medium12),
                      ),
                      buildProductView(),
                      if (controller.product?.productModel?.isNotEmpty ?? false)
                        Padding(
                          padding: EdgeInsets.fromLTRB(15, 20, 15, 10),
                          child: Text(S.of(context).ai_add_product_model, style: MFont.medium12),
                        ),
                      for (ProductModel m in (controller.product?.productModel ?? [])) modelTile(m),
                    ]),
                  ),
                  bottomButton(),
                ],
              );
            }),
      ),
    );
  }

  BorderSide get side => BorderSide(color: MColor.xFFE5E5E5);

  Widget buildProductView() {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 0, 15, 0),
      decoration: BoxDecoration(
          color: MColor.white,
          border: Border(
            top: side,
            bottom: side,
          )),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: TextField(
              controller: TextEditingController(
                text: controller.product?.productName,
              ),
              readOnly: true,
              onTap: () {
                showCustomDialog(S.current.ai_add_product_product_name,
                    textController: controller.editingController,
                    textPlaceHolder: S.current.ai_add_product_input_product_name,
                    cancel: true,
                    onConfirm: controller.inputProductNameDone);
              },
              style: MFont.medium18,
              // inputFormatters: [LengthLimitingTextInputFormatter(100)],
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: S.current.ai_add_product_input_product_name,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          chevron(),
        ],
      ),
    );
  }

  Widget chevron() {
    return Icon(CupertinoIcons.chevron_forward, size: 16);
  }

  Widget bottomButton() {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18),
      margin: EdgeInsets.only(bottom: Get.bottomBarHeight != 0 ? Get.bottomBarHeight : 20),
      child: TextButton(
        onPressed: () => controller.editModel(),
        style: ButtonStyle(
          backgroundColor: WidgetStatePropertyAll(MColor.skin),
          shape: WidgetStatePropertyAll(const StadiumBorder()),
          minimumSize: WidgetStatePropertyAll(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: WidgetStatePropertyAll(const Size(double.infinity, 49)),
        ),
        child: Text(
          S.current.ai_add_product_new_model,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }

  Widget deleteButton() {
    return IconButton(
        onPressed: deleteProduct,
        icon: Text(
          S.current.ai_delete,
          style: MFont.medium14.copyWith(
            color: MColor.skin,
          ),
        ));
  }

  //
  void deleteProduct() {
    showCustomDialog(S.current.ai_add_product_ask_product,
        cancel: true, onConfirm: controller.deleteProduct);
  }

  Widget modelTile(ProductModel model) {
    return GestureDetector(
      onTap: () => controller.editModel(model: model),
      child: Container(
        padding: EdgeInsets.fromLTRB(15, 10, 15, 10),
        height: 82,
        decoration: BoxDecoration(
            color: MColor.white,
            border: Border(
              top: side,
              bottom: side,
            )),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (model.pic?.isNotEmpty ?? false)
              GestureDetector(
                onTap: () {
                  ImageBrowsePage.pushBrowse(Get.context!, model.pic!);
                },
                child: CachedNetworkImage(
                  imageUrl: model.pic!,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                ),
              )
            else
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                    border: Border.all(color: MColor.xFFE5E5E5),
                    borderRadius: BorderRadius.circular(8)),
                child: Center(
                  child: Text(
                    S.current.ai_add_product_picture_lost,
                    style: MFont.medium12,
                  ),
                ),
              ),
            SizedBox(width: 20),
            Expanded(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  model.mark ?? '',
                  style: MFont.medium16,
                ),
                SizedBox(
                  height: 10,
                ),
                Text(
                  '${model.amount ?? ''}${model.unit ?? ''}',
                  // style: MFont.medium14.copyWith(color: MColor.xFFA6A6A6),
                ),
              ],
            )),
            chevron(),
          ],
        ),
      ),
    );
  }
}
