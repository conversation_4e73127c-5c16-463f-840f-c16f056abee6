import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../generated/l10n.dart';

class SupplementView extends GetView {
  const SupplementView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.back();
      },
      child: Center(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 38),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 15),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 98),
                child: Image.asset(Assets.alertPerson),
              ),
              const SizedBox(height: 10),
              Text(
                S.of(Get.context!).supplement_title,
                textAlign: TextAlign.center,
                style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
              ),
              const SizedBox(height: 35),
              Container(
                padding: const EdgeInsets.only(left: 30, right: 30, bottom: 15),
                width: double.infinity,
                child: TextButton(
                  style: ButtonStyle(
                    backgroundColor: MaterialStateProperty.all(MColor.skin),
                    shape: MaterialStateProperty.all(const StadiumBorder()),
                  ),
                  onPressed: () {
                    Get.back();
                    Get.toNamed(Routes.PROFILE);
                  },
                  child: Text(
                    S.of(Get.context!).supplement_next,
                    style: MFont.medium16.apply(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
