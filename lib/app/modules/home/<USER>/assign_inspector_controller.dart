import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:get/get_instance/src/bindings_interface.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_ticket_provider_mixin.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:inspector/app/data/assign_info.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/modules/home/<USER>/assign_service.dart';
import 'package:inspector/app/modules/message/chat_provider.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class AssignInspectorController extends GetxController {
  final service = AssignService();

  final orderInfo = Rxn<OrderInfoEntity>();
  final assignInfos = RxList.empty();

  RefreshController refreshController = RefreshController();
  TextEditingController searchController = TextEditingController();

  int? orderId;
  int? totalCount = 0;

  final assignedCount = 0.obs;

  int pageIndex = 1;
  String? query = '';

  final assignAccountType = 0.obs;

  final isSelectionMode = false.obs;

  final RxList<int> selectedImUserIds = RxList();

  Rx<AssignType> assignType = AssignType.AssignTypeAll.obs;

  AssignInspectorController() {
    logger.i('AssignInspectorController ${this.hashCode}');
  }

  @override
  void onInit() {
    super.onInit();
    orderId = Get.arguments['order_id'];
    if (orderId == null || orderId == 0) {
      Get.back();
    } else {
      fetchInspectorList(false);
    }
  }

  void fetchInspectorList(bool isLoadMore) {
    EasyLoading.show();
    service.getInspectorList(orderId!, pageIndex, 10, assignType.value, query).then((value) {
      if (value.isSuccess) {
        AssignResp? resp = value.data;
        if (resp != null) {
          if (!isLoadMore) {
            totalCount = resp.total;
            assignedCount.value = resp.assignCount ?? 0;
            assignInfos.value = [];
          }
          orderInfo.value = resp.orderInfo;
          if (resp.assignInfos?.isNotEmpty ?? false) {
            assignInfos.addAll(resp.assignInfos!);
          }
        }
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
      refreshController.refreshCompleted();
      if (assignInfos.isEmpty || totalCount == assignInfos.length) {
        refreshController.loadNoData();
      } else {
        refreshController.loadComplete();
      }
    });
  }

  void onRefresh() {
    pageIndex = 1;
    fetchInspectorList(false);
  }

  void onLoadMore() {
    pageIndex++;
    fetchInspectorList(true);
  }

  @override
  void onClose() {
    refreshController.dispose();
  }

  void assign(OrderInfoEntity orderInfo, AssignInfoEntity assignInfo, String cost) {
    EasyLoading.show();
    service.assignInspector(orderId!, assignInfo.inspector!.uid!, cost, '${assignAccountType.value}').then((value) {
      if (value.isSuccess) {
        assignInfo.hasAssigned = true;
        assignedCount.value = assignedCount.value + 1;
        assignInfos.refresh();
      } else {
        if (value.message?.isNotEmpty ?? false) {
          showToast(value.message!);
        }
      }
    }).catchError((error) {
      showToast(error.toString());
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void cancelAssign(AssignInfoEntity assignInfo) {
    EasyLoading.show();
    service.cancelAssign(orderId!, assignInfo.inspector!.uid!).then((value) {
      if (value.isSuccess) {
        assignInfo.hasAssigned = false;
        assignInfo.orderInspector = null;
        assignedCount.value = assignedCount.value - 1;
        assignInfos.refresh();
      } else {
        if (value.message?.isNotEmpty ?? false) {
          showToast(value.message!);
        }
      }
    }).catchError((error) {
      showToast(error.toString());
    }).whenComplete(() => EasyLoading.dismiss());
  }

  void onSelectionChanged(int? imUserId) {
    if (imUserId != null) {
      if (selectedImUserIds.contains(imUserId)) {
        selectedImUserIds.remove(imUserId);
      } else {
        selectedImUserIds.add(imUserId);
      }
    }
  }

  void onSelectionModeChanged() {
    isSelectionMode.value = !isSelectionMode.value;
    if (!isSelectionMode.value) {
      selectedImUserIds.clear();
    }
  }

  void sendMessage(int? uid) {
    if (uid != null) {
      ChatProvider.batchSendTemplateMessage([uid], 1, orderId);
      showToast('发送消息成功');
    }
  }

  void batchSendMessage() {
    ChatProvider.batchSendTemplateMessage(selectedImUserIds, 1, orderId).then((value) {
      if (value.isSuccess) {
        showToast('已成功给${selectedImUserIds.length}人发送消息成功');
      } else {
        if (value.message?.isNotEmpty ?? false) {
          showToast(value.message!);
        }
      }
    }).whenComplete(() {
      onSelectionModeChanged();
    });
  }
}
