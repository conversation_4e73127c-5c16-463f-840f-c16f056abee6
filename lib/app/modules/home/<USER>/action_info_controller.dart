import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:getwidget/components/toast/gf_toast.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/generated/l10n.dart';

import '../../../data/order_detail_entity.dart';
import '../../../data/public_model.dart';
import '../../../tools/tools.dart';

class ActionInfoController extends GetxController {
  OrderProvider provider = OrderProvider();

  OrderProduct? product;
  late int orderId;
  late final bool hasProduct;
    RxBool isEdit = false.obs;
  late TextEditingController editingController =
      TextEditingController(text: product?.productName ?? '');

  FocusNode focusNode = FocusNode();
  @override
  void onInit() {
    orderId = Get.arguments['orderId'];
    product = Get.arguments['product'];
    hasProduct = product != null;
    super.onInit();
  }


  String unit = '';
  ProductModel? model;
  late ProductModel inputModel =
  model != null ? ProductModel.fromJson(model!.toJson()) : ProductModel();


  void changeUnit(units){
    if(product!.productModel!=null){
      unit =units;
      for(var item in product!.productModel!){

        item.unit = units;
      }
      _digui(product!.productModel![0],0);
    }
  }

  void _digui(model,index){
    provider
        .editModel(
        modelId: model!.id!,
        productId: product!.id!,
        mark: model!.mark ?? '',
        amount: model!.amount ?? 0,
        unit: model!.unit ?? '',
        picUrl: model!.pic ?? '')
        .then(
          (value) {
            if(index<product!.productModel!.length-1){
              index++;
              _digui(product!.productModel![index],index);
            }
        if (value.isSuccess) {

        }
      },
    );
  }

  void checkData(context){
    if(product?.categoryId==0){
      showToast(S.of(context).publish_inspection_time_tip+S.current.ai_default_config_classify );
      return;
    }
    if(product!.productModel![0]!.unit!.isEmpty){
      showToast(S.of(context).publish_inspection_time_tip+S.current.ai_product_unit );
      return;
    }
    if(product!.productModel!=null){
      for(var item in product!.productModel!){
        item.unit = unit;
      }
    }

  }

  void addRow(){
    if(product!.productModel!=null){
      product!.productModel!.add(ProductModel());
      update();
    }

  }

   deleteModel2(id,index)  {
    if(id==null){
      product!.productModel!.removeAt(index);
      update();
    }else{
      provider.deleteModel(modelId: id).then(
            (value) {
          if (value.isSuccess) {
            product!.productModel!.removeAt(index);
            update();
            // Get.back(result: id);
          }
        },
      );
    }

  }

  final unitList = [
    S.current.ai_model_unit_piece,
    S.current.ai_model_unit_only,
    S.current.ai_model_unit_pair,
    S.current.ai_model_unit_set,
    S.current.ai_model_unit_dozen,
    S.current.ai_model_unit_roll,
    S.current.ai_model_unit_vehicle,
    S.current.ai_model_unit_head,
    S.current.ai_model_unit_bag,
    S.current.ai_model_unit_box,
    S.current.ai_model_unit_pack,
    S.current.ai_model_unit_yard,
    S.current.ai_model_unit_meter,
    S.current.ai_model_unit_kilogram,
    S.current.ai_model_unit_metric_ton,
    S.current.ai_model_unit_liter,
    S.current.ai_model_unit_gallon,
    S.current.ai_model_unit_other
  ];

  void inputProductNameDone(name) {
    provider.editProduct(productId: product!.id!, productName: name).then(
      (value) {
        if (value.isSuccess) {
          refresh();
        }
      },
    );
  }

  void deleteProduct() {
    provider.deleteProduct(productId: product!.id!, productName: editingController.text).then(
      (value) {
        if (value.isSuccess) {
          Get.back();
        }
      },
    );
  }


  OrderProvider orderProvider = OrderProvider();
  Future<void> loadData() async{
    BaseModel<OrderDetailEntity> result =  await orderProvider.takeOrderDetail(orderId);
    if(result.isSuccess && result.data != null){
        // productList = result.data?.orderProducts??[];
        for(var item in result!.data!.orderProducts!){
          if(item.id == product?.id){
            product = item;
            break;
          }
        }
        update();
    }else{
      showToast('${result.message}');
    }
  }

  void editModelData(isNew,model) {
    if (isNew) {//添加
      provider
          .addModel(
          orderId: orderId,
          productId: product!.id!,
          mark:  '',
          amount: 0,
          unit:unit,
          picUrl: '')
          .then(
            (value) {
          if (value.isSuccess) {
            // addRow();
            loadData();
          }else{
            showToast('${value.message}');
          }
        },
      );
    } else {//编辑
      provider
          .editModel(
          modelId: model!.id!,
          productId: product!.id!,
          mark: model!.mark ?? '',
          amount: model!.amount ?? 0,
          unit: model!.unit ?? '',
          picUrl: model!.pic ?? '')
          .then(
            (value) {
          if (value.isSuccess) {

          }else{
            showToast('${value.message}');
          }
        },
      );
    }
  }



  Future<void> editModel({ProductModel? model}) async {
    final result = await Get.toNamed(Routes.ADD_MODEL,
        arguments: {'model': model, 'orderProductId': product!.id, 'orderId': orderId});
    if (result == null) {
      refresh();
      return;
    }
    //返回删除的id
    if (result is int) {
      product!.productModel!.removeWhere((element) => element.id == result);
      refresh();
      return;
    }
    //返回的是model
    if (model == null) {
      product!.productModel ??= [];
      product!.productModel!.insert(0, result);
    } else {
      final index = product!.productModel!.indexOf(model);
      product!.productModel![index] = result;
    }
    refresh();
  }
}
