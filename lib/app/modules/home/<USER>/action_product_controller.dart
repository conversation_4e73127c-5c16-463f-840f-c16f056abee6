import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/routes/app_pages.dart';

class ActionProductController extends GetxController {
  OrderProvider provider = OrderProvider();

  OrderProduct? product;
  late int orderId;
  late final bool hasProduct;
  late TextEditingController editingController =
      TextEditingController(text: product?.productName ?? '');
  @override
  void onInit() {
    orderId = Get.arguments['orderId'];
    product = Get.arguments['product'];
    print('productproduct'+product.toString());
    hasProduct = product != null;
    super.onInit();
  }

  void inputProductNameDone() {
    provider.editProduct(productId: product!.id!, productName: editingController.text).then(
      (value) {
        if (value.isSuccess) {
          product!.productName = editingController.text;
          refresh();
        }
      },
    );
  }

  void deleteProduct() {
    provider.deleteProduct(productId: product!.id!, productName: editingController.text).then(
      (value) {
        if (value.isSuccess) {
          Get.back();
        }
      },
    );
  }

  Future<void> editModel({ProductModel? model}) async {
    final result = await Get.toNamed(Routes.ADD_MODEL,
        arguments: {'model': model, 'orderProductId': product!.id, 'orderId': orderId});
    if (result == null) {
      refresh();
      return;
    }
    //返回删除的id
    if (result is int) {
      product!.productModel!.removeWhere((element) => element.id == result);
      refresh();
      return;
    }
    //返回的是model
    if (model == null) {
      product!.productModel ??= [];
      product!.productModel!.insert(0, result);
    } else {
      final index = product!.productModel!.indexOf(model);
      product!.productModel![index] = result;
    }
    refresh();
  }
}
