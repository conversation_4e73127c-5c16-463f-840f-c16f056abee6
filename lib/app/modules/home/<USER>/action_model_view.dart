import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/widgets/upload/UploadWidget.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/modules/home/<USER>/action_model_controller.dart';
import 'package:inspector/generated/l10n.dart';

class ActionModelView extends GetView<ActionModelController> {
  const ActionModelView({super.key});
  final double rightPadding = 20.0;

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissOnTap(
      dismissOnCapturedTaps: false,
      child: PopScope(
        canPop: false,
        onPopInvoked: (didPop) async {
          if (didPop) return;
          bool need = false;
          await showCustomDialog(
            S.of(context).ai_add_product_ask_save,
            okTitle: S.of(context).ai_add_product_save,
            cancel: true,
            onConfirm: () => need = true,
            onCancel: () => need = false,
          );
          if (need) {
            controller.editModel();
          } else {
            Get.back();
          }
        },
        child: Scaffold(
          backgroundColor: MColor.backgroundColor,
          appBar: AppBar(
            title: Text(S.of(context).ai_add_product_edit_model),
            centerTitle: true,
            actions: [deleteButton()],
          ),
          body: Column(
            children: [
              Expanded(
                child: ListView(
                  keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                  children: [
                    SizedBox(height: 20),
                    buildImageWidget(context),
                    buildNameWidget(),
                    buildNumWidget(),
                    buildUnitWidget(),
                  ],
                ),
              ),
              bottomButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildImageWidget(BuildContext context) {
    return Obx(() {
      return buildRow(
          name: S.of(context).ai_inspection_image,
          expanded: false,
          child: GestureDetector(
            onTap: () {
              UploadWidget.showCuportionDialog(context, (url) {
                controller.imageUrl.value = url;
                controller.imageUrl.refresh();
                controller.inputModel.pic = url;
              }, UploadType.plain, false, selectCount: 1);
            },
            child: Padding(
              padding: EdgeInsets.only(right: rightPadding),
              child: DottedBorder(
                borderType: BorderType.RRect,
                color: Get.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC,
                radius: const Radius.circular(6),
                child: controller.imageUrl.value.isEmpty
                    ? Center(
                        child: Icon(
                          Icons.add,
                          size: 48,
                          color: context.isDarkMode ? DarkColor.xFFABABAC : MColor.xFFABABAC,
                        ),
                      )
                    : CachedNetworkImage(
                        imageUrl: controller.imageUrl.value,
                        fit: BoxFit.cover,
                        width: 48,
                        height: 48,
                      ),
              ),
            ),
          ));
    });
  }

  Widget buildNameWidget() {
    return buildRow(
        name: S.current.ai_add_product_model_name,
        child: SizedBox(
          width: 200,
          child: TextField(
            controller: TextEditingController(text: controller.inputModel.mark ?? ''),
            textAlign: TextAlign.right,
            style: MFont.medium14,
            onChanged: (value) => controller.inputModel.mark = value,
            // inputFormatters: [LengthLimitingTextInputFormatter(100)],
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: S.current.ai_add_product_input_model,
              contentPadding: EdgeInsets.only(right: rightPadding),
            ),
          ),
        ));
  }

  Widget buildNumWidget() {
    return buildRow(
        name: S.current.ai_add_product_num,
        child: SizedBox(
          width: 200,
          child: TextField(
            controller: TextEditingController(text: controller.inputModel.amount?.toString() ?? ''),
            textAlign: TextAlign.right,
            style: MFont.medium14,
            keyboardType: TextInputType.number,
            onChanged: (value) => controller.inputModel.amount = int.tryParse(value),
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp('[0-9]'))],
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: S.current.ai_add_product_input_num,
              contentPadding: EdgeInsets.only(right: rightPadding),
            ),
          ),
        ));
  }

  Widget buildUnitWidget() {
    return buildRow(
        name: S.current.ai_add_product_unit,
        child: StatefulBuilder(builder: (context, setState) {
          return PopupMenuButton<String>(
            color: MColor.xFFFAFAFA,
            initialValue: controller.inputModel.unit,
            constraints: BoxConstraints(maxHeight: 240),
            onSelected: (value) {
              setState(() {
                controller.inputModel.unit = value;
              });
            },
            itemBuilder: (context) {
              return controller.unitList
                  .map((e) => PopupMenuItem(
                        value: e,
                        child: Text(e),
                      ))
                  .toList();
            },
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    controller.inputModel.unit ?? '',
                    textAlign: TextAlign.end,
                    style: MFont.medium14,
                  ),
                ),
                SizedBox(width: 8),
                chevron(),
                SizedBox(width: rightPadding)
              ],
            ),
          );
        }));
  }

  Widget buildRow({required String name, required child, bool expanded = true}) {
    BorderSide side = BorderSide(color: MColor.xFFE5E5E5);

    return Container(
      padding: EdgeInsets.fromLTRB(15, 10, 0, 10),
      decoration: BoxDecoration(
          color: MColor.white,
          border: Border(
            top: side,
            // bottom: side,
          )),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            name,
            style: MFont.medium16,
          ),
          Spacer(),
          if (expanded) Expanded(child: child) else child
        ],
      ),
    );
  }

  Widget chevron() {
    return Icon(CupertinoIcons.chevron_forward, size: 16);
  }

  Widget deleteButton() {
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: IconButton(
          onPressed: deleteModel,
          icon: Text(
            S.current.ai_delete,
            style: MFont.medium14.copyWith(
              color: MColor.skin,
            ),
          )),
    );
  }

  //
  void deleteModel() {
    showCustomDialog(S.current.ai_add_product_ask_delete,
        cancel: true, onConfirm: controller.deleteModel);
  }

  Widget bottomButton() {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18),
      margin: EdgeInsets.only(bottom: Get.bottomBarHeight != 0 ? Get.bottomBarHeight : 20),
      child: TextButton(
        onPressed: () {
          if ((controller.inputModel.amount ?? 0) <= 0) {
            EasyLoading.showError(
              S.current.ai_add_model_count_warning,
              duration: Duration(milliseconds: 500),
            );
            return;
          }
          controller.editModel();
        },
        style: ButtonStyle(
          backgroundColor: WidgetStatePropertyAll(MColor.skin),
          shape: WidgetStatePropertyAll(const StadiumBorder()),
          minimumSize: WidgetStatePropertyAll(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: WidgetStatePropertyAll(const Size(double.infinity, 49)),
        ),
        child: Text(
          S.current.ai_complete,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }
}
