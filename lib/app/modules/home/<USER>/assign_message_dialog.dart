import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/assign_info.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/modules/home/<USER>/assign_inspector_controller.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/widgets/price_input_widget.dart';

import '../../../../generated/l10n.dart';

class AssignBottomView extends GetView<AssignInspectorController> {
  final OrderInfoEntity orderInfo;
  final AssignInfoEntity assignInfo;
  @override
  final String? tag;

  double assignCost = 0.0;

  final TextEditingController priceController = TextEditingController();
  FocusNode focusNode = FocusNode();

  AssignBottomView(this.orderInfo, this.assignInfo, {Key? key, this.tag}) : super(key: key) {
    Future.delayed(const Duration(milliseconds: 300), () {
      FocusScope.of(Get.context!).requestFocus(focusNode);
    });
  }

  @override
  Widget build(BuildContext context) {
    assignCost = double.tryParse(assignInfo.cost ?? '0') ?? 0.0;
    priceController.text = assignCost.toStringAsFixed(2);
    return Scaffold(
      backgroundColor: Colors.transparent,
      bottomSheet: Container(
        decoration: const BoxDecoration(color: MColor.xFFFFFFFF),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              height: 12,
            ),
            _titleView,
            const SizedBox(
              height: 8,
            ),
            _orderInfo,
            const SizedBox(height: 18),
            Row(
              children: [
                const SizedBox(width: 10),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      if (assignCost > 0) {
                        controller.assign(orderInfo, assignInfo, assignCost.toStringAsFixed(2));
                        Get.back();
                      } else {
                        showToast(S.of(Get.context!).assign_price_zero_tips);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      decoration:
                          BoxDecoration(gradient: const LinearGradient(colors: [MColor.skin, MColor.xFFFB6668]), borderRadius: BorderRadius.circular(24)),
                      child: Center(
                        child: Text(
                          S.of(Get.context!).assign.capitalize!,
                          style: MFont.medium16.apply(color: Colors.white),
                          strutStyle: const StrutStyle(
                            forceStrutHeight: true,
                            leading: 0.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
              ],
            ),
            const SizedBox(height: 25),
          ],
        ),
      ),
    );
  }

  Widget get _titleView {
    return SizedBox(height: 36, child: Center(child: Text(S.of(Get.context!).assign_inspector, style: MFont.semi_Bold16.apply(color: MColor.skin))));
  }

  Widget get _orderInfo {
    String productNames = '';
    orderInfo.orderProducts?.forEach((element) {
      if (productNames.isEmpty) {
        productNames = '${element.productName}';
      } else {
        productNames = '$productNames, ${element.productName}';
      }
    });
    return Builder(builder: (context) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                    child: Text(
                  '${S.of(Get.context!).tab_order} ID：${orderInfo.ordersNum}',
                  style: MFont.medium14.apply(
                    color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                  ),
                )),
              ],
            ),
            if (productNames.isNotEmpty) ...{
              const SizedBox(
                height: 8,
              ),
              Row(
                children: [
                  Expanded(
                      child: Text(
                    '${S.of(Get.context!).order_detail_inspection_product}: $productNames',
                    style: MFont.medium14.apply(
                      color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                    ),
                  )),
                ],
              ),
            },
            const SizedBox(
              height: 8,
            ),
            Row(
              children: [
                Expanded(
                    child: Text(
                  '${S.of(Get.context!).order_detail_inspection_address}：${orderInfo.city}',
                  style: MFont.medium14.apply(
                    color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                  ),
                )),
              ],
            ),
            const SizedBox(
              height: 8,
            ),
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${S.of(Get.context!).order_detail_inspection_type}：${Helper.parseOrderType(orderInfo.type)}',
                    style: MFont.medium14.apply(
                      color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 8,
            ),
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${S.of(Get.context!).date_title}：${orderInfo.inspectTime}',
                    style: MFont.medium14.apply(
                      color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 8,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${S.of(Get.context!).order_detail_inspection_cost}: ',
                  style: MFont.medium14.apply(
                    color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
                SizedBox(
                  width: 90,
                  child: TextField(
                      style: MFont.medium14.apply(color: MColor.xFFE95332),
                      controller: priceController,
                      textAlign: TextAlign.start,
                      showCursor: true,
                      readOnly: true,
                      keyboardType: TextInputType.number,
                      focusNode: focusNode,
                      // inputFormatters: [FilteringTextInputFormatter(RegExp('^([1-9]\\d{0,9}|0)(\\.\\d{1,2})?\$'), allow: true)],
                      decoration: InputDecoration(
                        filled: false,
                        isDense: true,
                        prefix: GestureDetector(
                            onTap: () {
                              if (controller.assignAccountType.value == 1) {
                                controller.assignAccountType.value = 2;
                              } else {
                                controller.assignAccountType.value = 1;
                              }
                            },
                            child: Text(
                              controller.assignAccountType.value == 1 ? '¥' : '\$',
                              style: MFont.medium14.apply(color: MColor.xFFE95332),
                            )),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                        fillColor: Get.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
                        constraints: const BoxConstraints(maxHeight: 30, minHeight: 20),
                        border: const UnderlineInputBorder(),
                      ),
                      onTap: () {
                        Get.bottomSheet(
                          SafeArea(
                            // and this
                            child: PriceInputWidget(
                                assignCost!.toStringAsFixed(2), S.of(Get.context!).order_detail_inspection_cost, controller.assignAccountType.value == 2, 7),
                          ),
                          isScrollControlled: true,
                          ignoreSafeArea: false, // add this
                        ).then((value) {
                          if (value is Map) {
                            controller.assignAccountType.value = value['isUSD'] ? 2 : 1;
                            String priceStr = value['price'];
                            assignCost = double.tryParse(priceStr) ?? 0;
                            priceController.value = TextEditingValue(
                                text: assignCost!.toStringAsFixed(2),
                                selection:
                                    TextSelection.fromPosition(TextPosition(affinity: TextAffinity.downstream, offset: assignCost!.toStringAsFixed(2).length)));
                          }
                        });
                      },
                      onChanged: (text) {
                        logger.i('price changed $text');
                        // controller.priceType.value = 2;
                      }),
                ),
              ],
            )
          ],
        ),
      );
    });
  }
}
