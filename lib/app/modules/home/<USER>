import 'package:inspector/app/config/api.dart';
import 'package:inspector/app/data/order_list_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/tools/public_provider.dart';

class HomeProvider {
  //获取验货列表
  Future<BaseModel<OrderListEntity>> takeOrderList(int orderType,
      {int page = 0,
      int limit = 10,
      double? lat,
      double? lon,
      int? sortBy,
      String? search,
      List<int>? filterStatus,
      List<String>? filterAreas,
      int? filterOrderDateFrom,
      int? filterOrderDateTo,
      int? filterInspDateFrom,
      int? filterInspDateTo}) {
    return PublicProvider.request<OrderListEntity>(path: Api.orderList, params: {
      'orderType': orderType,
      'page': page,
      'limit': limit,
      'lat': lat,
      'lon': lon,
      'sort_by': sortBy,
      'search': search,
      'filter_status': filterStatus,
      'filter_area': filterAreas,
      'filter_order_date_from': filterOrderDateFrom == null ? null : filterOrderDateFrom ~/ 1000,
      'filter_order_date_to': filterOrderDateTo == null ? null : filterOrderDateTo ~/ 1000,
      'filter_insp_date_from': filterInspDateFrom == null ? null : filterInspDateFrom ~/ 1000,
      'filter_insp_date_to': filterInspDateTo == null ? null : filterInspDateTo ~/ 1000,
    });
  }

  //获取标准价格
  Future<BaseModel<dynamic>> takePrice(int id) {
    return PublicProvider.request<dynamic>(path: '${Api.standPrice}/$id', isPost: false);
  }

  //申请
  Future<BaseModel<dynamic>> takeApply(int orderId, String price, int accountType, String note) {
    return PublicProvider.request<dynamic>(path: Api.orderApply, params: {'orderId': orderId, 'price': price, 'accountType': accountType, 'mark': note});
  }

  //获取申请列表
  Future<BaseModel<OrderListEntity>> takeRecordList({int page = 0, int limit = 10}) {
    return PublicProvider.request<OrderListEntity>(path: Api.applyList, params: {'page': page, 'limit': limit});
  }

  Future<BaseModel<AdminNoteResp>> getOrderNotes(int page, int pageSize, int orderId) {
    return PublicProvider.request(path: '${Api.orderNotes}?page=$page&page_size=$pageSize&order_id=$orderId', isPost: false);
  }

  Future<BaseModel<dynamic>> addOrderNote(int orderId, String note) {
    return PublicProvider.request(path: Api.addAdminNote, isPost: true, params: {'order_id': orderId, 'note': note});
  }
}
