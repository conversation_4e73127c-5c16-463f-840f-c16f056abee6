import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';

import 'package:get/get.dart';
import 'package:inspector/app/data/order_list_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/home/<USER>/assign_inspector_binding.dart';
import 'package:inspector/app/modules/home/<USER>/assign_inspector_view.dart';
import 'package:inspector/app/modules/home/<USER>/admin_note_view.dart';
import 'package:inspector/app/modules/inspect/views/inspect_list_item_view.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/widgets/search_filter/search_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/home/<USER>/home_list_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/app_theme.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../generated/l10n.dart';

class HomeListView extends GetView<HomeListController> {
  const HomeListView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // if (controller.tabIndex == 1) {
    //   controller.checkPermission();
    // }
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).home_title),
        centerTitle: true,
        bottom: const SearchFilterWidget(),
      ),
      body: SafeArea(
        child: Container(
          color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
          child: Obx(() {
            bool showPermisson = controller.sortBy.value == 1 && !controller.hasPermission.value;
            if (showPermisson) {
              return _permission();
            } else {
              return _refreshWidget();
            }
          }),
        ),
      ),
    );
  }

  Widget _permission() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(S.of(Get.context!).enable_location_service, style: MFont.bold20.apply(color: Get.context!.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000)),
          const SizedBox(height: 12),
          Text(S.of(Get.context!).enable_location_service_tips,
              style: MFont.regular15.apply(color: Get.context!.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
          const SizedBox(height: 12),
          TextButton(
            style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all(Get.context!.isDarkMode ? DarkColor.skin : MColor.skin),
              shape: MaterialStateProperty.all(const StadiumBorder()),
              visualDensity: VisualDensity.comfortable,
            ),
            onPressed: () {
              controller.requestPermission();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                S.of(Get.context!).enable_permission_goto_setting,
                style: MFont.medium15.apply(color: Get.context!.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _refreshWidget() {
    return SmartRefresher(
      controller: controller.refreshController,
      onRefresh: () => controller.refreshAction(),
      onLoading: () => controller.loadMore(),
      enablePullUp: true,
      child: ListView.builder(
        itemCount: controller.listEntity.isEmpty ? 1 : controller.listEntity.length,
        itemBuilder: (ctx, index) {
          if (controller.listEntity.isEmpty) {
            return SizedBox(
              width: double.infinity,
              height: Get.height / 2,
              child: Stack(
                children: [
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.hourglass_empty,
                          size: 40,
                          color: Get.context!.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                        ),
                        const SizedBox(height: 10),
                        Obx(
                          () {
                            bool isLocating = controller.isLocating.value;
                            return Text(isLocating ? S.of(Get.context!).msg_locating : S.of(Get.context!).no_data,
                                style: MFont.regular15.apply(color: Get.context!.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666));
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }
          return _cardView(index);
        },
      ),
    );
  }

  Widget _cardView(int index) {
    var model = controller.listEntity[index];
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        InspectListItemView(
          controller.listEntity[index],
          onItemTap: () {
            Get.toNamed(Routes.LIST_DETAIL, arguments: model.id, parameters: {'isApply': '1'})?.then((value) {
              if (value) {
                controller.refreshWithPermissionCheck();
              }
            });
          },
          onApplyTap: () {
            if (model.applyStatus == 0 || model.applyStatus == 1) {
              var first = true;
              if (model.applyStatus == 1) {
                first = false;
              }
              controller.fetchPrice(model.id ?? 0, first);
            }
          },
        ),
        if (model.connectLast) ...{
          SizedOverflowBox(
            alignment: Alignment.topCenter,
            size: const Size(72, 72),
            child: Image.asset(Assets.link),
          ),
        },
        if (model.connectNext) ...{
          Positioned(
            bottom: 0,
            child: SizedOverflowBox(
              alignment: Alignment.bottomCenter,
              size: const Size(72, 72),
              child: Image.asset(Assets.link),
            ),
          ),
        }
      ],
    );
  }
}
