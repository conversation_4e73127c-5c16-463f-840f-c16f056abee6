import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/order_list_entity.dart';
import 'package:inspector/app/modules/home/<USER>';
import 'package:inspector/app/tools/tools.dart';

class AdminNoteController extends GetxController {
  final scrollController = ScrollController();

  final TextEditingController txtController = TextEditingController();
  final FocusNode inputFocusNode = FocusNode();

  final isShowLoading = false.obs;

  final adminNotes = RxList<AdminNote>([]);

  final _service = HomeProvider();

  int total = 0;

  int page = 1;
  int orderId = 0;

  @override
  void onInit() {
    super.onInit();
    orderId = Get.arguments['order_id'];
    if (orderId == 0) {
      Get.back();
      return;
    }
    _initViews();
    getNoteList();
  }

  void _initViews() {
    scrollController.addListener(() {
      double offset = scrollController.offset;
      if (total != 0 && total > (adminNotes.length) && scrollController.position.pixels == scrollController.position.maxScrollExtent) {
        loadMore(); // 当滑到最底部时调用
      }
    });
  }

  Future<void> loadMore() async {
    isShowLoading.value = true;
    page++;
    var results = await _service.getOrderNotes(page, 10, orderId);
    isShowLoading.value = false;
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        // var reply = resp.list[0];
        total = resp.total;
        // replyInfo.value = reply;
        if (resp.list.isNotEmpty) {
          adminNotes.addAll(resp.list);
        }
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
    scrollController.dispose();
  }

  Future<void> getNoteList() async {
    unawaited(EasyLoading.show());
    page = 1;
    var results = await _service.getOrderNotes(page, 10, orderId);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        adminNotes.clear();
        total = resp.total;
        if (resp.list.isNotEmpty) {
          adminNotes.addAll(resp.list);
        }
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  void addAdminNote(String note) async {
    unawaited(EasyLoading.show());
    var results = await _service.addOrderNote(orderId, note);
    if (results.isSuccess) {
      txtController.text = '';
      await getNoteList();
    }
    unawaited(EasyLoading.dismiss());
  }
}
