import 'package:get/get.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/generated/l10n.dart';

class ActionModelController extends GetxController {
  OrderProvider provider = OrderProvider();

  ProductModel? model;
  late ProductModel inputModel =
      model != null ? ProductModel.fromJson(model!.toJson()) : ProductModel();

  late int orderId;
  late int orderProductId;

  late final bool isNew;

  final imageUrl = ''.obs;
  final unitList = [
  S.current.ai_model_unit_piece,
  S.current.ai_model_unit_only,
  S.current.ai_model_unit_pair,
  S.current.ai_model_unit_set,
  S.current.ai_model_unit_dozen,
  S.current.ai_model_unit_roll,
  S.current.ai_model_unit_vehicle,
  S.current.ai_model_unit_head,
  S.current.ai_model_unit_bag,
  S.current.ai_model_unit_box,
  S.current.ai_model_unit_pack,
  S.current.ai_model_unit_yard,
  S.current.ai_model_unit_meter,
  S.current.ai_model_unit_kilogram,
  S.current.ai_model_unit_metric_ton,
  S.current.ai_model_unit_liter,
  S.current.ai_model_unit_gallon,
  S.current.ai_model_unit_other
  ];
  @override
  void onInit() {
    model = Get.arguments['model'];
    orderId = Get.arguments['orderId'];
    orderProductId = Get.arguments['orderProductId'];
    isNew = model == null;
    imageUrl.value = model?.pic ?? '';

    super.onInit();
  }

  void deleteModel() {
    provider.deleteModel(modelId: model!.id!).then(
      (value) {
        if (value.isSuccess) {
          Get.back(result: model!.id);
        }
      },
    );
  }

  void editModel() {
    model = inputModel;
    if (isNew) {//添加
      provider
          .addModel(
              orderId: orderId,
              productId: orderProductId,
              mark: model!.mark ?? '',
              amount: model!.amount ?? 0,
              unit: model!.unit ?? '',
              picUrl: model!.pic ?? '')
          .then(
        (value) {
          if (value.isSuccess) {
            Get.until((route) => route.settings.name == Routes.LIST_DETAIL);
          }
        },
      );
    } else {//编辑
      provider
          .editModel(
              modelId: model!.id!,
              productId: orderProductId,
              mark: model!.mark ?? '',
              amount: model!.amount ?? 0,
              unit: model!.unit ?? '',
              picUrl: model!.pic ?? '')
          .then(
        (value) {
          if (value.isSuccess) {
            Get.back(result: model);
          }
        },
      );
    }
  }
}
