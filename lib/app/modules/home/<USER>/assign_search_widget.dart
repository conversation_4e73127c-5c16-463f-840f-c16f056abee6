import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/home/<USER>/assign_filter_panel.dart';
import 'package:inspector/app/modules/home/<USER>/assign_inspector_controller.dart';
import 'package:inspector/app/modules/home/<USER>/assign_service.dart';

import '../../../../generated/l10n.dart';

class AssignSearchWidget extends GetView<AssignInspectorController> {
  @override
  final String? tag;

  const AssignSearchWidget({Key? key, this.tag}) : super(key: key);

  @override
  AssignInspectorController get controller {
    return Get.find<AssignInspectorController>(tag: tag);
  }

  @override
  Widget build(BuildContext context) {
    Widget input = Builder(builder: (context) {
      return Column(
        children: [
          TextField(
            controller: controller.searchController,
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.only(left: 15),
              suffixIcon: GestureDetector(
                onTap: () {
                  controller.query = controller.searchController.text.trim();
                  controller.onRefresh();
                },
                child: Icon(
                  Icons.search,
                  size: 24,
                  color: context.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000,
                ),
              ),
              fillColor: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
              filled: true,
              isDense: true,
              hintText: S.of(Get.context!).assign_search_hint,
              hintStyle: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
              labelStyle: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000, width: 1.5),
                borderRadius: BorderRadius.circular(20),
              ),
              focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000, width: 1.5),
                  borderRadius: BorderRadius.circular(20)),
              constraints: const BoxConstraints(maxHeight: 40),
            ),
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.search,
            onSubmitted: (value) {
              controller.query = value.trim();
              controller.onRefresh();
            },
            onChanged: (val) {},
          ),
          SizedBox(
              height: 40,
              child: ListView.separated(
                itemCount: AssignType.values.length,
                scrollDirection: Axis.horizontal,
                separatorBuilder: (BuildContext context, int position) {
                  return const SizedBox(
                    width: 12,
                  );
                },
                itemBuilder: (BuildContext context, int position) {
                  if (position == 0) {
                    return GestureDetector(onTap: () {
                      if (controller.assignType.value != AssignType.AssignTypeAll) {
                        controller.assignType.value = AssignType.AssignTypeAll;
                        controller.onRefresh();
                      }
                    }, child: Obx(() {
                      bool isSelected = controller.assignType.value == AssignType.AssignTypeAll;
                      return Center(
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: isSelected ? MColor.skin : Colors.white,
                              style: BorderStyle.solid,
                            ),
                            color: Colors.white,
                            borderRadius: const BorderRadius.all(Radius.circular(24)),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.search_outlined,
                                size: 14,
                              ),
                              const SizedBox(
                                width: 4,
                              ),
                              Text(S.of(Get.context!).general_all.capitalize!),
                            ],
                          ),
                        ),
                      );
                    }));
                  } else if (position == 1) {
                    return GestureDetector(onTap: () {
                      if (controller.assignType.value != AssignType.AssignTypeAssigned) {
                        controller.assignType.value = AssignType.AssignTypeAssigned;
                        controller.onRefresh();
                      }
                    }, child: Obx(() {
                      var isSelected = controller.assignType.value == AssignType.AssignTypeAssigned;
                      return Center(
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: isSelected ? MColor.skin : Colors.white,
                              style: BorderStyle.solid,
                            ),
                            color: Colors.white,
                            borderRadius: const BorderRadius.all(Radius.circular(24)),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.search_outlined,
                                size: 14,
                              ),
                              const SizedBox(
                                width: 4,
                              ),
                              Text(S.of(Get.context!).assigned.capitalize!),
                            ],
                          ),
                        ),
                      );
                    }));
                  } else if (position == 2) {
                    return GestureDetector(onTap: () {
                      if (controller.assignType.value != AssignType.AssignTypeUnassigned) {
                        controller.assignType.value = AssignType.AssignTypeUnassigned;
                        controller.onRefresh();
                      }
                    }, child: Obx(() {
                      var isSelected = controller.assignType.value == AssignType.AssignTypeUnassigned;
                      return Center(
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: isSelected ? MColor.skin : Colors.white,
                              style: BorderStyle.solid,
                            ),
                            color: Colors.white,
                            borderRadius: const BorderRadius.all(Radius.circular(24)),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.search_outlined,
                                size: 14,
                              ),
                              const SizedBox(
                                width: 4,
                              ),
                              Text(S.of(Get.context!).unassigned.capitalize!),
                            ],
                          ),
                        ),
                      );
                    }));
                  } else if (position == 3) {
                    return GestureDetector(onTap: () {
                      if (controller.assignType.value != AssignType.AssignTypeApplied) {
                        controller.assignType.value = AssignType.AssignTypeApplied;
                        controller.onRefresh();
                      }
                    }, child: Obx(() {
                      var isSelected = controller.assignType.value == AssignType.AssignTypeApplied;
                      return Center(
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: isSelected ? MColor.skin : Colors.white,
                              style: BorderStyle.solid,
                            ),
                            color: Colors.white,
                            borderRadius: const BorderRadius.all(Radius.circular(24)),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.search_outlined,
                                size: 14,
                              ),
                              const SizedBox(
                                width: 4,
                              ),
                              Text(S.of(Get.context!).assign_applied.capitalize!),
                            ],
                          ),
                        ),
                      );
                    }));
                  } else {
                    return const SizedBox();
                  }
                },
              ))
        ],
      );
    });

    return Container(
      margin: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 6.0),
      child: input,
    );
  }
}
