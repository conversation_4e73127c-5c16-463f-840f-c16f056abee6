import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/assign_info.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/modules/home/<USER>/assign_inspector_binding.dart';
import 'package:inspector/app/modules/home/<USER>/assign_inspector_controller.dart';
import 'package:inspector/app/modules/home/<USER>/assign_message_dialog.dart';
import 'package:inspector/app/modules/home/<USER>/assign_search_widget.dart';
import 'package:inspector/app/modules/home/<USER>/assign_service.dart';
import 'package:inspector/app/modules/profile/user_profile/user_profile_binding.dart';
import 'package:inspector/app/modules/profile/user_profile/user_profile_view.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:rotated_corner_decoration/rotated_corner_decoration.dart';

import '../../../../generated/l10n.dart';

class AssignInspectorView extends GetView<AssignInspectorController> {
  @override
  final String? tag;
  const AssignInspectorView({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(S.of(Get.context!).assign_inspector),
        centerTitle: true,
        actions: [
          GestureDetector(
              onTap: () {
                controller.onSelectionModeChanged();
              },
              child: Center(
                  child: Container(
                padding: const EdgeInsets.all(12),
                alignment: Alignment.center,
                child: Obx(
                  () => Text(controller.isSelectionMode.value ? S.of(Get.context!).public_cancel.capitalize! : S.of(Get.context!).general_select.capitalize!),
                ),
              )))
        ],
      ),
      backgroundColor: MColor.xFFF2F2F2,
      body: Obx(() {
        return WillPopScope(
          onWillPop: () async {
            if (controller.isSelectionMode.value) {
              controller.onSelectionModeChanged();
              return false;
            }
            return true;
          },
          child: Column(
            children: [
              Visibility(
                visible: !controller.isSelectionMode.value,
                child: AssignSearchWidget(
                  tag: tag,
                ),
              ),
              Expanded(
                child: SmartRefresher(
                  controller: controller.refreshController,
                  onRefresh: () => controller.onRefresh(),
                  onLoading: () => controller.onLoadMore(),
                  enablePullDown: false,
                  enablePullUp: true,
                  child: ListView.builder(
                    itemCount: controller.assignInfos.isNotEmpty ? controller.assignInfos.length : 1,
                    itemBuilder: (ctx, index) {
                      if (controller.assignInfos.isEmpty) {
                        return SizedBox(
                          width: double.infinity,
                          height: Get.height / 2,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.hourglass_empty,
                                  size: 40,
                                  color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                                ),
                                const SizedBox(height: 10),
                                Text(S.of(Get.context!).no_data,
                                    style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
                              ],
                            ),
                          ),
                        );
                      }
                      return Obx(() {
                        return _assignmentItem(index);
                      });
                    },
                  ),
                ),
              ),
              if (controller.isSelectionMode.value) ...{
                Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Obx(() {
                      int selectedCount = controller.selectedImUserIds.length;
                      return Container(
                        padding: const EdgeInsets.only(left: 22, right: 22, top: 18),
                        child: TextButton(
                          onPressed: () {
                            if (selectedCount != 0) {
                              controller.batchSendMessage();
                            }
                          },
                          style: ButtonStyle(
                            backgroundColor: selectedCount == 0 ? const MaterialStatePropertyAll(MColor.xFF999999) : MaterialStateProperty.all(MColor.skin),
                            shape: MaterialStateProperty.all(const StadiumBorder()),
                            minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
                            visualDensity: VisualDensity.compact,
                            maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
                          ),
                          child: Text(
                            '${S.of(Get.context!).assign_leave_message_batch}($selectedCount)',
                            style: MFont.medium18.apply(color: Colors.white),
                          ),
                        ),
                      );
                    }))
              }
            ],
          ),
        );
      }),
    );
  }

  Widget _assignmentItem(int index) {
    AssignInfoEntity? assignInfo = controller.assignInfos[index];
    if (assignInfo == null) {
      return const SizedBox();
    }
    UserInfoEntity? userInfo = assignInfo.inspector;
    if (userInfo == null) {
      return const SizedBox();
    }

    bool hasForbidden = assignInfo.inspector?.is_auth == 0;
    bool hasAssigned = assignInfo.hasAssigned || assignInfo.orderInspector?.id != null;
    bool hasApplied = assignInfo.applyRecord != null && assignInfo.applyRecord!.id != null;
    var cornerColor = hasForbidden
        ? MColor.failed
        : hasAssigned
            ? MColor.xFF1BA12B
            : hasApplied
                ? MColor.xFFEA6A46
                : MColor.xFFDF8D14;

    String? statusLabel;
    if (hasForbidden) {
      statusLabel = S.of(Get.context!).is_auth_forbidden;
    } else if (hasAssigned) {
      statusLabel = S.of(Get.context!).assigned;
    } else if (hasApplied) {
      statusLabel = S.of(Get.context!).assign_applied;
    } else {
      statusLabel = S.of(Get.context!).unassigned;
    }

    bool selected = controller.selectedImUserIds.contains(userInfo.imUserId);
    return Builder(builder: (context) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          if (controller.isSelectionMode.value) {
            controller.onSelectionChanged(userInfo.imUserId);
          } else {
            Get.to(() => UserProfileView(tag: userInfo.uid.toString()),
                arguments: {'userId': userInfo.uid, 'isAdmin': false, 'name': userInfo.nick, 'head': userInfo.head, 'imUserId': userInfo.imUserId},
                binding: UserProfileBinding(tag: userInfo.uid.toString()));
          }
        },
        child: Card(
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
          child: Container(
            foregroundDecoration: statusLabel != null
                ? RotatedCornerDecoration.withColor(
                    color: cornerColor,
                    spanBaselineShift: 0,
                    badgeSize: const Size(48, 48),
                    badgeCornerRadius: const Radius.circular(6),
                    badgePosition: BadgePosition.topStart,
                    textSpan: TextSpan(
                      text: statusLabel,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        letterSpacing: 1,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                : null,
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _headView(userInfo),
                    Expanded(child: _infoView(assignInfo)),
                    const SizedBox(
                      width: 8,
                    ),
                    if (controller.isSelectionMode.value) ...{
                      Icon(selected ? Icons.check_box : Icons.check_box_outline_blank),
                    } else ...{
                      SizedBox(width: 70, child: _assignStatus(assignInfo))
                    },
                    const SizedBox(
                      width: 8,
                    )
                  ],
                ),
                if (assignInfo.applyRecord != null && assignInfo.applyRecord!.createAt != null) ...{
                  Row(
                    children: [
                      Expanded(
                          child: Text(
                        '${S.of(Get.context!).apply_time} ${assignInfo.applyRecord!.createAt ?? '-'}',
                        style: MFont.medium12.apply(
                          color: Get.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383,
                        ),
                        textAlign: TextAlign.end,
                        overflow: TextOverflow.ellipsis,
                      )),
                      const SizedBox(
                        width: 12,
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                },
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _infoView(AssignInfoEntity assignInfo) {
    UserInfoEntity user = assignInfo.inspector!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        const SizedBox(
          height: 14,
        ),
        Text(
          user.nick ?? '-',
          style: MFont.semi_Bold16.apply(
            color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        if (assignInfo.distance != null) ...{
          const SizedBox(
            height: 4,
          ),
          Row(
            children: [
              Expanded(
                  child: Text(
                '${S.of(Get.context!).public_distance}: ${assignInfo.distance!.toStringAsFixed(2)} km',
                style: MFont.medium14.apply(
                  color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                ),
                overflow: TextOverflow.ellipsis,
              )),
            ],
          ),
        },
        if (user.city != null && user.city!.isNotEmpty) ...{
          const SizedBox(
            height: 4,
          ),
          Row(
            children: [
              Expanded(
                  child: Text(
                '${S.of(Get.context!).profile_city}: ${user.city ?? '-'}',
                style: MFont.medium14.apply(
                  color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                ),
                overflow: TextOverflow.ellipsis,
              ))
            ],
          ),
        },
        if (assignInfo.inspectCount != null) ...{
          const SizedBox(
            height: 4,
          ),
          Row(
            children: [
              Expanded(
                  child: Text(
                '${S.of(Get.context!).assign_inspect_times}: ${assignInfo.inspectCount}',
                style: MFont.medium14.apply(
                  color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                ),
                overflow: TextOverflow.ellipsis,
              ))
            ],
          )
        },
        if (assignInfo.applyRecord != null && assignInfo.applyRecord!.id != null) ...{
          const SizedBox(
            height: 4,
          ),
          Row(
            children: [
              Expanded(
                  child: Text(
                '${S.of(Get.context!).assign_message}: ${assignInfo.applyRecord!.mark ?? '-'}',
                style: MFont.medium14.apply(
                  color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                ),
                overflow: TextOverflow.ellipsis,
              ))
            ],
          )
        },
        const SizedBox(
          height: 14,
        ),
      ],
    );
  }

  Widget _assignStatus(AssignInfoEntity assignInfo) {
    bool hasForbidden = assignInfo.inspector?.is_auth == 0;
    bool hasAssigned = assignInfo.hasAssigned || assignInfo.orderInspector?.id != null;
    bool hasApplied = assignInfo.applyRecord != null && assignInfo.applyRecord!.id != null;
    var color = hasAssigned
        ? MColor.xFF1BA12B
        : hasApplied
            ? MColor.xFFEA6A46
            : MColor.xFFDF8D14;
    //已申请
    if (hasForbidden || controller.orderInfo.value == null) {
      return const SizedBox();
    }
    return Column(
      children: [
        TextButton(
          onPressed: () {
            if (!hasAssigned) {
              controller.assignAccountType.value = assignInfo.account ?? 1;
              Get.bottomSheet(
                  SafeArea(
                    // and this
                    child: AssignBottomView(controller.orderInfo.value!, assignInfo, tag: tag),
                  ),
                  isScrollControlled: true,
                  ignoreSafeArea: false, // add this
                  backgroundColor: Colors.transparent);
            } else {
              showCustomDialog(
                S.of(Get.context!).assign_cancel_message,
                onConfirm: () async {
                  controller.cancelAssign(assignInfo);
                },
                cancel: true,
              );
            }
          },
          style: ButtonStyle(
            shape: MaterialStateProperty.all(const StadiumBorder()),
            side: MaterialStateProperty.all(BorderSide(color: color, width: 1)),
            visualDensity: VisualDensity.compact,
            padding: MaterialStateProperty.all(const EdgeInsets.symmetric(horizontal: 8, vertical: 16)),
          ),
          child: FittedBox(
            fit: BoxFit.fitWidth,
            child: Text(
              hasAssigned
                  ? S.of(Get.context!).assigned.capitalize!
                  : (hasApplied ? S.of(Get.context!).approve.capitalize! : S.of(Get.context!).assign.capitalize!),
              style: MFont.medium14.apply(color: color),
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            //发送消息
            UserInfoEntity userInfo = assignInfo.inspector!;
            controller.sendMessage(userInfo.imUserId);
          },
          style: ButtonStyle(
            shape: MaterialStateProperty.all(const StadiumBorder()),
            side: MaterialStateProperty.all(BorderSide(color: color, width: 1)),
            visualDensity: VisualDensity.compact,
            padding: MaterialStateProperty.all(const EdgeInsets.symmetric(horizontal: 8, vertical: 16)),
          ),
          child: FittedBox(
            fit: BoxFit.fitWidth,
            child: Text(
              S.of(Get.context!).public_leave_message.capitalize!,
              style: MFont.medium14.apply(color: color),
            ),
          ),
        ),
      ],
    );
  }

  Widget _headView(UserInfoEntity user) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: CachedNetworkImage(
          imageUrl: user.head ?? '',
          width: 48,
          height: 48,
          fit: BoxFit.cover,
          placeholder: (ctx, e) {
            return Container(
              decoration: BoxDecoration(
                color: Get.context!.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                borderRadius: BorderRadius.circular(35),
              ),
            );
          },
          errorWidget: (ctx, e, x) {
            return Container(
              decoration: BoxDecoration(
                color: Get.context!.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                borderRadius: BorderRadius.circular(35),
              ),
            );
          },
        ),
      ),
    );
  }
}
