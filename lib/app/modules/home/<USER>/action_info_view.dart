import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/modules/ai/photo_selected/image_browse_page.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/modules/home/<USER>/action_product_controller.dart';
import 'package:inspector/generated/l10n.dart';

import '../../ai/category/controller/ai_default_config_controller.dart';
import '../../ai/category/view/ai_default_config_view.dart';
import 'action_info_controller.dart';


class ActionInfoView extends GetView<ActionInfoController> {
  const ActionInfoView({super.key});


  @override
  Widget build(BuildContext context) {

    return KeyboardDismissOnTap(
    child:
        WillPopScope(
        onWillPop: (){
          const PointerEvent addPointer =  PointerAddedEvent(
              pointer: 0,
              position: Offset(10,10)
          );
          GestureBinding.instance.handlePointerEvent(addPointer);
          Future.delayed(Duration(milliseconds: 300), () {
            Get.back();
          });

          return Future.value(false);
    },
    child:

    Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          title: Text(S.of(context).order_goods_info),
          centerTitle: true,
            leading: IconButton(icon: Icon(Icons.arrow_back), onPressed: (){
              const PointerEvent addPointer =  PointerAddedEvent(
                  pointer: 0,
                  position: Offset(0, 0)
              );
              GestureBinding.instance.handlePointerEvent(addPointer);
              Future.delayed(Duration(milliseconds: 200), () {
                Get.back();
              });
            }),

          actions: controller.hasProduct ? [deleteButton()] : null,
        ),
        body: ListenableBuilder(
            listenable: controller,
            builder: (context, child) {
              return Column(
                children: [
                  Expanded(
                    child: ListView(children: [
                      buildProductView(context),
                      buildProductView2(context),
                      buildProductView3(context),
                      titleRow(context),
                      Column(
                        children:  modelList(),
                      )
                      // if (controller.product?.productModel?.isNotEmpty ?? false)
                      // for (ProductModel m in (controller.product?.productModel ?? [])) info(m),
                    ]),
                  ),
                  bottomButton(),
                ],
              );
            }),
      ),
    ));
  }

  List<Widget> modelList(){
    List<Widget> lists =[];
    if(controller.product!.productModel!=null){
      for(int i=0;i<controller.product!.productModel!.length;i++){
        lists.add(info(controller.product!.productModel![i],i));
      }
    }

    return lists;
  }

  BorderSide get side => BorderSide(color: MColor.xFFE5E5E5);

  Widget buildProductView(context) {
    return Container(
      
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
          color: MColor.white,
          border: Border(
            bottom: side,
          )),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text('*${S.of(context).ai_product_category}',style:  MFont.medium16,),
              SizedBox(width: 20,),
              // Text('${S.of(context).publish_inspection_time_tip}',style: TextStyle(
              //     color:MColor.xFF808080
              // ),)
            ],
          ),
          AiClassifyFilter2(),
        ],
      ),
    );
  }

  Widget buildProductView2(context) {
    return Container(
      padding: EdgeInsets.fromLTRB(20,10,20,10),
      decoration: BoxDecoration(
          color: MColor.white,
          border: Border(
            bottom: side,
          )),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
              Text('*${S.of(context).ai_add_product_product_name}',style:  MFont.medium16,),
              // SizedBox(width: 20,),
              Container(
                width: 250,
                  child: TextField(
                // focusNode: controller.focusNode,
                controller: TextEditingController(text: controller.product?.productName??''),
                textAlign: TextAlign.right,
                style: MFont.medium14,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: S.current.bind_hint,
                  contentPadding: EdgeInsets.only(right: 1),
                ),
                onChanged: (e){
                  controller.product?.productName= e;
                },
                onTapOutside: (e){
                  print('onTapOutside1111');
                  if(controller.product?.productName!=null){
                    controller.inputProductNameDone(controller.product?.productName);
                  }
                },
                onSubmitted: (e){
                },
                onEditingComplete: (){
                },
              ))

              // Text( "${controller.product?.productName}",style: MFont.medium14,)
        ],
      ),
    );
  }


  void showDilog(context1,  callBack(value)){
    String unit = '';
    showGeneralDialog(
        context: context1,
        barrierColor: Colors.black.withOpacity(.5),
        barrierDismissible: true,
        barrierLabel: '',
        pageBuilder: (BuildContext context,
            Animation<double> animation,
            Animation<double> secondaryAnimation) {
          return
            Scaffold(
                backgroundColor: Colors.transparent,
                body:  Center(
                    child: SingleChildScrollView(
                        child:   Center(
                          child: Container(
                              margin: EdgeInsets.all(20),
                              padding: EdgeInsets.fromLTRB(15, 18, 15, 18),
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(5)
                              ),
                              child:  Column(
                                mainAxisSize:MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  // _itemUnit(unit),
                                  Container(
                                      margin: EdgeInsets.only(top: 10),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(S.current.apply_enter),
                                          Container(
                                            width: Get.width-120,
                                            padding: EdgeInsets.only(left: 10),
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(5),
                                              border: Border.all(color:MColor.xFFE5E5E5,width: 1 ),
                                            ),
                                            child: TextField(
                                              keyboardType:TextInputType.text,
                                              style: TextStyle(
                                                  color: MColor.xFF808080,
                                                  fontSize: 14
                                              ),
                                              controller: TextEditingController(text:''),
                                              decoration: InputDecoration(
                                                  hintStyle: TextStyle(
                                                      color: MColor.xFF808080,
                                                      fontSize: 14
                                                  ),
                                                  hintText: S.current.apply_enter,
                                                  border: InputBorder.none
                                              ),
                                              onChanged: (values){
                                                unit = values;
                                              },
                                            ),
                                          )
                                        ],
                                      )
                                  ),
                                  SizedBox(height: 20,),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      InkWell(
                                          onTap: (){
                                            Navigator.pop(context);
                                          },
                                          child:    Container(
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius: BorderRadius.circular(10),
                                                  border: Border.all(color: MColor.skin,width: 1)
                                              ),
                                              height: 40,
                                              width: Get.width/2 - 80 ,
                                              child:  Text(   S.current.order_cancel,style: TextStyle(
                                                  color: MColor.skin,fontSize: 14
                                              ),))
                                      )
                                      ,
                                      SizedBox(width: 20,),
                                      InkWell(
                                          onTap: (){
                                            print('unitunit=='+unit);
                                            callBack(unit);
                                            Navigator.pop(context);
                                          },
                                          child:    Container(
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                color: MColor.skin,
                                                borderRadius: BorderRadius.circular(10),
                                              ),
                                              height: 40,
                                              width: Get.width/2 - 80 ,
                                              child:  Text(   S.current.charge_submit,style: TextStyle(
                                                  color: MColor.white,fontSize: 14
                                              ),))
                                      )

                                    ],
                                  ),


                                ],
                              )
                          ) ,
                        )
                    )))
          ;
        });
  }

  Widget buildProductView3(context) {
    return Container(

      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
          color: MColor.white,
          border: Border(
            bottom: side,
          )),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('*${S.of(context).ai_product_unit}',style:  MFont.medium16,),
                  Container(child: StatefulBuilder(builder: (context, setState) {
                    return PopupMenuButton<String>(
                      color: MColor.xFFFAFAFA,
                      initialValue: '',
                      constraints: BoxConstraints(maxHeight: 240),
                      onSelected: (value) {
                        setState(() {
                          if(value==S.current.ai_other){
                            showDilog(context,((value11){
                              setState(() {
                                controller. changeUnit(value11);
                              });
                            }));
                          }else{
                            controller. changeUnit(value);
                          }

                          // controller.unit = value;
                        });
                      },
                      itemBuilder: (context) {
                        return controller.unitList
                            .map((e) => PopupMenuItem(
                          value: e,
                          child: Text(e),
                        ))
                            .toList();
                      },
                      child: Row(
                        children: [
                          Container(
                            child: Text(
                              controller.product!.productModel==null?'':
                              controller.product!.productModel!.isNotEmpty?
                              "${controller.product!.productModel![0].unit!}": S.current. ai_please_select,
                              textAlign: TextAlign.end,
                              style: MFont.medium14,
                            ),
                          ),
                          SizedBox(width: 8),
                          Icon(Icons.expand_more,color: MColor.xFF808080,),
                          SizedBox(width: 1)
                        ],
                      ),
                    );
                  }),)

        ],
      ),
    );
  }

  Widget chevron() {
    return Icon(CupertinoIcons.chevron_forward, size: 16);
  }

  Widget bottomButton() {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18),
      margin: EdgeInsets.only(bottom: Get.bottomBarHeight != 0 ? Get.bottomBarHeight : 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(right: 7),
            child: TextButton(
              onPressed: () => controller.editModelData(true,null),
              style: ButtonStyle(
                backgroundColor: WidgetStatePropertyAll(MColor.skin),
                shape: MaterialStateProperty.all(
                    RoundedRectangleBorder(
                        borderRadius:
                        BorderRadius.circular(
                            5))),
                visualDensity: VisualDensity.compact,
              ),
              child: Text(
                S.current.ai_add_product_new_model,
                style: MFont.medium14.apply(color: Colors.white),
              ),
            )
          )
          ,
          Container(
              margin: EdgeInsets.only(left: 7),
            child:    TextButton(

              onPressed: () => {
                controller.isEdit.value = !controller.isEdit.value,
              },
              style: ButtonStyle(
                side: MaterialStateProperty.all(
                  BorderSide(
                      color: MColor.skin,
                      width: 1),
                ),
                backgroundColor: WidgetStatePropertyAll(MColor.white),
                shape: MaterialStateProperty.all(
                    RoundedRectangleBorder(
                        borderRadius:
                        BorderRadius.circular(
                            5))),
                visualDensity: VisualDensity.compact,
              ),
              child: Text(
                S.current.edit,
                style: MFont.medium14.apply(color: MColor.skin),
              ),
            )
          ),

          Container(

            margin: EdgeInsets.only(left: 7),
              child: TextButton(
                onPressed: () {
                  const PointerEvent addPointer =  PointerAddedEvent(
                      pointer: 0,
                      position: Offset(10,10)
                  );
                  GestureBinding.instance.handlePointerEvent(addPointer);
                  Future.delayed(Duration(milliseconds: 300), () {
                    Get.back();
                  });
                },
                style: ButtonStyle(
              backgroundColor: WidgetStatePropertyAll(MColor.skin),
                        shape: MaterialStateProperty.all(
                 RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(
               5))),
                        visualDensity: VisualDensity.compact,
    ),
                child: Text(
                  S.current.date_save,
    style: MFont.medium14.apply(color: Colors.white),
    ),
    )
          )

        ],
      ),
    );
  }

  Widget deleteButton() {
    return IconButton(
        onPressed: deleteProduct2,
        icon: Text(
          S.current.ai_delete,
          style: MFont.medium14.copyWith(
            color: MColor.skin,
          ),
        ));
  }

  //
  void deleteProduct2() {
    showCustomDialog(S.current.ai_add_product_ask_product,
        cancel: true, onConfirm: controller.deleteProduct);
  }

  Widget modelCol(ProductModel model) {
    return Container(
        padding: EdgeInsets.only(left: 28),
      width:  Get.width/2,
      decoration: BoxDecoration(
          border: Border(
            top: side,
            bottom: side,
            right: side
          )
      ),
      child: Row(
        children: [
      Container(
        width:(Get.width/2 - 30) ,
        child:  TextField(
          // focusNode: controller.focusNode,
          controller: TextEditingController(text:  model.mark??''),
          textAlign: TextAlign.left,
          style: MFont.medium14,
          decoration: InputDecoration(
            border: InputBorder.none,
            hintText: S.current.ai_add_product_input_model,
            contentPadding: EdgeInsets.only(right: 1),
          ),
          onChanged: (e){
            model.mark = e;

          },
          onTapOutside: (e){
            print('222222222onTapOutsideonTapOutside');
            if(controller.product!.productModel!=null){
              model.unit =  (controller.product!.productModel?.first.unit??'') as String?;
            }

            if(model.mark!=null){
              controller.editModelData(false, model);
            }
          },
          onSubmitted: (e){
          },
          onEditingComplete: (){
          },
        )
      )

        ],
      )
    ) ;
  }

  Widget titleRow(context){
    return Container(
      margin: EdgeInsets.only(top: 5),
      color: Colors.white,
      child:  Row(
        children: [
          titleCol(context,S.of(context).ai_add_product_model_title),
          titleCol(context,S.of(context).mall_goods_count),

        ],
      )
    );
  }

  Widget titleCol(context,title,) {
    return Container(
      height: 45,
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.only(left: 28),
        width: Get.width/2,
        decoration: BoxDecoration(
            border: Border(
                bottom: side,
                left: side
            )
        ),
        child: Container(
          child:
        Text(title,style:  TextStyle(
          color: Colors.black,
          fontSize: 16,
          fontWeight: FontWeight.bold
        ),),
        )
    ) ;
  }

  Widget numCol(ProductModel model,index) {
    return Container(
      padding: EdgeInsets.only(left: 28),
      width: Get.width/2,
        decoration: BoxDecoration(
            border: Border(
              top: side,
              bottom: side,
              left: side
            )
        ),

          child:Row(
            children: [
              Container(
                width: 120,
                  child: TextField(
                    keyboardType: TextInputType.number,
                    controller: TextEditingController(text:  model.amount!=null?model.amount.toString():''),
                    textAlign: TextAlign.left,
                    style: MFont.medium14,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: S.current.purchase_publish_quantity_hint,
                      contentPadding: EdgeInsets.only(right: 1),
                    ),
                    onChanged: (e){
                      if(e==''){
                      }else{
                        model.amount = int.parse(e);
                      }

                    },
                    onTapOutside: (e){
                      if(controller.product!.productModel!=null){
                        model.unit =  (controller.product!.productModel?.first.unit) as String?;
                      }
                      if(model.amount!=null){
                        controller.editModelData(false, model);
                      }
                    },
                    onSubmitted: (e){
                    },
                    onEditingComplete: (){
                    },
                  )

              )
              ,
              if(   controller.isEdit.value)
              InkWell(
                onTap: (){
                  deleteModel(model.id,index);
                },
                child:  Text(S.current.address_delete,style: TextStyle(
                    color: MColor.skin
                ),)
              )

            ],
          )

          ,
    ) ;
  }

  void deleteModel(id,index) {
    showCustomDialog(S.current.ai_add_product_ask_delete,
        cancel: true, onConfirm:(){
          controller.deleteModel2(id,index);
        } );
  }

  Widget info(ProductModel model,index) {
    return Obx(()=> Container(
        color: Colors.white,
        child: Row(
          children: [
            modelCol(model),
            numCol(model,index),
          ],
        )
    ))  ;
  }

  Widget modelTile(ProductModel model) {
    return GestureDetector(
      onTap: () => controller.editModel(model: model),
      child: Container(
        padding: EdgeInsets.fromLTRB(15, 10, 15, 10),
        height: 82,
        decoration: BoxDecoration(
            color: MColor.white,
            border: Border(
              top: side,
              bottom: side,
            )),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (model.pic?.isNotEmpty ?? false)
              GestureDetector(
                onTap: () {
                  ImageBrowsePage.pushBrowse(Get.context!, model.pic!);
                },
                child: CachedNetworkImage(
                  imageUrl: model.pic!,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                ),
              )
            else
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                    border: Border.all(color: MColor.xFFE5E5E5),
                    borderRadius: BorderRadius.circular(8)),
                child: Center(
                  child: Text(
                    S.current.ai_add_product_picture_lost,
                    style: MFont.medium12,
                  ),
                ),
              ),
            SizedBox(width: 20),
            Container(

                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  model.mark ?? '',
                  style: MFont.medium16,
                ),
                SizedBox(
                  height: 10,
                ),
                Text(
                  '${model.amount ?? ''}${model.unit ?? ''}',
                  // style: MFont.medium14.copyWith(color: MColor.xFFA6A6A6),
                ),
              ],
            )),
            chevron(),
          ],
        ),
      ),
    );
  }

}
class AiClassifyFilter2 extends StatefulWidget {
  const AiClassifyFilter2({super.key});

  @override
  _AiClassifyFilterState2 createState() => _AiClassifyFilterState2();
}

class _AiClassifyFilterState2 extends State<AiClassifyFilter2> {
  late AiDefaultConfigController controller = Get.find<AiDefaultConfigController>();

  final LayerLink layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool show = false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: (didPop) {
        hideOverlay();
      },
      child: GestureDetector(
        onTap: _toggleOverlay,
        child: CompositedTransformTarget(
          link: layerLink,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Container(
              margin: const EdgeInsets.only(left: 20,),
              height: 36,
              padding: EdgeInsets.symmetric(horizontal: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
              ),
              // child: ,
              child: IntrinsicWidth(
                child: Center(
                  child: Obx(() {
                    return Row(
                      children: [
                        Text(
                          controller.classifyOnRequest.value.data?.name ??
                              S.of(context).ai_default_config_category_all,
                          style: MFont.medium14.copyWith(
                              color: controller.classifyOnRequest.value.data?.name != null
                                  ? MColor.black
                                  : MColor.black),
                        ),
                        Icon(
                          Icons.keyboard_arrow_down_sharp,
                          color: controller.classifyOnRequest.value.data?.name != null
                              ? MColor.black
                              : MColor.black,
                          size: 20,
                        )
                      ],
                    );
                  }),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _createOverlayEntry() {

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: EdgeInsets.zero,
          child:  Material(
            type: MaterialType.transparency,
            child:   Container(
              width: Get.width,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AiDefaultClassifyView(
                    onConfirm: () {
                      setState(() {
                        // hideOverlay();
                        Navigator.pop(context);
                      });
                    },
                  )
                ],
              ),

            ),
          )
        );
      },
    );

  }

  void _toggleOverlay() {
    if (!show) {
      _showOverlay();
    } else {
      _hideOverlay();
    }
    show = !show;
  }

  void _showOverlay() {
    _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
  }

  void hideOverlay() {
    if (show) {
      _overlayEntry?.remove();
      _overlayEntry = null;
      show = false;
    }
  }
}
