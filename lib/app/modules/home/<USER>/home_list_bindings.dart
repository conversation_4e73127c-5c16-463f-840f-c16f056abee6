import 'package:get/get.dart';
import 'package:inspector/app/modules/home/<USER>/home_list_controller.dart';
import 'package:inspector/app/tools/tools.dart';

class HomeListBindings extends Bindings {
  @override
  void dependencies() {
    // Get.lazyPut<HomeListController>(
    //   () => HomeListController(),
    // );
    Get.lazyPut<HomeListController>(() {
      logger.i('HomeListController is creating');
      return HomeListController();
    });
  }
}
