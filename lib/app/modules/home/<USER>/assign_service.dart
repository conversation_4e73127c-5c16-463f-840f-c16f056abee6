import 'package:inspector/app/config/api.dart';
import 'package:inspector/app/data/assign_info.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/tools/public_provider.dart';

class AssignService {
  //指派列表
  // 列表类型 0所有 1未指派列表 2已指派列表 默认为0
  Future<BaseModel<AssignResp>> getInspectorList(int orderId, int page, int limit, AssignType assignType, String? query) {
    String apiPath = '';
    if (query != null && query.isNotEmpty) {
      apiPath = '${Api.assignInspectorList}?page=$page&limit=$limit&order_id=$orderId&assign_type=${assignType.index}&search=$query&is_auth=0';
    } else {
      apiPath = '${Api.assignInspectorList}?page=$page&limit=$limit&order_id=$orderId&assign_type=${assignType.index}&is_auth=0';
    }
    return PublicProvider.request<AssignResp>(path: apiPath, isPost: false);
  }

  //指派验货员
  Future<BaseModel<dynamic>> assignInspector(int orderId, int inspId, String cost, String account) {
    return PublicProvider.request<dynamic>(
        path: Api.assignInspector, isPost: true, params: {'order_id': '$orderId', 'insp_id': '$inspId', 'cost': cost, 'account': account});
  }

  // 取消指派
  Future<BaseModel<dynamic>> cancelAssign(int orderId, int inspId) {
    return PublicProvider.request<dynamic>(path: Api.cancelAssign, isPost: true, params: {'order_id': '$orderId', 'insp_id': '$inspId'});
  }
}

enum AssignType { AssignTypeAll, AssignTypeUnassigned, AssignTypeAssigned, AssignTypeApplied }
