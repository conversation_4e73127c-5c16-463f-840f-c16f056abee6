import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/message/chat/chat_binding.dart';
import 'package:inspector/app/modules/message/chat/chat_view.dart';
import 'package:inspector/app/modules/message/chat_provider.dart';
import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/modules/pulish/publish_provider.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/widgets/inspection/inspection_mixin.dart';

import '../../../../../generated/l10n.dart';

class OrderDetailController extends GetxController with InspectionMixin {
  OrderProvider provider = OrderProvider();
  PublishProvider payProvider = PublishProvider();
  int? orderId;
  final detailEntity = OrderDetailEntity().obs;
  bool isChange = false;
  bool isApply = false;
  TextEditingController editingController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    orderId = Get.arguments;
    if (Get.parameters['isApply'] != null) {
      isApply = true;
    }
    if (orderId != null) {
      fetchOrderDetail();
    }
  }

  void updateApply(bool isApply) {
    detailEntity.value.isApply = isApply;
    detailEntity.refresh();
  }

  void fetchOrderDetail() {
    EasyLoading.show();
    print('orderIdorderId=='+orderId.toString());
    provider.takeOrderDetail(orderId!).then((value) async {
      if (value.isSuccess) {
        detailEntity.value = value.data ?? OrderDetailEntity();
        detailEntity.refresh();
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void fetchImGroup() {
    EasyLoading.show();
    provider.takeImGroup(orderId!).then((value) async {
      if (value.isSuccess) {
        var gid = value.data['cid'] as int?;
        if (gid != null) {
          unawaited(Get.to(() => ChatView(tag: gid.toString()),
              arguments: {'cid': gid, 'order_detail_entity': detailEntity.value},
              binding: ChatBinding(tag: gid.toString())));
          // unawaited(Get.toNamed(Routes.CHAT, arguments: {'cid': gid}));
        }
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void payAction() {
    Get.toNamed(Routes.PAY, arguments: {
      'usd': detailEntity.value.userAccount == 2 ? true : false,
      'id': detailEntity.value.orderNo!,
      'price': detailEntity.value.allPrice ?? 0
    })?.then((value) {
      isChange = true;
      fetchOrderDetail();
    });
  }

  void fetchCancel() {
    var isSelf = detailEntity.value.isSelf ?? false;

    if (isSelf) {
      showCustomDialog(S.of(Get.context!).order_sure_cancel, onConfirm: () {
        canAction();
      }, cancel: true);
    } else {
      canAction();
    }
  }

  void canAction() {
    var isSelf = detailEntity.value.isSelf ?? false;

    EasyLoading.show();
    provider.takeOrderCancel(orderId!, !isSelf).then((value) async {
      if (value.isSuccess) {
        fetchOrderDetail();
        isChange = true;
      }
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void beginInspect() {
    EasyLoading.show();
    provider.beginInspect(orderId!).then((value) async {
      if (value.isSuccess) {
        fetchOrderDetail();
        isChange = true;
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() => EasyLoading.dismiss());
  }

  void confirmRefuse() {
    EasyLoading.show();
    provider.confirmOrRefuseOrder(orderId!, 2).then((value) async {
      if (value.isSuccess) {
        fetchOrderDetail();
        isChange = true;
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() => EasyLoading.dismiss());
  }

  void confirmOrder() {
    EasyLoading.show();
    provider.confirmOrRefuseOrder(orderId!, 1).then((value) async {
      if (value.isSuccess) {
        fetchOrderDetail();
        isChange = true;
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() => EasyLoading.dismiss());
  }

  void createConv(int imUserId) {
    EasyLoading.show();
    ChatProvider().createConv(imUserId).then((value) {
      if (value.isSuccess) {
        var gid = value.data['cid'] as int?;
        if (gid != null) {
          unawaited(Get.to(() => ChatView(tag: gid.toString()),
              arguments: {
                'cid': gid,
                'order_detail_entity': detailEntity.value,
                'showSendOrder': true
              },
              binding: ChatBinding(tag: gid.toString())));
        }
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void toAi() {
    bool notLost = true;
    String lostStr = S.current.ai_add_product_model_title;
    bool hasModel = false;
    String warning = '';
    // for (OrderProduct p in detailEntity.value.orderProducts ?? []) {
    //   hasModel = false; //每个产品都必须要有型号
    //   lostStr = S.current.ai_add_product_model_title;
    //   for (ProductModel m in p.productModel ?? []) {
    //     hasModel = true;
    //     notLost = notLost && m.lostName.isEmpty;
    //     if ((m.amount ?? 0) <= 0) {
    //       warning = S.current.ai_add_model_count_warning;
    //       break;
    //     }
    //     if (!notLost) {
    //       lostStr = m.lostName;
    //       warning = S.current.ai_add_product_lost_des(lostStr);
    //       break;
    //     } else {
    //       lostStr = '';
    //     }
    //   }
    //   if (!notLost) break;
    // }
    // if (!hasModel || lostStr.isNotEmpty) {
    //   EasyLoading.showInfo(warning,
    //       duration: Duration(
    //         seconds: 1,
    //       ));
    //   return;
    // }
    Get.toNamed(AiRoutes.CATEGORY, arguments: {
      'data':detailEntity.value,
      'orderId':orderId.toString()
    })?.then((value){
      fetchOrderDetail();
    });
  }

  Future<void> editProduct({OrderProduct? product}) async {
    if (product == null) {
      await showCustomDialog(
        S.current.ai_add_product_product_name,
        textController: editingController,
        textPlaceHolder: S.current.ai_add_product_input_product_name,
        cancel: true,
        onConfirm: () {
          provider.addProduct(orderId: orderId!, productName: editingController.text).then(
            (value) {
              if (value.isSuccess) {
                editingController.text = '';
                fetchOrderDetail();
              }
            },
          );
        },
      );
    } else {
      await Get.toNamed(Routes.ADD_PRODUCT, arguments: {'orderId': orderId, 'product': product});
      fetchOrderDetail();
    }
  }
}
