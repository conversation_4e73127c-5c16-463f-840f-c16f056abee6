import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/order_list_entity.dart';
import 'package:inspector/app/modules/home/<USER>/admin_note_controller.dart';
import 'package:inspector/app/modules/widgets/progress_indicator_widget.dart';
import 'package:inspector/app/modules/widgets/sliver_sticky_header_delegate.dart';
import 'package:inspector/app/shared/widgets/avatar.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

class AdminNoteView extends GetView<AdminNoteController> {
  @override
  final String? tag;

  const AdminNoteView({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put<AdminNoteController>(AdminNoteController(), tag: tag);
    return Container(
        margin: EdgeInsets.only(top: AppBar().preferredSize.height),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(6), topRight: Radius.circular(6)),
          color: Colors.white,
        ),
        child: Column(children: [
          Expanded(
            child: CustomScrollView(
              controller: controller.scrollController,
              slivers: [_titleView, _bodyView],
            ),
          ),
          _textInput,
        ]));
  }

  Widget get _bodyView {
    return Obx(() {
      var itemList = <AdminNote>[];
      itemList.addAll(controller.adminNotes);

      var itemCount = controller.adminNotes.length + 1;
      return SliverList.separated(
        // physics: const AlwaysScrollableScrollPhysics(),
        // controller: controller.scrollController,
        separatorBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.only(left: MIDDLE_AVATAR_SIZE + 8, top: 8),
            child: Container(
              color: MColor.xFFCFCFCF_50,
              height: 1,
            ),
          );
        },
        itemCount: itemCount,
        itemBuilder: (ctx, index) {
          if (index == itemCount - 1) {
            return ProgressIndicatorWidget(
              controller.isShowLoading.value,
              emptyView: _emptyReplyView,
            );
          } else {
            return Padding(
                padding: const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
                child: Column(
                  children: [
                    _headView(itemList[index]),
                    _contentView(itemList[index]),
                  ],
                ));
          }
        },
      );
    });
  }

  Widget get _emptyReplyView {
    return const SizedBox(
        height: 46,
        child: Row(
          children: [
            Expanded(
                child: Text(
              '暂无更多留言',
              textAlign: TextAlign.center,
            )),
          ],
        ));
  }

  Widget _contentView(AdminNote adminNote) {
    String content = adminNote.msg;
    String prefix = '';
    return Padding(
      padding: const EdgeInsets.only(left: MIDDLE_AVATAR_SIZE + 8, top: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                  child: Text('$prefix$content',
                      textAlign: TextAlign.start,
                      strutStyle: const StrutStyle(
                        forceStrutHeight: true,
                        leading: 0.5,
                      ),
                      style: MFont.regular14.apply(color: MColor.xFF000000))),
            ],
          ),
          const SizedBox(
            height: 12,
          ),
          Row(
            children: [
              Expanded(
                child: Text(formatRecentTime(adminNote.createdAt),
                    textAlign: TextAlign.start,
                    strutStyle: const StrutStyle(
                      forceStrutHeight: true,
                      leading: 0.5,
                    ),
                    style: MFont.regular10.apply(color: MColor.xFF999999)),
              ),
            ],
          ),
          const SizedBox(
            height: 12,
          ),
        ],
      ),
    );
  }

  Widget _headView(AdminNote adminNote) {
    var user = adminNote.admin;
    var userNick = '';
    if (user != null && user.name != null) {
      userNick = user.name!;
    }
    return Row(
      children: [
        Avatar(url: user?.head, displayName: user?.name, size: MIDDLE_AVATAR_SIZE),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: Text(userNick,
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
              style: MFont.medium15.apply(color: MColor.xFF333333)),
        ),
      ],
    );
  }

  Widget get _titleView {
    return SliverPersistentHeader(
      pinned: true,
      floating: true,
      delegate: SliverStickyHeaderDelegate(SizedBox(
        height: 42,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          color: MColor.xFFFFFFFF,
          child: Stack(children: [
            GestureDetector(
              onTap: () {
                Get.back();
              },
              child: const SizedBox(
                height: 42,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.close,
                      size: 24,
                    ),
                  ],
                ),
              ),
            ),
            Center(
                child: Text(
              '全部留言',
              style: MFont.semi_Bold16.apply(color: MColor.xFF666666),
            ))
          ]),
        ),
      )),
    );
  }

  Widget get _textInput {
    Widget current = TextField(
      // style: TextStyle(color: Theme.of(context).colorScheme.textPrimary),
      // cursorColor: Theme.of(context).colorScheme.cursor,
      controller: controller.txtController,
      maxLength: 1000,
      maxLines: null,
      autofocus: false,
      focusNode: controller.inputFocusNode,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16),
        fillColor: MColor.xFFEEEEEE,
        // filled: true,
        // isDense: true,
        counterText: '',
        hintText: '新增备注',
        hintStyle: MFont.regular15.apply(color: MColor.xFF999999),
        labelStyle: MFont.regular15.apply(color: Colors.red),
        // labelStyle: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: MColor.xFF000000, width: 1.5),
          borderRadius: BorderRadius.circular(20),
        ),
        focusedBorder: OutlineInputBorder(borderSide: const BorderSide(color: MColor.xFF000000, width: 1.5), borderRadius: BorderRadius.circular(20)),
        constraints: const BoxConstraints(maxHeight: 37, minHeight: 37),
      ),
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.send,
      onSubmitted: (String v) {
        // if (v.trim() != '') {
        //   if (!controller.canSend.value) controller.canSend.value = true;
        // } else {
        //   if (controller.canSend.value) controller.canSend.value = false;
        // }
        controller.addAdminNote(v);
      },
    );

    current = Container(
      margin: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 5.0),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(5), color: Colors.white),
      child: current,
    );

    current = Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(child: current),
        const SizedBox(width: 5.0),
      ],
    );

    return current;
  }
}
