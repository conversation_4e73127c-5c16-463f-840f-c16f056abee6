import 'dart:async';
import 'dart:collection';
import 'dart:developer';

import 'package:flutter/material.dart';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/api_service.dart';
import 'package:inspector/app/data/chat_message.dart';
import 'package:inspector/app/data/conversation_entity.dart';
import 'package:inspector/app/data/group_entity.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/data/receive_message.dart';
import 'package:inspector/app/data/sending_message.dart';
import 'package:inspector/app/modules/message/chat_provider.dart';
import 'package:inspector/app/modules/message/models/chat.dart';
import 'package:inspector/app/modules/message/socket/socket_channel.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:uuid/uuid.dart';

import '../../../../generated/l10n.dart';

class NoticeController extends GetxController {
  ChatProvider provider = ChatProvider();

  final title = RxString(S.of(Get.context!).message);
  final messages = RxList<NoticeConvRows>();
  final canSend = RxBool(false);

  int conversationId = 0;

  final showSendSendOrder = false.obs;

  PublishConvRows? conversationInfo;

  ScrollController listController = ScrollController();

  NoticeController() {
    logger.i('NoticeController ${this.hashCode} created');
  }

  @override
  void onInit() {
    super.onInit();
    conversationId = Get.arguments['cid'];
    title.value = Get.arguments['title'];
    logger.i('NoticeController onInit $conversationId');
    fetchList();
  }

  @override
  void onClose() {
    listController.dispose();
    super.onClose();
  }

  Future<void> _getMessages(int lastRecordId) async {
    BaseModel<List<NoticeConvRows>> result = await provider.getNoticeList(10, 1, conversationId);
    if (result.isSuccess) {
      List<NoticeConvRows>? messages = result.data;
      if (messages != null) {
        this.messages.clear();
        this.messages.addAll(messages);
        await SocketChannel.markRead(0, msgType: messages.first.type ?? 0);
      }
    }
  }

  Future<void> fetchList() async {
    unawaited(EasyLoading.show());
    try {
      await _getMessages(0);
      await Future.delayed(const Duration(milliseconds: 100));
      listController.jumpTo(listController.position.maxScrollExtent);
    } finally {
      unawaited(EasyLoading.dismiss());
    }
  }

  void onReceiveMessage(ReceiveMessage message) {
    logger.i('onReceiveMessage $message');
    SocketChannel.markRead(message.recordId ?? 0);
  }

  void onSendFailed(SendingMessage message) {}

  Future<void> loadMore(RefreshController refreshController) async {
    logger.i('loadmore');
    int lastRecordId = 0;
    // ChatMessage msg = messages.value.lastWhere((element) => element is ReceiveMessage);
    // if (msg is ReceiveMessage) {
    //   lastRecordId = msg.recordId ?? 0;
    // }
    await _getMessages(lastRecordId);
    refreshController.loadComplete();
  }
}
