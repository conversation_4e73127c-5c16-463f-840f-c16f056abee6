import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/conversation_entity.dart';
import 'package:inspector/app/modules/message/notice/notice_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/shared/services/page_helper.dart';
import 'package:inspector/app/tools/public_provider.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../generated/l10n.dart';

const _PAGE_SPACE = 8.0;

class NoticeView extends GetView<NoticeController> {
  @override
  final String? tag;

  const NoticeView({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    RefreshController refreshController = RefreshController();
    return Scaffold(
      appBar: PageHelper.getAppBar(
        titleWidget: Obx(() => Text(controller.title.value)),
      ),
      body: Stack(
        children: [
          Obx(() {
            return Column(
              children: [
                Expanded(
                  child: SmartRefresher(
                    controller: refreshController,
                    onRefresh: () => controller.loadMore(refreshController),
                    child: ScrollConfiguration(
                      behavior: MyBehavior(),
                      child: ListView.separated(
                          padding: const EdgeInsets.symmetric(horizontal: _PAGE_SPACE),
                          controller: controller.listController,
                          itemCount: controller.messages.isEmpty ? 0 : controller.messages.length + 1,
                          itemBuilder: (context, index) {
                            if (index == 0) {
                              return const SizedBox();
                            } else {
                              return _Message(message: List.of(controller.messages)[index - 1]);
                            }
                          },
                          separatorBuilder: (context, index) {
                            if (index == 0) {
                              return _DateMessage(current: List.of(controller.messages)[0], prev: null);
                            }
                            return _DateMessage(current: List.of(controller.messages)[index], prev: List.of(controller.messages)[index - 1]);
                          }),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                )
              ],
            );
          }),
        ],
      ),
    );
  }
}

class MyBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(BuildContext context, Widget child, ScrollableDetails details) {
    return child;
  }
}

class _DateMessage extends StatelessWidget {
  final NoticeConvRows current;
  final NoticeConvRows? prev;
  const _DateMessage({required this.current, this.prev, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    DateTime currentTime = DateTime.fromMillisecondsSinceEpoch(DateTime.parse(current.updatedAt!).millisecondsSinceEpoch);
    DateTime? prevTime = prev == null ? null : DateTime.fromMillisecondsSinceEpoch(DateTime.parse(prev!.updatedAt!).millisecondsSinceEpoch);
    if (currentTime.toLocal().day == prevTime?.toLocal().day) return const SizedBox();

    return Container(
      padding: const EdgeInsets.only(top: 10.0, bottom: 10.0),
      child: Row(
        children: [
          const Expanded(flex: 3, child: Divider()),
          const SizedBox(width: 10.0),
          Center(child: Text(_getDate(currentTime), style: const TextStyle(color: Colors.black))),
          const SizedBox(width: 10.0),
          const Expanded(flex: 3, child: Divider()),
        ],
      ),
    );
  }

  String _getDate(DateTime dt) {
    var now = DateTime.now();

    var sameYear = now.year == dt.year;

    var sameDay = sameYear && now.month == dt.month && now.day == dt.day;

    if (sameDay) return S.of(Get.context!).today;

    if (now.add(const Duration(days: -1)).day == dt.day) return S.of(Get.context!).yesterday;

    if (sameYear) return '${_z(dt.month)}-${_z(dt.day)}';

    return '${dt.year.toString().substring(2)}-${_z(dt.month)}-${_z(dt.day)}';
  }

  String _z(int i) => i < 10 ? '0$i' : '$i';
}

class _Message extends StatelessWidget {
  final NoticeConvRows message;

  _Message({required this.message, Key? key}) : super(key: key) {}

  @override
  Widget build(BuildContext context) {
    Widget body = _TextMessage(message: message);

    body = ClipRRect(borderRadius: BorderRadius.circular(4.0), child: body);

    body = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [body, const SizedBox(height: 16.0)],
    );
    List<Widget> children = [
      Flexible(child: body),
    ];

    Widget current = Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: children,
        ),
      ],
    );

    return GestureDetector(
        onTap: () {
          if (message.url?.isNotEmpty ?? false) {
            if (message.urlType == 2) {
              Get.toNamed(
                Routes.WEB,
                parameters: {'url': '${Server.web}/${message.url}', 'title': message.title ?? ''},
              );
            }
          }
        },
        child: Padding(padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 20), child: current));
  }
}

class _TextMessage extends StatelessWidget {
  final NoticeConvRows message;
  _TextMessage({required this.message, Key? key}) : super(key: key) {}

  @override
  Widget build(BuildContext context) {
    Widget current = HtmlWidget(
      message.getMsgDesc(),
      textStyle: const TextStyle(fontSize: 14.0),
      onTapUrl: (url) {
        launchUrl(Uri.parse(url));
        return true;
      },
      onTapImage: (p) {
        launchUrl(Uri.parse(p.sources.first.url));
      },
    );
    Widget title = Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: MColor.skin_80,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 4.0),
        child: Center(child: Text(message.title!, textAlign: TextAlign.center, style: TextStyle(color: Colors.grey[50], fontSize: 16.0))));

    current = Container(
      decoration: BoxDecoration(
          color: MColor.xFFF4F6F9,
          borderRadius: const BorderRadius.all(
            Radius.circular(4),
          ),
          border: Border.all(color: MColor.skin_80)),
      constraints: const BoxConstraints(minHeight: 40.0),
      child: Column(
        children: [
          title,
          const SizedBox(
            height: 4,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: current,
          ),
        ],
      ),
    );

    return current;
  }
}
