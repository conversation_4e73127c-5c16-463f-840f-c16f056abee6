// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

import 'package:dotted_border/dotted_border.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/group_entity.dart';

import 'package:inspector/app/modules/message/chat/chat_controller.dart';
import 'package:inspector/app/modules/message/models/member.dart';
import 'package:inspector/app/modules/message/settings/index/chat_settings_controller.dart';
import 'package:inspector/app/routes/app_navigator.dart';
import 'package:inspector/app/shared/components/basic/ibutton.dart';
import 'package:inspector/app/shared/services/page_helper.dart';
import 'package:inspector/app/shared/widgets/avatar.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';

const _ITEM_WIDTH = 48.0;
const _ITEM_HEIGHT = 64.0;
const _ADD_ICON_SIZE = 36.0;
const _ADD_ICON_COLOR = DefaultStyle.gray4;

class ChatSettingsPage extends GetView<ChatSettingsPageController> {
  @override
  final String? tag;
  const ChatSettingsPage({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PageHelper.getAppBar(title: S.of(Get.context!).exit_group_chat_page_title, backgroundColor: DefaultStyle.pageBackground),
      body: Obx(() {
        if (controller.state.loading.value) return const SizedBox();
        return ListView(
          children: [
            Container(color: DefaultStyle.white, child: Column(children: [_Members(tag: tag), _moreMembers])),
            const SizedBox(height: DefaultStyle.spaceNormal),
            _groupName,
            if (controller.state.groupInfo.value?.type == 2) ...{
              const SizedBox(height: DefaultStyle.spaceLarge),
              IButton(title: S.of(Get.context!).exit_group_chat_button_title, onPressed: controller.exitGroup),
            }
          ],
        );
      }),
    );
  }

  Widget get _moreMembers {
    if (controller.state.members.length < 24) return const SizedBox();

    Widget more = Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(S.of(Get.context!).group_chat_setting_view_more, style: TextStyle(color: _ADD_ICON_COLOR, fontSize: DefaultStyle.fontSizeLarge)),
        SizedBox(width: DefaultStyle.spaceSmall),
        Icon(Icons.keyboard_arrow_right, color: _ADD_ICON_COLOR),
      ],
    );
    return Padding(padding: const EdgeInsets.all(DefaultStyle.spaceNormal), child: more);
  }

  Widget get _groupName {
    Widget current = Row(
      children: [
        Text(S.of(Get.context!).group_chat_setting_name),
        const SizedBox(width: DefaultStyle.spaceLarge),
        Expanded(
          child: Align(
            alignment: Alignment.centerRight,
            child: Obx(() {
              return Text(
                controller.chatController.title.value,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(color: _ADD_ICON_COLOR),
              );
            }),
          ),
        ),
        const Icon(Icons.keyboard_arrow_right, color: _ADD_ICON_COLOR),
      ],
    );
    current = Container(
      color: DefaultStyle.white,
      padding: const EdgeInsets.symmetric(horizontal: DefaultStyle.spaceLarge, vertical: DefaultStyle.spaceNormal),
      child: current,
    );

    return GestureDetector(
      onTap: () {
        if (!controller.chatController.isGroupOwner) {
          showToast(S.of(Get.context!).group_chat_setting_owner_update);
          return;
        }
        AppNavigator.updateGroupName();
      },
      child: current,
    );
  }
}

class _Members extends GetView<ChatSettingsPageController> {
  @override
  final String? tag;
  const _Members({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // if (Get.find<ChatController>().orderId == 0) return const SizedBox();
    return Obx(() {
      List<Widget> children = controller.state.members.map((e) => _MemberItem(e, tag: tag)).cast<Widget>().toList();
      if (controller.isAdmin) {
        children.add(_addIcon);
      }

      Widget current = Wrap(
        runAlignment: WrapAlignment.center,
        alignment: WrapAlignment.start,
        spacing: DefaultStyle.spaceLarge,
        runSpacing: DefaultStyle.spaceLarge,
        children: children,
      );
      return Padding(
        padding: const EdgeInsets.all(DefaultStyle.spaceNormal),
        child: Align(alignment: Alignment.topCenter, child: current),
      );
    });
  }

  Widget get _addIcon {
    return DottedBorder(
      color: _ADD_ICON_COLOR,
      strokeWidth: 2.0,
      dashPattern: const <double>[6, 4],
      borderType: BorderType.RRect,
      padding: EdgeInsets.zero,
      radius: const Radius.circular(DefaultStyle.radiusSmall),
      child: const ClipRRect(
        borderRadius: BorderRadius.all(Radius.circular(DefaultStyle.radiusSmall)),
        child: SizedBox(
          width: _ITEM_WIDTH - 2.0,
          height: _ITEM_WIDTH - 2.0,
          child: Icon(Icons.add, size: _ADD_ICON_SIZE, color: _ADD_ICON_COLOR),
        ),
      ),
    );
  }
}

class _MemberItem extends GetView<ChatSettingsPageController> {
  @override
  final String? tag;
  const _MemberItem(this.member, {Key? key, this.tag}) : super(key: key);
  final GroupMemberEntity member;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        controller.createConv(member.imUserId!);
      },
      child: SizedBox(
        width: _ITEM_WIDTH,
        height: _ITEM_HEIGHT,
        child: Column(
          children: [
            Avatar(url: member.head, displayName: member.nick, radius: DefaultStyle.radiusSmall, size: _ITEM_WIDTH),
            const SizedBox(height: 2.0),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: DefaultStyle.spaceSmall),
              child: Text(member.nick ?? '', overflow: TextOverflow.ellipsis, style: const TextStyle(fontSize: 10.0)),
            ),
          ],
        ),
      ),
    );
  }
}
