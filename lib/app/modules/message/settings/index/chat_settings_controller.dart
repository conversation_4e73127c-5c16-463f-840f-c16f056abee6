import 'dart:async';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/api_service.dart';
import 'package:inspector/app/data/group_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/message/chat/chat_binding.dart';
import 'package:inspector/app/modules/message/chat/chat_controller.dart';
import 'package:inspector/app/modules/message/chat/chat_view.dart';
import 'package:inspector/app/modules/message/chat_provider.dart';
import 'package:inspector/app/modules/message/settings/index/state.dart';
import 'package:inspector/app/routes/app_navigator.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';

class ChatSettingsPageController extends GetxController {
  ChatProvider provider = ChatProvider();

  final String? tag;
  ChatSettingsPageController(this.tag);

  final state = ChatSettingsPageState();

  ChatController get chatController => Get.find<ChatController>(tag: tag);

  bool isAdmin = false;

  @override
  void onInit() {
    super.onInit();

    var groupInfo = Get.arguments['conversation_info'];
    state.groupInfo.value = groupInfo;

    state.members.value = state.groupInfo.value?.groupInfo?.members ?? [];
    state.members.forEach((element) {
      if (element.imUserId == GlobalConst.userModel?.uid) {
        if (element.role == 4) {
          isAdmin = true;
          return;
        }
      }
    });
    // _getMembers();
  }

  Future<void> _getMembers() async {
    state.loading.value = true;
    unawaited(EasyLoading.show());
    try {
      state.members.value = await ApiService.getChatGroupMembers(groupId: state.groupInfo.value?.groupInfo?.id ?? 0);
    } finally {
      state.loading.value = false;
      unawaited(EasyLoading.dismiss());
    }
  }

  Future<void> exitGroup() async {
    await showCustomDialog(
      S.of(Get.context!).exit_group_chat_confirm,
      onConfirm: () {
        EasyLoading.show();
        ApiService.exitChatGroup(state.groupInfo.value?.groupInfo?.id ?? 0).then((response) async {
          if (response.isSuccess) {
            showToast(S.of(Get.context!).exit_group_chat_success);
            AppNavigator.close(2);
          } else {
            showToast(response.message);
          }
        }).whenComplete(() {
          EasyLoading.dismiss();
        });
      },
      cancel: true,
    );
  }

  void createConv(int userId) {
    EasyLoading.show();
    provider.createConv(userId).then((value) {
      if (value.isSuccess) {
        var gid = value.data['cid'] as int?;
        if (gid != null) {
          unawaited(Get.to(() => ChatView(tag: gid.toString()), arguments: {'cid': gid}, binding: ChatBinding(tag: gid.toString())));
        }
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }
}
