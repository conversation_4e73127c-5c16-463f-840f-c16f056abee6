import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/api_service.dart';
import 'package:inspector/app/modules/message/chat/chat_controller.dart';
import 'package:inspector/app/routes/app_navigator.dart';
import 'package:inspector/app/shared/components/basic/ibutton.dart';
import 'package:inspector/app/shared/components/form/itextfield.dart';
import 'package:inspector/app/shared/services/page_helper.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';

class UpdateGroupNamePage extends StatefulWidget {
  const UpdateGroupNamePage({Key? key}) : super(key: key);

  @override
  State<UpdateGroupNamePage> createState() => _UpdateGroupNamePageState();
}

class _UpdateGroupNamePageState extends State<UpdateGroupNamePage> {
  TextEditingController? _txtController;

  ChatController get chatController => Get.find<ChatController>();

  @override
  void initState() {
    super.initState();
    _txtController = TextEditingController(text: chatController.title.value);
  }

  @override
  void dispose() {
    _txtController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: PageHelper.getAppBar(title: S.of(Get.context!).group_chat_name_page_title, backgroundColor: DefaultStyle.pageBackground),
        body: Column(
          children: [
            Container(
              color: DefaultStyle.white,
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: DefaultStyle.spaceLarge, vertical: DefaultStyle.spaceLarge),
              child: ITextField(
                controller: _txtController,
                placeholder: S.of(Get.context!).group_chat_name_page_required,
                maxLength: 64,
              ),
            ),
            IButton(title: S.of(Get.context!).group_chat_name_save, onPressed: _save),
          ],
        ));
  }

  Future<void> _save() async {
    String groupName = _txtController!.text.trim();
    if (groupName == '') {
      showToast(S.of(Get.context!).group_chat_name_page_required);
      return;
    }
    await ApiService.updateChatGroupName(chatController.groupId!, groupName);
    chatController.title.value = groupName;
    showToast(S.of(Get.context!).group_chat_name_saved);
    AppNavigator.back();
  }
}
