class ChatMemberModel {
  late int id;
  late String nickname;
  late String avatar;
  late DateTime joinAt;

  static ChatMemberModel fromJson(Map<String, dynamic> json) {
    ChatMemberModel model = ChatMemberModel();

    model.id = json['id'];
    model.nickname = json['user_info']['nickname'];
    model.avatar = json['user_info']['avatar'];
    model.joinAt = DateTime.parse(json['join_at']);

    return model;
  }

  static List<ChatMemberModel> fromMapList(dynamic mapList) {
    List<ChatMemberModel> models = [];
    for (int i = 0; i < mapList.length; i++) {
      models.add(fromJson(mapList[i]));
    }
    return models;
  }
}
