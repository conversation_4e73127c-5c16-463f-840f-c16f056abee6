class SenderInfo {
  late int id;
  String? username;
  late String nickname;
  late String avatar;
  int? sex;
  late String signature;
  String? city;
  String? realname;

  static SenderInfo fromJson(Map<String, dynamic> json) {
    SenderInfo model = SenderInfo();

    model.id = int.parse(json['id'].toString());
    model.username = json['username'];
    model.nickname = json['nickname'];
    model.avatar = json['avatar'];
    model.sex = json['sex'] == null ? null : int.parse(json['sex'].toString());
    model.signature = json['signature'];
    model.city = json['city'];
    model.realname = json['realname'];

    return model;
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};

    data['id'] = id;
    data['username'] = username;
    data['nickname'] = nickname;
    data['avatar'] = avatar;
    data['sex'] = sex;
    data['signature'] = signature;
    data['city'] = city;
    data['realname'] = realname;

    return data;
  }
}

class RecordModel {
  late int id;
  late String content;
  late int type;
  late int sender;
  late DateTime createdAt;
  String? senderRealname;
  late String brief;

  static RecordModel fromJson(Map<String, dynamic> json) {
    RecordModel model = RecordModel();

    model.id = int.parse(json['id'].toString());
    model.content = json['content'];
    model.type = int.parse(json['type'].toString());
    model.sender = int.parse(json['sender'].toString());
    model.createdAt = DateTime.parse(json['created_at']);
    model.senderRealname = json['sender_realname'];
    model.brief = json['brief'];

    return model;
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};

    data['id'] = id;
    data['content'] = content;
    data['type'] = type;
    data['sender'] = sender;
    data['created_at'] = createdAt;
    data['sender_realname'] = senderRealname;
    data['brief'] = brief;

    return data;
  }
}

class ChatModel {
  late int id;
  late int sender;
  late int receiver;
  int? recordId;
  int? mix;
  late int scene;
  String? direction;
  late int isRead;
  late DateTime createdAt;
  late DateTime updatedAt;
  late SenderInfo senderInfo;
  late RecordModel record;

  static ChatModel fromJson(Map<String, dynamic> json) {
    ChatModel model = ChatModel();

    model.id = int.parse(json['id'].toString());
    model.sender = int.parse(json['sender'].toString());
    model.receiver = int.parse(json['receiver'].toString());
    model.recordId = json['record_id'] == null ? null : int.parse(json['record_id'].toString());
    model.mix = json['mix'] == null ? null : int.parse(json['mix'].toString());
    model.scene = int.parse(json['scene'].toString());
    model.direction = json['direction'];
    model.isRead = int.parse(json['is_read'].toString());
    model.createdAt = DateTime.parse(json['created_at']);
    model.updatedAt = DateTime.parse(json['updated_at']);
    model.senderInfo = SenderInfo.fromJson(json['sender_info']);
    model.record = RecordModel.fromJson(json['record']);

    return model;
  }

  static List<ChatModel> fromMapList(dynamic mapList) {
    List<ChatModel> models = [];
    for (int i = 0; i < mapList.length; i++) {
      models.add(fromJson(mapList[i]));
    }
    return models;
  }
}
