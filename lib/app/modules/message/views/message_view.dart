// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:inspector/app/data/conversation_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/message/chat/chat_binding.dart';
import 'package:inspector/app/modules/message/chat/chat_view.dart';
import 'package:inspector/app/modules/message/notice/notice_binding.dart';
import 'package:inspector/app/modules/message/notice/notice_view.dart';
import 'package:inspector/app/modules/message/socket/socket_channel.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/message/controllers/message_controller.dart';
import 'package:inspector/app/modules/tabbar/controllers/tabbar_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/shared/extensions/string.dart';
import 'package:inspector/app/theme/app_theme.dart';

import '../../../../generated/l10n.dart';

class IMessageView extends GetView<MessageController> {
  const IMessageView({Key? key}) : super(key: key);

  @override
  MessageController get controller => GetInstance().putOrFind<MessageController>(() => MessageController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: Text(S.of(Get.context!).tab_message), centerTitle: true, actions: GlobalConst.userModel?.role == UserRole.admin ? [] : [_customService]),
      body: SafeArea(
        child: Obx(() {
          return SmartRefresher(
            controller: controller.refreshController,
            onRefresh: () => controller.fetchList(),
            child: ListView.separated(
              itemCount: controller.conversations.isEmpty ? 1 : controller.conversations.length,
              separatorBuilder: (context, index) => Container(
                padding: const EdgeInsets.only(left: 69),
                color: Colors.white,
                child: Container(
                  color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                  height: 1,
                ),
              ),
              itemBuilder: (ctx, index) {
                if (controller.conversations.isEmpty) {
                  return SizedBox(
                    width: double.infinity,
                    height: Get.height / 2,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.hourglass_empty,
                            size: 40,
                            color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                          ),
                          const SizedBox(height: 10),
                          Text(
                            S.of(Get.context!).no_data,
                            style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    var model = controller.conversations[index];
                    TabbarController tabbarController = Get.find<TabbarController>();
                    tabbarController.refreshUnreadMessageCount(tabbarController.unread.value - (model.unread ?? 0));
                    String title = model.getConvName();
                    if (model is PublishConvRows) {
                      Get.to(() => ChatView(tag: model.id.toString()),
                              preventDuplicates: true,
                              arguments: {'cid': model.id, 'title': title, 'type': model.type},
                              binding: ChatBinding(tag: model.id.toString()))
                          ?.then((value) {
                        controller.fetchList();
                      });
                      try {
                        SocketChannel.markRead(model.lastMsg?.recordId ?? 0);
                      } catch (_) {
                        logger.w('markRead failed');
                      }
                    } else if (model is NoticeConvRows) {
                      Get.to(() => NoticeView(tag: model.id.toString()),
                              preventDuplicates: true,
                              arguments: {'cid': model.type, 'title': title, 'type': model.type},
                              binding: NoticeBinding(tag: model.id.toString()))
                          ?.then((value) {
                        controller.fetchList();
                      });
                      try {
                        SocketChannel.markRead(0, msgType: model.type ?? 0);
                      } catch (_) {
                        logger.w('markRead failed');
                      }
                    }
                  },
                  child: _itemView(index),
                );
              },
            ),
          );
        }),
      ),
    );
  }

  Widget get _customService {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        controller.getCustomService();
        // Get.toNamed(Routes.ORDER_EDIT, arguments: {'editable': true, 'orderId': controller.orderId})?.then((value) {
        //   controller.fetchOrderDetail();
        // });
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        alignment: Alignment.center,
        child: const Icon(Icons.support_agent, size: 24, color: MColor.skin),
      ),
    );
  }

  Widget _itemView(int index) {
    var model = controller.conversations[index];

    var unread = model.unread;
    String text = model.getMsgDesc();
    String avatar = model.getConvAvatar();
    String title = model.getConvName();
    text = text.replaceAll('\n', ' ');

    String? datetime = Helper.parseMsgTime(model.updatedAt!);
    // ignore: non_constant_identifier_names

    return Builder(builder: (context) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        color: Theme.of(Get.context!).colorScheme.conatiner,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 50,
              height: 50,
              child: Stack(
                children: [
                  ClipRRect(borderRadius: BorderRadius.circular(25), child: Image.network(avatar, fit: BoxFit.cover)),
                  Visibility(
                    visible: unread! > 0,
                    child: Align(
                      alignment: Alignment.topRight,
                      child: Container(
                        width: 16,
                        height: 16,
                        margin: const EdgeInsets.only(top: 2),
                        decoration: BoxDecoration(
                          color: MColor.xFFE95332,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                            child: Text('$unread',
                                textAlign: TextAlign.center,
                                style: TextStyle(fontSize: 12, color: context.isDarkMode ? DarkColor.xFFFFFFFF : MColor.xFFFFFFFF))),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 6),
            Expanded(
              child: SizedBox(
                height: 50,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  title,
                                  style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Text(
                                datetime,
                                style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
                              ),
                            ],
                          ),
                          Text(
                            text.clearHtml().replaceAll('&nbsp;', ' '),
                            style: MFont.regular12.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
}
