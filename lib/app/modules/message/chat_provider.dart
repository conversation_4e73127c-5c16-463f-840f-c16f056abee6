import 'package:inspector/app/config/api.dart';
import 'package:inspector/app/data/group_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/data/conversation_entity.dart';
import 'package:inspector/app/data/receive_message.dart';
import 'package:inspector/app/modules/message/socket/socket_channel.dart';
import 'package:inspector/app/tools/public_provider.dart';

class ChatProvider {
  //获取聊天记录
  Future<BaseModel<PublishConvEntity>> getConversationList(int page, int pageSize) {
    //var params = {'limit': pageSize, 'page': page};
    return PublicProvider.request<PublishConvEntity>(
      path: '${Api.conversationList}?page=$page&limit=$pageSize&is_app=1',
      isPost: false,
      chat: true,
    );
  }

  //消息列表
  Future<BaseModel<List<ReceiveMessage>>> takeChatMsg(int id, int recordId, int pageSize) {
    return PublicProvider.request<List<ReceiveMessage>>(path: Api.chatRecords, isPost: true, params: {'cid': id, 'record_id': recordId, 'limit': pageSize});
  }

  Future<BaseModel<List<NoticeConvRows>>> getNoticeList(int pageSize, int page, int type) {
    var params = {'limit': pageSize, 'page': page, 'type': type};
    return PublicProvider.request<List<NoticeConvRows>>(path: '${Api.noticeList}?page=$page&limit=$pageSize&type=$type', isPost: false);
  }

  Future<BaseModel<PublishConvRows>> getCoversationInfo(int chatId) {
    return PublicProvider.request<PublishConvRows>(path: '${Api.chatInfo}/$chatId', isPost: false);
  }

  Future<BaseModel<GroupEntity>> getGroupInfo(int groupId) {
    return PublicProvider.request<GroupEntity>(path: Api.imGroupInfo, params: {'group_id': groupId}, isPost: true);
  }

  Future<BaseModel<dynamic>> getCustomService() {
    return PublicProvider.request(path: Api.contactService, isPost: false);
  }

  //发送文本消息
  void sendText(int cid, String content, int msgType, bool isGroup) {
    SocketChannel.sendMessage(cid, content, msgType, isGroup);
  }

  void sendOrder(int cid, int orderId, bool isGroup) {
    SocketChannel.sendMessage(cid, orderId.toString(), 5, isGroup);
  }

  Future<BaseModel<AdminListEntity>> getAdmins() async {
    return PublicProvider.request<AdminListEntity>(
      path: 'chat/adminlist',
      isPost: false,
    );
  }

  Future<BaseModel<dynamic>> createConv(int userId) {
    return PublicProvider.request<dynamic>(path: Api.createConv, params: {'to': userId});
  }

  static Future<BaseModel<dynamic>> batchSendTemplateMessage(List<int>? imUserIds, int msgId, int? orderId) {
    if (imUserIds == null || orderId == null) {
      return Future.value();
    }
    return PublicProvider.request(path: Api.batchTemplateMsg, params: {'im_user_ids': imUserIds, 'msg_id': msgId, 'order_id': orderId});
  }

  static Future<BaseModel<dynamic>> submitComplaint(int replyId, String type, String note) {
    return PublicProvider.request(path: Api.chatComplaint, params: {'record_id': replyId, 'note': note, 'type': type}, isPost: true);
  }

  static Future<BaseModel<dynamic>> submitUserReport(int userId, String type, String note) {
    return PublicProvider.request(path: Api.userComplaint, params: {'user_id': userId, 'note': note, 'type': type}, isPost: true);
  }
}
