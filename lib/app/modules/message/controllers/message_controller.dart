import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/message/chat/chat_binding.dart';
import 'package:inspector/app/modules/message/chat/chat_view.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/data/conversation_entity.dart';
import 'package:inspector/app/modules/message/chat_provider.dart';
import 'package:inspector/app/modules/tabbar/controllers/tabbar_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';
import '../../../tools/storage_util.dart';
import '../../auth/account_service.dart';

class MessageController extends GetxController with WidgetsBindingObserver {
  RefreshController refreshController = RefreshController();
  ChatProvider provider = ChatProvider();
  final lists = <int>[0, 1].obs;
  final conversations = <ConvRows>[].obs;
  final admin = RxList<ChatUserEntity>([]);
  @override
  void onInit() {
    provider.getAdmins().then((value) {
      if (value.isSuccess) {
        admin.value = value.data?.admins ?? [];
      }
    });
    WidgetsBinding.instance.addObserver(this);
    super.onInit();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    logger.i('MessageController state changed to $state');
    if (state == AppLifecycleState.resumed) {
      _refreshList();
    }
  }

  @override
  void onReady() {
    super.onReady();
    _refreshList();
  }

  void _refreshList() {
    logger.i('_refreshList');
    fetchList().then((value) {
      Get.find<TabbarController>().refreshUnreadMessageCount(conversations.fold(0, (p, e) => p + (e.unread ?? 0)));
    });
  }

  Future<void> fetchList() async {
    logger.i('fetchList');
    var total = 0;
    var value = await provider.getConversationList(1, 50);

    if (value.isSuccess) {
      var models = value.data?.rows ?? [];
      total = value.data?.total ?? 0;
      var notices = value.data?.notices ?? [];
      total += value.data?.noticeTotal ?? 0;

      conversations.value = [];
      conversations.addAll(notices);
      conversations.addAll(models);
    } else {
      conversations.value = [];
      if (value.code == -2) {
        showToast(S.of(Get.context!).login_expired);
        int? userId = GlobalConst.tempModel?.uid;
        if (userId != null) {
          await AccountService.instance.removeSavedAccount(userId);
        }
        GlobalConst.tempModel = null;
        await StorageUtil.remove(Constant.kUser);
        await AccountService.instance.saveToken('');
        unawaited(Get.offAllNamed(Routes.AUTH_LOGIN));
      } else {
        showToast(value.message ?? '');
      }
    }
    conversations.refresh();

    refreshController.refreshCompleted();
    if (total <= conversations.length && conversations.isNotEmpty) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  Future<void> getCustomService() async {
    unawaited(EasyLoading.show());
    var value = await provider.getCustomService();
    if (value.isSuccess) {
      var gid = value.data['cid'] as int?;
      if (gid != null) {
        unawaited(Get.to(() => ChatView(tag: gid.toString()), arguments: {'cid': gid}, binding: ChatBinding(tag: gid.toString())));
        _refreshList();
      }
    } else {
      showToast(value.message ?? '');
    }
    unawaited(EasyLoading.dismiss());
  }

  @override
  void onClose() {
    refreshController.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  void refreshConversation(Map<String, dynamic> map) {
    var model = PublishConvRows.fromJson(map);
    int index = conversations.indexWhere((e) => e.id == model.id);
    if (index > -1) conversations.removeAt(index);

    conversations.insert(0, model);
    conversations.refresh();
  }

  void createConv(int userId, {OrderDetailEntity? orderDetailEntity}) {
    EasyLoading.show();
    provider.createConv(userId).then((value) {
      if (value.isSuccess) {
        var gid = value.data['cid'] as int?;
        if (gid != null) {
          unawaited(Get.to(() => ChatView(tag: gid.toString()),
              arguments: {'cid': gid, 'order_detail_entity': orderDetailEntity}, binding: ChatBinding(tag: gid.toString())));
        }
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }
}
