import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/message/chat/chat_controller.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';
import '../../../config/design.dart';
import '../../../tools/privacy_helper.dart';

class BottomPanelIcon {
  IconData icon;
  String title;

  BottomPanelIcon(this.icon, this.title);
}

class ChatInputBar extends GetView<ChatController> {
  @override
  final String? tag;

  final List<BottomPanelIcon> bottomIcons = [
    BottomPanelIcon(Icons.image_outlined, S.of(Get.context!).chat_panel_album),
    BottomPanelIcon(Icons.photo_camera_outlined, S.of(Get.context!).chat_panel_camera),
    BottomPanelIcon(Icons.folder_outlined, S.of(Get.context!).chat_panel_file),
  ];

  ChatInputBar({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget current = Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(child: _textInput),
        const SizedBox(width: 5.0),
        _rightButton,
      ],
    );

    current = Container(
      constraints: const BoxConstraints(maxHeight: 110, minHeight: 50),
      decoration: BoxDecoration(border: Border(top: BorderSide(color: Theme.of(context).dividerColor, width: 0.2))),
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: current,
    );

    return Container(
      color: MColor.xFFEEEEEE,
      child: Column(
        children: [current, _bottomPanel],
      ),
    );
  }

  Widget get _textInput {
    Widget current = TextField(
      // style: TextStyle(color: Theme.of(context).colorScheme.textPrimary),
      // cursorColor: Theme.of(context).colorScheme.cursor,
      controller: controller.txtController,
      maxLength: 1000,
      maxLines: null,
      autofocus: false,
      focusNode: controller.inputFocusNode,
      decoration: const InputDecoration(
        isCollapsed: true,
        counterText: '',
        hintText: '',
        // hintStyle: TextStyle(color: Theme.of(context).colorScheme.textPlaceholder),
        border: InputBorder.none,
        contentPadding: EdgeInsets.only(left: 5, top: 10, right: 5, bottom: 10),
      ),
      onChanged: (String v) {
        if (v.trim() != '') {
          if (!controller.canSend.value) controller.canSend.value = true;
        } else {
          if (controller.canSend.value) controller.canSend.value = false;
        }
      },
    );

    current = Container(
      margin: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 5.0),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(5), color: Colors.white),
      child: current,
    );

    return current;
  }

  Widget get _rightButton {
    return Obx(() {
      if (!controller.canSend.value) {
        Widget btn = Container(
          padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 4.0),
          child: const Icon(
            Icons.add_circle_outline,
            size: 24,
          ),
        );
        return GestureDetector(onTap: controller.onPlusClicked, child: btn);
      } else {
        Widget btn = Container(
          decoration: BoxDecoration(color: const Color(0xFF67C23A), borderRadius: BorderRadius.circular(3)),
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 4.0),
          child: Text(S.of(Get.context!).public_send, style: const TextStyle(color: Colors.white)),
        );
        return GestureDetector(onTap: controller.sendText, child: btn);
      }
    });
  }

  Widget get _bottomPanel {
    return Obx(() {
      if (controller.showPopupPanel.value) {
        return GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 4, mainAxisSpacing: 0),
            itemBuilder: (context, position) {
              return _buildBottomIcon(bottomIcons[position]);
            },
            itemCount: bottomIcons.length);
      } else {
        return const SizedBox();
      }
    });
  }

  Widget _buildBottomIcon(BottomPanelIcon icon) {
    return GestureDetector(
      onTap: () async {
        if (icon.icon == Icons.folder_outlined) {
          if (!hasShowCameraTips()) {
            await showCameraTips();
          }
          FilePickerResult? result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: ['pdf', 'docx', 'txt', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'zip', 'rar'],
          );
          if (result == null || result.files.single.path == null) return;
          await controller.sendFile(File(result.files.single.path!));
        } else if (icon.icon == Icons.photo_camera_outlined) {
          if (!hasShowCameraTips()) {
            await showCameraTips();
          }
          String? image = await FilesPicker.openCamera();
          if (image == null || image.isEmpty) {
            return;
          }
          await controller.sendFile(File(image));
        } else if (icon.icon == Icons.image_outlined) {
          if (!hasShowCameraTips()) {
            await showCameraTips();
          }
          List<String> images = await FilesPicker.openImage(false, enableCrop: false, selectCount: 100);
          if (images.isEmpty) {
            return;
          }
          images.forEach((element) async {
            await controller.sendFile(File(element));
          });
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon.icon,
            size: 30,
          ),
          const SizedBox(height: 8),
          Text(icon.title)
        ],
      ),
    );
  }
}
