import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:inspector/app/data/receive_message.dart';
import 'package:inspector/app/data/sending_message.dart';
import 'package:inspector/app/modules/auth/account_service.dart';
import 'package:inspector/app/modules/message/socket/message_processor.dart';
import 'package:inspector/app/modules/message/socket/server_time_calibration.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:uuid/uuid.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class SocketChannel {
  static WebSocketChannel? _socket;
  static Timer? _timer;
  static Timer? _timeoutTimer;
  static int heartBeatCount = 0;
  static Uuid uuid = const Uuid();

  static Future<WebSocket> _createSocket() async {
    logger.i('_createSocket');
    var socket = await WebSocket.connect(Server.socket).timeout(const Duration(milliseconds: 30000)).onError((error, stackTrace) async {
      logger.e('connect error $error ${Server.socket}');
      await Future.delayed(const Duration(seconds: 5));
      return _createSocket();
    });
    return socket;
  }

  static Future<void> startConnect() async {
    final socket = await _createSocket();
    _socket = IOWebSocketChannel(socket);
    _socket!.stream.listen((event) {
      logger.i('onEvent $event');
      _timeoutDetect();
      Map<String, dynamic> items = json.decode(event);
      if (items.containsKey('type')) {
        var type = items['type'];
        if (type == 'init') {
          String clientId = items['client_id'];
          logger.i('clientId: $clientId');
          _socketLogin();
        } else if (type == 'heart') {
          int serverTime = items['ts'];
          ServerTimeCalibration.calibrateServerTime(serverTime);
        } else if (type == 'receive') {
          var rm = ReceiveMessage.fromJson(items['content']);
          MessageProcessor.instance.onReceiveMessage(rm);
        }
      }
    }, onDone: () {
      logger.i('onDone');
      _reconnect();
    }, onError: (Object error, StackTrace stack) {
      logger.i('onError $error $stack');
      _reconnect();
    });
    _timerHeartBeat();
  }

  static Future<void> sendMessage(int cid, String msg, int msgType, bool isGroup) async {
    SendingMessage sm = SendingMessage();
    sm.msg = msg;
    sm.cid = cid;
    sm.scene = isGroup ? 'group' : 'normal';
    sm.type = msgType;
    sm.uuid = uuid.v1();
    sm.timestamp = ServerTimeCalibration.getServerTime();
    MessageProcessor.instance.onSendMessage(sm);
    Map<String, dynamic> data = {'type': 'send', 'content': sm.toJson()};
    var jsonData = json.encode(data);
    logger.i('message $jsonData');
    try {
      _socket!.sink.add(jsonData);
    } catch (_) {
      logger.w('sendMessage failed');
    }
  }

  static Future<void> markRead(int recordId, {int? msgType}) async {
    Map<String, dynamic> data = {'type': 'read', 'record_id': recordId};
    if (msgType != null) {
      data['msg_type'] = msgType;
    }
    var jsonData = json.encode(data);
    logger.i('markRead $jsonData');
    try {
      _socket!.sink.add(jsonData);
    } catch (_) {
      logger.w('markRead failed');
    }
  }

  static Future<void> _socketLogin() async {
    logger.i('socketLogin');
    var token = AccountService.instance.getToken();
    Map<String, dynamic> data = {'type': 'login', 'token': token};
    logger.i('login $data');
    var jsonData = json.encode(data);
    try {
      _socket!.sink.add(jsonData);
    } catch (_) {
      logger.w('socketLogin failed');
    }
  }

  static void _sendHeartPacket() async {
    logger.i('_sendHeartPacket');
    Map<String, dynamic> data = {
      'type': 'heart',
    };
    var jsonData = json.encode(data);
    try {
      _socket!.sink.add(jsonData);
    } catch (_) {
      logger.w('_sendHeartPacket failed');
    }
    heartBeatCount++;
  }

  static void _timerHeartBeat() {
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _sendHeartPacket();
    });
  }

  static void _timeoutDetect() {
    _timeoutTimer?.cancel();
    _timeoutTimer = Timer(const Duration(seconds: 60), () {
      logger.i('timeout detected');
      stopConnect();
    });
  }

  static void stopConnect() {
    logger.i('stopConnect');
    _timeoutTimer?.cancel();
    _timer?.cancel();
    _socket?.sink.close();
  }

  static void _reconnect() {
    logger.i('reconnect');
    stopConnect();
    var token = AccountService.instance.getToken();
    if (token.isNotEmpty) {
      startConnect();
    } else {
      logger.w('reconnect but no token found');
    }
  }
}
