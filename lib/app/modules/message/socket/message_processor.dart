import 'dart:async';
import 'dart:collection';

import 'package:get/get.dart';
import 'package:inspector/app/data/chat_message.dart';
import 'package:inspector/app/data/receive_message.dart';
import 'package:inspector/app/data/sending_message.dart';
import 'package:inspector/app/modules/message/chat/chat_controller.dart';
import 'package:inspector/app/modules/message/controllers/message_controller.dart';
import 'package:inspector/app/modules/message/models/chat.dart';
import 'package:inspector/app/modules/message/socket/server_time_calibration.dart';
import 'package:inspector/app/tools/tools.dart';

class MessageProcessor {
  Map<int, Map<String, SendingMessage>> waitingList = HashMap();

  Map<int, Map<String, SendingMessage>> failedMessage = HashMap();

  Map<int, Map<String, ChatMessage>> chatMessages = HashMap();

  late Timer _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
    _timeoutMessage();
  });

// 静态变量_instance，存储唯一对象
  static MessageProcessor? _instance;

  MessageProcessor._internal() {
    _instance = this;
  }

  static MessageProcessor get instance => _instance ?? MessageProcessor._internal();

  void onSendMessage(SendingMessage sm) {
    logger.i('onSendMessage');
    int cid = sm.cid;
    Map<String, SendingMessage> messages = waitingList[cid] ?? <String, SendingMessage>{};
    messages[sm.uuid] = sm;
    waitingList[cid] = messages;

    Map<String, ChatMessage> messageList = chatMessages[cid] ?? <String, ChatMessage>{};
    messageList[sm.uuid] = sm;
    chatMessages[cid] = messageList;
    try {
      var chatController = Get.find<ChatController>(tag: cid.toString());
      chatController.onSendingMessage(sm);
    } catch (_) {
    } finally {
      GetInstance().putOrFind<MessageController>(() => MessageController()).fetchList();
    }
  }

  void onReceiveMessage(ReceiveMessage message) {
    logger.i('onReceiveMessage');
    int cid = message.cid;
    Map<String, ChatMessage> messageList = chatMessages[cid] ?? <String, ChatMessage>{};
    messageList[message.uuid] = message;
    chatMessages[cid] = messageList;
    Map<String, SendingMessage>? waitings = waitingList[cid];
    if (waitings?.containsKey(message.uuid) ?? false) {
      waitings?.remove(message.uuid);
    }
    Map<String, SendingMessage>? failures = failedMessage[cid];
    if (failures?.containsKey(message.uuid) ?? false) {
      failures?.remove(message.uuid);
    }
    try {
      var chatController = Get.find<ChatController>(tag: cid.toString());
      chatController.onReceiveMessage(message);
    } catch (_) {
    } finally {
      GetInstance().putOrFind<MessageController>(() => MessageController()).fetchList();
    }
  }

  void _timeoutMessage() {
    waitingList.forEach((cid, msgList) {
      List<SendingMessage> removed = [];
      msgList.removeWhere((key, msg) {
        if ((ServerTimeCalibration.getServerTime() - msg.timestamp).abs() >= 30000) {
          msg.isTimeout = true;
          removed.add(msg);
          return true;
        }
        return false;
      });
      Map<String, SendingMessage> failed = failedMessage[cid] ?? {};
      var removedMap = {for (var element in removed) element.uuid: element};
      failed.addAll(removedMap);
      failedMessage[cid] = failed;
    });
  }

  void destroy() {
    waitingList.clear();
    failedMessage.clear();
    chatMessages.clear();
    _timer.cancel();
  }
}
