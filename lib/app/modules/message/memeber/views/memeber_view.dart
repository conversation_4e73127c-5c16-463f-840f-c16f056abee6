import 'dart:io';

import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/message/memeber/controllers/memeber_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/generated/l10n.dart';

class MemeberView extends GetView<MemeberController> {
  const MemeberView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).group_title),
        centerTitle: true,
      ),
      body: Obx(() {
        return ListView.builder(
          itemCount: controller.members.length,
          itemBuilder: (ctx, index) {
            return _itemView(index);
          },
        );
      }),
    );
  }

  Widget _itemView(int index) {
    var model = controller.members[index];
    var isFile = File('//${model.user?.avatarThumbPath}').existsSync();
    var name = model.user?.nickname ?? '';
    if (name.isEmpty) {
      name = model.user?.username ?? '';
    }
    if (name.isEmpty) {
      name = '-';
    }

    return Builder(builder: (context) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Get.toNamed(Routes.CHAT, arguments: model.user!.username);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE, width: 1)),
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(25),
                child: Container(
                  color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                  width: 50,
                  height: 50,
                  child: isFile
                      ? Image.file(File('//${model.user?.avatarThumbPath}'))
                      : Container(
                          decoration: BoxDecoration(
                            color: context.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC,
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  name,
                  style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
