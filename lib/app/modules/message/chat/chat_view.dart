// ignore_for_file: must_be_immutable, constant_identifier_names

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/chat_message.dart';
import 'package:inspector/app/data/conversation_entity.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/receive_message.dart';
import 'package:inspector/app/data/sending_message.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/message/chat/chat_top_order_preview_widget.dart';
import 'package:inspector/app/modules/message/settings/index/binding.dart';
import 'package:inspector/app/modules/message/settings/index/chat_settings_view.dart';
import 'package:inspector/app/modules/message/widgets/chat_input_bar.dart';
import 'package:inspector/app/modules/profile/user_profile/user_profile_binding.dart';
import 'package:inspector/app/modules/profile/user_profile/user_profile_view.dart';
import 'package:inspector/app/modules/purchase/purchase_complaint/purchase_complaint_view.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/message/chat/chat_controller.dart';
import 'package:inspector/app/modules/message/models/chat.dart';
import 'package:inspector/app/routes/app_navigator.dart';
import 'package:inspector/app/shared/services/page_helper.dart';
import 'package:inspector/app/shared/widgets/avatar.dart';

import '../../../../generated/l10n.dart';

const _PAGE_SPACE = 8.0;
const _AVATAR_SIZE = 40.0;

class ChatView extends GetView<ChatController> {
  @override
  final String? tag;

  const ChatView({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    RefreshController refreshController = RefreshController();
    return Scaffold(
      appBar: PageHelper.getAppBar(
        titleWidget: Obx(() => Text(controller.title.value)),
        actionIcon: controller.isGroup.value ? Icons.more_horiz : null,
        actionClick: () {
          Get.to(() => ChatSettingsPage(tag: tag), arguments: {'conversation_info': controller.conversationInfo}, binding: ChatSettingsPageBinding(tag))
              ?.then((value) {});
        },
      ),
      body: Obx(() {
        bool showSendOrder = controller.showSendOrder.value;
        bool showTopOrder = controller.boundOrderInfo.value != null && !showSendOrder;

        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (showTopOrder) ...{ChatTopOrderPreviewWidget(controller.boundOrderInfo.value!, tag: tag)},
            Expanded(
              child: SmartRefresher(
                controller: refreshController,
                onLoading: () => controller.loadMore(refreshController),
                enablePullUp: true,
                enablePullDown: false,
                child: ListView.separated(
                    reverse: true,
                    padding: const EdgeInsets.symmetric(horizontal: _PAGE_SPACE),
                    controller: controller.scrollController,
                    itemCount: controller.messages.length + (showSendOrder ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (showSendOrder) {
                        return index == 0 ? _SendOrderWidget(tag: tag) : _Message(message: List.of(controller.messages)[index - 1]);
                      } else {
                        final msg = controller.messages[index];
                        return GestureDetector(
                          child: _Message(message: msg),
                          onLongPress: () {
                            int? recordId;
                            bool isOwner = false;
                            if (msg is SendingMessage) {
                              isOwner = true;
                            } else if (msg is ReceiveMessage) {
                              if ((msg as ReceiveMessage).sender?.imUserId == GlobalConst.userModel?.imUserId) {
                                isOwner = true;
                              }
                              recordId = msg.recordId;
                            }

                            if (isOwner || recordId == null) {
                              return;
                            }

                            showCupertinoModalPopup(
                                context: context,
                                builder: (context) {
                                  return CupertinoActionSheet(
                                    cancelButton: CupertinoActionSheetAction(
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: Text(S.of(Get.context!).public_cancel,
                                          style: TextStyle(
                                              fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333, fontWeight: FontWeight.bold)),
                                    ),
                                    actions: <Widget>[
                                      CupertinoActionSheetAction(
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                          Get.bottomSheet(
                                            SafeArea(
                                              child: BottomSheet(
                                                onClosing: () {},
                                                enableDrag: false,
                                                builder: (context) {
                                                  return PurchaseComplaintView(recordId!, 1, tag: tag);
                                                },
                                              ),
                                            ),
                                            isScrollControlled: true,
                                            ignoreSafeArea: false,
                                            persistent: false,
                                          ).then((value) {});
                                        },
                                        child: Text(
                                          S.of(Get.context!).purchase_detail_more_report,
                                          style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                                        ),
                                      ),
                                    ],
                                  );
                                });
                          },
                        );
                      }
                    },
                    separatorBuilder: (context, index) {
                      if (showSendOrder) {
                        if (index == 0) {
                          return const SizedBox();
                        } else {
                          return _DateMessage(current: List.of(controller.messages)[index - 1], prev: List.of(controller.messages)[index]);
                        }
                      } else {
                        return _DateMessage(current: List.of(controller.messages)[index], prev: List.of(controller.messages)[index + 1]);
                      }
                    }),
              ),
            ),
            ChatInputBar(tag: tag),
          ],
        );
      }),
    );
  }
}

class _SendOrderWidget extends GetView<ChatController> {
  @override
  final String? tag;

  const _SendOrderWidget({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    double totalWidth = size.width - 46;
    OrderDetailEntity entity = controller.boundOrderInfo.value!;
    return Container(
      decoration: const BoxDecoration(
        color: MColor.xFFF4F6F9,
        borderRadius: BorderRadius.all(
          Radius.circular(12.5),
        ),
      ),
      padding: const EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 10),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, mainAxisSize: MainAxisSize.max, children: [
        SizedBox(
          width: totalWidth / 4 * 3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (entity.city != null) ...{
                _orderInfoItem(S.of(Get.context!).order_detail_inspection_city, controller.boundOrderInfo.value!.city ?? ''),
              },
              if (entity.productName?.isNotEmpty ?? false) ...{
                _orderInfoItem(S.of(Get.context!).order_goods_name, entity.productName == '暂无' ? '' : entity.productName),
              },
              _orderInfoItem(S.of(Get.context!).order_detail_inspection_time, entity.inspectTime),
              _orderInfoItem(
                S.of(Get.context!).order_detail_inspection_type,
                Helper.parseOrderType(entity.type ?? 0),
              ),
            ],
          ),
        ),
        SizedBox(
          width: totalWidth / 4,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                margin: const EdgeInsets.only(right: 15),
                child: GestureDetector(
                  onTap: () {
                    controller.showSendOrder.value = false;
                  },
                  child: const Icon(
                    Icons.close,
                    size: 18,
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              SizedBox(
                height: 28,
                child: TextButton(
                  onPressed: () {
                    controller.sendOrderMsg(controller.boundOrderInfo.value!.orderId!);
                    controller.showSendOrder.value = false;
                  },
                  style: ButtonStyle(
                      shape: MaterialStateProperty.all(const StadiumBorder()),
                      backgroundColor: MaterialStateProperty.all(MColor.xFFE95332),
                      padding: MaterialStateProperty.all(const EdgeInsets.symmetric(horizontal: 15, vertical: 0))),
                  child: Text(
                    S.of(Get.context!).chat_send_order,
                    style: MFont.medium12.apply(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ]),
    );
  }
}

Widget _orderInfoItem(String? title, String? value) {
  return Container(
    padding: const EdgeInsets.only(right: 12, top: 2, bottom: 4),
    child: Builder(builder: (context) {
      return Row(
        children: [
          Text(
            '$title：',
            style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
          ),
          Expanded(
            child: Text(
              value ?? '-',
              style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
    }),
  );
}

class _DateMessage extends StatelessWidget {
  final ChatMessage current;
  final ChatMessage? prev;
  const _DateMessage({required this.current, this.prev, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    DateTime currentTime = DateTime.fromMillisecondsSinceEpoch(current.timestamp);
    DateTime? prevTime = prev == null ? null : DateTime.fromMillisecondsSinceEpoch(prev!.timestamp);
    if (currentTime.toLocal().day == prevTime?.toLocal().day) return const SizedBox();

    return Container(
      padding: const EdgeInsets.only(top: 10.0, bottom: 10.0),
      child: Row(
        children: [
          const Expanded(flex: 3, child: Divider()),
          const SizedBox(width: 10.0),
          Center(child: Text(_getDate(currentTime), style: const TextStyle(color: Colors.black))),
          const SizedBox(width: 10.0),
          const Expanded(flex: 3, child: Divider()),
        ],
      ),
    );
  }

  String _getDate(DateTime dt) {
    var now = DateTime.now();

    var sameYear = now.year == dt.year;

    var sameDay = sameYear && now.month == dt.month && now.day == dt.day;

    if (sameDay) return S.of(Get.context!).today;

    if (now.add(const Duration(days: -1)).day == dt.day) return S.of(Get.context!).yesterday;

    if (sameYear) return '${_z(dt.month)}-${_z(dt.day)}';

    return '${dt.year.toString().substring(2)}-${_z(dt.month)}-${_z(dt.day)}';
  }

  String _z(int i) => i < 10 ? '0$i' : '$i';
}

class _Message extends StatelessWidget {
  final ChatMessage message;
  bool _isOwner = false;
  String? senderName;
  bool showSender = false;

  _Message({required this.message, Key? key}) : super(key: key) {
    if (message is SendingMessage) {
      _isOwner = true;
    } else if (message is ReceiveMessage) {
      if ((message as ReceiveMessage).sender?.imUserId == GlobalConst.userModel?.imUserId) {
        _isOwner = true;
      } else {
        ChatUserEntity? sender = (message as ReceiveMessage).sender;
        senderName = sender?.name;
        senderName = '$senderName (${sender?.role?.name})';
        showSender = true;
      }
    }
  }

  Avatar get _avatar {
    var headUrl = '';
    var name = '';
    if (_isOwner) {
      headUrl = GlobalConst.userModel?.head ?? '';
      name = GlobalConst.userModel?.name ?? '';
    } else {
      headUrl = (message as ReceiveMessage).sender?.head ?? '';
      name = (message as ReceiveMessage).sender?.name ?? '';
    }
    return Avatar(url: headUrl, displayName: name, size: _AVATAR_SIZE);
  }

  @override
  Widget build(BuildContext context) {
    Widget avatar = _avatar;
    Widget body = message.type == 5 ? _OrderMessage(message: message) : _TextMessage(message: message);

    body = ClipRRect(borderRadius: BorderRadius.circular(4.0), child: body);

    body = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: _isOwner ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        if (showSender) ...{
          Text(senderName!),
          const SizedBox(
            height: 5,
          )
        },
        body,
        const SizedBox(height: 2.0),
        _TimeStatus(time: DateTime.fromMillisecondsSinceEpoch(message.timestamp))
      ],
    );
    List<Widget> children = [
      const SizedBox(width: _AVATAR_SIZE + _PAGE_SPACE),
      if (message is SendingMessage)
        const Flexible(
          fit: FlexFit.tight,
          flex: 0,
          child: SpinKitCircle(
            color: MColor.skin,
            size: 20.0,
          ),
        ),
      Flexible(child: body),
      // if (message.type == 1) _TrianglePointer(own: _isOwner),
      const SizedBox(width: 4.0),
      GestureDetector(
          onTap: () {
            if (_isOwner) {
              Get.toNamed(Routes.PROFILE);
              // Get.to(() => UserProfileView(tag: GlobalConst.userModel?.uid.toString()),
              //     arguments: {'userId': GlobalConst.userModel?.uid, 'isAdmin': false}, binding: UserProfileBinding(tag: GlobalConst.userModel?.uid.toString()));
            } else {
              ChatUserEntity? sender = (message as ReceiveMessage).sender;
              int userId = sender?.userId ?? 0;
              if (userId != 0) {
                Get.to(() => UserProfileView(tag: userId.toString()),
                    arguments: {
                      'userId': userId,
                      'isAdmin': sender?.role == UserRole.admin,
                      'name': sender?.name,
                      'head': sender?.head,
                      'imUserId': sender?.imUserId
                    },
                    binding: UserProfileBinding(tag: userId.toString()));
              }
            }
          },
          child: avatar)
    ];

    if (!_isOwner) children = children.reversed.toList();

    Widget current = Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: _isOwner ? MainAxisAlignment.end : MainAxisAlignment.start,
      children: children,
    );

    return Padding(padding: const EdgeInsets.symmetric(vertical: 4.0), child: current);
  }
}

class _OrderMessage extends StatelessWidget {
  final ChatMessage message;
  OrderDetailEntity? orderInfo;
  bool _isOwner = false;
  _OrderMessage({required this.message, Key? key}) : super(key: key) {
    if (message is SendingMessage) {
      _isOwner = true;
    } else if (message is ReceiveMessage) {
      if ((message as ReceiveMessage).sender?.imUserId == GlobalConst.userModel?.imUserId) {
        _isOwner = true;
      }
    }
    orderInfo = message.orderInfo;
  }

  @override
  Widget build(BuildContext context) {
    return orderInfo == null
        ? const SizedBox()
        : GestureDetector(
            onTap: () => AppNavigator.oderDetail(orderInfo!.orderId!),
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: MColor.skin, width: 0.5),
                borderRadius: const BorderRadius.all(
                  Radius.circular(4),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                      width: double.infinity,
                      decoration: const BoxDecoration(
                        color: MColor.skin_80,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(4),
                          topRight: Radius.circular(4),
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 6.0),
                      child: Center(child: Text(S.of(Get.context!).tab_order, style: TextStyle(color: Colors.grey[50], fontSize: 16.0)))),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 4),
                    child: Column(
                      children: [
                        if (orderInfo!.city != null) ...{
                          _orderInfoItem(S.of(Get.context!).order_detail_inspection_city, orderInfo?.city ?? ''),
                        },
                        if (orderInfo!.productName?.isNotEmpty ?? false) ...{
                          _orderInfoItem(S.of(Get.context!).order_goods_name, orderInfo!.productName),
                        },
                        _orderInfoItem(S.of(Get.context!).order_detail_inspection_time, orderInfo!.inspectTime),
                        _orderInfoItem(
                          S.of(Get.context!).order_detail_inspection_type,
                          Helper.parseOrderType(orderInfo!.type ?? 0),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          );
  }
}

class _TextMessage extends StatelessWidget {
  final ChatMessage message;
  bool _isOwner = false;
  _TextMessage({required this.message, Key? key}) : super(key: key) {
    if (message is SendingMessage) {
      _isOwner = true;
    } else if (message is ReceiveMessage) {
      if ((message as ReceiveMessage).sender?.imUserId == GlobalConst.userModel?.imUserId) {
        _isOwner = true;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget current = HtmlWidget(
      message.msg,
      textStyle: TextStyle(color: _isOwner ? const Color(0xFFFFFFFF) : null, fontSize: 14.0),
      onTapUrl: (url) {
        launchUrl(Uri.parse(url));
        return true;
      },
      onTapImage: (p) {
        launchUrl(Uri.parse(p.sources.first.url));
      },
    );
    current = Container(
      constraints: const BoxConstraints(minHeight: 40.0),
      padding: const EdgeInsets.all(10.0),
      color: _isOwner ? const Color(0xFF67C23A) : Colors.white54,
      child: current,
    );

    return current;
  }
}

class _TrianglePointer extends StatelessWidget {
  final bool own;

  const _TrianglePointer({required this.own, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color color = own ? const Color(0xFF67C23A) : Colors.white54;

    return CustomPaint(
      size: const Size(5, _AVATAR_SIZE),
      painter: _TrianglePainter(color: color, isInverted: !own),
    );
  }
}

class _TrianglePainter extends CustomPainter {
  late Paint _paint;
  final Color color;

  final bool isInverted;

  _TrianglePainter({required this.color, this.isInverted = false}) {
    _paint = Paint()
      ..style = PaintingStyle.fill
      ..color = color
      ..strokeWidth = 10;
  }

  @override
  void paint(Canvas canvas, Size size) {
    var path = Path();

    if (isInverted) {
      path.moveTo(5, 15);
      path.lineTo(0, 20);
      path.lineTo(5, 25);
    } else {
      path.moveTo(0, 15);
      path.lineTo(5, 20);
      path.lineTo(0, 25);
    }

    path.close();

    canvas.drawPath(path, _paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class _TimeStatus extends StatelessWidget {
  final DateTime time;

  const _TimeStatus({required this.time, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(_time, style: TextStyle(color: Colors.grey[500], fontSize: 9.0));
  }

  String get _time => '${_z(time.hour)}:${_z(time.minute)}';
  String _z(int i) => i < 10 ? '0$i' : '$i';
}
