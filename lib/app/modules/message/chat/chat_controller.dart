import 'dart:async';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/api_service.dart';
import 'package:inspector/app/data/chat_message.dart';
import 'package:inspector/app/data/conversation_entity.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/data/receive_message.dart';
import 'package:inspector/app/data/sending_message.dart';
import 'package:inspector/app/modules/message/chat/chat_binding.dart';
import 'package:inspector/app/modules/message/chat/chat_view.dart';
import 'package:inspector/app/modules/message/chat_provider.dart';
import 'package:inspector/app/modules/message/socket/socket_channel.dart';
import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as p;

import '../../../../generated/l10n.dart';

class ChatController extends GetxController with WidgetsBindingObserver {
  ChatProvider provider = ChatProvider();

  final title = RxString(S.of(Get.context!).message);
  final messages = RxList<ChatMessage>();
  final canSend = RxBool(false);
  final isGroup = RxBool(false); //是否是群聊

  final isKeyboardShow = RxBool(false);

  final showPopupPanel = RxBool(false);

  final messageMap = <String, ChatMessage>{};

  int conversationId = 0;
  int? groupId;

  bool isGroupOwner = false;
  final orderId = 0.obs;

  final showSendOrder = false.obs;

  final boundOrderInfo = Rxn<OrderDetailEntity>();

  List<String> conversations = [];

  PublishConvRows? conversationInfo;

  TextEditingController txtController = TextEditingController();
  ScrollController scrollController = ScrollController();

  FocusNode inputFocusNode = FocusNode();

  ChatController() {
    logger.i('ChatController ${this.hashCode} created');
  }

  @override
  void onInit() {
    super.onInit();
    conversationId = Get.arguments['cid'];
    OrderDetailEntity? orderDetail = Get.arguments['order_detail_entity'];
    if (orderDetail != null) {
      boundOrderInfo.value = orderDetail;
      showSendOrder.value = Get.arguments['showSendOrder'] ?? false;
    }
    _initViews();
    _initData();
  }

  void _initViews() {
    WidgetsBinding.instance.addObserver(this);
    scrollController.addListener(() {
      double offset = scrollController.offset;
      if (offset >= 5) {
        if (isKeyboardShow.value) {
          FocusManager.instance.primaryFocus?.unfocus();
        }
        if (showPopupPanel.value) {
          showPopupPanel.value = false;
        }
      }
    });
  }

  void _initData() {
    // title.value = Get.arguments['title'];
    _fetchChatInfo();
    fetchList();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    double keyboardHeight = MediaQuery.of(Get.context!).viewInsets.bottom;
    bool prevKeyboardShow = isKeyboardShow.value;
    bool curKeyboardShow = keyboardHeight >= 100;
    logger.i('keyboard $prevKeyboardShow $curKeyboardShow $keyboardHeight');
    if (prevKeyboardShow != curKeyboardShow) {
      isKeyboardShow.value = curKeyboardShow;
      if (curKeyboardShow) {
        showPopupPanel.value = false;
      }
    }
    // });
  }

  @override
  void onClose() {
    txtController.dispose();
    scrollController.dispose();
    conversations = [];
    WidgetsBinding.instance.removeObserver(this);

    super.onClose();
  }

  Future<void> _getMessages(int lastRecordId) async {
    BaseModel<List<ReceiveMessage>> result = await provider.takeChatMsg(conversationId, lastRecordId, 50);
    if (result.isSuccess) {
      List<ReceiveMessage>? messages = result.data;
      if (messages != null && messages.isNotEmpty) {
        ChatMessage? lastMsg;
        for (var element in messages) {
          if (element.uuid.isEmpty) {
            element.uuid = Uuid().v1();
          }
          messageMap[element.uuid] = element;
          lastMsg = element;
          logger.i('msg: $element');
        }
        if (lastMsg is ReceiveMessage) {
          lastRecordId = lastMsg.recordId ?? 0;
          await SocketChannel.markRead(lastRecordId);
        }
        _sortMessages();
      }
    }
  }

  void _fetchChatInfo() {
    EasyLoading.show();
    provider.getCoversationInfo(conversationId).then((value) async {
      if (value.isSuccess) {
        conversationInfo = value.data;
        title.value = conversationInfo?.name ?? '';
        if (conversationInfo?.type == 2) {
          isGroup.value = true;
        }
        if (conversationInfo != null && conversationInfo!.orderId != null && conversationInfo!.orderId != 0 && boundOrderInfo.value == null) {
          orderId.value = conversationInfo!.orderId!;
          await _fetchBoundOrderInfo(conversationInfo!.orderId!);
        }
      } else {
        showToast(value.message ?? '');
        Get.back();
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  Future<void> _fetchBoundOrderInfo(int orderId) async {
    logger.i('_fetchBoundOrderInfo $orderId');
    var orderResp = await OrderProvider().takeOrderDetail(orderId);
    if (orderResp.isSuccess && orderResp.data != null) {
      boundOrderInfo.value = orderResp.data;
      boundOrderInfo.refresh();
    }
  }

  Future<void> fetchList() async {
    unawaited(EasyLoading.show());
    try {
      await _getMessages(0);
    } finally {
      unawaited(EasyLoading.dismiss());
    }
  }

  void refreshConversation(Map<String, dynamic> arg) {
    if (arg['id'].toString() != conversationId.toString()) return;
    conversations.add(arg['record']['id']);
  }

  void _sendMessage(String message, int msgType) {
    provider.sendText(conversationId!, message, msgType, isGroup.value);
  }

  void sendText() {
    _sendMessage(txtController.text.trim(), 1);
    txtController.text = '';
    canSend.value = false;
  }

  void onPlusClicked() {
    if (showPopupPanel.value) {
      //Panel is showing, hide it and popup keyboard
      showPopupPanel.value = false;
      inputFocusNode.requestFocus();
    } else {
      if (isKeyboardShow.value) {
        FocusManager.instance.primaryFocus?.unfocus();
      }
      showPopupPanel.value = true;
    }
  }

  Future<void> sendFile(File file) async {
    if (file.path == null) return;
    String? fileUrl = await ApiService.uploadFile(file.path);
    if (fileUrl == null) return;

    String content = '';
    int type = 2;
    if (['.jpg', '.jpeg', '.png', '.webp', '.gif'].contains(fileUrl.toLowerCase().substring(fileUrl.lastIndexOf('.')))) {
      content = "<a target='_blank' href='$fileUrl'><img style='width:90px' src='$fileUrl' /></a>";
      type = 3;
    } else {
      var ext = p.extension(file.path);
      var filePath = file.path;
      var ficon = "text.png";
      if (ext == "doc" || ext == "docx") {
        ficon = "wd.png";
      } else if (ext == "xls" || ext == "xlsx") {
        ficon = "ex.png";
      } else if (ext == "pptx" || ext == "ppt") {
        ficon = "pt.png";
      } else if (ext == "rar" || ext == "zip") {
        ficon = "rar.png";
      } else if (ext == "pdf") {
        ficon = "pdf.png";
      } else {
        ficon = 'file.png';
      }
      var ficon_src = '${Server.domain}static/img/icon/$ficon';
      //  file_text="<a target='_blank' href='"+src+"' class='file_record'>< img src='"+ficon_src+"' style='width:25px;margin-right:5px;'/>"+info.file.name+"</a >";
      content =
          "<a target='_blank' href='$fileUrl'>${filePath.substring(filePath.lastIndexOf('/') + 1)}<img style='width:25px;margin-right:5px;' src='$ficon_src' /></a>";
    }

    _sendMessage(content, type);
  }

  void onReceiveMessage(ReceiveMessage message) {
    logger.i('onReceiveMessage $message');
    messageMap[message.uuid] = message;
    if (message.type == 5) {
      int msgOrderId = message.orderInfo?.orderId ?? 0;
      if (msgOrderId != 0) {
        _fetchBoundOrderInfo(msgOrderId);
      }
    }
    SocketChannel.markRead(message.recordId ?? 0);
    _sortMessages();
  }

  void onSendingMessage(SendingMessage message) {
    logger.i('onSendingMessage $message');
    messageMap[message.uuid] = message;
    _sortMessages();
  }

  void onSendFailed(SendingMessage message) {}

  void _sortMessages() {
    List<ChatMessage> msg = List.of(messageMap.values);
    msg.sort((m1, m2) => m2.timestamp - m1.timestamp);
    messages.clear();
    messages.addAll(msg);
  }

  Future<void> loadMore(RefreshController refreshController) async {
    logger.i('loadmore');
    int lastRecordId = 0;
    ChatMessage msg = messages.value.lastWhere((element) => element is ReceiveMessage);
    if (msg is ReceiveMessage) {
      lastRecordId = msg.recordId ?? 0;
    }
    await _getMessages(lastRecordId);
    refreshController.loadComplete();
  }

  void sendOrderMsg(int orderId) {
    provider.sendOrder(conversationId!, orderId, isGroup.value);
  }

  Future<void> createCustomService() async {
    unawaited(EasyLoading.show());
    var value = await provider.getCustomService();
    if (value.isSuccess) {
      var gid = value.data['cid'] as int?;
      if (gid != null) {
        Get.back();
        unawaited(Get.to(() => ChatView(tag: gid.toString()), arguments: {'cid': gid}, binding: ChatBinding(tag: gid.toString())));
      }
    } else {
      showToast(value.message ?? '');
    }
    unawaited(EasyLoading.dismiss());
  }
}
