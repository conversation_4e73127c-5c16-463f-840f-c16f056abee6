import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/message/chat/chat_binding.dart';
import 'package:inspector/app/modules/message/chat/chat_controller.dart';
import 'package:inspector/app/routes/app_navigator.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class ChatTopOrderPreviewWidget extends GetView<ChatController> {
  @override
  final String? tag;

  final OrderDetailEntity orderInfo;

  const ChatTopOrderPreviewWidget(this.orderInfo, {Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    double totalWidth = size.width - 46;
    OrderDetailEntity entity = controller.boundOrderInfo.value!;
    return GestureDetector(
      onTap: () {
        AppNavigator.oderDetail(orderInfo.orderId!);
      },
      child: Container(
        decoration: const BoxDecoration(
          color: MColor.xFFF4F6F9,
          borderRadius: BorderRadius.all(
            Radius.circular(12.5),
          ),
        ),
        padding: const EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 10),
        child: Column(children: [
          _topButtons,
          const SizedBox(
            height: 10,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (entity.productName?.isNotEmpty ?? false) ...{
                _orderInfoItem(S.of(Get.context!).order_goods_name, entity.productName == '暂无' ? '' : entity.productName),
              },
              if (entity.addressInfo != null) ...{
                _orderInfoItem(S.of(Get.context!).order_detail_inspection_address, entity.addressInfo?.fullArea() ?? ''),
              },
              _orderInfoItem(S.of(Get.context!).order_detail_inspection_time, entity.inspectTime),
              _orderInfoItem(
                S.of(Get.context!).order_detail_inspection_type,
                Helper.parseOrderType(orderInfo!.type ?? 0),
              ),
            ],
          ),
        ]),
      ),
    );
  }

  Widget _orderInfoItem(String? title, String? value) {
    return Container(
      padding: const EdgeInsets.only(right: 12, top: 2, bottom: 4),
      child: Builder(builder: (context) {
        return Row(
          children: [
            Text(
              '$title：',
              overflow: TextOverflow.ellipsis,
              style: MFont.semi_Bold13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
            ),
            Expanded(
              child: Text(
                value ?? '-',
                style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget get _topButtons {
    // 如果是自己下的订单
    if (GlobalConst.userModel?.role == UserRole.admin) {
      return const SizedBox();
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          GestureDetector(
            onTap: () {
              controller.createCustomService();
            },
            child: Column(children: [Icon(Icons.support_agent, size: 30, color: MColor.skin), Text(S.of(Get.context!).chat_toolbar_custom_service)]),
          ),
          GestureDetector(
            onTap: () {
              Get.toNamed(Routes.PUBLISH);
            },
            child: Column(children: [const Icon(Icons.send, size: 30, color: MColor.skin), Text(S.of(Get.context!).chat_toolbar_submit_order)]),
          ),
        ],
      );
    }
  }
}
