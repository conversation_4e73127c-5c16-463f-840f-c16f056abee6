// import 'package:flutter/material.dart';
// import 'package:flutter/src/widgets/framework.dart';
// import 'package:get/get.dart';
// import 'package:inspector/app/config/design.dart';
// import 'package:inspector/app/modules/purchase/purchase_edit/purchase_edit_controller.dart';
// import 'package:inspector/app/modules/purchase/purchase_publish/purchase_publish_controller.dart';
// import 'package:inspector/app/modules/purchase/purchase_publish/purchase_publish_view.dart';

// import '../../../../generated/l10n.dart';

// class PurchaseEditView extends GetView<PurchaseEditController> with AbsPurchasePublishView {
//   @override
//   final String? tag;

//   PurchaseEditView({Key? key, this.tag}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         appBar: AppBar(
//           title: Get.arguments?['is_edit'] == true ? Text(S.of(Get.context!).purchase_edit) : Text(S.of(context).purchase_publish),
//           centerTitle: true,
//           actions: [
//             GestureDetector(
//               onTap: () {
//                 controller.doPublish(id: controller.purchaseId);
//               },
//               child: Padding(
//                 padding: const EdgeInsets.only(right: 8.0),
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     Obx(() {
//                       bool canPublish = controller.canPublish.value;
//                       var bgColor = canPublish ? MColor.skin : MColor.skin_50;
//                       return Container(
//                           padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
//                           decoration: BoxDecoration(borderRadius: BorderRadius.circular(17), color: bgColor),
//                           height: 28,
//                           child: Center(
//                               child: Text(
//                             S.of(Get.context!).public_publish.capitalize!,
//                             strutStyle: const StrutStyle(
//                               forceStrutHeight: true,
//                               leading: 0.5,
//                             ),
//                             textAlign: TextAlign.center,
//                             style: MFont.regular11.apply(
//                               color: Colors.white,
//                             ),
//                           )));
//                     }),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//           elevation: 0,
//         ),
//         backgroundColor: Colors.white,
//         body: SafeArea(
//             child: SingleChildScrollView(
//           child: Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 12.0),
//             child: Column(
//               children: [
//                 productView,
//                 const SizedBox(
//                   height: 12.0,
//                 ),
//                 Divider(
//                   height: 1.0,
//                   color: MColor.xFF999999.withAlpha(50),
//                 ),
//                 const SizedBox(
//                   height: 12.0,
//                 ),
//                 titleView,
//                 const SizedBox(
//                   height: 12.0,
//                 ),
//                 Divider(
//                   height: 1.0,
//                   color: MColor.xFF999999.withAlpha(50),
//                 ),
//                 const SizedBox(
//                   height: 12.0,
//                 ),
//                 contentView,
//                 uploadView,
//                 const SizedBox(
//                   height: 16,
//                 ),
//                 categoryView,
//                 const SizedBox(
//                   height: 16,
//                 ),
//                 paidView,
//               ],
//             ),
//           ),
//         )));
//   }

//   @override
//   AbsPurchasePublishController getController() {
//     return controller;
//   }
// }
