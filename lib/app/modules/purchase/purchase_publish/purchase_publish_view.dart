import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/address/address_manager.dart';
import 'package:inspector/app/modules/purchase/purchase_publish/purchase_category_view.dart';
import 'package:inspector/app/modules/purchase/purchase_publish/purchase_publish_controller.dart';
import 'package:inspector/app/modules/store/setting_store.dart';
import 'package:inspector/app/modules/widgets/upload/UploadWidget.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/tools/extensions.dart';
import 'package:inspector/app/tools/privacy_helper.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/widgets/price_input_widget.dart';

import 'package:inspector/generated/l10n.dart';
import 'package:intl/intl.dart';

class PurchasePublishView extends GetView<PurchasePublishController> {
  const PurchasePublishView({Key? key}) : super(key: key);

  @override
  PurchasePublishController get controller {
    logger.i('PurchasePublishView get controller ${Get.parameters}');
    bool isEditPost = Get.parameters['is_edit'] == '1';
    if (isEditPost) {
      var purchaseId = Get.parameters['purchase_id'];
      return GetInstance().putOrFind<PurchasePublishController>(() => PurchasePublishController(), tag: purchaseId);
    } else {
      return GetInstance().putOrFind<PurchasePublishController>(() => PurchasePublishController());
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        if (controller.hasContentChanged()) {
          showCupertinoModalPopup(
              context: context,
              builder: (context) {
                return CupertinoActionSheet(
                  title: Text(
                    S.of(Get.context!).purchase_save_draft_title,
                    style: MFont.semi_Bold16.apply(color: MColor.xFF000000),
                  ),
                  cancelButton: CupertinoActionSheetAction(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(S.of(Get.context!).public_cancel,
                        style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333, fontWeight: FontWeight.bold)),
                  ),
                  actions: <Widget>[
                    CupertinoActionSheetAction(
                      onPressed: () {
                        controller.doPublish(id: controller.purchaseInfo?.id, draft: true);
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        S.of(Get.context!).purchase_save_draft_choice,
                        style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                      ),
                    ),
                    CupertinoActionSheetAction(
                        onPressed: () {
                          Navigator.of(context).pop();
                          Get.back();
                        },
                        child: Text(
                          S.of(Get.context!).purchase_save_draft_quit,
                          style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                        )),
                  ],
                );
              });
          return Future<bool>.value(false);
        }
        return Future<bool>.value(true);
      },
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerTheme: DividerThemeData(
            color: Colors.transparent,
          ),
        ),
        child: Scaffold(
            appBar: AppBar(
              title: Text(controller.isEditPost ? S.of(Get.context!).purchase_edit : S.of(Get.context!).purchase_publish,
                  style: TextStyle(color: MColor.xFF000000, fontSize: 22, fontWeight: FontWeight.w500)),
              centerTitle: true,
              elevation: 0,
              leading: IconButton(
                  onPressed: () {
                    Get.back();
                  },
                  icon: Icon(
                    Icons.arrow_back_ios,
                    size: 18,
                  )),
            ),
            backgroundColor: MColor.xFFF6F6F6,
            persistentFooterButtons: [
              SizedBox(
                height: 40,
                child: Obx(() {
                  return TextButton(
                    onPressed: () {
                      if (controller.canPublish.value) {
                        controller.doPublish(
                          id: controller.purchaseInfo?.id,
                        );
                      }
                    },
                    style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all(controller.canPublish.value ? MColor.skin : MColor.skin.withOpacity(0.5)),
                        shape: WidgetStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(21))))),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            S.of(Get.context!).confirm,
                            textAlign: TextAlign.center,
                            style: TextStyle(color: MColor.xFFFFFFFF, fontWeight: FontWeight.w500, fontSize: 16, height: 1),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ],
            body: SafeArea(
                child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    productCard,
                    const SizedBox(
                      height: 10.0,
                    ),
                    contentCard,
                    const SizedBox(
                      height: 10,
                    ),
                    categoryCard,
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
            ))),
      ),
    );
  }

  Widget get productCard {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 13),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
              text: WidgetSpan(
                  child: Row(
            children: [
              Text(
                S.of(Get.context!).purchase_publish_product_label,
                style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500),
              ),
              const SizedBox(
                width: 3,
              ),
              Text('*', style: TextStyle(color: MColor.skin, fontSize: 14, fontWeight: FontWeight.w500))
            ],
          ))),
          Obx(() {
            return TextField(
              controller: controller.productController,
              focusNode: controller.productFocusNode,
              style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w400),
              decoration: InputDecoration(
                counterText: '',
                alignLabelWithHint: true,
                fillColor: Colors.white,
                filled: true,
                contentPadding: const EdgeInsets.only(left: 0, right: 0, top: 0, bottom: 0),
                hintText: S.of(Get.context!).purchase_publish_product_hint,
                hintStyle: TextStyle(color: MColor.xFF999999, fontSize: 14, fontWeight: FontWeight.w400),
                suffixIcon: Text(
                  '${controller.productCount.value}/250',
                  style: TextStyle(
                    color: MColor.xFF808080,
                    fontSize: 12,
                  ),
                ),
                // labelText: S.of(Get.context!).purchase_publish_product_label,
                // floatingLabelBehavior: FloatingLabelBehavior.always,
                // labelStyle: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500),
                enabledBorder: InputBorder.none,
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                counterStyle: TextStyle(color: MColor.xFF808080, fontSize: 12),
              ),
              keyboardType: TextInputType.text,
              onSubmitted: (value) {},
              onChanged: (val) {
                logger.i('product $val');
                controller.productCount.value = val.length;
                controller.checkPublish();
              },
              minLines: 1,
              maxLines: 1,
              maxLength: 250,
            );
          }),
          RichText(
              text: WidgetSpan(
                  child: Row(
            children: [
              Text(
                S.of(Get.context!).purchase_publish_title_label,
                style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500),
              ),
              const SizedBox(
                width: 3,
              ),
              Text('*', style: TextStyle(color: MColor.skin, fontSize: 14, fontWeight: FontWeight.w500))
            ],
          ))),
          Obx(() {
            return TextField(
              controller: controller.titleController,
              focusNode: controller.titleFocusNode,
              style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w400),
              decoration: InputDecoration(
                fillColor: Colors.white,
                counterText: '',
                suffixIcon: Text(
                  '${controller.titleCounter.value}/250',
                  style: TextStyle(
                    color: MColor.xFF808080,
                    fontSize: 12,
                  ),
                ),
                alignLabelWithHint: true,
                // filled: true,
                contentPadding: const EdgeInsets.only(left: 0, right: 0, top: 0, bottom: 0),
                // hintText: S.of(Get.context!).purchase_publish_title_hint,
                // hintStyle: TextStyle(color: MColor.xFF999999, fontSize: 14, fontWeight: FontWeight.w400),
                // labelText: S.of(Get.context!).purchase_publish_title_label,
                // floatingLabelBehavior: FloatingLabelBehavior.always,
                // labelStyle: MFont.bold16.apply(color: MColor.xFF333333),
                enabledBorder: InputBorder.none,
                border: InputBorder.none,
                counterStyle: TextStyle(color: MColor.xFF808080),
                focusedBorder: InputBorder.none,
              ),
              keyboardType: TextInputType.text,
              onSubmitted: (value) {},
              onChanged: (val) {
                logger.i('title $val');
                controller.titleCounter.value = val.length;
                controller.checkPublish();
              },
              minLines: 1,
              maxLines: 1,
              maxLength: 250,
            );
          }),
          Row(
            children: [
              RichText(
                  text: TextSpan(
                      text: S.of(Get.context!).purchase_publish_quantity,
                      style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500),
                      children: [
                    WidgetSpan(
                        child: Row(
                      children: [
                        const SizedBox(
                          width: 3,
                        ),
                        Text('*', style: TextStyle(color: MColor.skin, fontSize: 14, fontWeight: FontWeight.w500))
                      ],
                    ))
                  ])),
              const SizedBox(
                width: 12,
              ),
              Expanded(
                child: TextField(
                  controller: controller.quantityController,
                  focusNode: controller.quantityFocusNode,
                  style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w400),
                  textAlign: TextAlign.end,
                  decoration: InputDecoration(
                    fillColor: Colors.white,
                    alignLabelWithHint: true,
                    counterText: '',
                    // filled: true,
                    contentPadding: const EdgeInsets.only(left: 0, right: 0, top: 0, bottom: 0),
                    hintText: S.of(Get.context!).purchase_publish_quantity_hint,
                    hintStyle: TextStyle(color: MColor.xFF999999, fontSize: 14, fontWeight: FontWeight.w400),
                    enabledBorder: InputBorder.none,
                    border: InputBorder.none,
                    counterStyle: TextStyle(color: MColor.xFF808080),
                    focusedBorder: InputBorder.none,
                  ),
                  keyboardType: TextInputType.number,
                  onSubmitted: (value) {},
                  onChanged: (val) {
                    logger.i('quantity $val');
                    controller.checkPublish();
                  },
                  minLines: 1,
                  maxLines: 1,
                ),
              )
            ],
          ),
          Row(
            children: [
              RichText(
                  text: TextSpan(
                      text: S.of(Get.context!).end_date,
                      style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500),
                      children: [
                    WidgetSpan(
                        child: Row(
                      children: [
                        const SizedBox(
                          width: 3,
                        ),
                        // Text('*', style: TextStyle(color: MColor.skin, fontSize: 14, fontWeight: FontWeight.w500))
                      ],
                    ))
                  ])),
              const SizedBox(
                width: 12,
              ),
              const Spacer(),
              Obx(() {
                return Text(
                  controller.deadline.value,
                  style: TextStyle(color: MColor.xFF333333, fontSize: 14, fontWeight: FontWeight.w400),
                );
              }),
              IconButton(
                  padding: EdgeInsets.zero,
                  style: IconButton.styleFrom(tapTargetSize: MaterialTapTargetSize.shrinkWrap),
                  onPressed: () {
                    showDatePicker(
                            locale: SettingStore.to.getCurrentLocale(), context: Get.context!, firstDate: DateTime.now(), lastDate: DateTime.now().nextYear())
                        .then((date) {
                      if (date != null) {
                        logger.d(date);
                        DateFormat formatter = DateFormat('yyyy-MM-dd');
                        controller.deadline.value = formatter.format(date);
                      }
                    });
                  },
                  icon: Icon(Icons.calendar_month_outlined, size: 16))
            ],
          ),
          Row(
            children: [
              Text(S.of(Get.context!).purchase_area, style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500)),
              const SizedBox(
                width: 12,
              ),
              const Spacer(),
              Obx(() {
                return Text(
                  '${controller.province}${controller.city}',
                  style: TextStyle(color: MColor.xFF333333, fontSize: 14, fontWeight: FontWeight.w400),
                );
              }),
              IconButton(
                  padding: EdgeInsets.zero,
                  style: IconButton.styleFrom(tapTargetSize: MaterialTapTargetSize.shrinkWrap),
                  onPressed: () {
                    return Pickers.showMultiLinkPicker(
                      Get.context!,
                      data: AddressManager.toCityMap(),
                      columeNum: 2,
                      selectData: ['', ''],
                      pickerStyle: PickerStyle(
                        // showTitleBar: true,
                        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
                        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
                        itemOverlay: Builder(builder: (context) {
                          return Container(
                            decoration: BoxDecoration(
                              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
                            ),
                          );
                        }),
                      ),
                      // initTown: '',
                      // addAllItem: false,
                      onConfirm: (List p, List i) {
                        var province = p[0];
                        var city = p[1];
                        controller.province.value = province;
                        controller.city.value = city;
                        // controller.province = province;
                        // controller.city = city;
                        // controller.area = area;
                        // controller.controllers[4].text = '$province$city$area';
                        // controller.focusNode.requestFocus();
                      },
                    );
                  },
                  icon: Icon(Icons.location_on_outlined, size: 16))
            ],
          )
        ],
      ),
    );
  }

  Widget get productView {
    return TextField(
      controller: controller.productController,
      focusNode: controller.productFocusNode,
      style: MFont.bold16.apply(color: MColor.xFF333333),
      decoration: InputDecoration(
        fillColor: Colors.white,
        filled: true,
        contentPadding: const EdgeInsets.only(left: 1, right: 0, top: 0, bottom: 0),
        hintText: S.of(Get.context!).purchase_publish_product_hint,
        hintStyle: MFont.bold16.apply(color: MColor.xFF999999),
        labelText: S.of(Get.context!).purchase_publish_product_label,
        floatingLabelBehavior: FloatingLabelBehavior.always,
        labelStyle: MFont.bold16.apply(color: MColor.xFF333333),
        enabledBorder: InputBorder.none,
        border: InputBorder.none,
        focusedBorder: InputBorder.none,
      ),
      keyboardType: TextInputType.text,
      onSubmitted: (value) {},
      onChanged: (val) {
        logger.i('title $val');
        controller.checkPublish();
      },
      minLines: 1,
      maxLines: 1,
      maxLength: 250,
    );
  }

  Widget get titleView {
    return TextField(
      controller: controller.titleController,
      focusNode: controller.titleFocusNode,
      style: MFont.bold16.apply(color: MColor.xFF333333),
      decoration: InputDecoration(
        fillColor: Colors.white,
        filled: true,
        contentPadding: const EdgeInsets.only(left: 1, right: 0, top: 0, bottom: 0),
        hintText: S.of(Get.context!).purchase_publish_title_hint,
        hintStyle: MFont.bold16.apply(color: MColor.xFF999999),
        labelText: S.of(Get.context!).purchase_publish_title_label,
        floatingLabelBehavior: FloatingLabelBehavior.always,
        labelStyle: MFont.bold16.apply(color: MColor.xFF333333),
        enabledBorder: InputBorder.none,
        border: InputBorder.none,
        focusedBorder: InputBorder.none,
      ),
      keyboardType: TextInputType.text,
      onSubmitted: (value) {},
      onChanged: (val) {
        logger.i('title $val');
        controller.checkPublish();
      },
      minLines: 1,
      maxLines: 1,
      maxLength: 250,
    );
  }

  Widget get contentCard {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 13),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(S.of(Get.context!).purchase_publish_content_label, style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500)),
          TextField(
            controller: controller.contentController,
            focusNode: controller.contentFocusNode,
            style: MFont.regular13.apply(color: MColor.xFF333333),
            decoration: InputDecoration(
              fillColor: MColor.xFFFFFFFF,
              filled: true,
              contentPadding: const EdgeInsets.only(left: 0, right: 0, top: 0, bottom: 0),
              hintText: S.of(Get.context!).purchase_publish_content_hint,
              hintStyle: MFont.regular13.apply(color: MColor.xFF999999),
              enabledBorder: InputBorder.none,
              border: InputBorder.none,
              focusedBorder: InputBorder.none,
              constraints: const BoxConstraints(minHeight: 100),
            ),
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.next,
            onSubmitted: (value) {},
            onChanged: (val) {
              controller.checkPublish();
            },
            minLines: 2,
            maxLines: 10,
            maxLength: 10000,
          ),
          Obx(
            () {
              return UploadWidget.generateUploadWidget(controller.attachments, true, (url) {
                controller.attachments.add(url);
              }, (position) {
                controller.attachments.removeAt(position);
                controller.attachments.refresh();
              }, showFile: false, color: MColor.xFFFFFFFF);
            },
          )
        ]));
  }

  Widget get contentView {
    return TextField(
      controller: controller.contentController,
      focusNode: controller.contentFocusNode,
      style: MFont.regular13.apply(color: MColor.xFF333333),
      decoration: InputDecoration(
        fillColor: MColor.xFFFFFFFF,
        filled: true,
        labelText: S.of(Get.context!).purchase_publish_content_label,
        floatingLabelBehavior: FloatingLabelBehavior.always,
        contentPadding: const EdgeInsets.only(left: 1, right: 0, top: 0, bottom: 0),
        hintText: S.of(Get.context!).purchase_publish_content_hint,
        hintStyle: MFont.regular13.apply(color: MColor.xFF999999),
        labelStyle: MFont.regular13.apply(color: MColor.xFF333333),
        enabledBorder: InputBorder.none,
        border: InputBorder.none,
        focusedBorder: InputBorder.none,
        constraints: const BoxConstraints(minHeight: 100),
      ),
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.search,
      onSubmitted: (value) {},
      onChanged: (val) {
        controller.checkPublish();
      },
      minLines: 2,
      maxLines: 10,
      maxLength: 10000,
    );
  }

  Widget get uploadView {
    return Obx(
      () {
        return UploadWidget.generateUploadWidget(controller.attachments, true, (url) {
          controller.attachments.add(url);
        }, (position) {
          controller.attachments.removeAt(position);
          controller.attachments.refresh();
        }, showFile: false);
      },
    );
  }

  Widget get categoryCard {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFFFFFFF),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          categoryView,
          const SizedBox(
            height: 12,
          ),
          authorOnlyView,
          const SizedBox(
            height: 12,
          ),
          Obx(() {
            return Column(
              children: [
                Row(
                  children: [
                    Text(
                      S.of(Get.context!).purchase_publish_price,
                      style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500),
                      textAlign: TextAlign.center,
                    ),
                    const Spacer(),
                    SizedBox(
                      height: 30,
                      width: 60,
                      child: FittedBox(
                        alignment: Alignment.centerRight,
                        child: CupertinoSwitch(
                          value: controller.paidSwitchOn.value,
                          activeColor: MColor.skin,
                          onChanged: (value) {
                            controller.paidSwitchOn.value = value;
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 9,
                ),
                if (controller.paidSwitchOn.value) ...{
                  GestureDetector(
                    onTap: () {
                      Get.bottomSheet(
                        SafeArea(
                          // and this
                          child: PriceInputWidget(controller.price.toStringAsFixed(2), S.of(Get.context!).order_detail_inspection_cost,
                              controller.accountType.value == UserAccountType.USD, 7),
                        ),
                        isScrollControlled: true,
                        ignoreSafeArea: false, // add this
                      ).then((value) {
                        if (value is Map) {
                          controller.accountType.value = value['isUSD'] == true ? UserAccountType.USD : UserAccountType.CNY;
                          String priceStr = value['price'];
                          controller.price.value = double.tryParse(priceStr) ?? 0;
                        }
                      });
                    },
                    child: Builder(builder: (context) {
                      return Row(
                        children: [
                          Obx(() {
                            return Text(
                              '${controller.accountType.value.symbol} ${controller.price.toStringAsFixed(2)}',
                              style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500),
                              textAlign: TextAlign.center,
                            );
                          }),
                        ],
                      );
                    }),
                  ),
                  Divider(height: 18, color: MColor.xFFF6F6F6)
                },
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(Icons.info_outline, size: 12, color: MColor.xFF999999),
                    const SizedBox(
                      width: 8,
                    ),
                    Expanded(
                      child: Text(
                        S.of(Get.context!).purchase_publish_bounty_tips,
                        style: MFont.medium12.apply(color: MColor.xFF999999),
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                        ),
                        textAlign: TextAlign.start,
                      ),
                    )
                  ],
                )
              ],
            );
          })
        ]));
  }

  Widget get authorOnlyView {
    return Obx(() {
      return Row(
        children: [
          Text(
            S.of(Get.context!).purchase_permission_author_only,
            style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
          const Spacer(),
          SizedBox(
            height: 30,
            width: 60,
            child: FittedBox(
              alignment: Alignment.centerRight,
              child: CupertinoSwitch(
                value: controller.authorOnlySwitchOn.value,
                activeColor: MColor.skin,
                onChanged: (value) {
                  controller.authorOnlySwitchOn.value = value;
                },
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget get categoryView {
    return Obx(() {
      return GestureDetector(
        onTap: () {
          Get.bottomSheet(
              const SafeArea(
                // and this
                child: PurchaseCategoryView(),
              ),
              isScrollControlled: true,
              ignoreSafeArea: false, // add this
              settings: RouteSettings(name: 'purchase_category', arguments: {'categories': controller.categoryList})).then((value) {
            if (value is List<PurchaseClassEntity>) {
              controller.selectedCategory.value = value;
              controller.checkPublish();
            }
          });
        },
        child: Row(
          children: [
            Text(
              S.of(Get.context!).purchase_publish_choose_category,
              style: TextStyle(height: 1, fontSize: 14, color: MColor.xFF000000),
            ),
            Expanded(
              child: Text(
                controller.getSelectedCategoryName(),
                style: TextStyle(color: MColor.xFF000000, fontSize: 14, fontWeight: FontWeight.w500),
                textAlign: TextAlign.end,
                maxLines: 1,
              ),
            ),
            const Icon(
              Icons.keyboard_arrow_right_outlined,
              size: 16,
              color: MColor.xFFBBBBBB,
            ),
          ],
        ),
      );
    });
  }

  Widget get paidView {
    return Obx(() {
      return Column(
        children: [
          Row(
            children: [
              Text(
                S.of(Get.context!).purchase_publish_price,
                style: TextStyle(fontSize: 14, color: MColor.xFF3D3D3D),
                strutStyle: const StrutStyle(
                  forceStrutHeight: true,
                  leading: 0.5,
                ),
                textAlign: TextAlign.center,
              ),
              Switch(
                value: controller.paidSwitchOn.value,
                activeColor: MColor.skin,
                onChanged: (value) {
                  controller.paidSwitchOn.value = value;
                },
              ),
              const Spacer(),
              if (controller.paidSwitchOn.value) ...{
                GestureDetector(
                  onTap: () {
                    Get.bottomSheet(
                      SafeArea(
                        // and this
                        child: PriceInputWidget(controller.price.toStringAsFixed(2), S.of(Get.context!).order_detail_inspection_cost,
                            controller.accountType.value == UserAccountType.USD, 7),
                      ),
                      isScrollControlled: true,
                      ignoreSafeArea: false, // add this
                    ).then((value) {
                      if (value is Map) {
                        controller.accountType.value = value['isUSD'] == true ? UserAccountType.USD : UserAccountType.CNY;
                        String priceStr = value['price'];
                        controller.price.value = double.tryParse(priceStr) ?? 0;
                      }
                    });
                  },
                  child: Builder(builder: (context) {
                    return Row(
                      children: [
                        Obx(() {
                          return Text(
                            '${controller.accountType.value.symbol} ${controller.price.toStringAsFixed(2)}',
                            style: MFont.medium16.apply(color: MColor.skin),
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            textAlign: TextAlign.center,
                          );
                        }),
                        Icon(
                          Icons.keyboard_arrow_right_outlined,
                          size: 24,
                          color: context.isDarkMode ? DarkColor.xFFBBBBBB : MColor.xFFBBBBBB,
                        ),
                      ],
                    );
                  }),
                )
              }
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Icon(Icons.info_outline, size: 12, color: MColor.xFF999999),
              const SizedBox(
                width: 8,
              ),
              Expanded(
                child: Text(
                  S.of(Get.context!).purchase_publish_bounty_tips,
                  style: MFont.medium12.apply(color: MColor.xFF999999),
                  strutStyle: const StrutStyle(
                    forceStrutHeight: true,
                  ),
                  textAlign: TextAlign.start,
                ),
              )
            ],
          )
        ],
      );
    });
  }
}
