import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_publish/purchase_category_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_publish/purchase_publish_controller.dart';

import '../../../../generated/l10n.dart';

class PurchaseCategoryView extends GetView<PurchaseCategoryController> {
  const PurchaseCategoryView({super.key});

  @override
  PurchaseCategoryController get controller => GetInstance().putOrFind<PurchaseCategoryController>(() => PurchaseCategoryController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: _currentView,
          automaticallyImplyLeading: false,
          backgroundColor: Colors.transparent,
        ),
        body: Obx(() {
          var categories = controller.categoryList.value;
          if (controller.selectedCategory.isNotEmpty) {
            categories = controller.selectedCategory.last.children;
          }
          return ListView.separated(
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return _itemView(categories[index]);
              },
              separatorBuilder: (context, index) {
                return Divider(
                  height: 1.0,
                  color: MColor.xFF999999.withAlpha(50),
                );
              },
              itemCount: categories.length);
        }));
  }

  Widget get _currentView {
    return Obx(() {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
              child: SizedBox(
            height: 42,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.selectedCategory.length + 1,
              itemBuilder: ((context, index) {
                return Stack(children: [
                  Center(
                    child: Text(
                        index == controller.selectedCategory.length ? S.of(Get.context!).publish_inspection_time_tip : controller.selectedCategory[index].name,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        textAlign: TextAlign.start,
                        style: MFont.regular14.apply(
                          color: MColor.xFF000000,
                        )),
                  ),
                  Positioned(
                      bottom: 0,
                      child: Container(
                        color: MColor.skin,
                        height: 3,
                      )),
                ]);
              }),
              separatorBuilder: (context, index) {
                return Center(
                  child: SizedBox(
                    width: 18,
                    child: Text('/',
                        textAlign: TextAlign.center,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        style: MFont.regular14.apply(
                          color: MColor.xFF000000,
                        )),
                  ),
                );
              },
            ),
          )),
          GestureDetector(
            onTap: () {
              Get.back();
            },
            child: const Icon(
              Icons.close,
              size: 24,
              color: MColor.xFFBBBBBB,
            ),
          ),
        ],
      );
    });
  }

  Widget _itemView(PurchaseClassEntity entity) {
    return Container(
      height: 42,
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: GestureDetector(
        onTap: () {
          controller.onCategorySelected(entity);
        },
        child: Row(
          children: [
            Expanded(
                child: Text(
              entity.name,
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              textAlign: TextAlign.start,
              style: MFont.regular14.apply(
                color: MColor.xFF000000,
              ),
            )),
            if (entity.children.isNotEmpty)
              const Icon(
                Icons.keyboard_arrow_right_outlined,
                size: 24,
                color: MColor.xFFBBBBBB,
              ),
          ],
        ),
      ),
    );
  }
}
