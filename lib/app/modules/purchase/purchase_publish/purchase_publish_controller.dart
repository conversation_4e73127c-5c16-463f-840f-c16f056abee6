import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:inspector/app/modules/tabbar/controllers/tabbar_controller.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:inspector/generated/l10n.dart';

class PurchasePublishController extends GetxController {
  final PurchaseService service = PurchaseService();
  String? purchaseId;

  bool isEditPost = false;

  PurchaseDetailEntity? purchaseInfo;

  TextEditingController titleController = TextEditingController();
  FocusNode titleFocusNode = FocusNode();

  TextEditingController productController = TextEditingController();
  FocusNode productFocusNode = FocusNode();

  TextEditingController contentController = TextEditingController();
  FocusNode contentFocusNode = FocusNode();

  TextEditingController paidController = TextEditingController();
  FocusNode paidFocusNode = FocusNode();

  TextEditingController quantityController = TextEditingController();
  FocusNode quantityFocusNode = FocusNode();

  final categoryList = <PurchaseClassEntity>[];

  final attachments = RxList<String>([]);

  final paidSwitchOn = false.obs;

  final authorOnlySwitchOn = false.obs;

  final deadline = ''.obs;

  final province = ''.obs;
  final city = ''.obs;

  final accountType = UserAccountType.CNY.obs;
  final price = 0.0.obs;

  final canPublish = false.obs;

  final titleCounter = 0.obs;
  final productCount = 0.obs;

  final selectedCategory = <PurchaseClassEntity>[].obs;

  @override
  void onInit() {
    super.onInit();

    isEditPost = Get.parameters['is_edit'] == '1';
    if (isEditPost) {
      purchaseId = Get.parameters['purchase_id'];
      if (purchaseId == null) {
        Get.back();
        return;
      }
      _getPurchaseDetails();
    } else {
      getDraft();
    }
    getCategory();
  }

  Future<void> _getPurchaseDetails() async {
    logger.i('getPurchaseDetails');
    unawaited(EasyLoading.show());
    var results = await service.getPurchaseDetails('$purchaseId', 0, 0, 'asc', null);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null && resp.detail != null) {
        PurchaseDetailEntity info = resp.detail!;
        purchaseInfo = info;
        fill(info);
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  Future<void> getDraft() async {
    unawaited(EasyLoading.show());
    var results = await service.getDrafts();
    if (results.isSuccess) {
      var resp = results.data;
      purchaseInfo = resp?.detail;
      if (purchaseInfo != null) {
        fill(purchaseInfo!);
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  bool hasContentChanged() {
    if (isEditPost) {
      if (purchaseInfo == null) {
        return titleController.text.isNotEmpty ||
            productController.text.isNotEmpty ||
            contentController.text.isNotEmpty ||
            selectedCategory.isNotEmpty ||
            attachments.isNotEmpty ||
            paidSwitchOn.value;
      } else {
        return purchaseInfo!.title != titleController.text ||
            purchaseInfo!.product == productController.text ||
            purchaseInfo!.replyInfo?.content != contentController.text ||
            purchaseInfo!.classId != selectedCategory.last.id ||
            purchaseInfo!.pic != attachments ||
            purchaseInfo!.bounty != price.value ||
            purchaseInfo!.account != accountType.value;
      }
    } else {
      return titleController.text.isNotEmpty ||
          productController.text.isNotEmpty ||
          contentController.text.isNotEmpty ||
          selectedCategory.isNotEmpty ||
          attachments.isNotEmpty ||
          paidSwitchOn.value;
    }
  }

  @override
  void onClose() {
    super.onClose();
    titleController.dispose();
    titleFocusNode.dispose();
    contentController.dispose();
    contentFocusNode.dispose();
    paidController.dispose();
    paidFocusNode.dispose();
    productController.dispose();
    productFocusNode.dispose();
    quantityController.dispose();
    quantityFocusNode.dispose();
  }

  void fill(PurchaseDetailEntity info) {
    productController.text = info.product;
    titleController.text = info.title;
    contentController.text = info.replyInfo?.content ?? '';
    attachments.clear();
    attachments.addAll(info.pic);
    accountType.value = info.account;
    price.value = info.bounty;
    paidSwitchOn.value = info.bounty != 0;
    authorOnlySwitchOn.value = info.justLandlord == 1;
    province.value = info.province ?? '';
    city.value = info.city ?? '';
    quantityController.text = info.count ?? '';
    deadline.value = info.deadline ?? '';
    List<PurchaseClassEntity> selected = _parseCategory(categoryList, info.classId);
    selectedCategory.addAll(selected);
    checkPublish();
    purchaseInfo = info;
  }

  List<PurchaseClassEntity> _parseCategory(List<PurchaseClassEntity> categories, int classId) {
    List<PurchaseClassEntity> selected = [];
    for (PurchaseClassEntity category in categories) {
      if (category.id == classId) {
        selected.add(category);
        break;
      } else if (category.children.isNotEmpty) {
        List<PurchaseClassEntity> parsed = _parseCategory(category.children, classId);
        if (parsed.isNotEmpty) {
          selected.add(category);
          selected.addAll(parsed);
          break;
        }
      }
    }
    return selected;
  }

  void checkPublish() {
    if (titleController.text.isNotEmpty && contentController.text.isNotEmpty && selectedCategory.isNotEmpty && productController.text.isNotEmpty) {
      canPublish.value = true;
    } else {
      canPublish.value = false;
    }
  }

  Future<void> getCategory() async {
    unawaited(EasyLoading.show());
    var result = await service.getPurchaseClasses(0);
    if (result.isSuccess) {
      var list = result.data;
      categoryList.clear();
      if (list != null) {
        categoryList.addAll(list);
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  Future<void> doPublish({int? id, bool? draft = false}) async {
    unawaited(EasyLoading.show());
    int? classId = selectedCategory.isEmpty ? null : selectedCategory.last.id;
    var results = await service.addPurchase(classId, productController.text, titleController.text, contentController.text, attachments, accountType.value.value,
        price.toStringAsFixed(2), draft == true ? 0 : 1, authorOnlySwitchOn.value, quantityController.text, deadline.value, province.value, city.value,
        id: id);
    if (results.isSuccess) {
      Get.back(result: {'isSuccess': true});
    } else {
      showToast(results.message!);
    }
    unawaited(EasyLoading.dismiss());
  }

  String getSelectedCategoryName() {
    if (selectedCategory.isEmpty) {
      return S.of(Get.context!).purchase_publish_choose_category_hint;
    } else {
      var name = '';
      for (var i = 0; i < selectedCategory.length; i++) {
        name += selectedCategory[i].name;
        if (i != selectedCategory.length - 1) {
          name += ' / ';
        }
      }
      return name;
    }
  }
}
