import 'package:get/get.dart';
import 'package:inspector/app/data/purchase_entity.dart';

class PurchaseCategoryController extends GetxController {
  final selectedCategory = <PurchaseClassEntity>[].obs;

  final categoryList = <PurchaseClassEntity>[].obs;

  @override
  void onInit() {
    super.onInit();
    categoryList.value = Get.arguments['categories'];
  }

  void onCategorySelected(PurchaseClassEntity entity) {
    if (entity.children.isEmpty) {
      Get.back(result: selectedCategory.value);
    }
    selectedCategory.add(entity);
  }
}
