import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_appeal/purchase_appeal_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_author_view.dart';
import 'package:inspector/app/shared/widgets/avatar.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/widgets/price_input_widget.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../generated/l10n.dart';

class PurchaseAppealView extends GetView<PurchaseAppealController> {
  @override
  final String? tag;

  const PurchaseAppealView({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).purchase_appeal_detail),
        centerTitle: true,
        elevation: 0,
      ),
      // backgroundColor: Colors.white,
      body: SafeArea(
        child: Obx(() {
          PurchaseAppealEntity? appealInfo = controller.appealInfo.value;
          if (appealInfo == null) {
            return const SizedBox();
          } else {
            List<Widget> widgets = [];
            //0未申诉 1已申诉 2已通过 3已拒绝 4已取消
            if (appealInfo.status == 0) {
              widgets = [_postContentView];
            } else if (appealInfo.status == 1) {
              widgets = [
                _createAppealView,
                _postContentView,
              ];
            } else if (appealInfo.status == 2) {
              widgets = [_approveAppealView, _createAppealView, _postContentView];
            } else if (appealInfo.status == 3) {
              widgets = [_refuseAppealView, _createAppealView, _postContentView];
            } else if (appealInfo.status == 4) {
              widgets = [
                const SizedBox(
                  height: 12,
                ),
                _cancelAppealView,
                _createAppealView,
                _postContentView,
              ];
            }
            return ListView(
              // mainAxisSize: MainAxisSize.min,
              children: widgets,
            );
          }
        }),
      ),
    );
  }

  Widget get _postContentView {
    PurchaseReplyEntity? replyInfo = controller.appealInfo.value?.replyInfo;
    if (replyInfo == null) {
      return const SizedBox();
    }
    return Container(
        margin: const EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 10),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(children: [
          Row(children: [
            Avatar(url: replyInfo.user?.head, displayName: replyInfo.user?.nick, size: AVATAR_SIZE),
            const SizedBox(
              width: 8,
            ),
            Expanded(
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(S.of(Get.context!).purchase_post_publish,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.medium15.apply(color: MColor.xFF333333)),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(replyInfo.createdAt,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.medium15.apply(color: MColor.xFF333333)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ]),
          _contentView
        ]));
  }

  Widget get _createAppealView {
    return Container(
        margin: const EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 10),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(children: [
          _headView,
          const SizedBox(
            height: 12,
          ),
          Row(
            children: [
              Expanded(
                  flex: 1,
                  child: Text(S.of(Get.context!).purchase_appeal_request_price,
                      strutStyle: const StrutStyle(
                        forceStrutHeight: true,
                        leading: 0.5,
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.start,
                      style: MFont.medium15.apply(color: MColor.xFF333333))),
              const SizedBox(
                width: 8,
              ),
              Expanded(
                flex: 3,
                child: Text(controller.appealInfo.value!.price,
                    strutStyle: const StrutStyle(
                      forceStrutHeight: true,
                      leading: 0.5,
                    ),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.start,
                    style: MFont.medium15.apply(color: MColor.xFF333333)),
              )
            ],
          ),
          Row(
            children: [
              Expanded(
                  flex: 1,
                  child: Text(S.of(Get.context!).purchase_appeal_request_reason,
                      strutStyle: const StrutStyle(
                        forceStrutHeight: true,
                        leading: 0.5,
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.start,
                      style: MFont.medium15.apply(color: MColor.xFF333333))),
              const SizedBox(
                width: 8,
              ),
              Expanded(
                flex: 3,
                child: Text(controller.appealInfo.value!.note,
                    strutStyle: const StrutStyle(
                      forceStrutHeight: true,
                      leading: 0.5,
                    ),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.start,
                    style: MFont.medium15.apply(color: MColor.xFF333333)),
              )
            ],
          )
        ]));
  }

  Widget get _headView {
    return Row(
      children: [
        Avatar(url: GlobalConst.userModel?.head, displayName: GlobalConst.userModel?.nick, size: AVATAR_SIZE),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(S.of(Get.context!).purchase_appeal_submit,
                  strutStyle: const StrutStyle(
                    forceStrutHeight: true,
                    leading: 0.5,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.start,
                  style: MFont.medium15.apply(color: MColor.xFF333333)),
              Text(controller.appealInfo.value!.created_at,
                  strutStyle: const StrutStyle(
                    forceStrutHeight: true,
                    leading: 0.5,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.start,
                  style: MFont.medium15.apply(color: MColor.xFF333333)),
            ],
          ),
        ),
      ],
    );
  }

  Widget get _approveAppealView {
    return Container(
        margin: const EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 10),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(children: [
          Row(
            children: [
              Avatar(url: GlobalConst.userModel?.head, displayName: GlobalConst.userModel?.nick, size: AVATAR_SIZE),
              const SizedBox(
                width: 8,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(S.of(Get.context!).purchase_appeal_approve,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: MFont.medium15.apply(color: MColor.xFF333333)),
                    Text(controller.appealInfo.value!.completed_time,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: MFont.medium15.apply(color: MColor.xFF333333)),
                  ],
                ),
              ),
            ],
          ),
        ]));
  }

  Widget get _refuseAppealView {
    return Container(
        margin: const EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 10),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(children: [
          Row(
            children: [
              Avatar(url: GlobalConst.userModel?.head, displayName: GlobalConst.userModel?.nick, size: AVATAR_SIZE),
              const SizedBox(
                width: 8,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(S.of(Get.context!).purchase_appeal_denied,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: MFont.medium15.apply(color: MColor.xFF333333)),
                    Text(controller.appealInfo.value!.completed_time,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: MFont.medium15.apply(color: MColor.xFF333333)),
                  ],
                ),
              ),
            ],
          ),
        ]));
  }

  Widget get _cancelAppealView {
    return Container(
        margin: const EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 10),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(children: [
          Row(
            children: [
              Avatar(url: GlobalConst.userModel?.head, displayName: GlobalConst.userModel?.nick, size: AVATAR_SIZE),
              const SizedBox(
                width: 8,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(S.of(Get.context!).purchase_appeal_cancel,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: MFont.medium15.apply(color: MColor.xFF333333)),
                    Text(controller.appealInfo.value!.updated_at,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: MFont.medium15.apply(color: MColor.xFF333333)),
                  ],
                ),
              ),
            ],
          ),
        ]));
  }

  Widget get _paidContentView {
    PurchasePaidContentEntity? paidContent = controller.appealInfo.value?.replyInfo?.paidContent;
    if (paidContent == null) {
      return const SizedBox();
    }
    return DottedBorder(
      color: MColor.xFFD95F66,
      borderType: BorderType.RRect,
      padding: const EdgeInsets.only(top: 0, bottom: 12, left: 6, right: 6),
      child: Stack(
        children: [
          Positioned(
            child: Column(
              children: [
                const SizedBox(
                  height: 12,
                ),
                const SizedBox(
                  height: 12,
                ),
                Row(
                  children: [
                    const Icon(
                      CupertinoIcons.chevron_left_2,
                      color: MColor.skin,
                      size: 12,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Expanded(child: _paidContentDetailView),
                    const SizedBox(
                      width: 4,
                    ),
                    const Icon(
                      CupertinoIcons.chevron_right_2,
                      color: MColor.skin,
                      size: 12,
                    ),
                  ],
                ),
                const SizedBox(
                  height: 12,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget get _contentView {
    PurchaseReplyEntity? replyInfo = controller.appealInfo.value?.replyInfo;
    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (replyInfo?.type == 2) ...{
            _paidContentView,
          },
          const SizedBox(
            height: 12,
          ),
        ],
      ),
    );
  }

  Widget get _paidContentDetailView {
    PurchasePaidContentEntity? paidContent = controller.appealInfo.value?.replyInfo?.paidContent;
    if (paidContent == null) {
      return const SizedBox();
    }
    var leadingStyle = const TextStyle(fontSize: 14, color: MColor.xFF777777, height: 1.5);
    var contentStyle = const TextStyle(fontSize: 14, color: MColor.xFF000000, height: 1.5, fontWeight: FontWeight.w600);
    var leadingFlex = 1;
    var contentFlex = 3;
    return Column(
      children: [
        if (paidContent!.supplier.isNotEmpty) ...{
          Row(
            children: [
              Expanded(
                flex: leadingFlex,
                child: Text(S.of(Get.context!).purchase_comment_paid_supplier, style: leadingStyle),
              ),
              const SizedBox(
                width: 4,
              ),
              Expanded(
                flex: contentFlex,
                child: Text(paidContent!.supplier, textAlign: TextAlign.end, style: contentStyle),
              ),
            ],
          ),
          const SizedBox(
            height: 4,
          ),
        },
        if (paidContent!.contact.isNotEmpty) ...{
          Row(
            children: [
              Expanded(
                flex: leadingFlex,
                child: Text(S.of(Get.context!).purchase_comment_paid_contact, style: leadingStyle),
              ),
              const SizedBox(
                width: 4,
              ),
              Expanded(
                flex: contentFlex,
                child: Text(paidContent!.contact, textAlign: TextAlign.end, style: contentStyle),
              ),
            ],
          ),
          const SizedBox(
            height: 4,
          ),
        },
        if (paidContent!.phone.isNotEmpty) ...{
          Row(
            children: [
              Expanded(
                flex: leadingFlex,
                child: Text(S.of(Get.context!).purchase_comment_paid_phone, style: leadingStyle),
              ),
              const SizedBox(
                width: 4,
              ),
              Expanded(
                flex: contentFlex,
                child: RichText(
                  textAlign: TextAlign.end,
                  text: TextSpan(
                    text: paidContent!.phone,
                    style: contentStyle.apply(decoration: TextDecoration.underline, color: MColor.xFF0081E7),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        String url = 'tel:${paidContent!.phone}';
                        launchUrl(Uri.parse(url));
                      },
                    children: const [
                      WidgetSpan(
                        child: Icon(
                          Icons.call,
                          size: 14,
                          color: MColor.xFF0081E7,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 4,
          ),
        },
        if (paidContent!.email.isNotEmpty) ...{
          Row(
            children: [
              Expanded(
                flex: leadingFlex,
                child: Text(S.of(Get.context!).purchase_comment_paid_email, style: leadingStyle),
              ),
              const SizedBox(
                width: 4,
              ),
              Expanded(
                flex: contentFlex,
                child: RichText(
                  textAlign: TextAlign.end,
                  text: TextSpan(
                    text: paidContent!.email,
                    style: contentStyle.apply(decoration: TextDecoration.underline, color: MColor.xFF0081E7),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        String url = 'mailto:${paidContent!.email}';
                        launchUrl(Uri.parse(url));
                      },
                    children: const [
                      WidgetSpan(
                        child: Icon(
                          Icons.email_outlined,
                          size: 14,
                          color: MColor.xFF0081E7,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 4,
          ),
        },
        if (paidContent!.addr.isNotEmpty) ...{
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: leadingFlex,
                child: Text(S.of(Get.context!).purchase_comment_paid_address, style: leadingStyle),
              ),
              const SizedBox(
                width: 4,
              ),
              Expanded(
                flex: contentFlex,
                child: Text(paidContent!.addr, textAlign: TextAlign.end, style: contentStyle),
              ),
            ],
          ),
          const SizedBox(
            height: 4,
          ),
        },
        if (paidContent.other.isNotEmpty) ...{
          Row(
            children: [
              Expanded(
                flex: leadingFlex,
                child: Text(S.of(Get.context!).purchase_comment_paid_other, style: leadingStyle),
              ),
              const SizedBox(
                width: 4,
              ),
              Expanded(
                flex: contentFlex,
                child: Text(paidContent.other, textAlign: TextAlign.end, style: contentStyle),
              ),
            ],
          ),
          const SizedBox(
            height: 4,
          ),
        },
        if (paidContent.lowPrice.isNotEmpty) ...{
          Row(
            children: [
              Expanded(
                flex: leadingFlex,
                child: Text(S.of(Get.context!).purchase_comment_paid_low_price, style: leadingStyle),
              ),
              const SizedBox(
                width: 4,
              ),
              Expanded(
                flex: contentFlex,
                child: Text(paidContent.lowPrice, textAlign: TextAlign.end, style: contentStyle),
              ),
            ],
          ),
        }
      ],
    );
  }
}
