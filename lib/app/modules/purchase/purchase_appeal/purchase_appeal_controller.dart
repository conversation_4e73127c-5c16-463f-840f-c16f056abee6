import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';

class PurchaseAppealController extends GetxController {
  final PurchaseService _service = PurchaseService();

  final appealInfo = Rxn<PurchaseAppealEntity>();

  @override
  void onInit() {
    super.onInit();
    var replyId = Get.arguments['reply_id'];
    var id = Get.arguments['id'];
    if (replyId == null && id == null) {
      Get.back();
      return;
    }
    _getAppealInfo(id: id, replyId: replyId);
  }

  Future<void> _getAppealInfo({int? id, int? replyId}) async {
    unawaited(EasyLoading.show());
    var results;
    if (id != null) {
      results = await _service.getAppealInfo(id: id);
    } else {
      results = await _service.getAppealInfo(replyId: replyId);
    }
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        appealInfo.value = resp;
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  @override
  void onClose() {
    super.onClose();
  }
}
