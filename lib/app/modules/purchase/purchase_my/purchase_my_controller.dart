import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class MyPurchaseController extends GetxController with GetSingleTickerProviderStateMixin {
  TabController? tabController;
  TextEditingController editingController = TextEditingController();
  TextEditingController noteController = TextEditingController();
  final isCheck = true.obs;
  final accountType = 1.obs;
  FocusNode focusNode = FocusNode();

  @override
  void onInit() {
    super.onInit();
    logger.i('MyPurchaseController onInit $hashCode');
    tabController = TabController(length: 3, vsync: this);

    int index = 0;
    tabController?.addListener(() {
      var isChange = !(tabController?.indexIsChanging ?? true);
      if (index != tabController?.index && isChange) {
        index = tabController?.index ?? 0;
        if (index == 0) {
          Get.find<MyPostsController>().getMyPosts();
        } else if (index == 1) {
          Get.find<MyRepliesController>().getMyReplies();
        } else {
          Get.find<MyAppealsController>().getMyAppeals();
        }
      }
    });
    Get.find<MyPostsController>().getMyPosts();
  }

  @override
  void onClose() {
    super.onClose();
    tabController?.dispose();
  }
}

class MyPostsController extends GetxController {
  final PurchaseService _service = PurchaseService();
  RefreshController refreshController = RefreshController();

  final purchaseList = <PurchaseItemEntity>[].obs;
  int offset = 0;
  int totalCount = 0;

  @override
  void onInit() {
    super.onInit();
  }

  Future<void> getMyPosts() async {
    unawaited(EasyLoading.show());
    offset = 0;
    var results = await _service.getMyPostList(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        totalCount = resp.total;
        purchaseList.value = resp.list;
      } else {
        purchaseList.clear();
      }
    }

    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (purchaseList.isEmpty || (totalCount != 0 && purchaseList.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  Future<void> getMore() async {
    unawaited(EasyLoading.show());
    offset = purchaseList.last.id ?? 0;
    var results = await _service.getMyPostList(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        totalCount = resp.total;
        purchaseList.addAll(resp.list);
      }
    }

    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (purchaseList.isEmpty || (totalCount != 0 && purchaseList.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  @override
  void onClose() {
    super.onClose();
    refreshController.dispose();
  }
}

class MyAppealsController extends GetxController {
  final PurchaseService _service = PurchaseService();
  RefreshController refreshController = RefreshController();

  final appeals = <PurchaseAppealEntity>[].obs;
  int offset = 0;
  int totalCount = 0;

  @override
  void onInit() {
    super.onInit();
  }

  Future<void> getMyAppeals() async {
    unawaited(EasyLoading.show());
    offset = 0;
    var results = await _service.getMyAppeals(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      appeals.clear();
      if (resp != null) {
        totalCount = resp.total;
        appeals.addAll(resp.list);
      }
    }
    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (appeals.isEmpty || (totalCount != 0 && appeals.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  Future<void> getMore() async {
    unawaited(EasyLoading.show());
    offset = appeals.last.id;
    var results = await _service.getMyAppeals(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        totalCount = resp.total;
        appeals.addAll(resp.list);
      }
    }
    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (appeals.isEmpty || (totalCount != 0 && appeals.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  @override
  void onClose() {
    super.onClose();
    refreshController.dispose();
  }
}

class MyRepliesController extends GetxController {
  final PurchaseService _service = PurchaseService();
  RefreshController refreshController = RefreshController();

  final purchaseReplies = <PurchaseMyReplyEntity>[].obs;
  int offset = 0;
  int totalCount = 0;

  @override
  void onInit() {
    super.onInit();
  }

  Future<void> getMyReplies() async {
    unawaited(EasyLoading.show());
    offset = 0;
    var results = await _service.getMyReplies(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      purchaseReplies.clear();
      if (resp != null) {
        totalCount = resp.total;
        purchaseReplies.addAll(resp.replies);
      }
    }
    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (purchaseReplies.isEmpty || (totalCount != 0 && purchaseReplies.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  Future<void> getMore() async {
    unawaited(EasyLoading.show());
    offset = purchaseReplies.last.reply?.id ?? 0;
    var results = await _service.getMyReplies(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        totalCount = resp.total;
        purchaseReplies.addAll(resp.replies);
      }
    }
    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (purchaseReplies.isEmpty || (totalCount != 0 && purchaseReplies.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  @override
  void onClose() {
    super.onClose();
    refreshController.dispose();
  }
}
