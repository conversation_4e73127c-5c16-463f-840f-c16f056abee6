import 'package:flutter/material.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/shared/widgets/avatar.dart';
import 'package:inspector/app/tools/global_const.dart';

class PurchaseMyHeadView extends StatelessWidget {
  final Widget? statusView;

  const PurchaseMyHeadView({this.statusView}) : super();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _avatar,
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _nickView,
              if (statusView != null) statusView!
              // _statusView,
            ],
          ),
        )
      ],
    );
  }

  Widget get _nickView {
    var user = GlobalConst.userModel;
    var userNick = '';
    if (user != null && user.name != null) {
      userNick = user.name!;
    }
    return Row(
      children: [
        Expanded(
          child: Text(userNick,
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
              style: MFont.medium15.apply(color: MColor.xFF333333)),
        ),
      ],
    );
  }

  Widget get _avatar {
    var user = GlobalConst.userModel;
    return Avatar(url: user?.head, displayName: user?.nick, size: AVATAR_SIZE);
  }
}
