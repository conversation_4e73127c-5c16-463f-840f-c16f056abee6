import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_appeal/purchase_appeal_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_appeal/purchase_appeal_view.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_appeals/purchase_my_appeals_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_empty_view.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_posts/purchase_my_posts_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_replies/purchase_my_replies_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_widgets/purchase_item_view.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:rotated_corner_decoration/rotated_corner_decoration.dart';

import '../../../../../generated/l10n.dart';

class PurchaseMyPostsView extends GetView<PurchaseMyPostsController> {
  const PurchaseMyPostsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(S.of(Get.context!).mine_purchase_mine),
          centerTitle: true,
          elevation: 0,
        ),
        body: SafeArea(child: Obx(() {
          var itemCount = controller.purchaseList.isEmpty ? 1 : controller.purchaseList.length;
          return SmartRefresher(
            controller: controller.refreshController,
            onRefresh: () => controller.getMyPosts(),
            child: ListView.builder(
              padding: const EdgeInsets.only(top: 10),
              itemCount: itemCount,
              itemBuilder: (ctx, index) {
                if (controller.purchaseList.isEmpty) {
                  return const PurchaseMyEmptyView();
                } else {
                  return PurchaseItemView(controller.purchaseList[index], bgColor: MColor.xFFFFFFFF);
                }
              },
            ),
          );
        })));
  }
}
