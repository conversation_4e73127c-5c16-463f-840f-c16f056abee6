import 'dart:async';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class PurchaseMyPostsController extends GetxController {
  final PurchaseService _service = PurchaseService();
  RefreshController refreshController = RefreshController();

  final purchaseList = <PurchaseItemEntity>[].obs;
  int offset = 0;
  int totalCount = 0;

  @override
  void onInit() {
    super.onInit();
    getMyPosts();
  }

  Future<void> getMyPosts() async {
    unawaited(EasyLoading.show());
    offset = 0;
    var results = await _service.getMyPostList(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        totalCount = resp.total;
        purchaseList.value = resp.list;
      } else {
        purchaseList.clear();
      }
    }

    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (purchaseList.isEmpty || (totalCount != 0 && purchaseList.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  Future<void> getMore() async {
    unawaited(EasyLoading.show());
    offset = purchaseList.last.id ?? 0;
    var results = await _service.getMyPostList(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        totalCount = resp.total;
        purchaseList.addAll(resp.list);
      }
    }

    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (purchaseList.isEmpty || (totalCount != 0 && purchaseList.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  @override
  void onClose() {
    super.onClose();
    refreshController.dispose();
  }
}
