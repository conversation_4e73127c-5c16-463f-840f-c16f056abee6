import 'package:get/get.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_appeals/purchase_my_appeals_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_posts/purchase_my_posts_controller.dart';

class PurchaseMyPostsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<PurchaseMyPostsController>(() => PurchaseMyPostsController());
  }
}
