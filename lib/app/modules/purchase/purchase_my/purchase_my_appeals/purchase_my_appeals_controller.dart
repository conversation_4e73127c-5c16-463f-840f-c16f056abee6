import 'dart:async';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class PurchaseMyAppealsController extends GetxController {
  final PurchaseService _service = PurchaseService();
  RefreshController refreshController = RefreshController();

  final appeals = <PurchaseAppealEntity>[].obs;
  int offset = 0;
  int totalCount = 0;

  @override
  void onInit() {
    super.onInit();
    getMyAppeals();
  }

  Future<void> getMyAppeals() async {
    unawaited(EasyLoading.show());
    offset = 0;
    var results = await _service.getMyAppeals(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      appeals.clear();
      if (resp != null) {
        totalCount = resp.total;
        appeals.addAll(resp.list);
      }
    }
    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (appeals.isEmpty || (totalCount != 0 && appeals.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  Future<void> getMore() async {
    unawaited(EasyLoading.show());
    offset = appeals.last.id;
    var results = await _service.getMyAppeals(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        totalCount = resp.total;
        appeals.addAll(resp.list);
      }
    }
    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (appeals.isEmpty || (totalCount != 0 && appeals.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  @override
  void onClose() {
    super.onClose();
    refreshController.dispose();
  }
}
