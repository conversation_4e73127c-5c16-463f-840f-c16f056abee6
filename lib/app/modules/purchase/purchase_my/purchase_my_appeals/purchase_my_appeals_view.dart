import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_appeal/purchase_appeal_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_appeal/purchase_appeal_view.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_appeals/purchase_my_appeals_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_empty_view.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_head_view.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_replies/purchase_my_replies_controller.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:rotated_corner_decoration/rotated_corner_decoration.dart';

import '../../../../../generated/l10n.dart';

class PurchaseMyAppealsView extends GetView<PurchaseMyAppealsController> {
  const PurchaseMyAppealsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(S.of(Get.context!).mine_purchase_appeal),
          centerTitle: true,
          elevation: 0,
        ),
        body: SafeArea(child: Obx(() {
          var itemCount = controller.appeals.isEmpty ? 1 : controller.appeals.length;
          return SmartRefresher(
            controller: controller.refreshController,
            onRefresh: () => controller.getMyAppeals(),
            child: ListView.builder(
              padding: const EdgeInsets.only(top: 10),
              itemCount: itemCount,
              itemBuilder: (ctx, index) {
                if (controller.appeals.isEmpty) {
                  return const PurchaseMyEmptyView();
                } else {
                  return appealView(controller.appeals[index]);
                }
              },
            ),
          );
        })));
  }

  Widget appealView(PurchaseAppealEntity appealInfo) {
    String? appealStatus;
    Color cornerColor = MColor.skin;
    //0未申诉 1已申诉 2已通过 3已拒绝 4已取消
    if (appealInfo.status == 1) {
      appealStatus = S.of(Get.context!).public_status_applied;
    } else if (appealInfo.status == 2) {
      appealStatus = S.of(Get.context!).public_status_approved;
      cornerColor = MColor.xFF40A900;
    } else if (appealInfo.status == 3) {
      appealStatus = S.of(Get.context!).public_status_refused;
      cornerColor = MColor.xFFD95F66;
    } else if (appealInfo.status == 4) {
      appealStatus = S.of(Get.context!).public_status_canceled;
      cornerColor = MColor.xFFD7A17C;
    }
    return GestureDetector(
      onTap: () {
        Get.to(() => PurchaseAppealView(tag: appealInfo.id.toString()),
            preventDuplicates: true, arguments: {'id': appealInfo.id}, binding: PurchaseAppealBinding(tag: appealInfo.id.toString()));
      },
      child: Container(
          margin: const EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 10),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
          decoration: BoxDecoration(
            color: MColor.xFFFFFFFF,
            borderRadius: BorderRadius.circular(6),
          ),
          foregroundDecoration: appealStatus != null
              ? RotatedCornerDecoration.withColor(
                  color: cornerColor,
                  spanBaselineShift: 0,
                  badgeSize: const Size(48, 48),
                  badgeCornerRadius: const Radius.circular(6),
                  badgePosition: BadgePosition.topEnd,
                  textSpan: TextSpan(
                    text: appealStatus,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      letterSpacing: 1,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
              : null,
          child: Column(
            children: [
              const PurchaseMyHeadView(),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  Expanded(
                      flex: 1,
                      child: Text(S.of(Get.context!).purchase_appeal_request_reason,
                          strutStyle: const StrutStyle(
                            forceStrutHeight: true,
                            leading: 0.5,
                          ),
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.start,
                          style: MFont.medium15.apply(color: MColor.xFF333333))),
                  const SizedBox(
                    width: 8,
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(appealInfo.note,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: MFont.medium15.apply(color: MColor.xFF333333)),
                  )
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              _postInfoView(appealInfo.replyInfo)
            ],
          )),
    );
  }

  Widget _postInfoView(PurchaseReplyEntity? replyInfo) {
    if (replyInfo == null) {
      return const SizedBox();
    }
    return Container(
      constraints: const BoxConstraints(minWidth: double.maxFinite, maxWidth: double.maxFinite),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: MColor.xFFCFCFCF_50,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(children: [
        Expanded(
          child: Text('${S.of(Get.context!).purchase_my_replies_original_header}: ${replyInfo.content}',
              textAlign: TextAlign.center,
              maxLines: 2,
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              style: MFont.regular14.apply(color: MColor.xFF000000)),
        )
      ]),
    );
  }
}
