import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';

import '../../../../generated/l10n.dart';

class PurchaseMyEmptyView extends StatelessWidget {
  const PurchaseMyEmptyView({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: Get.height / 2,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.hourglass_empty,
              size: 40,
              color: MColor.xFF999999,
            ),
            const SizedBox(height: 10),
            Text(
              S.of(Get.context!).no_data,
              style: MFont.regular15.apply(color: MColor.xFF666666),
            ),
          ],
        ),
      ),
    );
  }
}
