import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_empty_view.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_head_view.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_replies/purchase_my_replies_controller.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../generated/l10n.dart';

class PurchaseMyRepliesView extends GetView<PurchaseMyRepliesController> {
  const PurchaseMyRepliesView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(S.of(Get.context!).mine_purchase_reply),
          centerTitle: true,
          elevation: 0,
        ),
        body: SafeArea(child: Obx(() {
          var itemCount = controller.purchaseReplies.isEmpty ? 1 : controller.purchaseReplies.length;
          return SmartRefresher(
            controller: controller.refreshController,
            onRefresh: () => controller.getMyReplies(),
            child: ListView.builder(
              padding: const EdgeInsets.only(top: 10),
              itemCount: itemCount,
              itemBuilder: (ctx, index) {
                if (controller.purchaseReplies.isEmpty) {
                  return const PurchaseMyEmptyView();
                } else {
                  return _getReplyView(controller.purchaseReplies[index]);
                }
              },
            ),
          );
        })));
  }

  Widget _getReplyView(PurchaseMyReplyEntity replyInfo) {
    return Container(
      margin: const EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 10),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      decoration: BoxDecoration(
        color: MColor.xFFFFFFFF,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          PurchaseMyHeadView(
            statusView: _statusView(replyInfo),
          ),
          const SizedBox(
            height: 12,
          ),
          _replyInfoView(replyInfo),
          const SizedBox(
            height: 12,
          ),
          _postInfoView(replyInfo)
        ],
      ),
    );
  }

  Widget _replyInfoView(PurchaseMyReplyEntity myReplyInfo) {
    return Row(
      children: [
        Expanded(
          child: Text('${S.of(Get.context!).purchase_reply_to}: ${myReplyInfo.reply?.content}',
              textAlign: TextAlign.start,
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              style: MFont.regular14.apply(color: MColor.xFF000000)),
        ),
      ],
    );
  }

  Widget _postInfoView(PurchaseMyReplyEntity myReplyInfo) {
    return Container(
      constraints: const BoxConstraints(minWidth: double.maxFinite, maxWidth: double.maxFinite),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: MColor.xFFCFCFCF_50,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(children: [
        Expanded(
          child: Text('${S.of(Get.context!).purchase_my_replies_original_header}: ${myReplyInfo.topic?.title}',
              textAlign: TextAlign.center,
              maxLines: 2,
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              style: MFont.regular14.apply(color: MColor.xFF000000)),
        )
      ]),
    );
  }

  Widget _statusView(PurchaseMyReplyEntity replyInfo) {
    String content = '';
    if (replyInfo.reply?.updatedAt.isNotEmpty == true) {
      content += '${formatRecentTime(replyInfo.reply!.updatedAt)} ';
    }
    if (replyInfo.reply?.ipAddress.isNotEmpty == true) {
      content += '${S.of(Get.context!).public_ip_address} ${replyInfo.reply!.ipAddress}';
    }
    return Row(
      children: [
        Expanded(
          child: Text(content,
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
              style: MFont.regular12.apply(color: MColor.xFF999999)),
        ),
      ],
    );
  }
}
