import 'dart:async';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class PurchaseMyRepliesController extends GetxController {
  final PurchaseService _service = PurchaseService();
  RefreshController refreshController = RefreshController();

  final purchaseReplies = <PurchaseMyReplyEntity>[].obs;
  int offset = 0;
  int totalCount = 0;

  @override
  void onInit() {
    super.onInit();
    getMyReplies();
  }

  Future<void> getMyReplies() async {
    unawaited(EasyLoading.show());
    offset = 0;
    var results = await _service.getMyReplies(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      purchaseReplies.clear();
      if (resp != null) {
        totalCount = resp.total;
        purchaseReplies.addAll(resp.replies);
      }
    }
    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (purchaseReplies.isEmpty || (totalCount != 0 && purchaseReplies.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  Future<void> getMore() async {
    unawaited(EasyLoading.show());
    offset = purchaseReplies.last.reply?.id ?? 0;
    var results = await _service.getMyReplies(offset, 10);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        totalCount = resp.total;
        purchaseReplies.addAll(resp.replies);
      }
    }
    unawaited(EasyLoading.dismiss());
    refreshController.refreshCompleted();
    if (purchaseReplies.isEmpty || (totalCount != 0 && purchaseReplies.length == totalCount)) {
      refreshController.loadNoData();
    } else {
      refreshController.loadComplete();
    }
  }

  @override
  void onClose() {
    super.onClose();
    refreshController.dispose();
  }
}
