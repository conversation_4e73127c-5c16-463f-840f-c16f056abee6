import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/purchase/purchase_complaint/purchase_complaint_controller.dart';

import '../../../../generated/l10n.dart';

class PurchaseComplaintView extends GetView<PurchaseComplaintController> {
  @override
  final String? tag;

  final int replyId;

  final int reportSource;

  const PurchaseComplaintView(this.replyId, this.reportSource, {Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put<PurchaseComplaintController>(PurchaseComplaintController(reportSource), tag: tag);
    return Container(
      color: MColor.xFFFFFFFF,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _titleView,
            const SizedBox(
              height: 12,
            ),
            _typeView,
            const SizedBox(
              height: 12,
            ),
            _textView,
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    style: ButtonStyle(
                      padding: MaterialStateProperty.all(const EdgeInsets.symmetric(vertical: 8)),
                      textStyle: MaterialStateProperty.all(MFont.regular13),
                      backgroundColor: MaterialStateProperty.all(MColor.skin),
                    ),
                    onPressed: () {
                      controller.submitComplaint(replyId);
                    },
                    child: Text(
                      S.of(Get.context!).apply_submit,
                      style: const TextStyle(fontSize: 13, color: MColor.xFFFFFFFF, height: 1),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget get _titleView {
    return Row(
      children: [
        Expanded(
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '*',
                  style: MFont.regular16.apply(color: MColor.skin),
                ),
                const WidgetSpan(
                  child: SizedBox(width: 4),
                ),
                TextSpan(
                  text: S.of(Get.context!).purchase_complaint_type_leading,
                  style: MFont.regular16.apply(color: MColor.xFF000000),
                )
              ],
            ),
          ),
        ),
        const SizedBox(
          width: 12,
        ),
      ],
    );
  }

  Widget get _typeView {
    var gridHeight = 32;
    var gridWidth = (MediaQuery.of(Get.context!).size.width - 24 - 24) / 3;

    List<Widget> widgets = List.generate(controller.complaintTypes.length, (position) {
      return GestureDetector(
        onTap: () {
          controller.selectedType.value = position + 1;
        },
        child: Obx(() {
          ComplaintType complaintType = controller.complaintTypes[position];
          var selected = complaintType.pageId == controller.selectedType.value;
          return Container(
            // height: 24,
            decoration: BoxDecoration(
              color: !selected ? MColor.xFFCCCCCC : MColor.skin,
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
            child: Text(
              S.of(Get.context!).purchase_complaint_type(complaintType.value),
              style: TextStyle(fontSize: 14, color: selected ? MColor.xFFFFFFFF : MColor.xFF2A2A2A, height: 1),
              textAlign: TextAlign.center,
              maxLines: 1,
            ),
          );
        }),
      );
    });
    return Row(
      children: [
        Expanded(
          child: Wrap(
            children: widgets,
            spacing: 10.0,
            runSpacing: 8.0,
            // shrinkWrap: true,
            // physics: const NeverScrollableScrollPhysics(),
            // gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            //     crossAxisCount: 3, mainAxisSpacing: 10.0, crossAxisSpacing: 10.0, childAspectRatio: gridWidth / gridHeight),
          ),
        ),
      ],
    );
  }

  Widget get _textView {
    return TextField(
      controller: controller.textController,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.only(left: 4, right: 4, top: 2, bottom: 2),
        hintText: S.of(Get.context!).purchase_complaint_hint,
        hintStyle: MFont.regular13.apply(color: MColor.xFF999999),
        labelStyle: MFont.regular13.apply(color: MColor.xFF333333),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
        ),
        constraints: const BoxConstraints(minHeight: 50, maxHeight: 100),
      ),
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.search,
      onSubmitted: (value) {
        controller.submitComplaint(replyId);
      },
      onChanged: (val) {},
      minLines: 2,
      maxLines: 4,
      maxLength: 10000,
      autofocus: true,
    );
  }
}
