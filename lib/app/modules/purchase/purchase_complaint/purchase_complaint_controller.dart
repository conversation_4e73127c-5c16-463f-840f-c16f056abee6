import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:inspector/app/modules/message/chat_provider.dart';
import 'package:inspector/app/modules/purchase/purchase_controller.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

enum ComplaintType {
  Type1(1, 'type_1'),
  Type2(2, 'type_2'),
  Type3(3, 'type_3'),
  Type4(4, 'type_4'),
  Type5(5, 'type_5'),
  Type6(6, 'type_6'),
  Type7(7, 'type_7');

  final int pageId;
  final String value;

  const ComplaintType(this.pageId, this.value);
}

class PurchaseComplaintController extends PurchaseController {
  final TextEditingController textController = TextEditingController();

  final int reportSource; // 0 采购 1 聊天 2 举报用户

  PurchaseComplaintController(this.reportSource);

  int rating = 0;
  final selectedType = 0.obs;

  final complaintTypes = [
    ComplaintType.Type1,
    ComplaintType.Type2,
    ComplaintType.Type3,
    ComplaintType.Type4,
    ComplaintType.Type5,
    ComplaintType.Type6,
    ComplaintType.Type7,
  ];

  Future<void> submitComplaint(int replyId) async {
    unawaited(EasyLoading.show());
    var result = reportSource == 0
        ? await service.submitComplaint(replyId, selectedType.toString(), textController.text)
        : (reportSource == 1
            ? await ChatProvider.submitComplaint(replyId, selectedType.toString(), textController.text)
            : await ChatProvider.submitUserReport(replyId, selectedType.toString(), textController.text));
    if (result.isSuccess) {
      Get.back(result: true);
      showToast(S.of(Get.context!).report_success);
    }
    unawaited(EasyLoading.dismiss());
  }

  @override
  void onClose() {
    super.onClose();
    textController.dispose();
  }
}
