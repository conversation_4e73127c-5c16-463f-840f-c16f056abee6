import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_comment/purchase_comment_view.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class PurchaseCommentToolbar extends StatelessWidget {
  final PurchaseReplyEntity replyInfo;
  final int purchaseId;

  final DynamicCallback? onLikeCallback;
  final DynamicCallback? onReplyCallback;

  final bool isPostAuthor; //回复楼主

  const PurchaseCommentToolbar(this.purchaseId, this.replyInfo, {super.key, this.onReplyCallback, this.onLikeCallback, this.isPostAuthor = false});

  @override
  Widget build(BuildContext context) {
    var header = replyInfo;
    if (header == null) {
      return const SizedBox();
    }
    return SizedBox(
      height: 46,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12),
        child: Row(
          children: [
            Expanded(
                child: GestureDetector(
                    onTap: () {
                      Get.bottomSheet(SafeArea(child: WriteCommentWidget(isPostAuthor, header.user!, replyInfo.content, tag: 'write_comment-${replyInfo.id}')),
                          persistent: false,
                          isScrollControlled: true,
                          ignoreSafeArea: false,
                          settings: RouteSettings(name: 'comment', arguments: {'purchase_id': purchaseId, 'pid': replyInfo.id})).then((value) {
                        onReplyCallback?.call(value);
                      });
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                            child: Container(
                          constraints: const BoxConstraints(minHeight: 32, maxHeight: 32),
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: const BoxDecoration(
                            color: MColor.xFFEEEEEE,
                            borderRadius: BorderRadius.all(Radius.circular(16)),
                          ),
                          alignment: Alignment.centerLeft,
                          child: Text(
                            S.of(Get.context!).purchase_reply_hint,
                            style: MFont.regular13.apply(color: MColor.xFF999999),
                          ),
                        )),
                        const SizedBox(
                          width: 12,
                        ),
                        GestureDetector(
                          onTap: () {
                            onLikeCallback?.call(null);
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Icon(
                              Icons.thumb_up_outlined,
                              size: 20,
                              color: header.zanStatus == 1 ? MColor.xFFE95332 : MColor.xFF000000,
                            ),
                          ),
                        ),
                      ],
                    )))
          ],
        ),
      ),
    );
  }
}
