import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_reply/purchase_reply_view.dart';
import 'package:inspector/app/shared/widgets/avatar.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:url_launcher/url_launcher.dart';

import '../../../../generated/l10n.dart';

class PurchaseCommonReplyView extends StatelessWidget {
  @override
  final String? tag;

  final PurchaseReplyEntity replyInfo;

  final bool? showPaidContent;
  final bool? showSubReplies;

  final VoidCallback? onContentTapped;

  const PurchaseCommonReplyView(this.replyInfo, {Key? key, this.tag, this.showPaidContent = true, this.showSubReplies = true, this.onContentTapped})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
        child: Column(
          children: [
            _headView,
            _contentView,
          ],
        ));
  }

  Widget get _headView {
    var user = replyInfo.user;
    var userNick = '';
    if (user != null && user.nick != null) {
      userNick = user.nick!;
    }
    return Row(
      children: [
        _avatar,
        const SizedBox(
          width: 8,
        ),
        Expanded(
          child: Text(userNick,
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
              style: MFont.medium15.apply(color: MColor.xFF333333)),
        ),
      ],
    );
  }

  Widget get _contentView {
    String content = replyInfo.content;
    String replyToName = replyInfo.replyTo?.nick ?? '';
    String prefix = '';
    if (replyToName.isNotEmpty) {
      prefix = '${S.of(Get.context!).purchase_reply_to} $replyToName: ';
    }
    return GestureDetector(
      onTap: () {
        onContentTapped?.call();
      },
      child: Padding(
        padding: const EdgeInsets.only(left: AVATAR_SIZE + 8, top: 4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                    child: Text('$prefix$content',
                        textAlign: TextAlign.start,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        style: MFont.regular14.apply(color: MColor.xFF000000))),
              ],
            ),
            const SizedBox(
              height: 12,
            ),
            // if (replyInfo.type == 2) ...{
            //   _paidContentView,
            //   const SizedBox(
            //     height: 12,
            //   ),
            // },
            Row(
              children: [
                Expanded(
                  child: Text('${formatRecentTime(replyInfo.createdAt)} ${replyInfo.ipAddress}',
                      textAlign: TextAlign.start,
                      strutStyle: const StrutStyle(
                        forceStrutHeight: true,
                        leading: 0.5,
                      ),
                      style: MFont.regular10.apply(color: MColor.xFF999999)),
                ),
              ],
            ),
            const SizedBox(
              height: 12,
            ),
          ],
        ),
      ),
    );
  }

  Widget get _avatar {
    var user = replyInfo.user;
    return Avatar(url: user?.head, displayName: user?.nick, size: AVATAR_SIZE);
  }
}
