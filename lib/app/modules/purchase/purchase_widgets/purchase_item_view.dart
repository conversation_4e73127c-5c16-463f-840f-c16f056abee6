import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_view.dart';
import 'package:inspector/app/modules/purchase/purchase_edit/purchase_edit_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_edit/purchase_edit_view.dart';
import 'package:inspector/app/routes/app_pages.dart';

import 'package:rotated_corner_decoration/rotated_corner_decoration.dart';

import '../../../../generated/l10n.dart';
import '../../../tools/icons.dart';

class PurchaseItemView extends StatelessWidget {
  final PurchaseItemEntity entity;
  final Color? bgColor;
  const PurchaseItemView(this.entity, {super.key, this.bgColor});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (entity.status == 0) {
          Get.toNamed(Routes.PURCHASE_PUBLISH, parameters: {'purchase_id': entity.id.toString(), 'is_edit': '1'}, preventDuplicates: true);
          // Get.to(() => PurchaseEditView(tag: entity.id.toString()),
          // preventDuplicates: true, arguments: {'purchase_id': entity.id}, binding: PurchaseEditBinding(tag: entity.id.toString()));
        } else {
          Get.to(() => PurchaseDetailView(tag: entity.id.toString()),
              preventDuplicates: true, arguments: {'purchase_id': entity.id}, binding: PurchaseDetailBinding(tag: entity.id.toString()));
        }
      },
      child: Builder(builder: (context) {
        String? statusLabel;
        Color cornerColor = MColor.skin;
        if (entity.status == 1) {
          if (entity.verify == 0) {
            statusLabel = S.of(Get.context!).purchase_post_status_reviewing;
          } else if (entity.verify == 2) {
            statusLabel = S.of(Get.context!).purchase_post_status_denied;
          }
        } else if (entity.status == 0) {
          statusLabel = S.of(Get.context!).purchase_post_status_draft;
        }
        return Card(
          elevation: 0,
          color: MColor.xFFFFFFFF,
          margin: EdgeInsets.fromLTRB(15, 10, 15, 0),
          child: Container(
              padding: const EdgeInsets.only(left: 12, right: 12, top: 19, bottom: 10),
              decoration: BoxDecoration(
                color: bgColor ?? MColor.xFFDDDDDD.withAlpha(50),
                borderRadius: BorderRadius.circular(10),
              ),
              foregroundDecoration: statusLabel != null
                  ? RotatedCornerDecoration.withColor(
                      color: cornerColor,
                      spanBaselineShift: 0,
                      badgeSize: const Size(48, 48),
                      badgeCornerRadius: const Radius.circular(6),
                      badgePosition: BadgePosition.topEnd,
                      textSpan: TextSpan(
                        text: statusLabel,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          letterSpacing: 1,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  : null,
              child: Column(
                children: [
                  _purchaseItemHeadView(entity),
                  const SizedBox(
                    height: 8,
                  ),
                  _purchaseItemContentView(entity),
                  _purchaseTagsView(entity),
                  _purchaseItemBottomView(entity)
                ],
              )),
        );
      }),
    );
  }

  Widget _purchaseItemBottomView(PurchaseItemEntity entity) {
    return Row(
      children: [
        Expanded(
          child: Row(mainAxisSize: MainAxisSize.min, mainAxisAlignment: MainAxisAlignment.center, children: [
            AppIcons.icon(
              0xe60f,
              size: 14,
              color: MColor.xFF808080,
            ),
            const SizedBox(width: 4),
            Text('0',
                textAlign: TextAlign.center,
                strutStyle: const StrutStyle(
                  forceStrutHeight: true,
                  leading: 0.5,
                ),
                style: TextStyle(color: MColor.xFF808080))
          ]),
        ),
        Expanded(
          child: Row(mainAxisSize: MainAxisSize.min, mainAxisAlignment: MainAxisAlignment.center, children: [
            AppIcons.icon(
              0xe62f,
              size: 14,
              color: MColor.xFF808080,
            ),
            const SizedBox(width: 4),
            Text('${entity.reading}',
                textAlign: TextAlign.center,
                strutStyle: const StrutStyle(
                  forceStrutHeight: true,
                  leading: 0.5,
                ),
                style: TextStyle(color: MColor.xFF808080))
          ]),
        ),
        Expanded(
          child: Row(mainAxisSize: MainAxisSize.min, mainAxisAlignment: MainAxisAlignment.center, children: [
            AppIcons.icon(
              0xe67a,
              size: 14,
              color: MColor.xFF808080,
            ),
            const SizedBox(
              width: 4,
            ),
            Text('${entity.reply}',
                strutStyle: const StrutStyle(
                  forceStrutHeight: true,
                  leading: 0.5,
                ),
                textAlign: TextAlign.center,
                style: TextStyle(color: MColor.xFF808080))
          ]),
        )
      ],
    );
  }

  Widget _purchaseItemContentView(PurchaseItemEntity entity) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: Text(entity.title ?? '', style: TextStyle(fontSize: 16, color: MColor.xFF000000))),
          ],
        ),
        if (entity.pic.isNotEmpty) ...{
          const SizedBox(
            height: 8,
          ),
          GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
              ),
              itemBuilder: (context, position) {
                return _purchaseItemPreview(entity.pic[position]);
              },
              itemCount: min(3, entity.pic.length))
        },
        const SizedBox(
          height: 8,
        ),
      ],
    );
  }

  Widget _purchaseTagsView(PurchaseItemEntity entity) {
    if (entity.tags.isEmpty) {
      return const SizedBox();
    } else {
      return Column(
        children: [
          SizedBox(
            height: 20,
            child: ListView.separated(
                itemBuilder: (BuildContext context, int position) {
                  return GestureDetector(
                    onTap: () {},
                    child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
                        decoration: BoxDecoration(
                          color: MColor.xFFEEEEEE,
                          borderRadius: const BorderRadius.all(Radius.circular(12)),
                        ),
                        child: Center(
                          child: Text(entity.tags[position].val ?? '',
                              textAlign: TextAlign.center, style: TextStyle(height: 1, fontSize: 11, color: MColor.xFF666666)),
                        )),
                  );
                },
                scrollDirection: Axis.horizontal,
                separatorBuilder: (BuildContext context, int position) {
                  return const SizedBox(
                    width: 10,
                  );
                },
                itemCount: entity.tags.length),
          ),
          const SizedBox(
            height: 8,
          )
        ],
      );
    }
  }

  Widget _purchaseItemPreview(String picUrl) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(1),
      child: CachedNetworkImage(
        imageUrl: picUrl,
        width: 88,
        height: 88,
        fit: BoxFit.cover,
        placeholder: (ctx, e) {
          return Container(
            decoration: BoxDecoration(
              color: Get.context!.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
              borderRadius: BorderRadius.circular(1),
            ),
          );
        },
        errorWidget: (ctx, e, x) {
          return Container(
            decoration: BoxDecoration(
              color: Get.context!.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
              borderRadius: BorderRadius.circular(1),
            ),
          );
        },
      ),
    );
  }

  Widget _purchaseItemHeadView(PurchaseItemEntity entity) {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: CachedNetworkImage(
            imageUrl: entity.user?.head ?? '',
            width: 40,
            height: 40,
            fit: BoxFit.cover,
            placeholder: (ctx, e) {
              return Container(
                decoration: BoxDecoration(
                  color: Get.context!.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                  borderRadius: BorderRadius.circular(20),
                ),
              );
            },
            errorWidget: (ctx, e, x) {
              return Container(
                decoration: BoxDecoration(
                  color: Get.context!.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                  borderRadius: BorderRadius.circular(20),
                ),
              );
            },
          ),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
            child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Text((entity.user?.nick ?? ''),
                      strutStyle: const StrutStyle(
                        forceStrutHeight: true,
                        leading: 0.5,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.start,
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Get.context!.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000)),
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                    child: Text(entity.updatedAt,
                        strutStyle: const StrutStyle(
                          forceStrutHeight: true,
                          leading: 0.5,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: MFont.regular12.apply(color: Get.context!.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999))),
              ],
            )
          ],
        )),
        if (entity.bounty.isNotEmpty && double.tryParse(entity.bounty) != 0) ...{
          const SizedBox(width: 4),
          Text('${S.of(Get.context!).purchase_bounty_money}${entity.account.symbol}${entity.bounty}',
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              maxLines: 1,
              textAlign: TextAlign.end,
              style: TextStyle(color: MColor.xFFE8040A, fontSize: 14)),
          const SizedBox(
            width: 12,
          ),
        }
      ],
    );
  }
}
