import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:inspector/app/widgets/price_input_widget.dart';

import '../../../../generated/l10n.dart';

class PurchaseAppealPopupController extends GetxController {
  final TextEditingController textController = TextEditingController();
  final PurchaseService _service = PurchaseService();

  Future<void> submitAppeal(int replyId) async {
    unawaited(EasyLoading.show());
    var result = await _service.submitAppeal(replyId, textController.text);
    if (result.isSuccess) {
      Get.back(result: true);
    }
    unawaited(EasyLoading.dismiss());
  }

  @override
  void onClose() {
    super.onClose();
    textController.dispose();
  }
}

class PurchaseAppealPopupView extends GetView<PurchaseAppealPopupController> {
  @override
  final String? tag;

  final PurchaseReplyEntity replyInfo;

  const PurchaseAppealPopupView(this.replyInfo, {Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put<PurchaseAppealPopupController>(PurchaseAppealPopupController(), tag: tag);
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(S.of(Get.context!).purchase_appeal_title,
              textAlign: TextAlign.start,
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              style: MFont.regular16.apply(color: MColor.xFF000000)),
          const SizedBox(
            height: 6,
          ),
          const Divider(
            height: 1,
          ),
          const SizedBox(
            height: 6,
          ),
          _textView,
          Row(
            children: [
              Expanded(
                child: TextButton(
                  style: ButtonStyle(
                    padding: MaterialStateProperty.all(const EdgeInsets.symmetric(vertical: 8)),
                    textStyle: MaterialStateProperty.all(MFont.regular13),
                    backgroundColor: MaterialStateProperty.all(MColor.skin),
                  ),
                  onPressed: () {
                    controller.submitAppeal(replyInfo.id);
                  },
                  child: Text(
                    S.of(Get.context!).apply_submit,
                    style: const TextStyle(fontSize: 13, color: MColor.xFFFFFFFF, height: 1),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget get _textView {
    return TextField(
      controller: controller.textController,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.only(left: 4, right: 4, top: 2, bottom: 2),
        hintText: S.of(Get.context!).purchase_appeal_reason,
        hintStyle: MFont.regular13.apply(color: MColor.xFF999999),
        labelStyle: MFont.regular13.apply(color: MColor.xFF333333),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
        ),
        constraints: const BoxConstraints(minHeight: 50, maxHeight: 100),
      ),
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.search,
      onSubmitted: (value) {
        controller.submitAppeal(replyInfo.id);
      },
      onChanged: (val) {},
      minLines: 2,
      maxLines: 4,
      maxLength: 10000,
      autofocus: true,
    );
  }
}
