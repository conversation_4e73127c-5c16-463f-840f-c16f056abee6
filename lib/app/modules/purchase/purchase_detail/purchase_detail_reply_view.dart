import 'dart:async';
import 'dart:ui';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_appeal/purchase_appeal_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_appeal/purchase_appeal_view.dart';
import 'package:inspector/app/modules/purchase/purchase_comment/purchase_comment_view.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_appeal_popup.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_author_view.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_more_view.dart';
import 'package:inspector/app/modules/purchase/purchase_reply/purchase_reply_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_reply/purchase_reply_view.dart';
import 'package:inspector/app/modules/purchase/purchase_review/purchase_review_view.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/shared/widgets/avatar.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:url_launcher/url_launcher.dart';

import '../../../../generated/assets.dart';
import '../../../../generated/l10n.dart';

const int MAX_REPLY_COUNT = 4;

class PurchaseDetailReplyWidget extends GetView<PurchaseDetailController> {
  @override
  final String? tag;

  final PurchaseReplyEntity replyInfo;

  final bool? showPaidContent;
  final bool? showSubReplies;

  const PurchaseDetailReplyWidget(this.replyInfo, {Key? key, this.tag, this.showPaidContent = true, this.showSubReplies = true}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        controller.showWriteCommentWidget(false, tag, replyInfo);
      },
      child: Padding(
          padding: const EdgeInsets.only(left: 15, right: 15, top: 8, bottom: 8),
          child: Column(
            children: [
              _headView,
              _contentView,
            ],
          )),
    );
  }

  Widget get _headView {
    var user = replyInfo.user;
    var userNick = '';
    if (user != null && user.nick != null) {
      userNick = user.nick!;
    }
    return Row(
      children: [
        _avatar,
        const SizedBox(
          width: 11,
        ),
        Expanded(
          child:
              Text(userNick, maxLines: 1, overflow: TextOverflow.ellipsis, textAlign: TextAlign.start, style: MFont.regular16.apply(color: MColor.xFF000000)),
        ),
        GestureDetector(
          onTap: () {
            Get.bottomSheet(
                BottomSheet(
                    onClosing: () {},
                    // constraints: const BoxConstraints(maxHeight: 3 * SORT_HEIGHT + 28, minHeight: 3 * SORT_HEIGHT + 28),
                    builder: (context) {
                      return PurchaseDetailMoreView(replyInfo, false, tag: tag);
                    }),
                shape: RoundedRectangleBorder(
                  side: BorderSide(
                    color: Theme.of(Get.context!).colorScheme.outline,
                  ),
                  borderRadius: const BorderRadius.only(topLeft: Radius.circular(6), topRight: Radius.circular(6)),
                ),
                clipBehavior: Clip.antiAliasWithSaveLayer,
                settings: RouteSettings(name: 'comment', arguments: {'purchase_id': controller.purchaseId, 'pid': replyInfo.id})).then((value) {
              if (value is Map && value.containsKey('isSuccess') && value['isSuccess']) {
                controller.getSingleReplies(replyInfo.id!);
              }
            });
          },
          child: const Padding(
            padding: EdgeInsets.all(8.0),
            child: Icon(Icons.more_horiz, size: 14),
          ),
        )
      ],
    );
  }

  Widget get _paidContentView {
    return Builder(builder: (context) {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: RichText(
                  textAlign: TextAlign.start,
                  text: TextSpan(
                    text: S.of(Get.context!).purchase_reply_paid_title,
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: MColor.skin),
                    children: [
                      if (replyInfo.userId != GlobalConst.userModel?.uid && replyInfo.payInfo?.payStatus == 0) ...{
                        //未支付时才显示
                        TextSpan(
                            text: S.of(Get.context!).purchase_reply_paid_desc,
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: MColor.xFF000000)),
                      }
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 8,
          ),
          Builder(builder: (context) {
            if (replyInfo.userId == GlobalConst.userModel?.uid || (replyInfo.payInfo != null && replyInfo.payInfo?.payStatus != 0)) {
              //自己的帖子，或者已经支付了
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (replyInfo.paidContent!.lowPrice.isNotEmpty) ...{
                    RichText(
                      textAlign: TextAlign.start,
                      text: TextSpan(
                        text: '${S.of(Get.context!).purchase_comment_paid_low_price}: ',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: MColor.xFF000000),
                        children: [
                          //未支付时才显示
                          TextSpan(text: replyInfo.paidContent!.lowPrice, style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: MColor.xFF000000)),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                  },
                  Container(
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: MColor.xFFF6F6F6),
                    padding: const EdgeInsets.all(24.0),
                    // borderType: BorderType.RRect,
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  if (replyInfo.paidContent!.supplier.isNotEmpty) ...{
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Text(replyInfo.paidContent!.supplier,
                                              textAlign: TextAlign.start, style: TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: MColor.xFF000000)),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 4,
                                    ),
                                  },
                                  if (replyInfo.paidContent!.contact.isNotEmpty) ...{
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Text(replyInfo.paidContent!.contact,
                                              textAlign: TextAlign.start,
                                              style: const TextStyle(fontSize: 18, color: MColor.xFF000000, fontWeight: FontWeight.w500)),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 4,
                                    ),
                                  },
                                ],
                              ),
                            ),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                AppIcons.icon(
                                  0xe614,
                                  size: 32,
                                  color: MColor.skin,
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  if (replyInfo.paidContent!.phone.isNotEmpty) ...{
                                    GestureDetector(
                                      onTap: () {
                                        String url = 'tel:${replyInfo.paidContent!.phone}';
                                        launchUrl(Uri.parse(url));
                                      },
                                      child: Row(
                                        children: [
                                          AppIcons.icon(0xe842, size: 14, color: MColor.skin),
                                          const SizedBox(
                                            width: 4,
                                          ),
                                          Expanded(
                                            child: RichText(
                                              text: TextSpan(
                                                children: [
                                                  TextSpan(
                                                      text: replyInfo.paidContent!.phone,
                                                      style: const TextStyle(
                                                          fontSize: 12,
                                                          color: MColor.xFF000000,
                                                          fontWeight: FontWeight.w500,
                                                          decoration: TextDecoration.underline))
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 8,
                                    ),
                                  },
                                  if (replyInfo.paidContent!.email.isNotEmpty) ...{
                                    GestureDetector(
                                      onTap: () {
                                        String url = 'mailto:${replyInfo.paidContent!.email}';
                                        launchUrl(Uri.parse(url));
                                      },
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          AppIcons.icon(0xe62a, size: 14, color: MColor.skin),
                                          const SizedBox(
                                            width: 4,
                                          ),
                                          Expanded(
                                            child: RichText(
                                              text: TextSpan(
                                                recognizer: TapGestureRecognizer()
                                                  ..onTap = () {
                                                    String url = 'mailto:${replyInfo.paidContent!.email}';
                                                    launchUrl(Uri.parse(url));
                                                  },
                                                children: [
                                                  TextSpan(
                                                      text: replyInfo.paidContent!.email,
                                                      style: const TextStyle(
                                                          fontSize: 12,
                                                          color: MColor.xFF000000,
                                                          fontWeight: FontWeight.w500,
                                                          decoration: TextDecoration.underline))
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 8,
                                    ),
                                  },
                                  if (replyInfo.paidContent!.addr.isNotEmpty) ...{
                                    GestureDetector(
                                      onTap: () {
                                        Location.share.toNavigation(null, null, replyInfo.paidContent!.addr);
                                      },
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          AppIcons.icon(0xe641, size: 14, color: MColor.skin),
                                          const SizedBox(
                                            width: 4,
                                          ),
                                          Expanded(
                                            child: RichText(
                                              text: TextSpan(
                                                children: [
                                                  TextSpan(
                                                      text: replyInfo.paidContent!.addr,
                                                      style: const TextStyle(fontSize: 12, color: MColor.xFF000000, fontWeight: FontWeight.w500))
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 4,
                                    ),
                                  },
                                  if (replyInfo.paidContent!.other.isNotEmpty) ...{
                                    Row(
                                      children: [
                                        AppIcons.icon(0xe618, size: 14, color: MColor.skin),
                                        const SizedBox(
                                          width: 4,
                                        ),
                                        Expanded(
                                          child: Text(replyInfo.paidContent!.other,
                                              textAlign: TextAlign.start, style: TextStyle(fontSize: 12, color: MColor.xFF000000, fontWeight: FontWeight.w500)),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 4,
                                    ),
                                  },
                                ],
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              );
            } else {
              return Column(
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.toNamed(Routes.PURCHASE_PAY, arguments: {'reply_id': replyInfo.id!})?.then((value) {
                        if (value is bool && value == true) {
                          //刷新
                          controller.getSingleReplies(replyInfo.id!);
                        }
                      });
                    },
                    child: Container(
                      alignment: Alignment.center,
                      constraints: const BoxConstraints.expand(height: 124),
                      decoration: BoxDecoration(
                          color: MColor.xFF000000.withOpacity(0.1),
                          image: DecorationImage(
                              image: AssetImage(
                                Assets.purchaseUnpaidBg,
                              ),
                              repeat: ImageRepeat.repeat)),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  S.of(Get.context!).purchase_paid_content_tips,
                                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: MColor.skin),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          TextButton(
                              style: ButtonStyle(
                                padding: MaterialStateProperty.all(const EdgeInsets.symmetric(vertical: 8, horizontal: 16)),
                                textStyle: MaterialStateProperty.all(MFont.regular13),
                                backgroundColor: MaterialStateProperty.all(MColor.skin),
                              ),
                              onPressed: () {
                                Get.toNamed(Routes.PURCHASE_PAY, arguments: {'reply_id': replyInfo.id!})?.then((value) {
                                  if (value is bool && value == true) {
                                    //刷新
                                    controller.getSingleReplies(replyInfo.id!);
                                  }
                                });
                              },
                              child: RichText(
                                  textAlign: TextAlign.end,
                                  text: TextSpan(
                                    recognizer: TapGestureRecognizer()..onTap = () {},
                                    children: [
                                      WidgetSpan(
                                        child: AppIcons.icon(
                                          0xe606,
                                          size: 14,
                                          color: MColor.xFFFFFFFF,
                                        ),
                                      ),
                                      const WidgetSpan(child: SizedBox(width: 4)),
                                      TextSpan(
                                        text: '${S.of(Get.context!).pay_pay} (${replyInfo.account.symbol} ${replyInfo.price})',
                                        style: const TextStyle(fontSize: 13, color: MColor.xFFFFFFFF, height: 1),
                                      )
                                    ],
                                  ))),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            }
          }),
          Builder(builder: (context) {
            if (replyInfo.userId != GlobalConst.userModel?.uid &&
                replyInfo.payInfo != null &&
                replyInfo.payInfo?.payStatus != 0 &&
                replyInfo.evaluateStatus == 0) {
              //非自己的帖子，并且已经支付，并且未评价过，显示评价按钮
              return Column(
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    children: [
                      const Spacer(),
                      SizedBox(
                        height: 30,
                        child: TextButton(
                          style: ButtonStyle(
                            textStyle: WidgetStateProperty.all(MFont.regular13),
                            backgroundColor: WidgetStateProperty.all(MColor.xFFFFFFFF),
                            // shape: WidgetStateProperty.all(const StadiumBorder()),
                            side: WidgetStateProperty.all(BorderSide(color: MColor.skin, width: 1)),
                            visualDensity: VisualDensity.compact,
                          ),
                          onPressed: () {
                            Get.bottomSheet(SafeArea(
                              child: PurchaseReviewView(replyInfo.id, tag: tag),
                            )).then((value) {
                              if (value is bool && value == true) {
                                //刷新
                                controller.getSingleReplies(replyInfo.id);
                              }
                            });
                          },
                          child: Text(
                            S.of(Get.context!).purchase_review_leave,
                            style: const TextStyle(fontSize: 13, color: MColor.skin),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                    ],
                  ),
                ],
              );
            } else if (replyInfo.userId != GlobalConst.userModel?.uid &&
                replyInfo.payInfo != null &&
                replyInfo.payInfo?.payStatus != 0 &&
                replyInfo.evaluateStatus != 0) {
              //非自己的帖子，并且已经支付，并且评价过，显示我的评价
              return Column(
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    children: [
                      Text('${replyInfo.myEvaluate?.score}',
                          textAlign: TextAlign.start, style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: MColor.xFF000000)),
                      const SizedBox(
                        width: 4,
                      ),
                      AbsorbPointer(
                        child: RatingBar.builder(
                          initialRating: replyInfo.myEvaluate?.score ?? 0,
                          minRating: 1,
                          direction: Axis.horizontal,
                          allowHalfRating: false,
                          itemCount: 5,
                          itemSize: 16,
                          itemPadding: const EdgeInsets.symmetric(horizontal: 1.0),
                          itemBuilder: (context, _) => const Icon(
                            Icons.star,
                            size: 16,
                            color: MColor.skin,
                          ),
                          onRatingUpdate: (rating) {},
                        ),
                      ),
                      Text(S.of(Get.context!).purchase_reply_paid_my_evaluate,
                          textAlign: TextAlign.start, style: TextStyle(fontSize: 12, fontWeight: FontWeight.w300, color: MColor.xFF000000)),
                    ],
                  ),
                ],
              );
            } else {
              //自己的帖子，或者未支付，显示整体评价
              return Column(
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    children: [
                      Text('${replyInfo.evaluate}',
                          textAlign: TextAlign.start, style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: MColor.xFF000000)),
                      const SizedBox(
                        width: 4,
                      ),
                      AbsorbPointer(
                        child: RatingBar.builder(
                          initialRating: replyInfo.evaluate,
                          minRating: 1,
                          direction: Axis.horizontal,
                          allowHalfRating: false,
                          itemCount: 5,
                          itemSize: 16,
                          itemPadding: const EdgeInsets.symmetric(horizontal: 1.0),
                          itemBuilder: (context, _) => const Icon(
                            Icons.star,
                            size: 16,
                            color: MColor.skin,
                          ),
                          onRatingUpdate: (rating) {},
                        ),
                      ),
                      Text(S.of(Get.context!).purchase_reply_paid_evaluated_people(replyInfo.evaluateCount),
                          textAlign: TextAlign.start, style: TextStyle(fontSize: 12, fontWeight: FontWeight.w300, color: MColor.xFF000000)),
                    ],
                  ),
                ],
              );
            }
          })
        ],
      );
    });
  }

  Widget get _contentView {
    String content = replyInfo.content;
    return Padding(
      padding: const EdgeInsets.only(left: AVATAR_SIZE + 8, top: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                  child: Text(content,
                      textAlign: TextAlign.start,
                      strutStyle: const StrutStyle(
                        forceStrutHeight: true,
                        leading: 0.5,
                      ),
                      style: MFont.regular14.apply(color: MColor.xFF000000))),
            ],
          ),
          const SizedBox(
            height: 12,
          ),
          if (replyInfo.type == 2) ...{
            _paidContentView,
            const SizedBox(
              height: 12,
            ),
          },
          Row(
            children: [
              Expanded(
                child: Text('${formatRecentTime(replyInfo.createdAt)} ${replyInfo.ipAddress}',
                    textAlign: TextAlign.start, style: TextStyle(fontSize: 12, height: 1, fontWeight: FontWeight.w300, color: MColor.xFF000000)),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  controller.changeLikeStatus(false, replyInfo);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      Icon(
                        Icons.thumb_up_outlined,
                        size: 14,
                        color: replyInfo.zanStatus == 1 ? MColor.skin : MColor.xFF808080,
                      ),
                      if (replyInfo.zan != 0)
                        const SizedBox(
                          width: 2,
                        ),
                      Builder(builder: (context) {
                        int thumbUps = replyInfo.zan;
                        if (thumbUps == 0) {
                          return const SizedBox();
                        } else {
                          return Text(thumbUps > 99 ? '99+' : '$thumbUps',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.start,
                              style: TextStyle(
                                  fontSize: 12, height: 1, fontWeight: FontWeight.w300, color: replyInfo.zanStatus == 1 ? MColor.skin : MColor.xFF000000));
                        }
                      }),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 12,
          ),
          if (replyInfo.reply.isNotEmpty) ...{
            GestureDetector(
              onTap: () {
                Get.bottomSheet(SafeArea(child: PurchaseReplyView(tag: replyInfo.id.toString())),
                    persistent: false,
                    isScrollControlled: true,
                    ignoreSafeArea: false,
                    settings: RouteSettings(name: 'reply_view', arguments: {'purchase_id': controller.purchaseId, 'reply_id': replyInfo.id})).then((value) {
                  if (value is Map && value.containsKey('hasChanged')) {
                    var hasChanged = value['hasChanged'];
                    if (hasChanged) {
                      controller.getSingleReplies(replyInfo.id);
                    }
                  }
                });
                // Get.to(() => PurchaseReplyView(tag: replyInfo.id.toString()),
                //     preventDuplicates: true, arguments: {'reply_id': replyInfo.id}, binding: PurchaseReplyBinding(tag: replyInfo.id.toString()));
              },
              child: Container(
                constraints: const BoxConstraints(minWidth: double.maxFinite, maxWidth: double.maxFinite),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                decoration: BoxDecoration(
                  color: MColor.xFFF6F6F6,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (int i = 0; i < MAX_REPLY_COUNT && i < replyInfo.reply.length; i++)
                      _ReplyView(
                        replyInfo.reply[i],
                        tag: tag,
                      ),
                    if (replyInfo.reply.length > MAX_REPLY_COUNT) ...{
                      const SizedBox(
                        height: 5,
                      ),
                      Row(
                        children: [
                          Expanded(
                              child: Text(
                            S.of(Get.context!).more_replies,
                            style: TextStyle(color: MColor.xFF2A82E4),
                            textAlign: TextAlign.center,
                          )),
                        ],
                      )
                    }
                  ],
                ),
              ),
            )
          }
        ],
      ),
    );
  }

  Widget get _avatar {
    var user = replyInfo.user;
    return Avatar(url: user?.head, displayName: user?.nick, size: AVATAR_SIZE);
  }
}

class _ReplyView extends StatelessWidget {
  @override
  final String? tag;

  final PurchaseReplyEntity purchaseAuthor;

  const _ReplyView(this.purchaseAuthor, {Key? key, this.tag}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: RichText(
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          softWrap: true,
          text: TextSpan(
              text: '${purchaseAuthor.user!.nick}: ',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: MColor.xFF000000),
              //手势监听
              // recognizer: ,
              children: [
                TextSpan(
                  text: ' ${purchaseAuthor.content} ',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: MColor.xFF000000),
                ),
              ])),
    );
  }
}
