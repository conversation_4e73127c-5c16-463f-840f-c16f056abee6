import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_controller.dart';
import 'package:inspector/app/shared/widgets/avatar.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class PurchaseDetailAuthorWidget extends GetView<PurchaseDetailController> {
  @override
  final String? tag;

  const PurchaseDetailAuthorWidget({Key? key, this.tag}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 15, right: 15, top: 8, bottom: 8),
          child: Column(
            children: [
              _headerView,
              const SizedBox(
                height: 15,
              ),
              _titleView,
              const SizedBox(
                height: 12,
              ),
              _contentView,
            ],
          ),
        ),
        if (controller.purchaseInfo.value!.pic.isNotEmpty) ...{
          // GridView.builder(
          //   padding: const EdgeInsets.all(0),
          //   physics: const NeverScrollableScrollPhysics(),
          //   shrinkWrap: true,
          //   itemCount: controller.purchaseInfo.value!.pic.length,
          //   itemBuilder: (context, childIdx) {
          //     return _imageView(controller.purchaseInfo.value!.pic[childIdx]);
          //   },
          //   gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 2, mainAxisSpacing: 19, crossAxisSpacing: 19),
          // ),
          for (var i = 0; i < controller.purchaseInfo.value!.pic.length; i++) ...{
            _imageView(controller.purchaseInfo.value!.pic[i]),
          }
        },
        const SizedBox(
          height: 12,
        ),
        _shareView,
        const SizedBox(
          height: 16,
        ),
      ],
    );
  }

  Widget get _headerView {
    return Row(
      children: [
        _avatar,
        const SizedBox(
          width: 11,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [_nickView, _statusView],
          ),
        ),
        _bountyView
      ],
    );
  }

  Widget get _bountyView {
    if (controller.purchaseInfo.value?.bounty != 0) {
      return Text(
          '${S.of(Get.context!).purchase_bounty_money} ${controller.purchaseInfo.value!.account.symbol} ${controller.purchaseInfo.value!.bounty.toStringAsFixed(2)}',
          textAlign: TextAlign.end,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: MColor.xFFE8040A));
    } else {
      return const SizedBox();
    }
  }

  Widget get _statusView {
    String content = '';
    PurchaseReplyEntity? replyInfo = controller.purchaseInfo.value?.replyInfo;
    if (replyInfo != null) {
      if (replyInfo.updatedAt.isNotEmpty) {
        content += '${formatRecentTime(replyInfo.updatedAt)} ';
      }
      if (replyInfo.ipAddress.isNotEmpty) {
        content += '${S.of(Get.context!).public_ip_address} ${replyInfo.ipAddress}';
      }
    }
    return Row(
      children: [
        Expanded(
          child: Text(content,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w300, color: MColor.xFF000000)),
        ),
      ],
    );
  }

  Widget get _nickView {
    var user = controller.purchaseInfo.value?.user;
    var userNick = '';
    if (user != null && user.nick != null) {
      userNick = user.nick!;
    }
    return Row(
      children: [
        Expanded(
          child: Text(userNick, maxLines: 1, overflow: TextOverflow.ellipsis, textAlign: TextAlign.start, style: MFont.medium16.apply(color: MColor.xFF000000)),
        ),
      ],
    );
  }

  Widget get _avatar {
    var user = controller.purchaseInfo.value?.user;
    return Avatar(url: user?.head, displayName: user?.nick, size: AVATAR_SIZE);
  }

  Widget _imageView(String imageUrl) {
    return Container(
      padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
      // color: Theme.of(Get.context!).colorScheme.conatiner,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            var imageProvider = Image.network(imageUrl).image;
            showImageViewer(
              Get.context!,
              imageProvider,
              onViewerDismissed: () {},
              immersive: false,
              useSafeArea: true,
            );
          },
          child: Builder(builder: (context) {
            return Container(
              // color: Theme.of(Get.context!).colorScheme.conatiner,
              child: CachedNetworkImage(
                imageUrl: imageUrl,
                placeholder: (ctx, _) {
                  return const Center(
                    child: SpinKitCircle(
                      color: MColor.skin,
                      size: 40.0,
                    ),
                  );
                },
                errorWidget: (ctx, a1, a2) {
                  return const Center(
                    child: SpinKitCircle(
                      color: MColor.skin,
                      size: 40.0,
                    ),
                  );
                },
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget get _shareView {
    PurchaseReplyEntity? replyInfo = controller.purchaseInfo.value?.replyInfo;
    if (replyInfo == null) {
      return const SizedBox();
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Expanded(
            child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              IconButton(
                onPressed: () {},
                icon: const Icon(
                  Icons.share_outlined,
                  size: 20,
                  color: MColor.xFF000000,
                ),
              ),
              Text(S.of(Get.context!).public_share, textAlign: TextAlign.center, style: MFont.regular14.apply(color: MColor.xFF808080))
            ],
          ),
        )),
        Expanded(
            child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              IconButton(
                onPressed: () {
                  controller.changeLikeStatus(true, replyInfo);
                },
                icon: Icon(
                  Icons.thumb_up_outlined,
                  size: 20,
                  color: replyInfo.zanStatus == 1 ? MColor.xFFE95332 : MColor.xFF000000,
                ),
              ),
              Builder(builder: (BuildContext context) {
                return Text('${replyInfo.zan}',
                    textAlign: TextAlign.center, style: MFont.regular14.apply(color: replyInfo.zanStatus == 1 ? MColor.xFFE95332 : MColor.xFF808080));
              }),
            ],
          ),
        )),
        // if (GlobalConst.userModel?.uid == replyInfo.user?.uid) ...{
        //   Expanded(
        //       child: Padding(
        //     padding: const EdgeInsets.all(8.0),
        //     child: Column(
        //       children: [
        //         IconButton(
        //           onPressed: () {},
        //           icon: const Icon(
        //             Icons.paid_outlined,
        //             size: 20,
        //             color: MColor.xFF000000,
        //           ),
        //         ),
        //         Text(S.of(Get.context!).purchase_modify_bounty,
        //             textAlign: TextAlign.center,
        //             strutStyle: const StrutStyle(
        //               forceStrutHeight: true,
        //               leading: 0.5,
        //             ),
        //             style: MFont.regular13.apply(color: MColor.xFF999999))
        //       ],
        //     ),
        //   )),
        // }
      ],
    );
  }

  Widget get _titleView {
    String? title = controller.purchaseInfo.value?.title;
    return Row(
      children: [
        Expanded(child: Text(title ?? '', textAlign: TextAlign.start, style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: MColor.xFF000000))),
      ],
    );
  }

  Widget get _contentView {
    PurchaseReplyEntity? replyInfo = controller.purchaseInfo.value?.replyInfo;
    if (replyInfo == null) {
      return const SizedBox();
    }
    String content = replyInfo.content;
    return Row(
      children: [
        Expanded(child: Text(content, textAlign: TextAlign.start, style: MFont.regular14.apply(color: MColor.xFF000000))),
      ],
    );
  }
}
