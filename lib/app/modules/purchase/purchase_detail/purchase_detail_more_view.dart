import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_appeal/purchase_appeal_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_appeal/purchase_appeal_view.dart';
import 'package:inspector/app/modules/purchase/purchase_complaint/purchase_complaint_view.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_appeal_popup.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_edit/purchase_edit_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_edit/purchase_edit_view.dart';
import 'package:inspector/app/modules/purchase/purchase_publish/purchase_publish_view.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';

import '../../../../generated/l10n.dart';

class PurchaseDetailMoreView extends GetView<PurchaseDetailController> {
  final String? tag;
  final PurchaseReplyEntity replyInfo;
  final bool isPostAuthor; //是否是一楼，一楼如果是楼主，可以编辑帖子

  const PurchaseDetailMoreView(this.replyInfo, this.isPostAuthor, {super.key, this.tag});

  @override
  Widget build(BuildContext context) {
    List<Widget> tiles = _buildTiles();
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
            ),
            itemBuilder: (context, position) {
              return tiles[position];
            },
            itemCount: tiles.length),
      ],
    );
  }

  List<Widget> _buildTiles() {
    List<Widget> tiles = [];
    if (isPostAuthor && replyInfo.user?.uid == GlobalConst.userModel?.uid) {
      tiles.add(_TileView(
        () {
          Get.back();
          Get.toNamed(Routes.PURCHASE_PUBLISH, parameters: {'purchase_id': controller.purchaseId.toString(), 'is_edit': '1'}, preventDuplicates: true)
              ?.then((value) {
            if (value is Map && value.containsKey('isSuccess') && value['isSuccess']) {
              controller.getPurchaseDetails();
            }
          });
          // Get.to(() => PurchaseEditView(tag: controller.purchaseId.toString()),
          //         preventDuplicates: true,
          //         arguments: {'purchase_id': controller.purchaseId, 'is_edit': true},
          //         binding: PurchaseEditBinding(tag: controller.purchaseId.toString()))
          //     ?.then((value) {
          //   if (value is Map && value.containsKey('isSuccess') && value['isSuccess']) {
          //     controller.getPurchaseDetails();
          //   }
          // });
        },
        Icons.edit,
        S.of(Get.context!).edit,
      ));
    }
    tiles.add(_TileView(
      () {
        controller.changeLikeStatus(isPostAuthor, replyInfo);
        Get.back();
      },
      Icons.thumb_up_outlined,
      replyInfo.zanStatus == 1 ? S.of(Get.context!).purchase_detail_more_cancel_up : S.of(Get.context!).purchase_detail_more_up,
      iconColor: replyInfo.zanStatus == 1 ? MColor.xFFE95332 : MColor.xFF333333,
    ));
    tiles.add(_TileView(
      () {
        Get.back();
        controller.showWriteCommentWidget(isPostAuthor, tag, replyInfo);
      },
      Icons.reply_outlined,
      S.of(Get.context!).purchase_detail_more_reply,
    ));
    tiles.add(_TileView(
      () {
        Get.back();
        Get.bottomSheet(
          SafeArea(
            child: BottomSheet(
              onClosing: () {},
              enableDrag: false,
              builder: (context) {
                return PurchaseComplaintView(replyInfo.id, 0, tag: tag);
              },
            ),
          ),
          isScrollControlled: true,
          ignoreSafeArea: false,
          persistent: false,
        ).then((value) {
          if (value is bool && value == true) {
            //刷新
            controller.getSingleReplies(replyInfo.id);
          }
        });
      },
      Icons.report_outlined,
      S.of(Get.context!).purchase_detail_more_report,
    ));
    if (replyInfo.user?.uid != GlobalConst.userModel?.uid && replyInfo.paidContent != null && replyInfo.payInfo != null && replyInfo.payInfo!.payStatus != 0) {
      tiles.add(_TileView(() {
        Get.back();
        if (replyInfo.appealStatus == 0) {
          Get.bottomSheet(
              BottomSheet(
                  onClosing: () {},
                  // constraints: const BoxConstraints(maxHeight: 3 * SORT_HEIGHT + 28, minHeight: 3 * SORT_HEIGHT + 28),
                  builder: (context) {
                    return PurchaseAppealPopupView(replyInfo, tag: 'purchase_appeal_popup-$tag-${replyInfo.id}');
                  }),
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  color: Theme.of(Get.context!).colorScheme.outline,
                ),
                borderRadius: const BorderRadius.only(topLeft: Radius.circular(6), topRight: Radius.circular(6)),
              ),
              clipBehavior: Clip.antiAliasWithSaveLayer,
              settings: RouteSettings(name: 'purchase_appeal_popup', arguments: {'reply_id': replyInfo.id})).then((value) {
            if (value is Map && value.containsKey('isSuccess') && value['isSuccess']) {
              controller.getSingleReplies(replyInfo.id);
            }
          });
        } else {
          Get.to(() => PurchaseAppealView(tag: replyInfo.id.toString()),
              preventDuplicates: true, arguments: {'reply_id': replyInfo.id}, binding: PurchaseAppealBinding(tag: replyInfo.id.toString()));
        }
        // Get.bottomSheet(
        //     BottomSheet(
        //         onClosing: () {},
        //         // constraints: const BoxConstraints(maxHeight: 3 * SORT_HEIGHT + 28, minHeight: 3 * SORT_HEIGHT + 28),
        //         builder: (context) {
        //           return PurchaseAppealPopupView(replyInfo, tag: 'purchase_appeal_popup-$tag-${replyInfo.id}');
        //         }),
        //     shape: RoundedRectangleBorder(
        //       side: BorderSide(
        //         color: Theme.of(Get.context!).colorScheme.outline,
        //       ),
        //       borderRadius: const BorderRadius.only(topLeft: Radius.circular(6), topRight: Radius.circular(6)),
        //     ),
        //     clipBehavior: Clip.antiAliasWithSaveLayer,
        //     settings: RouteSettings(name: 'purchase_appeal_popup', arguments: {'reply_id': replyInfo.id})).then((value) {
        //   if (value is Map && value.containsKey('isSuccess') && value['isSuccess']) {
        //     controller.getSingleReplies(replyInfo.id);
        //   }
        // });
      }, const IconData(0xe7bc, fontFamily: 'AppIcons'), S.of(Get.context!).purchase_paid_content_appeal));
    }

    return tiles;
  }
}

class _TileView extends StatelessWidget {
  final GestureTapCallback? onTap;
  final IconData icon;
  final String text;
  final Color? iconColor;

  const _TileView(this.onTap, this.icon, this.text, {this.iconColor});
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 24,
            color: iconColor,
          ),
          const SizedBox(
            height: 6,
          ),
          Text(text,
              textAlign: TextAlign.start,
              strutStyle: const StrutStyle(
                forceStrutHeight: true,
                leading: 0.5,
              ),
              style: MFont.regular14.apply(color: MColor.xFF000000))
        ],
      ),
    );
  }
}
