import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_comment/purchase_comment_view.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_author_view.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_more_view.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_reply_view.dart';
import 'package:inspector/app/modules/widgets/progress_indicator_widget.dart';
import 'package:inspector/app/modules/widgets/sliver_sticky_header_delegate.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../generated/l10n.dart';

const SORT_HEIGHT = 45.0;

class PurchaseDetailView extends GetView<PurchaseDetailController> {
  @override
  final String? tag;

  const PurchaseDetailView({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          String? title = controller.purchaseInfo.value == null ? S.of(Get.context!).tab_purchase : controller.purchaseInfo.value!.title;
          return Text(title);
        }),
        centerTitle: true,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            size: 18,
          ),
          onPressed: () {
            Get.back();
          },
        ),
        actions: [
          GestureDetector(
            onTap: () {
              PurchaseReplyEntity? replyInfo = controller.purchaseInfo.value?.replyInfo;
              if (replyInfo != null) {
                Get.bottomSheet(
                    BottomSheet(
                        onClosing: () {},
                        // constraints: const BoxConstraints(maxHeight: 3 * SORT_HEIGHT + 28, minHeight: 3 * SORT_HEIGHT + 28),
                        builder: (context) {
                          return PurchaseDetailMoreView(replyInfo, true, tag: tag);
                        }),
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        color: Theme.of(Get.context!).colorScheme.outline,
                      ),
                      borderRadius: const BorderRadius.only(topLeft: Radius.circular(6), topRight: Radius.circular(6)),
                    ),
                    clipBehavior: Clip.antiAliasWithSaveLayer,
                    settings: RouteSettings(name: 'comment', arguments: {'purchase_id': controller.purchaseId, 'pid': replyInfo.id})).then((value) {
                  // if (value is Map && value.containsKey('isSuccess') && value['isSuccess']) {
                  //   controller.getSingleReplies(replyInfo.id!);
                  // }
                });
              }
            },
            child: const SizedBox(
              height: 56,
              width: 56,
              child: Icon(
                Icons.more_horiz,
              ),
            ),
          )
        ],
      ),
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Obx(() {
          return Column(children: [
            Expanded(
              child: Obx(() {
                if (controller.purchaseInfo.value == null) {
                  return const SizedBox();
                } else {
                  return CustomScrollView(
                    controller: controller.scrollController,
                    slivers: [_headerView, _sortView, _bodyView],
                  );
                }
              }),
            ),
            _commentToolbar,
          ]);
        }),
      ),
    );
  }

  Widget get _headerView {
    return SliverToBoxAdapter(
      child: Builder(builder: (context) {
        if (controller.purchaseInfo.value == null) {
          return const SizedBox();
        } else {
          return Column(
            children: [
              PurchaseDetailAuthorWidget(
                tag: tag,
              ),
              Container(
                color: MColor.xFFCFCFCF_50,
                height: 4,
              )
            ],
          );
        }
      }),
    );
  }

  Widget get _sortContainer {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15.0),
      color: MColor.xFFFFFFFF,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              controller.authorOnly.value = false;
              controller.getPurchaseDetails();
            },
            child: Obx(() {
              TextStyle style =
                  !controller.authorOnly.value ? MFont.semi_Bold13.apply(color: MColor.xFF000000) : MFont.regular13.apply(color: MColor.xFF383838);
              return Text(S.of(Get.context!).purchase_detail_response_all,
                  textAlign: TextAlign.center,
                  strutStyle: const StrutStyle(
                    forceStrutHeight: true,
                    leading: 0.5,
                  ),
                  style: style);
            }),
          ),
          const SizedBox(
            width: 12,
          ),
          GestureDetector(
            onTap: () {
              controller.authorOnly.value = true;
              controller.getPurchaseDetails();
            },
            child: Obx(() {
              TextStyle style = controller.authorOnly.value ? MFont.semi_Bold13.apply(color: MColor.xFF000000) : MFont.regular13.apply(color: MColor.xFF383838);
              return Text(S.of(Get.context!).purchase_detail_response_author_only,
                  textAlign: TextAlign.center,
                  strutStyle: const StrutStyle(
                    forceStrutHeight: true,
                    leading: 0.5,
                  ),
                  style: style);
            }),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.all(1.0),
            decoration: const BoxDecoration(
              color: MColor.xFFF6F6F6,
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    controller.sortAsc.value = true;
                    controller.getPurchaseDetails();
                  },
                  child: Obx(() {
                    var color = controller.sortAsc.value ? MColor.xFFFFFFFF : Colors.transparent;
                    var radius = controller.sortAsc.value ? 14.0 : 0.0;
                    TextStyle style = controller.sortAsc.value ? MFont.semi_Bold13.apply(color: MColor.skin) : MFont.regular13.apply(color: MColor.xFF808080);
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(color: color, borderRadius: BorderRadius.all(Radius.circular(radius))),
                      child: Text(S.of(Get.context!).purchase_detail_response_asc,
                          textAlign: TextAlign.center,
                          strutStyle: const StrutStyle(
                            forceStrutHeight: true,
                            leading: 0.5,
                          ),
                          style: style),
                    );
                  }),
                ),
                GestureDetector(
                  onTap: () {
                    controller.sortAsc.value = false;
                    controller.getPurchaseDetails();
                  },
                  child: Obx(() {
                    var color = !controller.sortAsc.value ? MColor.xFFFFFFFF : Colors.transparent;
                    var radius = !controller.sortAsc.value ? 14.0 : 0.0;
                    TextStyle style = !controller.sortAsc.value ? MFont.semi_Bold13.apply(color: MColor.skin) : MFont.regular13.apply(color: MColor.xFF808080);
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(color: color, borderRadius: BorderRadius.all(Radius.circular(radius))),
                      child: Text(S.of(Get.context!).purchase_detail_response_desc,
                          textAlign: TextAlign.center,
                          strutStyle: const StrutStyle(
                            forceStrutHeight: true,
                            leading: 0.5,
                          ),
                          style: style),
                    );
                  }),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget get _sortView {
    return SliverPersistentHeader(
      pinned: true,
      floating: true,
      delegate: SliverStickyHeaderDelegate(SizedBox(
        height: 42,
        child: _sortContainer,
      )),
    );
  }

  Widget get _bodyView {
    return Obx(() {
      var itemList = <PurchaseReplyEntity>[];
      if (controller.sortAsc.value) {
        itemList.addAll(controller.purchaseReplies);
        itemList.addAll(controller.lastReplies);
      } else {
        itemList.addAll(controller.lastReplies);
        itemList.addAll(controller.purchaseReplies);
      }

      var itemCount = itemList.length + 1;
      logger.i('reply ${controller.purchaseReplies.length} last ${controller.lastReplies.length}');
      return SliverList.separated(
        // physics: const AlwaysScrollableScrollPhysics(),
        // controller: controller.scrollController,
        separatorBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.only(left: AVATAR_SIZE + 8, top: 8),
            child: Container(
              color: MColor.xFFF6F6F6,
              height: 1,
            ),
          );
        },
        itemCount: itemCount,
        itemBuilder: (ctx, index) {
          if (index == itemCount - 1) {
            return ProgressIndicatorWidget(
              controller.isShowLoading.value,
              emptyView: _emptyReplyView,
            );
          } else {
            return PurchaseDetailReplyWidget(
              itemList[index],
              tag: tag,
            );
          }
        },
      );
    });
  }

  Widget get _commentToolbar {
    var purchaseInfo = controller.purchaseInfo.value;
    var header = purchaseInfo?.replyInfo;
    if (header == null) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      child: Row(
        children: [
          Expanded(
              child: GestureDetector(
                  onTap: () {
                    Get.bottomSheet(
                        SafeArea(child: WriteCommentWidget(true, header.user!, purchaseInfo!.title, tag: tag)
                            // child: BottomSheet(
                            //     onClosing: () {},
                            //     builder: (
                            //       context,
                            //     ) {
                            //       return WriteCommentWidget(true, header.user!, purchaseInfo!.title, tag: tag);
                            //     }),
                            ),
                        persistent: false,
                        isScrollControlled: true,
                        ignoreSafeArea: false,
                        // shape: const RoundedRectangleBorder(
                        //   borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
                        // ),
                        // clipBehavior: Clip.antiAliasWithSaveLayer,
                        settings: RouteSettings(name: 'comment', arguments: {'purchase_id': controller.purchaseId, 'pid': 0})).then((value) {
                      if (value is Map && value.containsKey('isSuccess') && value.containsKey('reply')) {
                        var isSuccess = value['isSuccess'] && value['reply'] != null;
                        if (isSuccess) {
                          controller.replyCount += 1;
                          controller.lastReplies.add(value['reply']);
                        }
                      }
                    });
                  },
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                          child: Container(
                        constraints: const BoxConstraints(minHeight: 40, maxHeight: 40),
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        decoration: const BoxDecoration(
                          color: MColor.xFFF6F6F6,
                          borderRadius: BorderRadius.all(Radius.circular(20)),
                        ),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          S.of(Get.context!).purchase_reply_hint,
                          style: MFont.medium16.apply(color: MColor.xFF808080),
                        ),
                      )),
                      // const SizedBox(
                      //   width: 12,
                      // ),
                      // GestureDetector(
                      //   onTap: () {
                      //     controller.changeLikeStatus(true, header);
                      //   },
                      //   child: Padding(
                      //     padding: const EdgeInsets.all(2.0),
                      //     child: Icon(
                      //       Icons.thumb_up_outlined,
                      //       size: 20,
                      //       color: header.zanStatus == 1 ? MColor.xFFE95332 : MColor.xFF000000,
                      //     ),
                      //   ),
                      // ),
                    ],
                  )))
        ],
      ),
    );
  }

  Widget get _emptyReplyView {
    return SizedBox(
        height: 46,
        child: Row(
          children: [
            Expanded(
                child: Text(
              S.of(Get.context!).purchase_no_more_replies,
              textAlign: TextAlign.center,
            )),
          ],
        ));
  }
}
