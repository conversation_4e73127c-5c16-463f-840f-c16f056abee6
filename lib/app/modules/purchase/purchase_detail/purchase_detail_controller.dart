import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_appeal/purchase_appeal_view.dart';
import 'package:inspector/app/modules/purchase/purchase_comment/purchase_comment_view.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class PurchaseDetailController extends GetxController {
  int purchaseId = 0;

  final scrollController = ScrollController();

  final purchaseInfo = Rxn<PurchaseDetailEntity>();

  final purchaseReplies = RxList<PurchaseReplyEntity>([]);
  final lastReplies = RxList<PurchaseReplyEntity>([]);

  final _service = PurchaseService();

  final isShowLoading = false.obs;

  int replyCount = 0;

  int offset = 0;
  final sortAsc = true.obs;
  final authorOnly = false.obs;

  @override
  void onInit() {
    super.onInit();
    purchaseId = Get.arguments['purchase_id'];
    if (purchaseId == 0) {
      Get.back();
      return;
    }
    _initViews();
    getPurchaseDetails();
  }

  void _initViews() {
    scrollController.addListener(() {
      double offset = scrollController.offset;
      if (replyCount != 0 &&
          replyCount > (purchaseReplies.length + lastReplies.length) &&
          scrollController.position.pixels == scrollController.position.maxScrollExtent) {
        loadMore(); // 当滑到最底部时调用
      }
    });
  }

  Future<void> getSingleReplies(int pid) async {
    logger.i('getSingleReplies $pid ');
    unawaited(EasyLoading.show());
    var results = await _service.getSingleReplies(purchaseId, pid);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        var foundIdx = -1;
        for (var i = 0; i < purchaseReplies.length; i++) {
          var element = purchaseReplies[i];
          if (element.id == pid) {
            foundIdx = i;
            break;
          }
        }
        if (foundIdx != -1) {
          purchaseReplies[foundIdx] = resp.list[0];
          purchaseReplies.refresh();
        } else {
          for (var i = 0; i < lastReplies.length; i++) {
            var element = lastReplies[i];
            if (element.id == pid) {
              foundIdx = i;
              break;
            }
          }
          if (foundIdx != -1) {
            lastReplies[foundIdx] = resp.list[0];
            lastReplies.refresh();
          }
        }
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  Future<void> getPurchaseDetails() async {
    logger.i('getPurchaseDetails');
    unawaited(EasyLoading.show());
    offset = 0;
    var authorId = purchaseInfo.value?.user?.uid;
    var results = await _service.getPurchaseDetails('$purchaseId', offset, 15, sortAsc.value == true ? 'asc' : 'desc', authorOnly.value ? authorId : null);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        replyCount = resp.replys?.total ?? 0;
        purchaseInfo.value = resp.detail;
        lastReplies.clear();
        if (resp.replys != null) {
          purchaseReplies.value = resp.replys!.list;
        } else {
          purchaseReplies.value = [];
        }
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  Future<void> loadMore() async {
    offset = purchaseReplies.last.id ?? 0;
    logger.i('loadMore $offset');
    isShowLoading.value = true;
    var authorId = purchaseInfo.value?.user?.uid;
    var results = await _service.getPurchaseDetails('$purchaseId', offset, 15, sortAsc.value == true ? 'asc' : 'desc', authorOnly.value ? authorId : null);
    isShowLoading.value = false;
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        lastReplies.clear();
        purchaseInfo.value = resp.detail;
        if (resp.replys != null) {
          purchaseReplies.addAll(resp.replys!.list);
        }
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
    scrollController.dispose();
  }

  void showWriteCommentWidget(bool isPostAuthor, String? tag, PurchaseReplyEntity replyInfo) {
    Get.bottomSheet(SafeArea(child: WriteCommentWidget(isPostAuthor, replyInfo.user!, replyInfo.content, tag: tag)),
        persistent: false,
        isScrollControlled: true,
        ignoreSafeArea: false,
        settings: RouteSettings(name: 'comment', arguments: {'purchase_id': purchaseId, 'pid': replyInfo.id})).then((value) {
      if (value is Map && value.containsKey('isSuccess') && value['isSuccess']) {
        getSingleReplies(replyInfo.id!);
      }
    });
  }

  Future<void> changeLikeStatus(bool isAuthor, PurchaseReplyEntity reply) async {
    var replyId = reply.id;
    var newStatus = reply.zanStatus == 1 ? 0 : 1;
    if (replyId != null) {
      var result = await _service.addRemoveLike(replyId, newStatus);
      if (result.isSuccess) {
        reply.zanStatus = result.data['zan_status'];
        reply.zan = result.data['zan'];
        // if (newStatus == 0) {
        //   var zanCount = reply.zan - 1;
        //   reply.zan = zanCount >= 0 ? zanCount : 0;
        // } else {
        //   var zanCount = reply.zan + 1;
        //   reply.zan = zanCount >= 0 ? zanCount : 0;
        // }
        if (isAuthor) {
          purchaseInfo.refresh();
        } else {
          purchaseReplies.refresh();
        }
      }
    }
  }
}
