import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/purchase/purchase_review/purchase_review_controller.dart';

import '../../../../generated/l10n.dart';

class PurchaseReviewView extends GetView<PurchaseReviewController> {
  @override
  final String? tag;

  final int replyId;

  const PurchaseReviewView(this.replyId, {Key? key, this.tag}) : super(key: key);

  @override
  PurchaseReviewController get controller {
    return GetInstance().putOrFind(() => PurchaseReviewController(), tag: tag);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: AppBar().preferredSize.height),
      padding: EdgeInsets.all(12),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(15), topRight: Radius.circular(15)),
        color: Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _titleView,
          const SizedBox(
            height: 6,
          ),
          const Divider(
            height: 1,
          ),
          const SizedBox(
            height: 6,
          ),
          _textView,
          Row(
            children: [
              Expanded(
                child: TextButton(
                  style: ButtonStyle(
                    padding: MaterialStateProperty.all(const EdgeInsets.symmetric(vertical: 8)),
                    textStyle: MaterialStateProperty.all(MFont.regular13),
                    backgroundColor: MaterialStateProperty.all(MColor.skin),
                  ),
                  onPressed: () {
                    controller.submitReview(replyId);
                  },
                  child: Text(
                    S.of(Get.context!).apply_submit,
                    style: const TextStyle(fontSize: 13, color: MColor.xFFFFFFFF, height: 1),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget get _titleView {
    return Row(
      children: [
        Text(S.of(Get.context!).purchase_review_leave,
            textAlign: TextAlign.start,
            strutStyle: const StrutStyle(
              forceStrutHeight: true,
              leading: 0.5,
            ),
            style: MFont.regular16.apply(color: MColor.xFF000000)),
        const SizedBox(
          width: 12,
        ),
        RatingBar.builder(
          initialRating: 0,
          minRating: 1,
          direction: Axis.horizontal,
          allowHalfRating: false,
          itemCount: 5,
          itemSize: 24,
          itemPadding: const EdgeInsets.symmetric(horizontal: 2.0),
          itemBuilder: (context, _) => const Icon(
            Icons.star,
            size: 24,
            color: MColor.skin,
          ),
          onRatingUpdate: (rating) {
            controller.rating = rating.toInt();
          },
        ),
      ],
    );
  }

  Widget get _textView {
    return TextField(
      controller: controller.textController,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.only(left: 4, right: 4, top: 2, bottom: 2),
        hintText: S.of(Get.context!).purchase_reply_hint,
        hintStyle: MFont.regular13.apply(color: MColor.xFF999999),
        labelStyle: MFont.regular13.apply(color: MColor.xFF333333),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
        ),
        constraints: const BoxConstraints(minHeight: 50, maxHeight: 100),
      ),
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.search,
      onSubmitted: (value) {
        controller.submitReview(replyId);
      },
      onChanged: (val) {},
      minLines: 2,
      maxLines: 4,
      maxLength: 10000,
      autofocus: true,
    );
  }
}
