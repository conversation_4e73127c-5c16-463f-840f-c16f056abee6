import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';

class PurchaseReviewController extends GetxController {
  final TextEditingController textController = TextEditingController();
  final PurchaseService _service = PurchaseService();

  int rating = 0;

  Future<void> submitReview(int replyId) async {
    unawaited(EasyLoading.show());
    var result = await _service.submitReview(replyId, textController.text, rating);
    if (result.isSuccess) {
      Get.back(result: true);
    }
    unawaited(EasyLoading.dismiss());
  }

  @override
  void onClose() {
    super.onClose();
    textController.dispose();
  }
}
