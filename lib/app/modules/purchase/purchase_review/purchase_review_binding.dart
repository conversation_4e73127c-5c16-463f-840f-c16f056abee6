import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/bindings_interface.dart';
import 'package:inspector/app/modules/purchase/purchase_review/purchase_review_controller.dart';

class PurchaseReviewBinding extends Bindings {
  final String? tag;
  PurchaseReviewBinding({this.tag});

  @override
  void dependencies() {
    Get.lazyPut<PurchaseReviewController>(() => PurchaseReviewController(), tag: tag);
  }
}
