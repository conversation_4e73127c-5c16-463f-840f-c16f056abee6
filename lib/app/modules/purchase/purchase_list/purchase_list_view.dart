import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_edit/purchase_edit_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_edit/purchase_edit_view.dart';
import 'package:inspector/app/modules/purchase/purchase_list/purchase_list_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_widgets/purchase_item_view.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/widgets/empty_view.dart';

import '../../../../generated/l10n.dart';

const int ITEM_TYPE_CATEGORY = 0;
const int ITEM_TYPE_PURCHASE = 1;
const int ITEM_TYPE_EMPTY = 2;

class PurchaseListView extends GetView<PurchaseListController> {
  const PurchaseListView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          actions: [
            IconButton(
                onPressed: () {
                  // Get.toNamed(Routes.PURCHASE_PUBLISH);
                  // Get.to(() => PurchaseEditView(tag: '0'), preventDuplicates: true, binding: PurchaseEditBinding(tag: '0'));
                  Get.toNamed(Routes.PURCHASE_PUBLISH, parameters: {'is_edit': '0'}, preventDuplicates: true);
                },
                icon: Icon(Icons.add_circle_outline, color: MColor.skin))
          ],
          leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: Icon(
                Icons.arrow_back_ios,
                size: 18,
              )),
          title: Text(S.of(Get.context!).tab_purchase, style: TextStyle(fontSize: 22, color: MColor.xFF000000, fontWeight: FontWeight.w500)),
          centerTitle: true,
          /*bottom: const _PurchaseSearchBar(),*/ elevation: 0,
          scrolledUnderElevation: 0,
        ),
        backgroundColor: MColor.xFFF6F6F6,
        body: SafeArea(
            child: RefreshIndicator(
          key: controller.indicatorKey,
          onRefresh: () async {
            await controller.refreshAction();
          },
          child: Obx(() {
            if (controller.isLoading.value) {
              return Center(
                child: SpinKitCircle(color: MColor.skin),
              );
            }
            if (controller.purchaseItems.isEmpty) {
              return EmptyView();
            }
            return ListView.builder(
              physics: const AlwaysScrollableScrollPhysics(),
              controller: controller.scrollController,
              itemCount: controller.purchaseItems.length + 2,
              itemBuilder: (ctx, index) {
                if (index == 0) {
                  return _getCategoryView(ctx);
                } else if (index == controller.purchaseItems.length + 1) {
                  return _loadMoreView;
                } else {
                  PurchaseItemEntity entity = controller.purchaseItems[index - 1];
                  return PurchaseItemView(entity, bgColor: MColor.xFFFFFFFF);
                }
              },
            );
          }),
        )));
  }

  Widget get _loadMoreView {
    return Builder(builder: (context) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Text(
                controller.hasMoreItems() ? S.of(Get.context!).loading : S.of(Get.context!).nomore,
                style: TextStyle(fontSize: 14.0, color: MColor.xFF808080),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _getCategoryView(BuildContext context) {
    var itemCount = controller.classEntities.length;
    return Container(
        height: 40,
        padding: EdgeInsets.only(top: 10, left: 15, right: 15),
        child: ListView.separated(
          itemCount: itemCount,
          scrollDirection: Axis.horizontal,
          separatorBuilder: (BuildContext context, int position) {
            return const SizedBox(
              width: 20,
            );
          },
          itemBuilder: (BuildContext context, int position) {
            return Obx(() {
              var entity = controller.classEntities[position];
              bool selected = entity.id == controller.classPid;
              return GestureDetector(
                onTap: () {
                  if (selected) {
                    return;
                  }
                  var entity = controller.classEntities[position];
                  controller.classPid = entity.id!;
                  controller.refreshAction();
                },
                child: Center(
                  child: Text(
                    entity.name,
                    style: TextStyle(fontSize: 14, height: 1, color: selected ? MColor.skin : MColor.xFF000000),
                  ),
                ),
              );
            });
          },
        ));
  }

  Widget _emptyView(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: Get.height / 2,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.hourglass_empty,
              size: 40,
              color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
            ),
            const SizedBox(height: 10),
            Text(
              S.of(Get.context!).no_data,
              style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666),
            ),
          ],
        ),
      ),
    );
  }
}

class _PurchaseSearchBar extends GetView<PurchaseListController> implements PreferredSizeWidget {
  const _PurchaseSearchBar({super.key});
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 46,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12),
        child: Row(
          children: [
            Expanded(
                child: GestureDetector(
                    onTap: () {
                      Get.toNamed(Routes.PURCHASE_SEARCH);
                    },
                    child: Container(
                      constraints: const BoxConstraints(minHeight: 40, maxHeight: 40),
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: context.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000,
                          width: 1.5,
                          style: BorderStyle.solid,
                        ),
                        color: Colors.white,
                        borderRadius: const BorderRadius.all(Radius.circular(24)),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                              child: Text(
                            S.of(Get.context!).purchase_search_hint,
                            style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
                          )),
                          Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Icon(
                              Icons.search_sharp,
                              size: 20,
                              color: context.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000,
                            ),
                          )
                        ],
                      ),
                    )))
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(46);
}
