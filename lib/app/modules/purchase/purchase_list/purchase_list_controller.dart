import 'dart:async';
import 'package:async/async.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';

import '../../../tools/tools.dart';

class PurchaseListController extends GetxController {
  final PurchaseService _service = PurchaseService();
  final ScrollController scrollController = ScrollController();

  GlobalKey<RefreshIndicatorState> indicatorKey = GlobalKey<RefreshIndicatorState>();

  final classEntities = RxList<PurchaseClassEntity>([]);
  final purchaseItems = RxList<PurchaseItemEntity>([]);
  int classPid = 0;
  int offset = 0;

  int totalCount = 0;

  final isLoading = true.obs;

  @override
  void onInit() {
    super.onInit();
    scrollController.addListener(() {
      if (scrollController.position.pixels == scrollController.position.maxScrollExtent) {
        if (purchaseItems.length < totalCount) {
          loadMore();
        }
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    indicatorKey.currentState?.show();
  }

  @override
  void onClose() {
    scrollController.dispose();
  }

  Future<void> refreshAction() async {
    offset = 0;
    logger.i('PurchaseListController refreshAction');
    unawaited(EasyLoading.show());
    var results = await _service.getPurchaseItems(classPid, offset, 5, 0);
    isLoading.value = false;
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        totalCount = resp.total;
        classEntities.value = resp.classList;
        purchaseItems.value = resp.list;
      } else {
        classEntities.clear();
        purchaseItems.clear();
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  Future<void> loadMore() async {
    offset = purchaseItems.last.id ?? 0;
    unawaited(EasyLoading.show());
    var results = await _service.getPurchaseItems(classPid, offset, 5, 0);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        totalCount = resp.total;
        purchaseItems.addAll(resp.list);
      }
    }

    unawaited(EasyLoading.dismiss());
    // refreshController.refreshCompleted();
    if (purchaseItems.isEmpty || (totalCount != 0 && purchaseItems.length == totalCount)) {
      // refreshController.loadNoData();
    } else {
      // refreshController.loadComplete();
    }
  }

  bool hasMoreItems() {
    return purchaseItems.length != totalCount;
  }
}
