import 'package:inspector/app/config/api.dart';
import 'package:inspector/app/data/pay_model.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/tools/public_provider.dart';

class PurchaseService {
  //列表
  Future<BaseModel<List<PurchaseClassEntity>>> getPurchaseClasses(int id) {
    return PublicProvider.request<List<PurchaseClassEntity>>(path: Api.purchaseClassList, isPost: false, params: {'pid': id});
  }

  //列表
  Future<BaseModel<PurchaseResp>> getPurchaseItems(int id, int page, int pageSize, int sort) {
    return PublicProvider.request<PurchaseResp>(path: '${Api.purchaseItemList}?offset=$page&page_size=$pageSize&my=0&cid=$id&sort=$sort', isPost: false);
  }

  //详情
  Future<BaseModel<PurchaseDetailResp>> getPurchaseDetails(String purchaseId, int page, int pageSize, String sort, int? look) {
    // return PublicProvider.request<PurchaseDetailResp>(path: '${Api.purchaseDetails}/$purchaseId', isPost: false);
    return PublicProvider.request<PurchaseDetailResp>(
        path: '${Api.purchaseDetails}/$purchaseId?offset=$page&page_size=$pageSize&sort=$sort${look == null ? '' : '&look=$look'}', isPost: false);
  }

  Future<BaseModel<dynamic>> addPurchase(int? classId, String product, String title, String content, List<String> pics, int account, String bounty, int status,
      bool justLandlord, String count, String deadline, String province, String city,
      {int? id}) {
    var params = {'product': product, 'content': content, 'pic': pics, 'account': account, 'bounty': bounty, 'title': title, 'status': status};
    if (classId != null) {
      params['class_id'] = classId;
    }
    if (id != null) {
      params['id'] = id;
    }
    params['just_landlord'] = justLandlord ? 1 : 0;
    params['count'] = count;
    params['deadline'] = deadline;
    params['province'] = province;
    params['city'] = city;
    return PublicProvider.request(path: Api.purchaseAdd, isPost: true, params: params);
  }

  //回复
  Future<BaseModel<PurchaseCommentResp?>> addReply(int purchaseId, int pid, String content, int type,
      {int? replyId,
      String? price,
      int? account,
      String? supplier,
      String? contact,
      String? phone,
      String? email,
      String? address,
      String? other,
      String? lowPrice}) {
    var params = {
      'purchase_id': purchaseId,
      'pid': pid,
      'type': type,
      'content': content,
    };
    if (replyId != null) {
      params['reply_id'] = replyId;
    }
    if (price != null) {
      params['price'] = price;
    }
    if (account != null) {
      params['account'] = account;
    }
    if (supplier != null) {
      params['supplier'] = supplier;
    }
    if (contact != null) {
      params['contact'] = contact;
    }
    if (phone != null) {
      params['phone'] = phone;
    }
    if (email != null) {
      params['email'] = email;
    }
    if (address != null) {
      params['addr'] = address;
    }
    if (other != null) {
      params['other'] = other;
    }
    if (lowPrice != null) {
      params['low_price'] = lowPrice;
    }
    return PublicProvider.request(path: Api.purchaseAddReply, isPost: true, params: params);
  }

  //获取某一层的回复
  Future<BaseModel<PurchaseReplyResp>> getSingleReplies(int purchaseId, int pid, {int? offset, int? pageSize}) {
    return PublicProvider.request(path: '${Api.purchaseAddReply}/$purchaseId/$pid?offset=$offset&page_size=$pageSize', isPost: false);
  }

  Future<BaseModel<dynamic>> addRemoveLike(int id, int status) {
    return PublicProvider.request(path: Api.purchaseAddLike, isPost: true, params: {'id': id, 'val': status});
  }

  //支付
  Future<BaseModel<dynamic>> payPurchase(int replyId, int payType) {
    return PublicProvider.request(path: Api.payPurchase, isPost: true, params: {'id': '$replyId', 'pay_type': '$payType'});
  }

  Future<BaseModel<PayInfoResp>> getPayInfo(int replyId) {
    return PublicProvider.request(path: Api.payInfo, isPost: true, params: {'id': replyId});
  }

  //我的帖子列表
  Future<BaseModel<PurchaseResp>> getMyPostList(int page, int pageSize) {
    return PublicProvider.request<PurchaseResp>(path: '${Api.purchaseItemList}?offset=$page&page_size=$pageSize&my=1', isPost: false);
  }

  //获取我的回复
  Future<BaseModel<PurchaseMyReplyResp>> getMyReplies(int page, int pageSize) {
    return PublicProvider.request(path: '${Api.purchaseMyReplies}?offset=$page&page_size=$pageSize', isPost: false);
  }

  //获取我的申诉
  Future<BaseModel<PurchaseMyAppealResp>> getMyAppeals(int page, int pageSize) {
    return PublicProvider.request(path: '${Api.purchaseMyAppeals}?offset=$page&page_size=$pageSize', isPost: false);
  }

  Future<BaseModel<PurchaseAppealEntity>> getAppealInfo({int? replyId, int? id}) {
    if (id != null) {
      return PublicProvider.request(path: '${Api.purchaseAppealDetail}?id=$id', isPost: false);
    } else if (replyId != null) {
      return PublicProvider.request(path: '${Api.purchaseAppealDetail}?reply_id=$replyId', isPost: false);
    } else {
      return Future.error(ArgumentError());
    }
  }

  Future<BaseModel<dynamic>> submitReview(int replyId, String note, int score) {
    return PublicProvider.request(path: Api.purchaseSubmitReview, params: {'reply_id': replyId, 'note': note, 'score': score}, isPost: true);
  }

  Future<BaseModel<dynamic>> submitComplaint(int replyId, String type, String note) {
    return PublicProvider.request(path: Api.purchaseComplaint, params: {'reply_id': replyId, 'note': note, 'type': type}, isPost: true);
  }

  Future<BaseModel<dynamic>> submitAppeal(int replyId, String note) {
    return PublicProvider.request(path: Api.purchaseAppeal, params: {'reply_id': replyId, 'note': note}, isPost: true);
  }

  Future<BaseModel<PurchaseDetailResp>> getDrafts({int? id}) {
    var params = {};
    if (id != null) {
      params = {'id': id};
    }
    return PublicProvider.request<PurchaseDetailResp>(path: Api.purchaseDraft, params: params, isPost: false);
  }
}
