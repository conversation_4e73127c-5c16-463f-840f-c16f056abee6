import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_pay/purchase_pay_controller.dart';

import '../../../../generated/l10n.dart';

class PurchasePayView extends GetView<PurchasePayController> {
  const PurchasePayView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).pay_title),
        centerTitle: true,
      ),
      body: Container(
        color: MColor.xFFF4F5F7,
        child: Column(
          children: [
            _amountView,
            const SizedBox(height: 15),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Obx(() {
                  return Column(
                    children: [
                      for (int i = 0; i < controller.supportedPayTypes.length; i++) ...{
                        _payItemView(context, controller.supportedPayTypes[i]),
                        Divider(
                          thickness: 1,
                          height: 0.5,
                          color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
                          indent: 15,
                          endIndent: 15,
                        ),
                      },
                    ],
                  );
                }),
              ),
            ),
            const SizedBox(height: 20),
            _nextButton,
          ],
        ),
      ),
    );
  }

  Widget get _amountView {
    return Obx(() {
      var currency = controller.userAccountType.value.symbol;
      return Container(
        margin: const EdgeInsets.only(top: 18),
        child: Center(
          child: Builder(builder: (context) {
            return Text(
              '$currency ${controller.price}',
              style: MFont.semi_Bold24.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
              textAlign: TextAlign.center,
            );
          }),
        ),
      );
    });
  }

  Widget _payItemView(BuildContext context, PayType payType) {
    String payTitle = S.of(context).pay_type(payType.title);
    if (payType == PayType.PayTypeRmb || payType == PayType.PayTypeUsd) {
      payTitle = '$payTitle (${controller.userAccountType.value.symbol} ${controller.balance.toStringAsFixed(2)})';
    }
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        controller.selectedPayType.value = payType;
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
        color: Colors.white,
        child: Row(
          children: [
            Image.asset(
              payType.icon,
            ),
            const SizedBox(width: 4),
            Builder(builder: (context) {
              return Text(
                payTitle,
                style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
              );
            }),
            const Spacer(),
            Obx(() {
              return Icon(
                controller.selectedPayType.value == payType ? Icons.radio_button_checked : Icons.radio_button_off,
                size: 16,
                color: MColor.skin,
              );
            }),
          ],
        ),
      ),
    );
  }

  Container get _nextButton {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      width: double.infinity,
      child: TextButton(
        style: ButtonStyle(
          shape: MaterialStateProperty.all(const StadiumBorder()),
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 44)),
        ),
        onPressed: () {
          controller.payPurchase();
        },
        child: Text(
          S.of(Get.context!).apply_submit,
          style: MFont.medium16.apply(color: Colors.white),
        ),
      ),
    );
  }
}
