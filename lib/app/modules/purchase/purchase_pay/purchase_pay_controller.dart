import 'dart:async';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/pay_model.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';
import 'package:tobias/tobias.dart';

import '../../../../generated/l10n.dart';

enum PayType {
  PayTypeRmb(1, 'pay_rmb', Assets.cnyIcon),
  PayTypeUsd(1, 'pay_usd', Assets.usdIcon),
  PayTypeAlipay(5, 'pay_zfb', Assets.zhifubao),
  PayTypePaypal(4, 'pay_paypal', Assets.paypalIcon);

  final int payId;
  final String title;
  final String icon;
  const PayType(this.payId, this.title, this.icon);
}

class PurchasePayController extends GetxController {
  final PurchaseService _service = PurchaseService();
  final Rx<UserAccountType> userAccountType = UserAccountType.CNY.obs;
  final Rx<double> price = 0.0.obs;

  final selectedPayType = PayType.PayTypeRmb.obs;

  final supportedPayTypes = <PayType>[].obs;

  double balance = 0;

  int? replyId; //采购

  int countDownTimer = 10;

  @override
  void onInit() {
    super.onInit();
    replyId = Get.arguments['reply_id'];
    if (replyId == null) {
      Get.back();
      return;
    }
    // supportedPayTypes.add(PayType.PayTypeRmb);
    // supportedPayTypes.add(PayType.PayTypeUsd);
    // supportedPayTypes.add(PayType.PayTypeAlipay);
    // supportedPayTypes.add(PayType.PayTypePaypal);

    _getPayInfo();
  }

  Future<void> _getPayInfo() async {
    unawaited(EasyLoading.show());
    var result = await _service.getPayInfo(replyId!);
    if (result.isSuccess) {
      PayInfoResp? payInfo = result.data;
      if (payInfo != null) {
        price.value = double.tryParse(payInfo.price) ?? 0;
        userAccountType.value = payInfo.account == 1 ? UserAccountType.CNY : UserAccountType.USD;
        supportedPayTypes.clear();
        if (userAccountType.value == UserAccountType.CNY) {
          supportedPayTypes.add(PayType.PayTypeRmb);
          supportedPayTypes.add(PayType.PayTypeAlipay);
          balance = double.tryParse(payInfo.userAccount?.amount ?? '') ?? 0;
          selectedPayType.value = PayType.PayTypeRmb;
        } else {
          supportedPayTypes.add(PayType.PayTypeUsd);
          supportedPayTypes.add(PayType.PayTypePaypal);
          balance = double.tryParse(payInfo.userAccount?.amountUs ?? '') ?? 0;
          selectedPayType.value = PayType.PayTypeUsd;
        }
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  /// 秒转时分秒
  String second2MS(int sec) {
    String hms = '00:00';
    if (sec > 0) {
      // int h = sec ~/ 3600;
      int m = (sec % 3600) ~/ 60;
      int s = sec % 60;
      hms = '${zeroFill(m)}:${zeroFill(s)}';
    }
    return hms;
  }

  String zeroFill(int i) {
    return i >= 10 ? '$i' : '0$i';
  }

  Future<void> payPurchase() async {
    unawaited(EasyLoading.show());
    countDownTimer = 10;
    var results = await PurchaseService().payPurchase(replyId!, selectedPayType.value.payId);
    if (results.isSuccess) {
      if (selectedPayType.value == PayType.PayTypeRmb || selectedPayType.value == PayType.PayTypeUsd) {
        Get.back(result: true);
      } else if (selectedPayType.value == PayType.PayTypeAlipay) {
        if (results.data == null || results.data is! Map || !(results.data as Map).containsKey('aliPayurl')) {
          showToast(S.of(Get.context!).failed_get_payment_info);
          return;
        }
        var text = results.data['aliPayurl'] as String;
        Tobias tobias = Tobias();
        var payResult = await tobias.pay(text, evn: AliPayEvn.online);
        if (payResult['resultStatus'] == '9000') {
          showToast(S.of(Get.context!).pay_result_success);
          unawaited(EasyLoading.show(status: S.of(Get.context!).pay_result_success_wait));
          await Future.delayed(const Duration(seconds: 3));
          Get.back(result: true);
        } else {
          showToast(S.of(Get.context!).pay_result_failed);
        }
        logger.e('value value');
      }
    } else {
      if (results.message != null) {
        showToast(results.message!);
      }
    }

    unawaited(EasyLoading.dismiss());
  }
}
