import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_comment/purchase_comment_view.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_reply_view.dart';
import 'package:inspector/app/modules/purchase/purchase_reply/purchase_reply_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_widgets/purchase_comment_toolbar.dart';
import 'package:inspector/app/modules/purchase/purchase_widgets/purchase_common_reply_view.dart';
import 'package:inspector/app/modules/widgets/progress_indicator_widget.dart';
import 'package:inspector/app/modules/widgets/sliver_sticky_header_delegate.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class PurchaseReplyView extends GetView<PurchaseReplyController> {
  @override
  final String? tag;

  const PurchaseReplyView({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put<PurchaseReplyController>(PurchaseReplyController(), tag: tag);
    return Container(
      margin: EdgeInsets.only(top: AppBar().preferredSize.height),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(6), topRight: Radius.circular(6)),
        color: Colors.white,
      ),
      child: Obx(() {
        return Column(children: [
          Expanded(
            child: Obx(() {
              if (controller.replyInfo.value == null) {
                return const SizedBox();
              } else {
                return CustomScrollView(
                  controller: controller.scrollController,
                  slivers: [_titleView, _bodyView],
                );
              }
            }),
          ),
          if (controller.replyInfo.value == null) ...{
            const SizedBox(),
          } else ...{
            PurchaseCommentToolbar(controller.purchaseId, controller.replyInfo.value!, onLikeCallback: (value) {}, onReplyCallback: (value) {
              if (value is Map && value.containsKey('isSuccess') && value.containsKey('reply')) {
                var isSuccess = value['isSuccess'] && value['reply'] != null;
                if (isSuccess) {
                  controller.replyCount += 1;
                  controller.lastReplies.add(value['reply']);
                }
              }
            }),
          }
        ]);
      }),
    );
  }

  Widget get _bodyView {
    return Obx(() {
      var itemList = <PurchaseReplyEntity>[];
      itemList.add(controller.replyInfo.value!);
      itemList.addAll(controller.subReplies);
      itemList.addAll(controller.lastReplies);

      var itemCount = itemList.length + 1;
      return SliverList.separated(
        // physics: const AlwaysScrollableScrollPhysics(),
        // controller: controller.scrollController,
        separatorBuilder: (context, index) {
          if (index == 0) {
            return Column(
              children: [
                Container(
                  color: MColor.xFFCFCFCF_50,
                  height: 10,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                  height: 46,
                  child: Row(
                    children: [Expanded(child: Text('${itemList.length - 1}${S.of(Get.context!).purchase_replies_count}'))],
                  ),
                )
              ],
            );
          }
          return Padding(
            padding: const EdgeInsets.only(left: AVATAR_SIZE + 8, top: 8),
            child: Container(
              color: MColor.xFFCFCFCF_50,
              height: 1,
            ),
          );
        },
        itemCount: itemCount,
        itemBuilder: (ctx, index) {
          if (index == itemCount - 1) {
            return ProgressIndicatorWidget(
              controller.isShowLoading.value,
              emptyView: _emptyReplyView,
            );
          } else {
            return PurchaseCommonReplyView(
              itemList[index],
              tag: tag,
              onContentTapped: () {
                Get.bottomSheet(
                    SafeArea(child: WriteCommentWidget(false, itemList[index].user!, itemList[index].content, tag: 'write_comment-${itemList[index].id}')),
                    persistent: false,
                    isScrollControlled: true,
                    ignoreSafeArea: false,
                    settings: RouteSettings(
                        name: 'comment',
                        arguments: {'purchase_id': controller.purchaseId, 'pid': controller.replyId, 'reply_id': itemList[index].id})).then((value) {
                  if (value is Map && value.containsKey('isSuccess') && value.containsKey('reply')) {
                    var isSuccess = value['isSuccess'] && value['reply'] != null;
                    if (isSuccess) {
                      controller.replyCount += 1;
                      controller.lastReplies.add(value['reply']);
                    }
                  }
                });
              },
            );
          }
        },
      );
    });
  }

  Widget get _emptyReplyView {
    return SizedBox(
        height: 46,
        child: Row(
          children: [
            Expanded(
                child: Text(
              S.of(Get.context!).purchase_no_more_replies,
              textAlign: TextAlign.center,
            )),
          ],
        ));
  }

  Widget get _titleView {
    return SliverPersistentHeader(
      pinned: true,
      floating: true,
      delegate: SliverStickyHeaderDelegate(SizedBox(
        height: 42,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          color: MColor.xFFFFFFFF,
          child: Stack(children: [
            GestureDetector(
              onTap: () {
                Get.back();
              },
              child: const SizedBox(
                height: 42,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.close,
                      size: 24,
                    ),
                  ],
                ),
              ),
            ),
            Center(child: Text(S.of(Get.context!).purchase_all_replies))
          ]),
        ),
      )),
    );
  }
}
