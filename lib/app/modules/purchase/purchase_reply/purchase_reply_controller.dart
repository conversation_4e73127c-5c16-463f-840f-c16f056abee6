import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:inspector/app/tools/tools.dart';

class PurchaseReplyController extends GetxController {
  final replyInfo = Rxn<PurchaseReplyEntity>();
  final scrollController = ScrollController();

  final isShowLoading = false.obs;

  int purchaseId = 0;
  int replyId = 0;

  final lastReplies = RxList<PurchaseReplyEntity>([]);

  final subReplies = RxList<PurchaseReplyEntity>([]);

  final _service = PurchaseService();

  int replyCount = 0;

  int offset = 0;

  @override
  void onInit() {
    super.onInit();
    purchaseId = Get.arguments['purchase_id'];
    if (purchaseId == 0) {
      Get.back();
      return;
    }
    replyId = Get.arguments['reply_id'];
    if (replyId == 0) {
      Get.back();
      return;
    }
    _initViews();
    getReplyInfo();
  }

  void _initViews() {
    scrollController.addListener(() {
      double offset = scrollController.offset;
      if (replyCount != 0 &&
          replyCount > (subReplies.length + lastReplies.length) &&
          scrollController.position.pixels == scrollController.position.maxScrollExtent) {
        loadMore(); // 当滑到最底部时调用
      }
    });
  }

  Future<void> loadMore() async {
    offset = subReplies.last.id ?? 0;
    logger.i('loadMore $offset');
    isShowLoading.value = true;
    var results = await _service.getSingleReplies(purchaseId, replyId, offset: offset, pageSize: 10);
    isShowLoading.value = false;
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        lastReplies.clear();
        var reply = resp.list[0];
        replyCount = resp.total;
        replyInfo.value = reply;
        if (reply.reply.isNotEmpty) {
          subReplies.addAll(reply.reply);
        }
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
    scrollController.dispose();
  }

  Future<void> getReplyInfo() async {
    logger.i('getReplyInfo $replyId ');
    unawaited(EasyLoading.show());
    var results = await _service.getSingleReplies(purchaseId, replyId, offset: 0, pageSize: 10);
    if (results.isSuccess) {
      var resp = results.data;
      if (resp != null) {
        lastReplies.clear();
        subReplies.clear();
        var reply = resp.list[0];
        replyCount = resp.total;
        replyInfo.value = reply;
        if (reply.reply.isNotEmpty) {
          subReplies.addAll(reply.reply);
        }
      }
    }
    unawaited(EasyLoading.dismiss());
  }
}
