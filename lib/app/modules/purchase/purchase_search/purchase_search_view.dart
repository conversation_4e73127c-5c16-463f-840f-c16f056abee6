import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_search/purchase_search_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_search/purchase_search_model.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class PurchaseSearchView extends GetView<PurchaseSearchController> {
  const PurchaseSearchView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const _PurchaseSearchBar(), titleSpacing: 0, centerTitle: true, elevation: 0),
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: Stack(
          children: [if (controller.pageType.value == 0) _inputPage else _resultPage],
        ),
      ),
    );
  }

  Widget get _inputPage {
    return _PurchaseSearchInput();
  }

  Widget get _resultPage {
    return _PurchaseSearchResult();
  }
}

class _PurchaseSearchInput extends GetView<PurchaseSearchController> {
  _PurchaseSearchInput({super.key}) {
    logger.i('aaa');
  }
  @override
  Widget build(BuildContext context) {
    return _historyItems;
  }

  Widget get _historyItems {
    return Obx(() {
      if (controller.searchHistory.isEmpty) {
        return const SizedBox();
      }
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(
            children: [
              Expanded(
                  child: Text(S.of(Get.context!).search_recent_history,
                      textAlign: TextAlign.start,
                      strutStyle: const StrutStyle(
                        forceStrutHeight: true,
                        leading: 0.5,
                      ),
                      style: MFont.semi_Bold14.apply(color: MColor.xFF000000))),
              GestureDetector(
                child: const Icon(Icons.delete_outline, size: 14),
                onTap: () {},
              )
            ],
          ),
          Wrap(
            spacing: 8.0,
            runSpacing: 8.0,
            children: [
              for (PurchaseSearchHistoryModel filter in controller.searchHistory) ...{
                GestureDetector(
                  onTap: () {},
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      color: (Get.context!.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    child: Text(
                      filter.query,
                      style: MFont.regular14.apply(color: Get.context!.isDarkMode ? DarkColor.xFF2A2A2A : MColor.xFF2A2A2A),
                    ),
                  ),
                )
              }
            ],
          )
        ]),
      );
    });
  }
}

class _PurchaseSearchResult extends GetView<PurchaseSearchController> {
  _PurchaseSearchResult({super.key}) {
    logger.i('bbbb');
  }
  @override
  Widget build(BuildContext context) {
    return SizedBox();
  }
}

class _PurchaseSearchBar extends GetView<PurchaseSearchController> {
  const _PurchaseSearchBar({super.key});

  @override
  Widget build(BuildContext context) {
    Widget input = Builder(builder: (context) {
      return Row(
        children: [
          Expanded(
            child: TextField(
              style: const TextStyle(fontSize: 15),
              controller: controller.searchEditController,
              focusNode: controller.searchFocusNode,
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.only(left: 0),
                prefixIcon: Padding(
                  padding: const EdgeInsets.only(left: 12.0, right: 4),
                  child: Icon(
                    Icons.search_rounded,
                    size: 16,
                    color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                  ),
                ),
                prefixIconConstraints: const BoxConstraints(minWidth: 28, minHeight: 37),
                suffixIconConstraints: const BoxConstraints(minWidth: 28, minHeight: 37),
                suffixIcon: Obx(() {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Visibility(
                        visible: controller.showClear.value,
                        child: Row(
                          children: [
                            GestureDetector(
                              onTap: () {
                                controller.searchEditController.text = '';
                                controller.showClear.value = false;
                              },
                              child: Padding(
                                  padding: const EdgeInsets.only(left: 4.0, right: 12.0),
                                  child: Icon(Icons.cancel_rounded, size: 16, color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999)),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }),
                fillColor: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                filled: true,
                isDense: true,
                hintText:
                    GlobalConst.userModel?.role == UserRole.admin ? S.of(Get.context!).home_search_hint_admin : S.of(Get.context!).home_search_hint_inspector,
                hintStyle: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
                labelStyle: MFont.regular15.apply(color: Colors.red),
                // labelStyle: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000, width: 1.5),
                  borderRadius: BorderRadius.circular(20),
                ),
                focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFF000000 : MColor.xFF000000, width: 1.5),
                    borderRadius: BorderRadius.circular(20)),
                constraints: const BoxConstraints(maxHeight: 37, minHeight: 37),
              ),
              keyboardType: TextInputType.text,
              textInputAction: TextInputAction.search,
              onSubmitted: (value) {
                controller.searchFocusNode.unfocus();
                controller.startQuery(value);
              },
              onChanged: (val) {
                controller.showClear.value = val.isNotEmpty;
              },
            ),
          ),
          const SizedBox(
            width: 16,
          )
        ],
      );
    });

    return input;
  }
}
