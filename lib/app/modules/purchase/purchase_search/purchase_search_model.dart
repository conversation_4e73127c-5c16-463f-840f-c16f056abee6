class PurchaseSearchHistoryModel {
  PurchaseSearchHistoryModel(this.query) {
    timestamp = DateTime.now().millisecondsSinceEpoch;
  }
  String query = '';
  int timestamp = 0;

  @override
  int get hashCode => query.hashCode;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is PurchaseSearchHistoryModel && runtimeType == other.runtimeType && query == other.query;
  }

  PurchaseSearchHistoryModel.fromJson(Map<String, dynamic> json)
      : query = json['query'],
        timestamp = json['timestamp'];

  Map<String, dynamic> toJson() => {'query': query, 'timestamp': timestamp};

  @override
  String toString() {
    return toJson().toString();
  }
}
