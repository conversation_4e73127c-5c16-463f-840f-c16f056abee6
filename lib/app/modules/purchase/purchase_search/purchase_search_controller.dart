import 'dart:async';
import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/modules/purchase/purchase_search/purchase_search_model.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/generated/json/base/json_convert_content.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../tools/storage_util.dart';

class PurchaseSearchController extends GetxController {
  TextEditingController searchEditController = TextEditingController();
  FocusNode searchFocusNode = FocusNode();
  final PurchaseService _service = PurchaseService();
  final searchQuery = RxString('');
  final showClear = false.obs;

  final searchHistory = RxList<PurchaseSearchHistoryModel>([]);

  final pageType = 0.obs; //0激活页 1 结果页

  @override
  void onInit() {
    super.onInit();
    searchFocusNode.requestFocus();
    unawaited(_loadSearchHistory());
  }

  @override
  void onClose() {
    super.onClose();
    searchEditController.dispose();
    searchFocusNode.dispose();
  }

  Future<void> startQuery(String query) async {
    searchQuery.value = query;
    unawaited(EasyLoading.show());
    await _saveSearchQuery(searchQuery.value);
    var results = await _service.getPurchaseItems(0, 0, 15, 0);
    if (results.isSuccess) {
      var resp = results.data;
      // if (resp != null) {
      //   totalCount = resp.total;
      //   classEntities.value = resp.classList;
      //   purchaseItems.value = resp.list;
      // } else {
      //   classEntities.clear();
      //   purchaseItems.clear();
      // }
    }

    unawaited(EasyLoading.dismiss());
    // refreshController.refreshCompleted();
    // if (purchaseItems.isEmpty || (totalCount != 0 && purchaseItems.length == totalCount)) {
    //   refreshController.loadNoData();
    // } else {
    //   refreshController.loadComplete();
    // }
  }

  Future<void> _loadSearchHistory() async {
    String history = StorageUtil.getString(Constant.kPurchaseSearchHistory);
    if (history.isNotEmpty == true) {
      List<PurchaseSearchHistoryModel> models = jsonConvert.convertListNotNull<PurchaseSearchHistoryModel>(jsonDecode(history!)) ?? [];
      searchHistory.clear();
      searchHistory.addAll(models);
    }
  }

  Future<void> _saveSearchQuery(String? query) async {
    if (query?.isNotEmpty == true) {
      PurchaseSearchHistoryModel model = PurchaseSearchHistoryModel(query!);
      searchHistory.remove(model);
      searchHistory.insert(0, model);
      String encodedAccount = jsonEncode(searchHistory);
      await StorageUtil.setString(Constant.kPurchaseSearchHistory, encodedAccount);
    }
  }
}
