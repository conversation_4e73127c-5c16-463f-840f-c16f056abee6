import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_service.dart';
import 'package:inspector/app/tools/tools.dart';

class PurchaseCommentController extends GetxController {
  TextEditingController replyController = TextEditingController();
  TextEditingController supplierController = TextEditingController();
  TextEditingController contactController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  TextEditingController otherController = TextEditingController();
  TextEditingController lowPriceController = TextEditingController();
  ScrollController scrollController = ScrollController();
  FocusNode inputFocusNode = FocusNode();
  FocusNode supplierFocusNode = FocusNode();
  int purchaseId = 0;
  int pid = 0;
  int? replyId;

  final PurchaseService _service = PurchaseService();

  final accountType = UserAccountType.CNY.obs;
  final price = 100.0.obs;

  final attachments = RxList<String>([]);

  final canPublish = false.obs;

  final bool isPostAuthor;

  PurchaseCommentController(this.isPostAuthor);

  @override
  void onInit() {
    super.onInit();
    purchaseId = Get.arguments['purchase_id'];
    if (purchaseId == 0) {
      Get.back();
      return;
    }
    if (Get.arguments['pid'] != null) {
      pid = Get.arguments['pid'];
    }
    if (Get.arguments['reply_id'] != null) {
      replyId = Get.arguments['reply_id'];
    }
    inputFocusNode.requestFocus();
  }

  @override
  void onClose() {
    super.onClose();
    replyController.dispose();
    supplierController.dispose();
    contactController.dispose();
    phoneController.dispose();
    emailController.dispose();
    addressController.dispose();
    otherController.dispose();
    lowPriceController.dispose();
    inputFocusNode.dispose();
    scrollController.dispose();
    supplierFocusNode.dispose();
  }

  void checkCanPublish() {
    if (isPostAuthor) {
      canPublish.value = replyController.text.isNotEmpty && price.value != 0 && contactController.text.isNotEmpty && phoneController.text.isPhoneNumber;
    } else {
      canPublish.value = replyController.text.isNotEmpty;
    }
  }

  Future<void> submitComment() async {
    unawaited(EasyLoading.show());
    try {
      var result = await _service.addReply(purchaseId, pid, replyController.text, isPostAuthor ? 2 : 1,
          replyId: replyId,
          price: isPostAuthor ? price.toStringAsFixed(2) : null,
          account: isPostAuthor ? accountType.value.value : null,
          supplier: isPostAuthor ? supplierController.text : null,
          contact: isPostAuthor ? contactController.text : null,
          phone: isPostAuthor ? phoneController.text : null,
          email: isPostAuthor ? emailController.text : null,
          address: isPostAuthor ? addressController.text : null,
          other: isPostAuthor ? otherController.text : null,
          lowPrice: isPostAuthor ? lowPriceController.text : null);
      if (result.isSuccess) {
        Get.back(result: {'isSuccess': true, 'reply': result.data?.reply});
      } else {
        if (result.message?.isNotEmpty == true) {
          showToast(result.message!);
        }
      }
    } catch (e) {
      logger.e('submitComment error $e');
    } finally {
      unawaited(EasyLoading.dismiss());
    }
  }
}
