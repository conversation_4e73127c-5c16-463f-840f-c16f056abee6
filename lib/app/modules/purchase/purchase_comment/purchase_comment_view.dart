import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_comment/purchase_comment_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_detail/purchase_detail_controller.dart';
import 'package:inspector/app/modules/widgets/sliver_sticky_header_delegate.dart';
import 'package:inspector/app/modules/widgets/upload/UploadWidget.dart';
import 'package:inspector/app/widgets/price_input_widget.dart';

import '../../../../generated/l10n.dart';

class WriteCommentWidget extends GetView<PurchaseCommentController> {
  @override
  final String? tag;

  final UserInfoEntity author;
  final String content;
  final bool isPostAuthor;

  const WriteCommentWidget(this.isPostAuthor, this.author, this.content, {Key? key, this.tag}) : super(key: key);

  @override
  PurchaseCommentController get controller {
    return GetInstance().putOrFind(() => PurchaseCommentController(isPostAuthor), tag: tag);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: AppBar().preferredSize.height),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(15), topRight: Radius.circular(15)),
        color: Colors.white,
      ),
      child: Column(
        children: [
          Expanded(
            child: CustomScrollView(
              controller: controller.scrollController,
              slivers: [_headerView, _bodyView],
            ),
          ),
          const SizedBox(
            height: 12,
          ),
          Obx(() {
            bool canPublish = controller.canPublish.value;
            return Row(
              children: [
                const SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      if (canPublish) {
                        controller.submitComment();
                      }
                    },
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all(canPublish ? MColor.skin : MColor.skin_50),
                      shape: WidgetStateProperty.all(const StadiumBorder()),
                    ),
                    child: Text(
                      S.of(Get.context!).public_send,
                      style: const TextStyle(color: Colors.white, height: 1),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                const SizedBox(
                  width: 15,
                ),
              ],
            );
          }),
          const SizedBox(
            height: 12,
          ),
        ],
      ),
    );
  }

  Widget get _headerView {
    return SliverPersistentHeader(
      pinned: true,
      floating: true,
      delegate: SliverStickyHeaderDelegate(
        SizedBox(
          child: _replyToWidget,
        ),
        minExtend: 66,
        maxExtend: 66,
      ),
    );
  }

  Widget get _bodyView {
    return SliverToBoxAdapter(
      child: Builder(builder: (context) {
        return Padding(
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: Column(
            children: [
              _textInput,
              const SizedBox(
                height: 14,
              ),
              // uploadView,
              // const SizedBox(
              //   height: 20,
              // ),
              if (isPostAuthor) ...{
                Divider(height: 1, color: MColor.xFFF6F6F6),
                const SizedBox(
                  height: 20,
                ),
                _paidView
              }
            ],
          ),
        );
      }),
    );
  }

  Widget get _replyToWidget {
    return Container(
      decoration: BoxDecoration(
        color: MColor.xFFFFFFFF,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(15), topRight: Radius.circular(15)),
      ),
      padding: const EdgeInsets.only(left: 14, top: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
              child: Text(
            '${S.of(Get.context!).purchase_reply_to} ${author.nick}: $content',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: MColor.xFF000000, overflow: TextOverflow.ellipsis),
            textAlign: TextAlign.start,
            maxLines: 1,
          )),
          const SizedBox(
            width: 12,
          ),
          IconButton(
            padding: EdgeInsets.all(0.0),
            onPressed: () {
              Get.back();
            },
            icon: Icon(Icons.close_outlined, size: 20),
          ),
        ],
      ),
    );
  }

  Widget get _textInput {
    return Builder(builder: (context) {
      return TextField(
        controller: controller.replyController,
        focusNode: controller.inputFocusNode,
        decoration: InputDecoration(
            fillColor: MColor.xFFF6F6F6,
            filled: true,
            contentPadding: const EdgeInsets.only(left: 15, right: 15, top: 12, bottom: 12),
            hintText: S.of(Get.context!).purchase_reply_reason_hint,
            hintStyle: TextStyle(fontSize: 16, color: MColor.xFFA6A6A6),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
            ),
            constraints: const BoxConstraints(minHeight: 50, maxHeight: 100),
            counterStyle: TextStyle(color: MColor.xFFA6A6A6)),
        keyboardType: TextInputType.text,
        textInputAction: TextInputAction.search,
        onSubmitted: (value) {},
        onChanged: (val) {
          controller.checkCanPublish();
        },
        minLines: 2,
        maxLines: 4,
        maxLength: 10000,
        autofocus: true,
      );
    });
  }

  Widget _paidInputView(TextEditingController teController, String title, String hint, {bool? mandatory, TextInputType? kbType, TextInputAction? action}) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          // child: Text(
          //   title,
          //   style: TextStyle(fontSize: 16, color: MColor.xFF383838),
          // ),

          child: RichText(
              text: WidgetSpan(
                  child: Row(
            children: [
              Text(
                title,
                style: TextStyle(color: MColor.xFF000000, fontSize: mandatory == true ? 16 : 14, fontWeight: FontWeight.w500),
              ),
              const SizedBox(
                width: 3,
              ),
              mandatory == true ? Text('*', style: TextStyle(color: MColor.skin, fontSize: 14, fontWeight: FontWeight.w500)) : const SizedBox()
            ],
          ))),
        ),
        const SizedBox(
          width: 8,
        ),
        Expanded(
          flex: 3,
          child: TextField(
            controller: teController,
            style: const TextStyle(fontSize: 14, color: MColor.xFF000000, fontWeight: FontWeight.w500),
            decoration: InputDecoration(
              filled: true,
              isDense: true,
              fillColor: Colors.transparent,
              // contentPadding: EdgeInsets.fromLTRB(0, 0, 0, 0),
              // suffixText: mandatory == true ? '*' : '',
              // suffixStyle: TextStyle(color: MColor.skin),
              // suffixIconConstraints: BoxConstraints(minWidth: 0, minHeight: 0),
              // suffixIcon: Column(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              //   mainAxisAlignment: MainAxisAlignment.center,
              //   children: [
              //     Text(
              //       mandatory == true ? '*' : '',
              //       textAlign: TextAlign.center,
              //       style: MFont.regular13.apply(color: MColor.skin),
              //     ),
              //   ],
              // ),
              // suffixStyle: MFont.regular13.apply(color: MColor.skin),
              // contentPadding: const EdgeInsets.only(left: 1, right: 0, top: 0, bottom: 0),
              hintText: hint,
              hintStyle: const TextStyle(fontSize: 16, color: MColor.xFFA6A6A6),
              // floatingLabelBehavior: FloatingLabelBehavior.auto,
              enabledBorder: InputBorder.none,
              border: InputBorder.none,
              focusedBorder: InputBorder.none,
            ),
            keyboardType: kbType ?? TextInputType.text,
            textInputAction: action ?? TextInputAction.next,
            minLines: 1,
            maxLines: 1,
            onChanged: (text) {
              controller.checkCanPublish();
            },
          ),
        ),
      ],
    );
  }

  Widget get _paidView {
    return Column(
      children: [
        GestureDetector(
            onTap: () {
              FocusScope.of(Get.context!).unfocus();
              Get.bottomSheet(
                SafeArea(
                  // and this
                  child: PriceInputWidget(controller.price.toStringAsFixed(2), S.of(Get.context!).order_detail_inspection_cost,
                      controller.accountType.value == UserAccountType.USD, 7),
                ),
                isScrollControlled: true,
                ignoreSafeArea: false, // add this
              ).then((value) {
                if (value is Map) {
                  controller.accountType.value = value['isUSD'] ? UserAccountType.USD : UserAccountType.CNY;
                  String priceStr = value['price'];
                  controller.price.value = double.tryParse(priceStr) ?? 0;
                  controller.checkCanPublish();
                  controller.supplierFocusNode.requestFocus();
                }
              });
            },
            child: Row(
              children: [
                Text(
                  S.of(Get.context!).purchase_set_fee,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: MColor.xFF000000),
                ),
                const Spacer(),
                Obx(() {
                  return Text(
                    '${controller.accountType.value.symbol} ${controller.price.toStringAsFixed(2)}',
                    style: MFont.medium16.apply(color: MColor.xFFE95332),
                    textAlign: TextAlign.center,
                  );
                }),
                const Icon(
                  Icons.keyboard_arrow_right_outlined,
                  size: 24,
                  color: MColor.xFFBBBBBB,
                ),
              ],
            )),
        const SizedBox(
          height: 8,
        ),
        Divider(
          height: 18,
          color: MColor.xFFF6F6F6,
        ),
        Row(children: [
          Expanded(
            child: Text(S.of(Get.context!).purchase_paid_publish_information_title,
                style: MFont.medium16.apply(color: MColor.xFF000000), textAlign: TextAlign.start),
          ),
        ]),
        const SizedBox(
          height: 8,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Icon(Icons.info_outline, size: 12, color: MColor.xFF999999),
            const SizedBox(
              width: 8,
            ),
            Expanded(
              child: Text(
                S.of(Get.context!).purchase_reply_paid_content_tips,
                style: MFont.medium12.apply(color: MColor.xFF999999),
                strutStyle: const StrutStyle(
                  forceStrutHeight: true,
                ),
                textAlign: TextAlign.start,
              ),
            )
          ],
        ),
        const SizedBox(
          height: 14,
        ),
        _paidInputView(controller.contactController, S.of(Get.context!).purchase_comment_paid_contact, S.of(Get.context!).purchase_comment_paid_contact_hint,
            mandatory: true),
        const SizedBox(
          height: 14,
        ),
        _paidInputView(controller.phoneController, S.of(Get.context!).purchase_comment_paid_phone, S.of(Get.context!).purchase_comment_paid_phone_hint,
            mandatory: true, kbType: TextInputType.phone),
        const SizedBox(
          height: 14,
        ),
        _paidInputView(controller.supplierController, S.of(Get.context!).purchase_comment_paid_supplier, S.of(Get.context!).purchase_comment_paid_supplier_hint,
            mandatory: false),
        const SizedBox(
          height: 14,
        ),
        _paidInputView(controller.emailController, S.of(Get.context!).purchase_comment_paid_email, S.of(Get.context!).purchase_comment_paid_email_hint,
            mandatory: false, kbType: TextInputType.emailAddress),
        const SizedBox(
          height: 14,
        ),
        _paidInputView(
          controller.addressController,
          S.of(Get.context!).purchase_comment_paid_address,
          S.of(Get.context!).purchase_comment_paid_address_hint,
        ),
        const SizedBox(
          height: 14,
        ),
        _paidInputView(
          controller.otherController,
          S.of(Get.context!).purchase_comment_paid_other,
          S.of(Get.context!).purchase_comment_paid_other_hint,
        ),
        const SizedBox(
          height: 14,
        ),
        _paidInputView(
          controller.lowPriceController,
          S.of(Get.context!).purchase_comment_paid_low_price,
          S.of(Get.context!).purchase_comment_paid_low_price_hint,
        ),
      ],
    );
  }

  Widget get uploadView {
    return Obx(
      () {
        return UploadWidget.generateUploadWidget(controller.attachments, true, (url) {
          controller.attachments.add(url);
        }, (position) {
          controller.attachments.removeAt(position);
          controller.attachments.refresh();
        }, showFile: false);
      },
    );
  }
}
