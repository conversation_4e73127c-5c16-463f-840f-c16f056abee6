import 'dart:async';
import 'dart:io';
import 'package:app_installer/app_installer.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/init/views/download_view.dart';
import 'package:inspector/generated/l10n.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/tools/tools.dart';

class UpdateAppView extends StatefulWidget {
  final Map<String, dynamic> info;
  const UpdateAppView(this.info, {Key? key}) : super(key: key);

  @override
  State<UpdateAppView> createState() => _UpdateAppViewState();
}

class _UpdateAppViewState extends State<UpdateAppView> {
  double percent = 0;
  bool isDown = false;
  int uid = 0;

  @override
  void initState() {
    super.initState();
    if (Platform.isAndroid) {
      /*RUpgrade.stream.listen((DownloadInfo info) {
        if (info.status == DownloadStatus.STATUS_FAILED) {
          if (Platform.isAndroid) {
            RUpgrade.cancel(uid);
            showToast(S.of(Get.context!).failed_to_download);
            isDown = false;
          }
          return;
        } else if (info.status == DownloadStatus.STATUS_SUCCESSFUL && info.id != null) {
          RUpgrade.install(info.id!);
        }
        setState(() {
          if (percent == 100) {
            return;
          }
          percent = info.percent ?? 0;
        });
      });*/
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isForce = widget.info['isMustUpdate'] ?? false;
    return WillPopScope(
      onWillPop: () {
        return Future.value(false);
      },
      child: Center(
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          margin: const EdgeInsets.symmetric(horizontal: 40),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              Center(
                child: Text(
                  '${widget.info['version']}${S.of(Get.context!).new_version}',
                  style: MFont.semi_Bold17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  widget.info['content'],
                  style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666),
                ),
              ),
              if (isDown && Platform.isAndroid) ...{
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(2),
                    child: LinearProgressIndicator(
                      value: percent / 100,
                      valueColor: const AlwaysStoppedAnimation<Color>(MColor.skin),
                      backgroundColor: context.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC,
                    ),
                  ),
                ),
              },
              if (!isForce) ...{
                const SizedBox(height: 16),
                Divider(height: 1, color: context.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 14),
                          child: Center(
                            child: Text(
                              S.of(Get.context!).public_cancel,
                              style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                            ),
                          ),
                        ),
                        onTap: () {
                          if (Platform.isAndroid) {
                            //RUpgrade.cancel(uid);
                            isDown = false;
                          }
                          Get.back();
                        },
                      ),
                    ),
                    Container(
                      color: context.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC,
                      width: 0.5,
                      height: 50,
                    ),
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 14),
                          child: Center(child: Text(S.of(Get.context!).update_now, style: MFont.semi_Bold15.apply(color: MColor.skin))),
                        ),
                        onTap: () {
                          var url = widget.info['url'];
                          percent = 0;
                          toUpdate(url);
                          isDown = true;
                        },
                      ),
                    ),
                  ],
                ),
              } else ...{
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    child: Container(
                      height: 44,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: MColor.xFF25C56F,
                        borderRadius: BorderRadius.circular(22),
                      ),
                      child: Center(child: Text(S.of(Get.context!).update_now, style: MFont.semi_Bold15.apply(color: Colors.white))),
                    ),
                    onTap: () {
                      var url = widget.info['url'];
                      percent = 0;
                      toUpdate(url);
                      isDown = true;
                    },
                  ),
                ),
                const SizedBox(height: 16),
              },
            ],
          ),
        ),
      ),
    );
  }

  Future<void> toUpdate(String url) async {
    logger.i('开始更新->$url');
    if (Platform.isAndroid) {
      if (isDown) {
        return;
      }
      Get.back();
      await Get.dialog(DownloadView(url: url));
    } else {
      await AppInstaller.goStore('', '1524403672',macOSAppId: '',);
    }
    /*AzhonAppUpdate.update(model).then((value) => debugPrint('$value'));
    if (Platform.isAndroid) {
      if (isDown) {
        return;
      }
      uid = await RUpgrade.upgrade(url, fileName: 'inspection.apk', useDownloadManager: true) ?? 0;
    } else {
      unawaited(RUpgrade.upgradeFromAppStore('1524403672'));
    }*/
  }

}
