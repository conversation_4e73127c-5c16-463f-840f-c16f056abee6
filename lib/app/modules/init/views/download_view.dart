import 'dart:async';
import 'dart:io';
import 'package:app_installer/app_installer.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';
import 'package:path_provider/path_provider.dart';

class DownloadView extends StatefulWidget{

  final String url;

  const DownloadView({required this.url, super.key});

  @override
  State<StatefulWidget> createState() => DownloadViewState();

}

class DownloadViewState extends State<DownloadView>{

  final dio = Dio();

  double value = 0;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((d){
      downloadApk();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        height: 80,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8)
        ),
        child: Column(
          children: [
            LinearProgressIndicator(value: value,),
            SizedBox(height: 16,),
            Text(S.of(context).downloading)
          ],
        ),
      ),
    );
  }

  Future<void> downloadApk() async{
    try {
      final dir = await getApplicationDocumentsDirectory();
      String savePath = '${dir.path}/inspection.apk';
      File file = File(savePath);
      if(file.existsSync()){
        file.deleteSync();
      }
      await dio.download(
        widget.url,
        savePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            setState(() {
              value = received / total;
            });
          }
        },
      );
      final res = AppInstaller.installApk(savePath);
      Get.back();
    } catch (e) {
      if(mounted){
        showToast(S.of(context).failed_to_download);
      }
      Get.back();
    }
  }

  @override
  void dispose() {
    dio.close();
    super.dispose();
  }

}