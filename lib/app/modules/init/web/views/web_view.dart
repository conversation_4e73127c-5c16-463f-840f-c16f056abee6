import 'package:flutter/material.dart';

import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/constant.dart';

import 'package:inspector/app/modules/init/web/controllers/web_controller.dart';
import 'package:inspector/app/tools/device.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/translation_service.dart';

import '../../../../tools/storage_util.dart';
import '../../../auth/account_service.dart';
import '../../../store/setting_store.dart';

class WebView extends GetView<WebController> {
  WebView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Map<String, String> headers = {};
    headers['versionName'] = GlobalConst.versionName;
    headers['versionCode'] = GlobalConst.versionCode;
    headers['osType'] = DeviceUtils.osType ?? '';
    headers['osVersion'] = DeviceUtils.sysVersion ?? '';
    headers['deviceType'] = DeviceUtils.deviceName ?? '';
    headers['deviceId'] = DeviceUtils.deviceId ?? '';
    headers['timestamp'] = '${DateTime.now().millisecondsSinceEpoch ~/ 1000}';
    headers['timeZone'] = '${DateTime.now().timeZoneOffset.inSeconds}';
    headers['token'] = AccountService.instance.getToken();

    headers['language'] = SettingStore.to.getCurrentLocale().toString();

    return Obx(() {
      return Scaffold(
          appBar: AppBar(
            title: Text(controller.title.value),
            centerTitle: true,
          ),
          body: InAppWebView(
            initialUrlRequest: URLRequest(url: WebUri(controller.url.value), headers: headers),
          ));
    });
  }
}
