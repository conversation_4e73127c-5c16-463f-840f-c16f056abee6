import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/inspect/my_insp_list/my_insp_list_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/app_theme.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class MyInspListPage extends GetView<MyInspListController> {
  const MyInspListPage({super.key});

  @override
  MyInspListController get controller {
    return GetInstance().putOrFind<MyInspListController>(() => MyInspListController());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        title: Text(S.of(Get.context!).mine_inspect_mine),
        centerTitle: true,
      ),
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            _topView,
            Expanded(
              child: Obx(() {
                return Container(
                  color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
                  child: SmartRefresher(
                    controller: controller.refreshController,
                    onRefresh: () => controller.refreshAction(),
                    onLoading: () => controller.loadMore(),
                    enablePullUp: true,
                    child: ListView.builder(
                      itemCount: controller.listEntity.isEmpty ? 1 : controller.listEntity.length,
                      padding: const EdgeInsets.only(bottom: 12),
                      itemBuilder: (ctx, index) {
                        if (controller.listEntity.isEmpty) {
                          return SizedBox(
                            width: double.infinity,
                            height: Get.height / 2,
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.hourglass_empty,
                                    size: 40,
                                    color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                                  ),
                                  const SizedBox(height: 10),
                                  Text(S.of(Get.context!).no, style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
                                ],
                              ),
                            ),
                          );
                        }
                        return GestureDetector(
                          onTap: () {
                            var oid = controller.listEntity[index].id ?? controller.listEntity[index].orderId;
                            Get.toNamed(Routes.LIST_DETAIL, arguments: oid)?.then((value) {
                              if (value) {
                                controller.refreshAction();
                              }
                            });
                          },
                          child: _itemView(index),
                        );
                      },
                    ),
                  ),
                );
              }),
            )
          ],
        ),
      ),
    );
  }

  Widget get _topView {
    List<String> tabTitles = [
      S.of(Get.context!).order_all,
      S.of(Get.context!).order_confirm,
      S.of(Get.context!).order_wait,
      S.of(Get.context!).order_doing,
      S.of(Get.context!).order_finished
    ];

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 14),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 14),
        children: [
          Wrap(
            spacing: 10,
            direction: Axis.horizontal,
            alignment: WrapAlignment.center,
            children: [
              for (int i = 0; i < tabTitles.length; i++) ...{
                Obx(() {
                  var selected = controller.selectedIndex.value == i;
                  return Builder(builder: (context) {
                    return ChoiceChip(
                      showCheckmark: false,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                      visualDensity: const VisualDensity(vertical: -4),
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      label: Text(tabTitles[i]),
                      labelStyle: MFont.regular14.apply(
                          color: selected
                              ? MColor.skin
                              : context.isDarkMode
                                  ? DarkColor.xFF565656
                                  : MColor.xFF565656),
                      selected: selected,
                      selectedColor: MColor.skin_05,
                      side: selected ? const BorderSide(color: MColor.skin) : null,
                      onSelected: (selected) {
                        controller.selectedIndex.value = i;
                        controller.refreshAction();
                      },
                      autofocus: true,
                    );
                  });
                })
              }
            ],
          ),
        ],
      ),
    );
  }

  Widget _itemView(int index) {
    var model = controller.listEntity[index];
    var lat = model.lat ?? 0;
    var lon = model.lon ?? 0;
    var showLocation = (lon > 0) && (lat > 0);

    var statusText = S.of(Get.context!).order_wait;
    var textColor = MColor.xFFDF8D14;

    switch (model.insStatus) {
      case 1:
        statusText = S.of(Get.context!).order_inspection_status_unpaid;
        textColor = MColor.skin;
        break;
      case 2:
        statusText = S.of(Get.context!).order_confirm;
        textColor = MColor.skin;
        break;
      case 3:
        statusText = S.of(Get.context!).order_ready_inspect;
        textColor = MColor.xFF0081E7;
        break;
      case 4:
        statusText = S.of(Get.context!).order_doing;
        textColor = MColor.xFF1BA12B;
        break;
      case 5:
        statusText = S.of(Get.context!).order_inspection_status_returned;
        textColor = MColor.xFF1BA12B;
        break;
      case 6:
        statusText = S.of(Get.context!).order_cancelled;
        textColor = MColor.xFF1BA12B;
        break;
      case 7:
        statusText = S.of(Get.context!).order_finished;
        textColor = MColor.xFF1BA12B;
        break;
      case 8:
        statusText = S.of(Get.context!).order_inspection_status_waiting_start;
        textColor = MColor.xFF1BA12B;
        break;
      default:
        statusText = '';
    }
    return Container(
      margin: const EdgeInsets.only(left: 12, right: 12, top: 10),
      padding: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: Theme.of(Get.context!).colorScheme.conatiner,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Builder(builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.only(left: 12, right: 12, top: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    Helper.parseChinaArea(model.provinceCity ?? '${model.province ?? ''}${model.city ?? ''}'),
                    style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Location.share.toNavigation(model.lat, model.lon, model.address);
                    },
                    child: Offstage(
                      offstage: !showLocation,
                      child: Container(
                        padding: const EdgeInsets.only(top: 5),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            RichText(
                              text: TextSpan(
                                children: [
                                  WidgetSpan(
                                    child: Icon(
                                      Icons.location_on_sharp,
                                      color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383,
                                      size: 16,
                                    ),
                                  ),
                                  TextSpan(
                                    text: S.of(Get.context!).home_navigation,
                                    style: MFont.regular11.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const Spacer(),
                  Center(child: Text(statusText, style: MFont.medium13.apply(color: textColor))),
                ],
              ),
            ),
            Visibility(
              visible: model.address != null && model.address!.isNotEmpty,
              child: Container(
                padding: const EdgeInsets.only(left: 12, right: 12, top: 8),
                child: Text(
                  model.address ?? '',
                  style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                ),
              ),
            ),
            _textItem(S.of(Get.context!).order_goods_name, model.productName == '暂无' ? '' : model.productName),
            _textItem(S.of(Get.context!).order_detail_inspection_time, model.inspectionDate),
            _textItem(
              S.of(Get.context!).order_order_amount,
              model.userAccount == 1 ? '￥${model.price.toString()}' : '\$${model.price.toString()}',
            ),
            _textItem(
              S.of(Get.context!).order_detail_inspection_type,
              Helper.parseOrderType(model.type ?? 0),
            ),
          ],
        );
      }),
    );
  }

  Widget _textItem(String? title, String? value) {
    return Container(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 2, bottom: 4),
      child: Builder(builder: (context) {
        return Row(
          children: [
            Text(
              '$title：',
              style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
            ),
            Expanded(
              child: Text(
                value ?? '-',
                style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      }),
    );
  }
}
