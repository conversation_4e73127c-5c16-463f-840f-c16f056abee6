import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:inspector/app/data/publish_order_entity.dart';
import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:inspector/generated/l10n.dart';

class MyPublishController extends GetxController {
  RefreshController refreshController = RefreshController();
  OrderProvider provider = OrderProvider();
  final selectedIndex = 0.obs;
  var page = 1;
  final listEntity = <PublishOrderRows>[].obs;

  @override
  void onInit() {
    var index = Get.parameters['tab'];
    if (index != null) {
      selectedIndex.value = int.tryParse(index) ?? 0;
    }
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    fetchPublishList(false);
  }

  void refreshAction() {
    page = 1;
    fetchPublishList(false);
  }

  void loadMore() {
    page++;
    fetchPublishList(true);
  }

  void fetchPublishList(bool isMore) {
    EasyLoading.show();
    var total = 0;

    int? type;
    if (selectedIndex.value == 1) {
      //待派单
      type = 1;
    } else if (selectedIndex.value == 2) {
      //准备验货
      type = 3;
    } else if (selectedIndex.value == 3) {
      //验货中
      type = 4;
    } else if (selectedIndex.value == 4) {
      //待支付
      type = 0;
    } else if (selectedIndex.value == 5) {
      //取消
      type = -1;
    } else if (selectedIndex.value == 6) {
      //已完成
      type = 5;
    }
    provider.takePublish(type, page, 10).then((value) async {
      if (value.isSuccess) {
        var models = value.data?.rows ?? [];
        total = value.data?.total ?? 0;
        if (!isMore) {
          listEntity.value = [];
        }
        listEntity.addAll(models);
      } else {
        listEntity.value = [];
        showToast(value.message ?? '');
      }
      listEntity.refresh();
    }).whenComplete(() {
      EasyLoading.dismiss();
      refreshController.refreshCompleted();
      if (total <= listEntity.length && listEntity.isNotEmpty) {
        refreshController.loadNoData();
      } else {
        refreshController.loadComplete();
      }
    });
  }

  @override
  void onClose() {
    refreshController.dispose();
  }
}
