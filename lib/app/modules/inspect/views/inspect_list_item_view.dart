import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/order_list_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/home/<USER>/assign_inspector_binding.dart';
import 'package:inspector/app/modules/home/<USER>/assign_inspector_view.dart';
import 'package:inspector/app/modules/home/<USER>/admin_note_view.dart';
import 'package:inspector/app/theme/app_theme.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../generated/l10n.dart';

class InspectListItemView extends StatefulWidget {
  final OrderListRows model;
  final VoidCallback? onItemTap;
  final VoidCallback? onApplyTap;

  const InspectListItemView(this.model, {super.key, this.onItemTap, this.onApplyTap});

  @override
  State<StatefulWidget> createState() {
    return _InspectListItemState();
  }
}

class _InspectListItemState extends State<InspectListItemView> {
  @override
  Widget build(BuildContext context) {
    OrderListRows model = widget.model;
    var lat = model.lat ?? 0;
    var lon = model.lon ?? 0;
    var showLocation = (lon > 0) && (lat > 0) && GlobalConst.userModel?.role != UserRole.admin;
    logger.i('${model.orderNumber} ${model.applyStatus}');
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        widget.onItemTap?.call();
      },
      child: Builder(builder: (context) {
        return Container(
            margin: const EdgeInsets.only(left: 12, right: 12, top: 10),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.conatiner,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: Row(
                    children: [
                      const Spacer(),
                      Offstage(
                        offstage: model.award == null,
                        child: Container(
                          decoration: const BoxDecoration(
                            image: DecorationImage(image: AssetImage(Assets.rmbIcon)),
                          ),
                          width: 127,
                          height: 27,
                          child: Center(
                            child: Text(
                              S.of(Get.context!).home_recommend(model.award ?? ''),
                              style: MFont.medium12.apply(color: Colors.white),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: SizedBox(
                    width: 40,
                    height: 40,
                    child: Offstage(
                      offstage: GlobalConst.userModel?.role != UserRole.admin,
                      child: GestureDetector(
                        onTap: () {
                          String toTag = '${model.id}';
                          Get.to(() => AssignInspectorView(tag: toTag),
                              arguments: {
                                'order_id': model.id,
                              },
                              binding: AssignInspectorBinding(tag: toTag));
                          // Get.toNamed(Routes.ASSIGN_INSPECTOR, arguments: {'order_id': model.id, 'assign_type': 1});
                        },
                        child: Icon(
                          Icons.assignment,
                          size: 32,
                          color: Get.context!.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                        ),
                      ),
                    ),
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(12, 12, 12, 5),
                      child: Row(
                        children: [
                          Text(
                            model.orderNumber ?? '-',
                            style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                          ),
                          Expanded(
                            child: Text(
                              '${model.date?.replaceAll('小时前', S.of(Get.context!).hour_ago).replaceAll('分钟前', S.of(Get.context!).minute_ago)}',
                              textAlign: TextAlign.right,
                              style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.only(top: 0, left: 12),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                Helper.parseChinaArea(model.provinceCity ?? '-').replaceAll('China ', ''),
                                style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                              ),
                              GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  Location.share.toNavigation(model.lat, model.lon, model.address);
                                },
                                child: Offstage(
                                  offstage: !showLocation,
                                  child: Container(
                                    padding: const EdgeInsets.fromLTRB(5, 5, 5, 0),
                                    child: Row(
                                      children: [
                                        RichText(
                                          text: TextSpan(
                                            children: [
                                              WidgetSpan(
                                                child: Icon(
                                                  Icons.location_on_sharp,
                                                  color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383,
                                                  size: 16,
                                                ),
                                              ),
                                              TextSpan(
                                                text: S.of(Get.context!).home_navigation,
                                                style: MFont.regular11.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (GlobalConst.userModel?.role == UserRole.admin) ...{
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.only(left: 12, right: 12, top: 0),
                            child: Row(
                              children: [
                                Builder(builder: (context) {
                                  OrderStatus orderStatus = model.calculateStatus();
                                  return Text(
                                    S.of(context).order_status_desc(orderStatus.value),
                                    style: MFont.medium16.apply(
                                        color: orderStatus == OrderStatus.WaitDispatch
                                            ? (context.isDarkMode ? DarkColor.xFFE95332 : MColor.xFFE95332)
                                            : (context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  );
                                }),
                              ],
                            ),
                          ),
                        },
                      ],
                    ),
                    Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                      Expanded(
                        child: Visibility(
                            visible: model.address != null && model.address!.isNotEmpty,
                            child: Container(
                              padding: const EdgeInsets.only(left: 12, right: 12, top: 8),
                              child: Text(
                                model.address ?? '',
                                overflow: TextOverflow.clip,
                                maxLines: 1,
                                style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                              ),
                            )),
                      ),
                      const SizedBox(width: 4),
                      Offstage(
                        offstage: model.distance == null || model.distance!.isEmpty || GlobalConst.userModel?.role == UserRole.admin,
                        child: Container(
                          padding: const EdgeInsets.only(left: 12, right: 12, top: 8),
                          child: Text(
                            '${model.distance}',
                            style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                          ),
                        ),
                      ),
                    ]),
                    Container(
                      margin: const EdgeInsets.only(top: 13, left: 12, right: 12),
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      height: 44,
                      color: context.isDarkMode ? DarkColor.xFFF7F8F9 : MColor.xFFF7F8F9,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Expanded(
                            child: Text(
                              model.inspectionDate ?? '-',
                              style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(color: context.isDarkMode ? DarkColor.xFFDDDDDD : MColor.xFFDDDDDD, width: 1, height: 24),
                          Expanded(
                            child: Text(
                              Helper.parseOrderType(model.type ?? 0),
                              style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Container(color: context.isDarkMode ? DarkColor.xFFDDDDDD : MColor.xFFDDDDDD, width: 1, height: 24),
                          Expanded(
                            child: Text(
                              S.of(Get.context!).home_unit('${model.inspNumber ?? 0}', '${model.inspDay ?? 0}'),
                              style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.only(left: 12, right: 12, top: 6, bottom: 2),
                      child: Row(
                        children: [
                          Text(
                            S.of(Get.context!).home_product_tip,
                            style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                          ),
                          Expanded(
                            child: Text(
                              model.productName ?? '-',
                              style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (model.salesman?.isNotEmpty == true)
                      Row(
                        children: [
                          const SizedBox(
                            width: 12,
                          ),
                          Text(
                            S.of(context).salesman,
                            style: MFont.regular13.apply(color: MColor.xFF838383),
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Expanded(
                            child: Text(
                              '${model.salesman}',
                              style: MFont.regular13.apply(color: MColor.xFF565656),
                            ),
                          ),
                          const SizedBox(
                            width: 12,
                          ),
                        ],
                      ),
                    Row(
                      children: [
                        Container(
                            padding: const EdgeInsets.only(left: 12, right: 12, top: 7, bottom: 7),
                            child: Text(
                              '${model.applyNum ?? 0} ${S.of(Get.context!).home_person_apply}',
                              style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                            )),
                        const Spacer(),
                        Visibility(
                          visible: !(model.isSelf ?? false) && GlobalConst.userModel?.role != UserRole.admin,
                          child: UnconstrainedBox(
                            child: TextButton(
                              onPressed: () {
                                widget.onApplyTap?.call();
                              },
                              style: ButtonStyle(
                                shape: MaterialStateProperty.all(const StadiumBorder()),
                                side: MaterialStateProperty.all(BorderSide(color: model.applyStatus != 1 ? MColor.skin : MColor.xFF838383, width: 1)),
                                visualDensity: VisualDensity.compact,
                                padding: MaterialStateProperty.all(const EdgeInsets.symmetric(horizontal: 24, vertical: 5)),
                              ),
                              child: Text(
                                Helper.parseApplyStatus(model.applyStatus ?? 0),
                                style: MFont.medium14.apply(color: model.applyStatus != 1 ? MColor.skin : MColor.xFF838383),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (model.adminNote.isNotEmpty)
                      GestureDetector(
                        onTap: () {
                          Get.bottomSheet(SafeArea(child: AdminNoteView(tag: model.id.toString())),
                              persistent: false,
                              isScrollControlled: true,
                              ignoreSafeArea: false,
                              settings: RouteSettings(name: 'admin-note', arguments: {'order_id': model.id})).then((value) {
                            if (value is Map && value.containsKey('isSuccess') && value.containsKey('reply')) {}
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(left: 12, right: 40, top: 7, bottom: 7),
                          child: Row(
                            children: [
                              Text(
                                '备注：',
                                style: MFont.regular13.apply(color: MColor.xFF838383),
                              ),
                              Expanded(
                                  child: RichText(
                                maxLines: 2,
                                text: TextSpan(
                                  recognizer: TapGestureRecognizer()..onTap = () {},
                                  children: [
                                    TextSpan(
                                      text: model.adminNote.first.msg ?? '',
                                      // text: 'MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),',
                                      style: MFont.regular13.apply(color: MColor.xFF565656),
                                    ),
                                    const WidgetSpan(
                                        child: SizedBox(
                                      width: 2,
                                    )),
                                    const WidgetSpan(
                                      child: Icon(
                                        Icons.more,
                                        size: 14,
                                        color: MColor.xFF565656,
                                      ),
                                    ),
                                  ],
                                ),
                              ))
                            ],
                          ),
                        ),
                      )
                  ],
                ),
              ],
            ));
      }),
    );
  }
}
