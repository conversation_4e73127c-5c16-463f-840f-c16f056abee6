import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/order_explain_entity.dart';
import 'package:inspector/app/modules/pulish/controllers/publish_controller.dart';
import 'package:inspector/app/modules/tabbar/controllers/tabbar_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/translation_service.dart';

import '../../../../generated/l10n.dart';
import '../../store/setting_store.dart';

class PublishView extends GetView<PublishController> {
  const PublishView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).publish_title),
        centerTitle: true,
      ),
      body: Container(
        padding: const EdgeInsets.only(left: 15, right: 15, top: 15),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                children: [
                  Text(
                    S.of(Get.context!).publish_welcome,
                    style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                  ),
                  const SizedBox(height: 10),
                  for (int i = 0; i < controller.explains.length; i++) ...{
                    _itemView(controller.explains[i]),
                  },
                ],
              ),
            ),
            _nextButton,
          ],
        ),
      ),
    );
  }

  Widget _itemView(OrderExplain explain) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Obx(() {
        var type = controller.indexType.value;
        bool isSelected = explain.type == type;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                controller.checkData(explain.type);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Builder(builder: (context) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(explain.icon, width: 20, height: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                explain.title,
                                style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                                maxLines: 2,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656)),
                              child: Center(
                                child: Icon(
                                  isSelected ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                                  size: 15,
                                  color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        isSelected ? Icons.radio_button_on : Icons.radio_button_off,
                        size: 20,
                        color: MColor.skin,
                      ),
                    ],
                  );
                }),
              ),
            ),
            Builder(builder: (context) {
              return Container(
                color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                height: 0.5,
              );
            }),
            if (isSelected) ...{
              Container(
                margin: const EdgeInsets.only(top: 10),
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Get.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE.withOpacity(0.6),
                ),
                child: Builder(builder: (context) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (isSelected) ...{
                        if (SettingStore.to.getCurrentLocale() != const Locale('zh', 'CN')) Text(explain.title, style: MFont.semi_Bold16),
                        if (SettingStore.to.getCurrentLocale() != const Locale('zh', 'CN')) const Text(' '),
                        Text(
                          explain.content,
                          style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          explain.pointTitle,
                          style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          explain.points,
                          style: MFont.medium14.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                        ),
                        if (explain.otherTitle != null) ...{
                          Text(
                            explain.otherTitle ?? '',
                            style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                          ),
                          const SizedBox(width: 5),
                          Text(
                            explain.otherContent ?? '',
                            style: MFont.medium14.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                          ),
                        }
                      },
                    ],
                  );
                }),
              ),
            },
          ],
        );
      }),
    );
  }

  Container get _nextButton {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: TextButton(
        style: ButtonStyle(
          shape: MaterialStateProperty.all(const StadiumBorder()),
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        onPressed: () {
          if (controller.indexType.value <= 0) {
            return;
          }
          Get.toNamed(Routes.INSPECTION_INFO, arguments: {'type': controller.indexType.value})?.then((value) {
            if (value == null) {
              return;
            }
            Get.back();
            Get.toNamed(Routes.INSP_MY_INSP);
          });
        },
        child: Text(
          S.of(Get.context!).publish_next,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }
}
