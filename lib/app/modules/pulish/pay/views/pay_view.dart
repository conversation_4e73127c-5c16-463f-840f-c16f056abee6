import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/pulish/pay/controllers/pay_controller.dart';

import '../../../../../generated/l10n.dart';

class PayView extends GetView<PayController> {
  const PayView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).pay_title),
        centerTitle: true,
      ),
      body: Container(
        color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
        child: Column(
          children: [
            _amountView,
            const SizedBox(height: 15),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Column(
                  children: [
                    for (int i = 0; i < controller.titles.length; i++) ...{
                      Visibility(
                        visible: controller.isUsd ? i == 1 || i == 2 : i == 0 || i == 3,
                        child: _payItemView(i, controller.icons[i], controller.titles[i]),
                      ),
                      Divider(
                        thickness: 1,
                        height: 0.5,
                        color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
                        indent: 15,
                        endIndent: 15,
                      ),
                    },
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            _nextButton,
          ],
        ),
      ),
    );
  }

  Container get _amountView {
    var amount = '￥';
    if (controller.isUsd) {
      amount = '\$';
    }
    amount += '${controller.price}';
    return Container(
      margin: const EdgeInsets.only(top: 18),
      child: Center(
        child: Builder(builder: (context) {
          return Text(
            amount,
            style: MFont.semi_Bold24.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
            textAlign: TextAlign.center,
          );
        }),
      ),
    );
  }

  Widget _payItemView(int index, String icon, String title) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        controller.index.value = index;
        controller.checkPayType(index);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
        color: Colors.white,
        child: Row(
          children: [
            Image.asset(
              icon,
            ),
            const SizedBox(width: 4),
            Builder(builder: (context) {
              return Text(
                title,
                style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
              );
            }),
            const Spacer(),
            Obx(() {
              return Icon(
                controller.index.value == index ? Icons.radio_button_checked : Icons.radio_button_off,
                size: 16,
                color: MColor.skin,
              );
            }),
          ],
        ),
      ),
    );
  }

  Container get _nextButton {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      width: double.infinity,
      child: TextButton(
        style: ButtonStyle(
          shape: MaterialStateProperty.all(const StadiumBorder()),
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 44)),
        ),
        onPressed: () {
          controller.fetchPayInfo();
        },
        child: Text(
          S.of(Get.context!).apply_submit,
          style: MFont.medium16.apply(color: Colors.white),
        ),
      ),
    );
  }
}
