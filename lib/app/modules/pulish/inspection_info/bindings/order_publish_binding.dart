import 'package:get/get.dart';
import 'package:inspector/app/modules/pulish/inspection_info/controllers/order_address_confirm_controller.dart';

import 'package:inspector/app/modules/pulish/inspection_info/controllers/order_publish_controller.dart';

class OrderPublishBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<OrderPublishController>(
      () => OrderPublishController(),
    );
    Get.lazyPut<OrderAddressConfirmController>(() => OrderAddressConfirmController());
  }
}
