import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/modules/pulish/inspection_info/controllers/order_address_confirm_controller.dart';
import 'package:inspector/app/modules/pulish/inspection_info/controllers/order_publish_controller.dart';
import 'package:inspector/app/theme/app_theme.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';

class OrderAddressConfirmView extends GetView<OrderAddressConfirmController> {
  const OrderAddressConfirmView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            child: Container(),
            onTap: () {
              Get.back();
            },
          ),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: Theme.of(context).colorScheme.conatiner,
                  ),
                  margin: const EdgeInsets.symmetric(horizontal: 12),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [const SizedBox(height: 20), _titleView, const SizedBox(height: 12), _infoView, _actionsView],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget get _titleView {
    return Center(
      child: Text(
        S.of(Get.context!).address_detected_paste,
        style: MFont.semi_Bold17.apply(color: Get.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
      ),
    );
  }

  Widget get _actionsView {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 14),
              child: Center(
                child: Text(
                  S.of(Get.context!).public_cancel,
                  style: MFont.semi_Bold15.apply(color: Get.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
              ),
            ),
            onTap: () {
              Get.back();
            },
          ),
        ),
        Expanded(
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 14),
              child: Center(
                child: Text(
                  S.of(Get.context!).public_ok,
                  style: MFont.semi_Bold15.apply(color: Get.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
              ),
            ),
            onTap: () {
              controller.saveAddress();
            },
          ),
        )
      ],
    );
  }

  Widget get _infoView {
    return Obx(() {
      String area = controller.area.value;
      return Container(
        margin: const EdgeInsets.only(top: 10, left: 10, right: 10),
        decoration: BoxDecoration(
          color: Get.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          children: [
            if (controller.addressInfo.value?.factoryName != null && controller.addressInfo.value!.factoryName!.isNotEmpty) ...{
              _textView(0, S.of(Get.context!).address_name, S.of(Get.context!).address_name_tip, controller.addressInfo.value!.factoryName!)
            },
            if (controller.addressInfo.value?.name != null && controller.addressInfo.value!.name!.isNotEmpty) ...{
              _textView(1, S.of(Get.context!).address_person, S.of(Get.context!).address_person_tip, controller.addressInfo.value!.name!)
            },
            _textView(2, S.of(Get.context!).address_mobile, S.of(Get.context!).address_mobile_tip, controller.addressInfo.value!.phone ?? ''),
            _textView(3, S.of(Get.context!).address_area, S.of(Get.context!).address_area_tip, area),
            if (controller.addressInfo.value?.address != null && controller.addressInfo.value!.address!.isNotEmpty) ...{
              _textView(4, S.of(Get.context!).address_person, S.of(Get.context!).address_person_tip, controller.addressInfo.value!.name!),
            },
          ],
        ),
      );
    });
  }

  Widget _textView(int index, String title, String tips, String value) {
    return Builder(builder: (context) {
      return Container(
        decoration: BoxDecoration(border: Border(bottom: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, width: 0.5))),
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: 70,
              child: index != 4 && index != 1 && index != 0
                  ? RichText(
                      text: TextSpan(
                        text: '*',
                        style: MFont.medium15.apply(color: MColor.skin),
                        children: [
                          const WidgetSpan(child: SizedBox(width: 10)),
                          TextSpan(
                            text: title,
                            style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.right,
                    )
                  : Text(
                      title,
                      style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                      textAlign: TextAlign.right,
                    ),
            ),
            Expanded(
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  if (index == 3) {
                    _pickerAddress();
                  }
                },
                child: TextField(
                  focusNode: index == 4 ? controller.focusNode : null,
                  controller: controller.controllers[index],
                  keyboardType: index == 2
                      ? TextInputType.phone
                      : index == 4
                          ? TextInputType.multiline
                          : TextInputType.text,
                  minLines: 1,
                  maxLines: null,
                  style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
                  textAlign: TextAlign.start,
                  scrollPadding: EdgeInsets.zero,
                  enabled: index == 3 ? false : true,
                  decoration: InputDecoration(
                    hintText: tips,
                    hintStyle: MFont.medium15.apply(color: !context.isDarkMode ? DarkColor.xFFD7D9DD : MColor.xFFD7D9DD),
                    filled: true,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 10),
                    fillColor: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6,
                    border: const OutlineInputBorder(borderSide: BorderSide(color: Colors.white)),
                    focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6)),
                    enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6)),
                    disabledBorder: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6)),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  void _pickerAddress() {
    return Pickers.showAddressPicker(
      Get.context!,
      pickerStyle: PickerStyle(
        // showTitleBar: true,
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
        itemOverlay: Builder(builder: (context) {
          return Container(
            decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
            ),
          );
        }),
      ),
      initTown: '',
      addAllItem: false,
      onConfirm: (province, city, area) {
        controller.addressInfo.value?.province = province;
        controller.addressInfo.value?.city = city;
        controller.addressInfo.value?.area = area ?? '';
        controller.area.value = '$province$city$area';
        controller.controllers[3].text = controller.area.value;
      },
    );
  }
}
