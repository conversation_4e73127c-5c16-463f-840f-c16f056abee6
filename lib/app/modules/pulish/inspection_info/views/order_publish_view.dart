import 'package:flutter/material.dart';

import 'package:date_format/date_format.dart' as DateFormat;
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/modules/order/base/order_edit_view_mixin.dart';
import 'package:inspector/app/modules/widgets/upload/UploadWidget.dart';
import 'package:inspector/app/theme/style.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/pulish/inspection_info/controllers/order_publish_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/wallet/wallet_provider.dart';
import 'package:inspector/app/widgets/price_input_widget.dart';

import '../../../../../generated/l10n.dart';

class OrderPublishView extends GetView<OrderPublishController> with OrderEditViewMixin {
  const OrderPublishView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          S.of(Get.context!).publish_title,
          style: const TextStyle(color: Colors.white),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
      ),
      body: Obx(() {
        bool validOrder = controller.validOrder.value;
        return Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0, 0.6],
                  colors: [MColor.xFFE95332, context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE],
                ),
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                FocusManager.instance.primaryFocus?.unfocus();
              },
              child: SizedBox(
                height: Get.height - (validOrder ? 85 : 0),
                child: SafeArea(
                  bottom: false,
                  child: ListView(
                    children: [
                      Obx(() {
                        AddressRows? address = controller.addressRows.value;
                        return addressView(address, callback: (dynamic addressInfo) {
                          controller.validOrder.value = true;
                          controller.addressRows.value = addressInfo;
                          controller.fetchVipPrice();
                          FocusManager.instance.primaryFocus?.unfocus();
                        });
                      }),
                      _topDateView,
                      orderProductView(controller.productNameEditor, controller.poEditor, controller.remarkEditor, controller.docAttachments, (url) {
                        controller.docAttachments.add(url);
                      }, (position) {
                        controller.docAttachments.removeAt(position);
                        controller.docAttachments.refresh();
                      }),
                    ],
                  ),
                ),
              ),
            ),
            if (validOrder) ...{
              SizedBox(
                height: Get.height,
                child: Column(
                  children: [
                    const Spacer(),
                    _bottomView,
                  ],
                ),
              ),
            },
          ],
        );
      }),
    );
  }

  Container get _topDateView {
    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6,
        borderRadius: BorderRadius.circular(6),
      ),
      margin: const EdgeInsets.only(top: 14, left: 10, right: 10),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Obx(() {
        return Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            if (controller.dateList.isEmpty) ...{
              _dateView(-1),
              Divider(thickness: 1, height: 1, color: Get.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6),
            } else ...{
              for (int i = 0; i < controller.dateList.length; i++) ...{
                _dateView(i),
                const Divider(thickness: 1, height: 1),
              },
            },
          ],
        );
      }),
    );
  }

  Widget _dateView(int index) {
    Map<DateTime, int>? time;
    String text = S.of(Get.context!).publish_inspection_time_selected;
    if (index >= 0) {
      time = controller.dateList[index];
      text = DateFormat.formatDate(time.keys.first, [
        DateFormat.yyyy,
        '-',
        DateFormat.mm,
        ''
            '-',
        DateFormat.dd
      ]);
    }
    TextStyle headerStyle =
        const TextStyle(fontSize: 16, height: 1, fontWeight: FontWeight.w500).apply(color: Get.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666);
    TextStyle boldStyle = const TextStyle(fontSize: 17, height: 1, fontWeight: FontWeight.w600);
    bool isLast = index == controller.dateList.length - 1;
    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 12),
            Row(
              children: [
                SizedBox(width: 72, child: Text(S.of(Get.context!).publish_inspection_time, style: headerStyle)),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    Get.toNamed(Routes.DATE, arguments: List<Map<DateTime, int>>.from(controller.dateList))?.then((value) {
                      if (value != null) {
                        controller.dateList.value = value;
                        controller.dateList.refresh();
                      }
                    });
                  },
                  child: Text(text, style: boldStyle),
                ),
              ],
            ),
            const SizedBox(height: 12),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                _pickerSingle(index);
              },
              child: Row(
                children: [
                  Builder(builder: (context) {
                    return SizedBox(
                      width: 72,
                      child: Text(
                        S.of(Get.context!).publish_inspection_people,
                        style: headerStyle,
                      ),
                    );
                  }),
                  Text(
                    '${time?.values.first ?? ''} ',
                    style: boldStyle,
                    textAlign: TextAlign.center,
                  ),
                  Builder(builder: (context) {
                    return Text(
                      S.of(Get.context!).publish_people,
                      style: headerStyle,
                      maxLines: 1,
                    );
                  }),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],
        ),
        const Spacer(),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            if (isLast) {
              var now = DateTime.now();
              var nextDay = 1;
              while (true) {
                var exist = false;
                var checkDate = DateTime(now.year, now.month, now.day).add(Duration(days: nextDay));
                for (var element in controller.dateList) {
                  var date = element.keys.first;
                  if (date == checkDate) {
                    exist = true;
                    break;
                  }
                }
                if (!exist) {
                  controller.dateList.add({checkDate: 1});
                  break;
                }
                nextDay++;
              }
            } else {
              controller.dateList.removeAt(index);
            }
          },
          child: Icon(isLast ? Icons.add : Icons.highlight_remove, color: MColor.skin, size: 18),
        ),
      ],
    );
  }

  Widget get _bottomView {
    return Obx(() {
      return Container(
        padding: const EdgeInsets.only(left: 23, right: 23, top: 10, bottom: 26),
        height: 85,
        color: Colors.white,
        child: Row(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              child: Row(
                children: [
                  Icon(
                    Icons.currency_exchange,
                    color: !controller.isUSD.value ? MColor.xFF838383 : MColor.xFFE95332,
                    size: 18,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${S.of(Get.context!).publish_total}: ${controller.isUSD.value ? '\$' : '¥'}',
                    style: MFont.regular14.apply(color: MColor.xFFE95332),
                  ),
                ],
              ),
              onTap: () {
                controller.switchAction();
              },
            ),
            const SizedBox(width: 4),
            Flexible(
              flex: 100,
              child: ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 150),
                child: TextField(
                  style: MFont.semi_Bold20.apply(color: MColor.xFFE95332),
                  controller: controller.priceEditor,
                  textAlign: TextAlign.start,
                  showCursor: true,
                  readOnly: true,
                  keyboardType: TextInputType.number,
                  focusNode: controller.priceFocusNode,
                  // inputFormatters: [FilteringTextInputFormatter(RegExp('^([1-9]\\d{0,9}|0)(\\.\\d{1,2})?\$'), allow: true)],
                  decoration: InputDecoration(
                    filled: false,
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                    fillColor: Get.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
                    constraints: const BoxConstraints(maxHeight: 30, minHeight: 20),
                    border: const UnderlineInputBorder(),
                  ),
                  onTap: () {
                    Get.bottomSheet(
                      SafeArea(
                        // and this
                        child: PriceInputWidget(controller.priceEditor.text, S.of(Get.context!).publish_total, controller.isUSD.value, controller.huilv!),
                      ),
                      isScrollControlled: true,
                      ignoreSafeArea: false, // add this
                    ).then((value) {
                      if (value is Map) {
                        controller.isUSD.value = value['isUSD'];
                        controller.priceType.value = 2;
                        controller.priceEditor.value = TextEditingValue(
                            text: value['price'],
                            selection: TextSelection.fromPosition(TextPosition(affinity: TextAffinity.downstream, offset: value['price'].length)));
                      }
                    });
                  },
                ),
              ),
            ),
            const SizedBox(width: 10),
            const Spacer(),
            TextButton(
              onPressed: () {
                controller.publishOrder();
              },
              style: ButtonStyle(
                  shape: MaterialStateProperty.all(const StadiumBorder()),
                  backgroundColor: MaterialStateProperty.all(MColor.xFFE95332),
                  padding: MaterialStateProperty.all(const EdgeInsets.symmetric(horizontal: 28, vertical: 8))),
              child: Text(
                S.of(Get.context!).publish_submit,
                style: MFont.medium16.apply(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    });
  }

  void _pickerSingle(int index) {
    if (index < 0) {
      showToast(S.of(Get.context!).publish_date_tips);
      return;
    }
    List<int> data = [];
    for (int i = 0; i < 50; i++) {
      data.add(i + 1);
    }
    return Pickers.showSinglePicker(
      Get.context!,
      data: data,
      pickerStyle: PickerStyle(
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
        itemOverlay: Container(
          decoration: BoxDecoration(
            border: Border.symmetric(horizontal: BorderSide(color: Get.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
          ),
        ),
      ),
      onConfirm: (value, _) {
        var key = controller.dateList[index].keys.first;
        controller.dateList[index][key] = value;
        controller.dateList.refresh();
      },
    );
  }
}
