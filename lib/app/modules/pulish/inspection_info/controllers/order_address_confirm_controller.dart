import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';

class OrderAddressConfirmController extends GetxController {
  List<String> titles = [
    S.of(Get.context!).address_name,
    S.of(Get.context!).address_person,
    S.of(Get.context!).address_mobile,
    S.of(Get.context!).address_area,
    S.of(Get.context!).address_detail
  ];
  List<String> placeHolds = [
    S.of(Get.context!).address_name_tip,
    S.of(Get.context!).address_person_tip,
    S.of(Get.context!).address_mobile_tip,
    S.of(Get.context!).address_area_tip,
    S.of(Get.context!).address_detail_tip
  ];
  FocusNode focusNode = FocusNode();
  List<TextEditingController> controllers = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
  ];
  MineProvider mProvider = MineProvider();

  final addressInfo = Rxn<AddressRows>();

  final area = ''.obs;

  @override
  void onInit() {
    super.onInit();
    addressInfo.value = Get.arguments;
  }

  @override
  void onReady() {
    super.onReady();
    if (addressInfo.value?.factoryName?.isNotEmpty ?? false) {
      controllers[0].text = addressInfo.value!.factoryName!;
    }
    if (addressInfo.value?.name?.isNotEmpty ?? false) {
      controllers[1].text = addressInfo.value!.name!;
    }
    if (addressInfo.value?.phone?.isNotEmpty ?? false) {
      controllers[2].text = addressInfo.value!.phone!;
    }
    String area = '';
    if (addressInfo.value?.province?.isNotEmpty ?? false) {
      area += addressInfo.value!.province!;
    }
    if (addressInfo.value?.city?.isNotEmpty ?? false) {
      area += addressInfo.value!.city!;
    }
    if (addressInfo.value?.area?.isNotEmpty ?? false) {
      area += addressInfo.value!.area!;
    }
    if (area.isNotEmpty) {
      controllers[3].text = area;
    }
    if (addressInfo.value?.address?.isNotEmpty ?? false) {
      controllers[4].text = addressInfo.value!.address!;
    }
  }

  Future<void> saveAddress() async {
    for (int i = 0; i < controllers.length; i++) {
      if (controllers[i].text.trim().isEmpty) {
        if (i == 2 || i == 3) {
          showToast(titles[i]);
          return;
        }
      }
    }

    await mProvider
        .addressSave(
            controllers[0].text,
            controllers[1].text,
            controllers[2].text,
            addressInfo.value?.email ?? '',
            addressInfo.value?.province ?? '',
            addressInfo.value?.city ?? '',
            addressInfo.value?.area ?? '',
            controllers[4].text,
            addressInfo.value?.lat,
            addressInfo.value?.lon,
            addressInfo.value?.id)
        .then((value) {
      if (value.isSuccess) {
        Get.back(result: value.data!);
      }
      showToast(value.message ?? '');
    }).catchError((error) {
      showToast(error.toString());
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }
}
