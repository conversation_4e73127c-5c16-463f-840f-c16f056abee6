import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/address_entity.dart';

import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/order_input_entity.dart';
import 'package:inspector/app/data/vip_price_entity.dart';
import 'package:inspector/app/data/wallet_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/order/base/order_edit_controller_mixin.dart';
import 'package:inspector/app/modules/pulish/inspection_info/views/order_address_confirm_view.dart';
import 'package:inspector/app/modules/pulish/publish_provider.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/wallet/wallet_provider.dart';

import 'package:intl/intl.dart';

import '../../../../../generated/l10n.dart';

class OrderPublishController extends GetxController with OrderEditControllerMixin {
  TextEditingController priceEditor = TextEditingController();
  TextEditingController remarkEditor = TextEditingController();
  TextEditingController productNameEditor = TextEditingController();
  TextEditingController poEditor = TextEditingController();
  PublishProvider provider = PublishProvider();
  MineProvider mineProvider = MineProvider();

  FocusNode priceFocusNode = FocusNode();

  final validOrder = false.obs;

  int type = 1;
  final priceType = 1.obs; //1是vip 2是一口价
  final dateList = <Map<DateTime, int>>[
    {DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day).add(const Duration(days: 1)): 1}
  ].obs;
  final docAttachments = RxList<String>([]);
  final addressRows = Rxn<AddressRows>();

  double? huilv;
  final isUSD = false.obs;
  final vipEntity = VipPriceEntity().obs;

  @override
  void onInit() {
    super.onInit();
    type = Get.arguments['type'];
  }

  @override
  void onReady() {
    dateList.listen((p0) {
      if (dateList.isNotEmpty && addressRows.value != null && addressRows.value!.city != null && addressRows.value!.city!.isNotEmpty) {
        validOrder.value = true;
        fetchVipPrice();
      } else {
        validOrder.value = false;
      }
    });

    huilv = WalletProvider.instance.walletModel.value.rate ?? 1;
    isUSD.value = WalletProvider.instance.walletModel.value.userAccount == 2;

    readClipboard();
    super.onReady();
  }

  Future<void> readClipboard() async {
    ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data != null && data.text != null) {
      logger.i(data);
      recognizeAddress(data.text!);
    }
    return Future.value();
  }

  void recognizeAddress(String text) {
    EasyLoading.show();
    mineProvider.recognizeAddress(text).then((value) {
      if (value.isSuccess) {
        AddressRows address = value.data!;
        if (address.phone != null &&
            address.phone!.isNotEmpty &&
            address.province != null &&
            address.province!.isNotEmpty &&
            address.city != null &&
            address.city!.isNotEmpty &&
            address.area != null &&
            address.area!.isNotEmpty) {
          Get.generalDialog(
            pageBuilder: (ctx, a1, a2) {
              return const OrderAddressConfirmView();
            },
            routeSettings: RouteSettings(arguments: address),
          ).then((value) {
            if (value != null) {
              addressRows.value = value;
            }
          });
        }
        FocusManager.instance.primaryFocus?.unfocus();
      } else {
        showToast(value.message ?? '');
      }
    }).catchError((error) {
      showToast(error.toString());
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void _showAddressDialog() {}

  void fetchVipPrice() {
    if (dateList.isEmpty || addressRows.value == null || addressRows.value!.city == null || addressRows.value!.city!.isEmpty) {
      return;
    }
    var peopleNum = 0;
    for (var element in dateList) {
      var num = element.values.first;
      peopleNum += num;
    }

    if (peopleNum <= 0) {
      showToast(S.of(Get.context!).publish_inspection_people);
      return;
    }

    EasyLoading.show();
    provider.vipPrice(dateList.length, addressRows.value!.city!, peopleNum, type).then((value) {
      if (value.isSuccess) {
        priceType.value = 1;
        vipEntity.value = value.data ?? VipPriceEntity();
        String priceText = (isUSD.value ? vipEntity.value.usdPrice : vipEntity.value.rmbPrice) ?? '0.0';
        priceEditor.value =
            TextEditingValue(text: priceText, selection: TextSelection.fromPosition(TextPosition(affinity: TextAffinity.downstream, offset: priceText.length)));
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void publishOrder() {
    if (dateList.isEmpty) {
      showToast(S.of(Get.context!).publish_inspection_time);
      return;
    }
    var peopleNum = 0;
    for (var element in dateList) {
      var num = element.values.first;
      peopleNum += num;
    }
    if (peopleNum <= 0) {
      showToast(S.of(Get.context!).publish_inspection_people);
      return;
    }

    String? allPrice = priceType.value == 1
        ? (isUSD.value ? vipEntity.value.usdAllPrice : vipEntity.value.rmbAllPrice)
        : NumUtil.getNumByValueStr(priceEditor.text, fractionDigits: 2)!.toString();
    String priceText = priceEditor.text;
    if (priceText.isEmpty) {
      showToast(S.of(Get.context!).publish_price_tip);
      return;
    }

    String message = '';
    if (dateList.length == 1) {
      message = '您的订单共需支付${isUSD.value ? '\$' : '¥'}$priceText, 付款完成后，我们将尽快为您安排验货员';
    } else {
      message = '您的订单共需支付${isUSD.value ? '\$' : '¥'}$priceText, 为了尽快为您安排验货员，付款完成后，该订单将被拆分为${dateList.length}个子订单';
    }
    showCustomDialog(message, onConfirm: () {
      List<Map<String, dynamic>> dates = [];
      DateFormat formatter = DateFormat('yyyy-MM-dd');
      for (int i = 0; i < dateList.length; i++) {
        Map<String, dynamic> dict = {'date': formatter.format(dateList[i].keys.first)};
        dict['inspectNum'] = dateList[i].values.first;
        dates.add(dict);
      }

      OrderInputEntity entity = OrderInputEntity();
      entity.inspectType = type;
      entity.payText = priceText;
      entity.isUSD = isUSD.value;
      entity.price = NumUtil.getNumByValueStr(priceText, fractionDigits: 2);

      EasyLoading.show();
      provider
          .submitOrder(addressRows.value?.id ?? 0, addressRows.value?.userId ?? 0, dates, docAttachments, type, allPrice, productNameEditor.text, poEditor.text,
              remarkEditor.text, isUSD.value ? 2 : 1, priceType.value)
          .then((value) {
        if (value.isSuccess) {
          var oNo = value.data['orderNumber'];

          Get.toNamed(Routes.PAY, arguments: {'usd': isUSD.value, 'id': oNo, 'price': entity.price})?.then((value) {
            Get.back(result: true);
          });
          FocusManager.instance.primaryFocus?.unfocus();
        } else {
          showToast(value.message ?? '');
        }
      }).whenComplete(() {
        EasyLoading.dismiss();
      });
    });
  }

  void switchAction() {
    if (priceType.value == 1) {
      //当前是vip价格，取vipEntity里的值
      priceEditor.text = (isUSD.value ? vipEntity.value.rmbPrice : vipEntity.value.usdPrice) ?? '';
    } else {
      var text = priceEditor.text.trim();
      if (text.isNotEmpty) {
        double money = double.tryParse(text) ?? 0;
        if (isUSD.value) {
          money = money * huilv!;
        } else {
          money = money / huilv!;
        }
        priceEditor.text = NumUtil.getNumByValueStr('$money', fractionDigits: 2)?.toStringAsFixed(2) ?? '0';
        priceEditor.selection = TextSelection.fromPosition(TextPosition(offset: priceEditor.text.length));
      }
    }

    isUSD.value = !isUSD.value;
  }

  @override
  void onClose() {
    remarkEditor.dispose();
    priceEditor.dispose();
    productNameEditor.dispose();
  }
}
