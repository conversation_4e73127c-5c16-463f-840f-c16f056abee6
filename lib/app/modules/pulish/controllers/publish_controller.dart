import 'package:get/get.dart';

import 'package:inspector/app/data/order_explain_entity.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../generated/l10n.dart';

class PublishController extends GetxController {
  final indexType = (-1).obs;
  List<OrderExplain> explains = [
    OrderExplain(
      Assets.mark1,
      S.of(Get.context!).publish_sampling,
      S.of(Get.context!).publish_sampling_content,
      S.of(Get.context!).publish_point,
      S.of(Get.context!).publish_sampling_point,
      null,
      null,
      1,
    ),
    OrderExplain(
      Assets.mark2,
      S.of(Get.context!).publish_all,
      S.of(Get.context!).publish_all_content,
      S.of(Get.context!).publish_point,
      S.of(Get.context!).publish_sampling_point,
      null,
      null,
      2,
    ),
    OrderExplain(
      Assets.mark3,
      S.of(Get.context!).publish_online,
      S.of(Get.context!).publish_online_content,
      S.of(Get.context!).publish_point,
      S.of(Get.context!).publish_online_point,
      null,
      null,
      3,
    ),
    OrderExplain(
      Assets.mark4,
      S.of(Get.context!).publish_factory,
      S.of(Get.context!).publish_factory_content,
      S.of(Get.context!).publish_factory_point_title,
      S.of(Get.context!).publish_factory_point,
      S.of(Get.context!).publish_factory_review,
      S.of(Get.context!).publish_factory_review_content,
      4,
    ),
    OrderExplain(
      Assets.mark5,
      S.of(Get.context!).publish_watch,
      S.of(Get.context!).publish_watch_content,
      S.of(Get.context!).publish_point,
      S.of(Get.context!).publish_watch_point,
      null,
      null,
      5,
    ),
    OrderExplain(
      Assets.mark5,
      S.of(Get.context!).publish_watch_inspection,
      S.of(Get.context!).publish_watch_inspection_content,
      S.of(Get.context!).publish_point,
      S.of(Get.context!).publish_watch_inspection_point,
      null,
      null,
      6,
    ),
  ];

  void checkData(int type) {
    if (indexType.value == type) {
      indexType.value = -1;
    } else {
      indexType.value = type;
    }
  }
}
