// ignore_for_file: avoid_print

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/pulish/date/controllers/date_controller.dart';

import '../../../../../generated/l10n.dart';

final kToday = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
final kFirstDay = DateTime(kToday.year, kToday.month - 3, kToday.day);
final kLastDay = DateTime(kToday.year, kToday.month + 3, kToday.day);

class DateView extends GetView<DateController> {
  const DateView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).date_title),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Obx(() {
            var selectDates = controller.selectDates;
            print(selectDates); //This line can not be deleted
            return TableCalendar(
              focusedDay: controller.lastSelectedDate,
              firstDay: kFirstDay,
              lastDay: kLastDay,
              selectedDayPredicate: (e) {
                var current = DateTime(e.year, e.month, e.day);
                var temp = selectDates.firstWhereOrNull((element) {
                  var now = DateTime(element.keys.first.year, element.keys.first.month, element.keys.first.day);
                  if (current == now) {
                    return true;
                  }

                  return false;
                });

                return temp == null ? false : true;
              },
              headerStyle: HeaderStyle(
                titleCentered: true,
                formatButtonVisible: false,
                leftChevronIcon: Icon(
                  Icons.arrow_back_ios,
                  color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                  size: 16,
                ),
                rightChevronIcon: Icon(
                  Icons.arrow_forward_ios_sharp,
                  color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                  size: 16,
                ),
              ),
              onDaySelected: (date1, date2) {
                var current = DateTime(date1.year, date1.month, date1.day);
                var temp = selectDates.firstWhereOrNull((element) {
                  var now = DateTime(element.keys.first.year, element.keys.first.month, element.keys.first.day);
                  if (current == now) {
                    return true;
                  }

                  return false;
                });
                var person = controller.outDates.firstWhereOrNull((element) {
                  var now = DateTime(element.keys.first.year, element.keys.first.month, element.keys.first.day);
                  if (current == now) {
                    return true;
                  }

                  return false;
                });
                var tempNow = DateTime.now();
                var now = DateTime(tempNow.year, tempNow.month, tempNow.day);

                if (current.millisecondsSinceEpoch >= now.millisecondsSinceEpoch && temp == null) {
                  controller.selectDates.add({current: person?.values.first ?? 1});
                } else {
                  controller.selectDates.removeWhere((element) {
                    var now = DateTime(element.keys.first.year, element.keys.first.month, element.keys.first.day);
                    if (current == now) {
                      return true;
                    }

                    return false;
                  });
                }

                controller.lastSelectedDate = date1;
                controller.selectDates.sort((left, right) => left.keys.first.compareTo(right.keys.first));
                controller.selectDates.refresh();
              },
              calendarStyle: const CalendarStyle(
                selectedDecoration: BoxDecoration(color: MColor.skin, shape: BoxShape.circle),
              ),
            );
          }),
          const Spacer(),
          _nextButton,
        ],
      ),
    );
  }

  Container get _nextButton {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 20),
      child: TextButton(
        style: ButtonStyle(
          shape: MaterialStateProperty.all(const StadiumBorder()),
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        onPressed: () {
          Get.back(result: List<Map<DateTime, int>>.from(controller.selectDates));
        },
        child: Text(
          S.of(Get.context!).publish_next,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }
}
