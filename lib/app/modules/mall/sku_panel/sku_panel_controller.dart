import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/mall/mall_address/mall_address_service.dart';
import 'package:inspector/app/modules/mall/mall_service/mall_service.dart';

class SkuPanelController extends GetxController {
  final scrollController = ScrollController();
  final MallService _service = MallService();

  final MallAddressService _addressService = MallAddressService();

  late GoodsDetailResp goodsResp;
  late String skuId;
  late String goodsId;

  final selectedSkuIds = RxList<int>([]);

  final selectedSpecs = RxMap<String, String>({});

  final selectedAddress = Rxn<MallAddressEntity>();

  final quantity = 1.obs;

  final selectedSku = Rxn<SkuEntity>();

  final totalPrice = 0.0.obs;

  @override
  void onInit() {
    super.onInit();
    goodsResp = Get.arguments['goods_detail'];
    skuId = goodsResp.detail!.skuId;
    goodsId = goodsResp.detail!.id;
    _findSkuById();
    getDefaultAddress();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    scrollController.dispose();
  }

  Future<void> getDefaultAddress() async {
    var result = await _addressService.getDefaultMallAddress();
    if (result.isSuccess) {
      selectedAddress.value = result.data;
    }
  }

  Future<void> getAddressInfo(int id) async {
    var result = await _addressService.getMallAddressInfo(id);
    if (result.isSuccess) {
      selectedAddress.value = result.data;
    }
  }

  void onSpecSelected(SpecAttrEntity specAttr, SpecEntity spec) {
    selectedSpecs[specAttr.id] = spec.id;
    selectedSku.value = _findSkuBySelectedSpecs();
  }

  SkuEntity? _findSkuBySelectedSpecs() {
    for (var sku in goodsResp.skus) {
      bool matched = true;
      int matchCount = 0;
      for (var specKV in sku.specs) {
        if (selectedSpecs[specKV.id] != specKV.vId) {
          matched = false;
          break;
        } else {
          matchCount++;
        }
      }
      if (matched && matchCount == selectedSpecs.length) {
        return sku;
      }
    }
    return null;
  }

  void _findSkuById() {
    for (var sku in goodsResp.skus) {
      if (skuId == sku.id) {
        for (var specKV in sku.specs) {
          selectedSpecs[specKV.id] = specKV.vId;
        }
        selectedSku.value = sku;
        break;
      }
    }
  }
}
