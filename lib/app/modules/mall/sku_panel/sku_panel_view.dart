import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';
import 'package:inspector/app/modules/mall/order_shop_item.dart';
import 'package:inspector/app/modules/mall/sku_panel/sku_panel_controller.dart';
import 'package:inspector/app/modules/widgets/sliver_sticky_header_delegate.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/icons.dart';

import '../../../../generated/l10n.dart';

class SkuPanelView extends GetView<SkuPanelController> {
  @override
  final String? tag;

  const SkuPanelView({super.key, this.tag});

  @override
  Widget build(BuildContext context) {
    Get.put<SkuPanelController>(SkuPanelController(), tag: tag);
    return Container(
        margin: EdgeInsets.only(top: 215),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(14), topRight: Radius.circular(14)),
          color: MColor.xFFF2F2F2,
        ),
        child: controller.goodsResp == null
            ? const SizedBox()
            : Column(children: [
                Expanded(child: CustomScrollView(controller: controller.scrollController, slivers: [_titleView, _bodyView])),
                _confirmBtn
              ]));
  }

  Widget get _confirmBtn {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
      child: TextButton(
        onPressed: () {
          if (controller.selectedSku.value != null) {
            // OrderShopItem item =
            //     OrderShopItem(controller.goodsResp.shopInfo!, [controller.goodsResp], [controller.selectedSku.value!], [controller.quantity.value]);
            // Get.offNamed(MallRoutes.MALL_ORDER_CONFIRM, arguments: {
            //   'goods_list': [item],
            //   'address_info': controller.selectedAddress.value,
            // });
            Get.offNamed(MallRoutes.MALL_ORDER_CONFIRM, arguments: {
              'goods_list': [controller.goodsResp],
              'address_info': controller.selectedAddress.value,
              'sku_list': [controller.selectedSku.value!],
              'quantities': [controller.quantity.value]
            });
          }
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.xFFE64724),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          '确认支付',
          style: MFont.medium16.apply(color: Colors.white),
        ),
      ),
    );
  }

  Widget get _titleView {
    return SliverPersistentHeader(
      pinned: true,
      floating: true,
      delegate: SliverStickyHeaderDelegate(SizedBox(
        height: 42,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          color: Colors.transparent,
          child: Row(children: [
            const Spacer(),
            GestureDetector(
              onTap: () {
                Get.back();
              },
              child: const SizedBox(
                height: 42,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.close,
                      size: 24,
                    ),
                  ],
                ),
              ),
            ),
          ]),
        ),
      )),
    );
  }

  Widget get _goodsInfoView {
    return Obx(() {
      var selectedSku = controller.selectedSku.value;
      var price = double.tryParse(selectedSku?.finalPrice ?? '0.0') ?? 0;
      var finalPrice = price * controller.quantity.value;
      return Card(
        color: MColor.xFFFFFFFF,
        margin: EdgeInsets.zero,
        elevation: 0,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
        child: Padding(
          padding: const EdgeInsets.only(left: 14, right: 14, bottom: 10, top: 10),
          child: Column(
            children: [
              Row(
                children: [
                  Builder(builder: (context) {
                    var url = selectedSku!.pic.isNotEmpty ? selectedSku.pic[0] : controller.goodsResp.detail!.pic[0];
                    return ClipRRect(
                      child: CachedNetworkImage(
                        imageUrl: url,
                        width: 110,
                        height: 110,
                        fit: BoxFit.cover,
                        placeholder: (ctx, e) {
                          return Container(
                            decoration: const BoxDecoration(
                              color: MColor.xFFEEEEEE,
                            ),
                          );
                        },
                        errorWidget: (ctx, e, x) {
                          return Container(
                            decoration: const BoxDecoration(
                              color: MColor.xFFEEEEEE,
                            ),
                          );
                        },
                      ),
                    );
                  }),
                  const SizedBox(
                    width: 8,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Builder(builder: (context) {
                        return RichText(
                          textAlign: TextAlign.end,
                          text: TextSpan(
                            text: selectedSku!.accountType.symbol,
                            style: MFont.medium14.apply(color: MColor.xFFF2591D),
                            children: [
                              TextSpan(text: finalPrice.toStringAsFixed(2), style: MFont.medium24.apply(color: MColor.xFFF2591D)),
                            ],
                          ),
                        );
                      }),
                      const SizedBox(
                        height: 4,
                      ),
                      Text(
                        controller.goodsResp!.shopInfo!.name,
                        style: MFont.regular14.apply(color: MColor.xFF383838),
                      )
                    ],
                  )
                ],
              ),
              const SizedBox(
                height: 32,
              ),
              _quantityView
            ],
          ),
        ),
      );
    });
  }

  Widget get _quantityView {
    return Row(
      children: [
        Text(S.of(Get.context!).mall_goods_count, style: MFont.medium16.apply(color: MColor.xFF000000)),
        const Spacer(),
        Container(
          width: 64,
          height: 20,
          decoration: BoxDecoration(border: Border.all(color: MColor.xFFE5E5E5, width: 0.5), borderRadius: BorderRadius.circular(5)),
          child: Row(children: [
            GestureDetector(
              onTap: () {
                var quantity = controller.quantity.value;
                if (quantity <= 1) {
                  controller.quantity.value = 1;
                } else {
                  controller.quantity.value = quantity - 1;
                }
              },
              child: SizedBox(
                  width: 17,
                  child: Text(
                    '-',
                    style: MFont.regular14.apply(color: MColor.xFF000000),
                    textAlign: TextAlign.center,
                  )),
            ),
            VerticalDivider(
              color: MColor.xFFE5E5E5,
              width: 1,
              thickness: 0.5,
            ),
            Expanded(child: Obx(() {
              return Text(
                '${controller.quantity.value}',
                style: MFont.regular14.apply(color: MColor.xFF000000),
                textAlign: TextAlign.center,
              );
            })),
            VerticalDivider(
              color: MColor.xFFE5E5E5,
              width: 1,
              thickness: 0.5,
            ),
            GestureDetector(
              onTap: () {
                var quantity = controller.quantity.value;
                if (quantity >= 99) {
                  controller.quantity.value = 99;
                } else {
                  controller.quantity.value = quantity + 1;
                }
              },
              child: SizedBox(
                  width: 17,
                  child: Text(
                    '+',
                    style: MFont.regular14.apply(color: MColor.xFF000000),
                    textAlign: TextAlign.center,
                  )),
            )
          ]),
        )
      ],
    );
  }

  Widget get _emptyAddressView {
    return Card(
        color: MColor.xFFFFFFFF,
        margin: EdgeInsets.zero,
        elevation: 0,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
        child: GestureDetector(
          onTap: () {
            Get.toNamed(MallRoutes.MALL_ADDRESS_EDIT)?.then((value) {
              if (value is Map && value.containsKey('address_id')) {
                int addressId = value['address_id'];
                controller.getAddressInfo(addressId);
              }
            });
          },
          child: Padding(
            padding: const EdgeInsets.only(left: 14, right: 14, bottom: 10, top: 10),
            child: Row(
              children: [
                Icon(AppIcons.location, size: 24, color: MColor.xFFD6D6D6),
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                    child: Text(
                  '您还没有填写默认收货地址，点击添加',
                  style: MFont.medium16.apply(color: MColor.xFF000000),
                )),
                const SizedBox(
                  width: 8,
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: MColor.xFFA6A6A6,
                )
              ],
            ),
          ),
        ));
  }

  Widget get _addressView {
    return Card(
      color: MColor.xFFFFFFFF,
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          Get.toNamed(MallRoutes.MALL_ADDRESS_LIST, parameters: {'address_id': controller.selectedAddress.value!.id.toString(), 'select_mode': '1'})
              ?.then((value) {
            if (value is Map && value.containsKey('address_id')) {
              int? addressId = int.tryParse(value['address_id']);
              if (addressId != null) {
                controller.getAddressInfo(addressId);
              }
            }
          });
        },
        child: Padding(
          padding: const EdgeInsets.only(left: 14, right: 14, bottom: 10, top: 10),
          child: Row(
            children: [
              Icon(AppIcons.location, size: 24, color: MColor.xFFE64724),
              const SizedBox(
                width: 13,
              ),
              Expanded(
                  child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                Text(
                  '${controller.selectedAddress.value!.name} ${controller.selectedAddress.value!.phone}',
                  textAlign: TextAlign.start,
                  style: MFont.medium16.apply(color: MColor.xFF000000),
                ),
                const SizedBox(
                  height: 2,
                ),
                Text(
                  '${controller.selectedAddress.value!.province}${controller.selectedAddress.value!.city}${controller.selectedAddress.value!.area}${controller.selectedAddress.value!.address}',
                  textAlign: TextAlign.start,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: MFont.regular14.apply(color: MColor.xFF616161),
                )
              ])),
              const SizedBox(
                width: 13,
              ),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget get _skuAreaView {
    return Card(
      color: MColor.xFFFFFFFF,
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
      child: Padding(
        padding: const EdgeInsets.only(left: 14, right: 14, bottom: 10, top: 10),
        child: Column(
          children: [
            for (var specAttr in controller.goodsResp!.specAttrs) ...{
              Obx(() {
                var selectedSpecs = controller.selectedSpecs;
                return _specView(specAttr, selectedSpecs[specAttr.id]);
              }),
              const SizedBox(
                height: 15,
              )
            }
          ],
        ),
      ),
    );
  }

  Widget _specView(SpecAttrEntity specAttr, String? selectedSpecId) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 6.0),
          child: Text(specAttr.name, style: MFont.medium14.apply(color: MColor.xFF000000)),
        ),
        const SizedBox(
          width: 14,
        ),
        Expanded(
          child: Wrap(
            spacing: 10.0,
            runSpacing: 8.0,
            children: [
              for (SpecEntity spec in specAttr.specs) ...{
                GestureDetector(
                  onTap: () {
                    controller.onSpecSelected(specAttr, spec);
                  },
                  child: Builder(builder: (context) {
                    bool isSelected = selectedSpecId == spec.id;
                    return Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: MColor.xFFF4F5F7,
                          border: Border.all(color: isSelected ? MColor.xFFF2591D : MColor.xFFF4F5F7, width: 1)),
                      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 4),
                      child: Text(
                        spec.name,
                        style: MFont.regular14.apply(color: isSelected ? MColor.xFFF2591D : MColor.xFF000000),
                      ),
                    );
                  }),
                )
              }
            ],
          ),
        )
      ],
    );
  }

  Widget get _bodyView {
    return Obx(() {
      var itemList = <Widget>[];
      if (controller.selectedSku.value != null) {
        itemList.add(_goodsInfoView);
      }
      if (controller.selectedAddress.value == null) {
        itemList.add(_emptyAddressView);
      } else {
        itemList.add(_addressView);
      }
      itemList.add(_skuAreaView);

      var itemCount = itemList.length;
      return SliverList.separated(
        // physics: const AlwaysScrollableScrollPhysics(),
        // controller: controller.scrollController,
        separatorBuilder: (context, index) {
          return const SizedBox(
            height: 5,
          );
        },
        itemCount: itemCount,
        itemBuilder: (ctx, index) {
          return itemList[index];
        },
      );
    });
  }
}
