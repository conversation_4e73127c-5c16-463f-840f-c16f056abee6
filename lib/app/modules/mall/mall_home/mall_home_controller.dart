import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_service/mall_service.dart';

class MallHomeController extends GetxController with GetTickerProviderStateMixin {
  final MallService _service = MallService();
  final scrollController = ScrollController();

  TabController? tabController;
  GlobalKey<RefreshIndicatorState> indicatorKey = GlobalKey<RefreshIndicatorState>();

  final tabList = <MallHomeTab>[].obs;
  final wellChosen = <GoodsDetailEntity>[].obs;
  final goodsList = <GoodsDetailEntity>[].obs;

  @override
  void onInit() {
    super.onInit();
  }

  Future<void> fetchData() async {
    var result = await _service.getHomeList();
    if (result.isSuccess) {
      goodsList.clear();
      wellChosen.clear();
      tabList.clear();
      if (result.data != null) {
        tabController?.dispose();
        tabController = TabController(length: result.data!.tabList.length, vsync: this);
        goodsList.addAll(result.data!.goodsList);
        wellChosen.addAll(result.data!.wellChosen);
        tabList.addAll(result.data!.tabList);
      } else {}
    }
  }

  @override
  void onReady() {
    super.onReady();
    indicatorKey.currentState?.show();
  }

  @override
  void onClose() {
    super.onClose();
    tabController?.dispose();
  }
}
