import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_home/mall_home_controller.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';
import 'package:inspector/app/modules/mall/widget/goods_cart_view.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/app/widgets/empty_view.dart';

import 'package:nested_scroll_view_plus/nested_scroll_view_plus.dart';

class MallHomeView extends GetView<MallHomeController> {
  const MallHomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.xFFFAFAFA,
      appBar: AppBar(
          backgroundColor: MColor.xFFE8E8E8,
          title: Text(
            '库存商城',
            style: MFont.medium24.apply(color: MColor.xFFF2591D),
          ),
          leading: GestureDetector(
            onTap: () {
              Get.back();
            },
            child: const Icon(
              Icons.arrow_back_ios,
              color: MColor.xFFF2591D,
              size: 24,
            ),
          ),
          centerTitle: true,
          elevation: 0),
      body: SafeArea(
          child: RefreshIndicator(
        key: controller.indicatorKey,
        onRefresh: () => controller.fetchData(),
        child: Obx(() {
          List<Widget> headers = [];
          headers.add(_searchView);

          final bodyList = <Widget>[];
          if (controller.tabList.isNotEmpty) {
            bodyList.add(_tabView);
            bodyList.add(_tabContentView);
          }
          if (controller.wellChosen.isNotEmpty) {
            bodyList.add(_wellChosenView);
          }
          if (controller.goodsList.isNotEmpty) {
            bodyList.add(_goodsListView);
          }
          return NestedScrollViewPlus(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return headers;
            },
            body: ListView(
              physics: const BouncingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics(),
              ),
              children: bodyList,
            ),
          );
        }),
      )),
    );
  }

  Widget get _searchView {
    return SliverToBoxAdapter(child: const _MallHomeSearchBar());
  }

  Widget get _tabView {
    return DefaultTabController(
      length: controller.tabList.length,
      child: TabBar(
        controller: controller.tabController,
        indicatorColor: MColor.xFFF2591D,
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorWeight: 1,
        isScrollable: false,
        labelColor: MColor.xFFF2591D,
        unselectedLabelColor: MColor.xFF808080,
        labelStyle: MFont.medium14.apply(color: MColor.xFFF2591D),
        unselectedLabelStyle: MFont.medium14.apply(color: MColor.xFF808080),
        tabs: [
          for (var tab in controller.tabList) ...{
            Container(
                color: MColor.xFFFFFFFF,
                child: Tab(
                  text: tab.name,
                ))
          }
        ],
      ),
    );
  }

  Widget get _tabContentView {
    List<Widget> tabViews = [];
    for (var tab in controller.tabList) {
      tabViews.add(_MallHomeTab(tab));
    }
    return SizedBox(
      height: 150,
      child: TabBarView(
        controller: controller.tabController,
        children: tabViews,
      ),
    );
  }

  Widget get _wellChosenView {
    if (controller.wellChosen.isNotEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 14.0),
        child: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Row(children: [
              Expanded(
                  child: Text(
                '精选商品',
                style: MFont.medium16.apply(color: MColor.xFF000000),
              )),
              RichText(
                  text: TextSpan(
                text: '更多',
                style: MFont.medium14.apply(color: MColor.xFF383838),
                children: [
                  // TextSpan(text: '共${controller.totalQuantity}件', style: MFont.regular11.apply(color: MColor.xFFF2591D)),
                  // TextSpan(),
                  const WidgetSpan(
                    alignment: PlaceholderAlignment.middle,
                    child: const SizedBox(
                      width: 3,
                    ),
                  ),
                  const WidgetSpan(
                    alignment: PlaceholderAlignment.middle,
                    child: const Icon(
                      Icons.arrow_forward_ios,
                      size: 12,
                      color: MColor.xFF383838,
                    ),
                  ),
                ],
              ))
            ]),
            const SizedBox(
              height: 12,
            ),
            SizedBox(
                height: 161,
                child: ListView.separated(
                  itemCount: controller.wellChosen.length,
                  scrollDirection: Axis.horizontal,
                  separatorBuilder: (BuildContext context, int position) {
                    return const SizedBox(
                      width: 8,
                    );
                  },
                  itemBuilder: (BuildContext context, int position) {
                    return Obx(() {
                      var entity = controller.wellChosen[position];
                      return GoodsCardHView(entity);
                    });
                  },
                ))
          ],
        ),
      );
    }
    return const SizedBox();
  }

  Widget get _goodsListView {
    if (controller.goodsList.isNotEmpty) {
      return MasonryGridView.count(
          padding: EdgeInsets.only(top: 10, bottom: 10, left: 14, right: 14),
          crossAxisCount: 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 19,
          shrinkWrap: true,
          itemCount: controller.goodsList.length,
          itemBuilder: (context, index) {
            GoodsDetailEntity entity = controller.goodsList[index];
            return SizedBox(height: 180, child: GoodsCardView(entity));
          });
    }
    return const SizedBox();
  }
}

class _MallHomeSearchBar extends GetView<MallHomeController> implements PreferredSizeWidget {
  const _MallHomeSearchBar({super.key});
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 13, left: 14, right: 14, bottom: 8),
      child: Row(
        children: [
          Expanded(
              child: GestureDetector(
                  onTap: () {
                    Get.toNamed(MallRoutes.MALL_SEARCH);
                  },
                  child: Container(
                    constraints: const BoxConstraints(minHeight: 30, maxHeight: 30),
                    padding: const EdgeInsets.only(left: 9),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: const BorderRadius.all(Radius.circular(15)),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_sharp,
                          size: 21,
                          color: MColor.xFFB8B8B8,
                        ),
                        Expanded(
                            child: Text(
                          '输入产品名称',
                          style: MFont.regular14.apply(color: MColor.xFFB8B8B8),
                        )),
                      ],
                    ),
                  ))),
          Icon(AppIcons.shopping_cart, size: 21, color: MColor.xFF616161),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(46);
}

class _MallHomeTab extends GetView<MallHomeController> {
  final MallHomeTab tab;
  const _MallHomeTab(this.tab, {super.key});
  @override
  Widget build(BuildContext context) {
    return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 5, mainAxisSpacing: 0),
        itemBuilder: (context, position) {
          return _buildIcon(tab.list[0]);
        },
        itemCount: tab.list.length > 10 ? 10 : tab.list.length);
  }

  Widget _buildIcon(MallHomeTabIcon entrance) {
    return GestureDetector(
      onTap: () {
        RouterJump.jump(entrance.url);
      },
      child: Container(
        decoration: BoxDecoration(
          color: MColor.xFFFFFFFF,
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Builder(builder: (context) {
              if (entrance.image.isNotEmpty) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(18),
                  child: CachedNetworkImage(
                    imageUrl: entrance.image,
                    width: 36,
                    height: 36,
                    fit: BoxFit.cover,
                    placeholder: (ctx, e) {
                      return Container(
                        decoration: BoxDecoration(
                          color: MColor.xFFEEEEEE,
                          borderRadius: BorderRadius.circular(35),
                        ),
                      );
                    },
                    errorWidget: (ctx, e, x) {
                      return Container(
                        decoration: BoxDecoration(
                          color: MColor.xFFEEEEEE,
                          borderRadius: BorderRadius.circular(35),
                        ),
                      );
                    },
                  ),
                );
              }
              return AppIcons.icon(int.tryParse(entrance.icon, radix: 16), size: 36, color: MColor.xFFEA6A46);
            }),
            const SizedBox(
              height: 8,
            ),
            Text(
              entrance.name,
              maxLines: 1,
              style: const TextStyle(fontSize: 12, color: MColor.xFF2A2A2A, height: 1),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
