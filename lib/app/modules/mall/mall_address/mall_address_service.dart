import 'package:inspector/app/config/api.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/tools/public_provider.dart';

class MallAddressService {
  Future<BaseModel<MallAddressEntity>> getDefaultMallAddress() {
    return PublicProvider.request<MallAddressEntity>(path: Api.mallDefaultAddress, isPost: false);
  }

  Future<BaseModel<MallAddressListResp>> getMallAddressList() {
    return PublicProvider.request<MallAddressListResp>(path: Api.mallAddressList, isPost: false);
  }

  Future<BaseModel<MallAddressEntity>> getMallAddressInfo(int addressId) {
    return PublicProvider.request<MallAddressEntity>(path: '${Api.mallAddressInfo}?id=$addressId}', isPost: false);
  }

  Future<BaseModel<dynamic>> setDefaultAddress(int addressId) {
    return PublicProvider.request(path: Api.mallSetDefault, params: {'id': addressId}, isPost: true);
  }

  Future<BaseModel<dynamic>> deleteAddress(int addressId) {
    return PublicProvider.request(path: Api.mallDeleteAddress, params: {'id': addressId}, isPost: true);
  }

  Future<BaseModel<dynamic>> saveMallAddress(
      {int? addrId, String? name, String? phone, String? province, String? city, String? area, String? address, bool? isDefault}) {
    var params = {};
    if (addrId != null) {
      params['id'] = addrId;
    }
    if (name != null) {
      params['name'] = name;
    }
    if (phone != null) {
      params['phone'] = phone;
    }
    if (province != null) {
      params['province'] = province;
    }
    if (city != null) {
      params['city'] = city;
    }
    if (area != null) {
      params['area'] = area;
    }
    if (address != null) {
      params['address'] = address;
    }
    if (isDefault != null) {
      params['default'] = isDefault ? '1' : '0';
    }
    return PublicProvider.request(path: Api.mallSaveAddress, isPost: true, params: params);
  }
}
