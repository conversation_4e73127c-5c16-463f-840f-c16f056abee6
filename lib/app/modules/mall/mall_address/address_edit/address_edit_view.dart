import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/address/address_manager.dart';
import 'package:inspector/app/modules/mall/mall_address/address_edit/address_edit_controller.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/generated/l10n.dart';

class MallAddressEditView extends GetView<MallAddressEditController> {
  @override
  final String? tag;
  const MallAddressEditView({super.key, this.tag});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('编辑地址'),
        centerTitle: true,
        elevation: 0,
        // actions: [
        //   GestureDetector(
        //     onTap: () {},
        //     child: const SizedBox(
        //       height: 56,
        //       width: 56,
        //       child: Icon(
        //         Icons.more_vert,
        //       ),
        //     ),
        //   )
        // ],
      ),
      backgroundColor: MColor.xFFF2F2F2,
      body: SafeArea(
          child: Column(children: [
        Card(
          color: MColor.xFFFFFFFF,
          margin: const EdgeInsets.only(top: 5, left: 14, right: 14),
          elevation: 0,
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10))),
          child: Padding(
            padding: const EdgeInsets.only(left: 12.0, right: 12),
            child: Column(
              children: [
                _textInputView('收货人', '请输入收货人', controller: controller.controllers[0]),
                const Divider(
                  height: 1,
                  color: MColor.xFFF0F0F0,
                ),
                _textInputView('联系电话', '请输入联系电话', controller: controller.controllers[1], keyboardType: TextInputType.phone),
                const Divider(
                  height: 1,
                  color: MColor.xFFF0F0F0,
                ),
                _textInputView('所在地区', '省、市、区', enabled: false, onTap: () {
                  _pickerAddress();
                }, controller: controller.controllers[2]),
                const Divider(
                  height: 1,
                  color: MColor.xFFF0F0F0,
                ),
                _textInputView('详细地址', '输入详细地址', controller: controller.controllers[3]),
              ],
            ),
          ),
        ),
        const SizedBox(
          height: 30,
        ),
        _defaultBtn,
        const Spacer(),
        _saveBtn
      ])),
    );
  }

  Widget _textInputView(String header, String hint, {TextEditingController? controller, TextInputType? keyboardType, bool? enabled, VoidCallback? onTap}) {
    return SizedBox(
      height: 41,
      child: Row(
        children: [
          Expanded(
            child: Text(
              header,
              style: MFont.medium12.apply(color: MColor.xFF000000),
            ),
            flex: 1,
          ),
          Expanded(
            flex: 2,
            child: GestureDetector(
              onTap: () {
                onTap?.call();
              },
              child: TextField(
                controller: controller,
                keyboardType: keyboardType ?? TextInputType.text,
                minLines: 1,
                maxLines: 1,
                style: MFont.regular12.apply(color: MColor.xFF000000),
                textAlign: TextAlign.start,
                scrollPadding: EdgeInsets.zero,
                enabled: enabled ?? true,
                decoration: InputDecoration(
                  hintText: hint,
                  hintStyle: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                  // hintStyle: MFont.medium15.apply(color: !context.isDarkMode ? DarkColor.xFFD7D9DD : MColor.xFFD7D9DD),
                  filled: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 10),
                  fillColor: Colors.transparent,
                  border: const OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent)),
                  focusedBorder: const OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent)),
                  enabledBorder: const OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent)),
                  disabledBorder: const OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent)),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget get _defaultBtn {
    return Row(
      children: [
        Spacer(),
        Text(
          '设为默认地址',
          style: MFont.regular12.apply(color: MColor.xFF000000),
        ),
        const SizedBox(
          width: 1,
        ),
        SizedBox(
          height: 31,
          child: Obx(() {
            // bool isDefault = controller.isDefault.value;
            return Switch(
              value: controller.isDefault.value,
              activeColor: MColor.xFFF2591D,
              onChanged: (value) {
                controller.isDefault.value = value;
              },
            );
          }),
        ),
      ],
    );
  }

  Widget get _saveBtn {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
      child: TextButton(
        onPressed: () {
          controller.saveAddress();
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.xFFE64724),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          '保存',
          style: MFont.medium13.apply(color: Colors.white),
        ),
      ),
    );
  }

  void _pickerAddress() {
    return Pickers.showMultiLinkPicker(
      Get.context!,
      data: AddressManager.toMapJson(forceChinese: true),
      columeNum: 3,
      selectData: ['', '', ''],
      pickerStyle: PickerStyle(
        // showTitleBar: true,
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok.tr)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel.tr)),
        itemOverlay: Builder(builder: (context) {
          return Container(
            decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
            ),
          );
        }),
      ),
      // initTown: '',
      // addAllItem: false,
      onConfirm: (List p, List i) {
        var province = p[0];
        var city = p[1];
        var area = p[2];
        controller.province = province;
        controller.city = city;
        controller.area = area;
        controller.controllers[2].text = '$province$city$area';
        controller.focusNode.requestFocus();
      },
    );
  }
}
