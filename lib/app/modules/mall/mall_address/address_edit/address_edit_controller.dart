import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_address/mall_address_service.dart';
import 'package:inspector/app/tools/tools.dart';

class MallAddressEditController extends GetxController {
  final MallAddressService _service = MallAddressService();
  int? addressId;

  FocusNode focusNode = FocusNode();
  List<TextEditingController> controllers = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
  ];

  String province = '';
  String city = '';
  String area = '';

  final isDefault = false.obs;

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey('address_id')) {
      addressId = int.tryParse(Get.parameters['address_id']!);
    }
  }

  @override
  void onReady() {
    super.onReady();
    if (addressId != null) {
      _getAddressInfo();
    }
  }

  Future<void> _getAddressInfo() async {
    unawaited(EasyLoading.show());
    var result = await _service.getMallAddressInfo(addressId!);
    if (result.isSuccess) {
      // setInfo();
      MallAddressEntity? address = result.data;
      if (address != null) {
        province = address.province;
        city = address.city;
        area = address.area;
        controllers[0].text = address.name;
        controllers[1].text = address.phone;
        controllers[2].text = '$province$city$area';
        controllers[3].text = address.address;
        isDefault.value = address.isDefault;
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  @override
  void onClose() {
    focusNode.dispose();
    for (var element in controllers) {
      element.dispose();
    }
  }

  Future<void> saveAddress() async {
    for (int i = 0; i < controllers.length; i++) {
      if (controllers[i].text.trim().isEmpty) {
        showToast('输入不完整');
        return;
      }
    }
    unawaited(EasyLoading.show());
    var result = await _service.saveMallAddress(
        addrId: addressId,
        name: controllers[0].text.trim(),
        phone: controllers[1].text.trim(),
        province: province,
        city: city,
        area: area,
        address: controllers[3].text.trim(),
        isDefault: isDefault.value);
    if (result.isSuccess) {
      if (result.data is Map) {
        addressId = (result.data as Map)['id'];
      }
      if (addressId != null) {
        Get.back(result: {'address_id': addressId!});
      } else {
        Get.back();
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  // void recognizeAddress(String text) {
  //   EasyLoading.show();
  //   mProvider.recognizeAddress(text).then((value) {
  //     if (value.isSuccess) {
  //       AddressRows address = value.data!;
  //       province = address.province ?? '';
  //       city = address.city ?? '';
  //       area = address.area ?? '';
  //       lat = address.lat ?? 0;
  //       lng = address.lon ?? 0;
  //       controllers[0].text = address.factoryName;
  //       controllers[1].text = address.name ?? '';
  //       controllers[2].text = address.phone ?? '';
  //       controllers[3].text = address.email ?? '';
  //       controllers[4].text = '$province$city$area'.fixAutoLines();
  //       controllers[5].text = (address.address ?? '').fixAutoLines();
  //       FocusManager.instance.primaryFocus?.unfocus();
  //     } else {
  //       showToast(value.message ?? '');
  //     }
  //   }).catchError((error) {
  //     showToast(error.toString());
  //   }).whenComplete(() {
  //     EasyLoading.dismiss();
  //   });
  // }

  // void setInfo(AddressRows? info) {
  //   if (info != null) {
  //     controllers[0].text = info.factoryName;
  //     controllers[1].text = info.name ?? '';
  //     controllers[2].text = info.phone ?? '';
  //     controllers[3].text = info.email ?? '';
  //     lat = info.lat ?? 0;
  //     lng = info.lon ?? 0;
  //     province = info.province ?? '';
  //     city = info.city ?? '';
  //     area = info.area ?? '';
  //     factoryUserId = info.userId ?? 0;
  //     controllers[4].text = province + city + area;
  //     controllers[5].text = info.address ?? '';
  //   }
  // }
}
