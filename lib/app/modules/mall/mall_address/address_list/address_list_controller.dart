import 'dart:async';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_address/mall_address_service.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

class MallAddressListController extends GetxController {
  final MallAddressService _service = MallAddressService();

  int page = 0;
  int total = 0;
  final addressList = <MallAddressEntity>[].obs;
  final isManager = false.obs;
  bool isSelectMode = false;
  RefreshController refreshController = RefreshController();

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey('select_mode')) {
      isSelectMode = int.tryParse(Get.parameters['select_mode']!) == 1;
    }
    fetchAddressList();
  }

  void freshData() {
    page = 0;
    fetchAddressList();
  }

  void loadMore() {
    if (addressList.length >= total) {
      refreshController.loadComplete();
      refreshController.refreshCompleted();
      return;
    } else {
      page++;
      fetchAddressList();
    }
  }

  Future<void> fetchAddressList() async {
    unawaited(EasyLoading.show());
    var result = await _service.getMallAddressList();
    if (result.isSuccess) {
      addressList.value = result.data?.list ?? [];
      total = result.data?.total ?? 0;
    }

    refreshController.loadComplete();
    refreshController.refreshCompleted();
    unawaited(EasyLoading.dismiss());
  }

  @override
  void onClose() {
    refreshController.dispose();
  }

  Future<void> setDefaultAddress(int id) async {
    unawaited(EasyLoading.show());
    var result = await _service.setDefaultAddress(id);
    if (result.isSuccess) {
      freshData();
    } else {
      unawaited(EasyLoading.dismiss());
    }
  }

  Future<void> deleteAddress(int id) async {
    unawaited(EasyLoading.show());
    var result = await _service.deleteAddress(id);
    if (result.isSuccess) {
      freshData();
    } else {
      unawaited(EasyLoading.dismiss());
    }
  }
}
