import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_address/address_list/address_list_controller.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';

class MallAddressListView extends GetView<MallAddressListController> {
  const MallAddressListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的地址'),
        centerTitle: true,
        elevation: 0,
        actions: [
          Obx(() {
            var isManager = controller.isManager.value;
            return isManager
                ? const SizedBox()
                : GestureDetector(
                    onTap: () {
                      controller.isManager.value = true;
                    },
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 14, top: 8, bottom: 8, right: 14.0),
                        child: Text(
                          '管理',
                          style: MFont.medium14.apply(color: MColor.xFFF2591D),
                        ),
                      ),
                    ),
                  );
          })
        ],
      ),
      backgroundColor: MColor.xFFF2F2F2,
      body: SafeArea(
          child: Column(children: [
        const SizedBox(
          height: 5,
        ),
        Expanded(
          child: Obx(() {
            return ListView.separated(
              itemCount: controller.addressList.isEmpty ? 1 : controller.addressList.length,
              separatorBuilder: (context, index) {
                return const SizedBox(
                  height: 5,
                );
              },
              itemBuilder: (ctx, index) {
                if (controller.addressList.isEmpty) {
                  return SizedBox(
                    width: double.infinity,
                    height: Get.height / 2,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.hourglass_empty,
                            size: 40,
                            color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                          ),
                          const SizedBox(height: 10),
                          Text(S.of(Get.context!).no_data, style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
                        ],
                      ),
                    ),
                  );
                }
                return _addressItem(index);
              },
            );
          }),
        ),
        _textButton,
      ])),
    );
  }

  Widget get _textButton {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
      child: Obx(() {
        var isManager = controller.isManager.value;
        return TextButton(
          onPressed: () {
            if (isManager) {
              controller.isManager.value = false;
            } else {
              Get.toNamed(MallRoutes.MALL_ADDRESS_EDIT)?.then((value) {
                controller.freshData();
              });
            }
          },
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all(MColor.xFFE64724),
            shape: MaterialStateProperty.all(const StadiumBorder()),
            minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
            visualDensity: VisualDensity.compact,
            maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          ),
          child: Text(
            isManager ? '退出管理' : '新建收货地址',
            style: MFont.medium13.apply(color: Colors.white),
          ),
        );
      }),
    );
  }

  Widget _addressItem(int index) {
    MallAddressEntity address = controller.addressList[index];
    var name = address.name ?? '';
    var phone = address.phone ?? '';
    var province = address.province ?? '';
    var city = address.city ?? '';
    var area = address.area ?? '';
    var detail = address.address ?? '';
    var text = '$province-$city-$area-$detail';
    return Builder(builder: (context) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          if (controller.isSelectMode) {
            Get.back(result: {'address_id': address.id.toString()});
          } else {
            Get.toNamed(MallRoutes.MALL_ADDRESS_EDIT, parameters: {'address_id': address.id.toString()})?.then((value) {
              controller.freshData();
            });
          }
        },
        child: Card(
          color: MColor.xFFFFFFFF,
          margin: const EdgeInsets.only(
            left: 14,
            right: 14,
          ),
          elevation: 0,
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
          child: Padding(
            padding: const EdgeInsets.only(left: 17, right: 9, bottom: 10, top: 10),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(AppIcons.location, size: 24, color: MColor.xFFE64724),
                    const SizedBox(
                      width: 13,
                    ),
                    Expanded(
                        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              '${address.name} ${address.phone}',
                              textAlign: TextAlign.start,
                              style: MFont.medium14.apply(color: MColor.xFF000000),
                            ),
                          ),
                          if (address.isDefault) ...{
                            Container(
                                decoration: BoxDecoration(borderRadius: BorderRadius.circular(3), color: MColor.xFFF2591D),
                                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 3),
                                child: const Text('默认', style: TextStyle(fontSize: 10, height: 1.2, color: MColor.xFFFFFFFF))),
                          }
                        ],
                      ),
                      const SizedBox(
                        height: 2,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              '${address.province}${address.city}${address.area}${address.address}',
                              textAlign: TextAlign.start,
                              style: MFont.regular14.apply(color: MColor.xFF616161),
                            ),
                          ),
                        ],
                      ),
                    ])),
                    const SizedBox(
                      width: 13,
                    ),
                    GestureDetector(
                      onTap: () {
                        Get.toNamed(MallRoutes.MALL_ADDRESS_EDIT, parameters: {'address_id': address.id.toString()})?.then((value) {
                          controller.freshData();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Icon(
                          AppIcons.edit,
                          size: 16,
                        ),
                      ),
                    )
                  ],
                ),
                Obx(
                  () {
                    if (controller.isManager.value) {
                      return Column(
                        children: [
                          Divider(thickness: 0.5, color: MColor.xFFF0F0F0),
                          Row(
                            children: [
                              const SizedBox(
                                width: 7,
                              ),
                              GestureDetector(
                                onTap: () {
                                  if (!address.isDefault) {
                                    controller.setDefaultAddress(address.id);
                                  }
                                },
                                child: Row(children: [
                                  Container(
                                    width: 11,
                                    height: 11,
                                    decoration: BoxDecoration(
                                      color: address.isDefault ? MColor.xFFF2591D : Colors.transparent,
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(
                                        width: 1,
                                        color: address.isDefault ? MColor.xFFF2591D : MColor.xFF808080,
                                        style: BorderStyle.solid,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 4,
                                  ),
                                  Text('设为默认地址', style: MFont.regular12.apply(color: MColor.xFFA6A6A6))
                                ]),
                              ),
                              const Spacer(),
                              GestureDetector(
                                  onTap: () {
                                    controller.deleteAddress(address.id);
                                  },
                                  child: Row(children: [
                                    Icon(AppIcons.location, size: 16),
                                    const SizedBox(
                                      width: 1,
                                    ),
                                    Text(
                                      '删除',
                                      style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                                    ),
                                    const SizedBox(
                                      width: 11,
                                    )
                                  ])),
                            ],
                          )
                        ],
                      );
                    } else {
                      return const SizedBox();
                    }
                  },
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}
