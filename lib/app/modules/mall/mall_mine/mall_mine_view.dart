import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mall/mall_cart/mall_cart_controller.dart';
import 'package:inspector/app/modules/mall/mall_mine/mall_mine_controller.dart';

class MallMineView extends GetView<MallMineController> {
  const MallMineView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('我的'),
        centerTitle: true,
        elevation: 0,
      ),
      backgroundColor: MColor.xFFF2F2F2,
      body: SafeArea(
          child: Column(children: [
        const SizedBox(
          height: 5,
        ),
        // Expanded(
        //   child: Obx(() {
        //     if (controller.orderInfo.value == null) {
        //       return SizedBox();
        //     }
        //     List<Widget> items = [];
        //     items.add(_orderStateView);
        //     items.add(_addressItem);
        //     items.add(_trackingView);
        //     items.add(_goodsListView);
        //     items.add(_priceView);

        //     return ListView.separated(
        //       itemCount: items.length,
        //       separatorBuilder: (context, index) {
        //         return const SizedBox(
        //           height: 5,
        //         );
        //       },
        //       itemBuilder: (ctx, index) {
        //         return items[index];
        //       },
        //     );
        //   }),
        // ),
        // _textButton,
      ])),
    );
  }
}
