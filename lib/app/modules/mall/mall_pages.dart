import 'package:get/get.dart';
import 'package:inspector/app/modules/mall/goods_detail/goods_detail_binding.dart';
import 'package:inspector/app/modules/mall/goods_detail/goods_detail_view.dart';
import 'package:inspector/app/modules/mall/mall_address/address_edit/address_edit_binding.dart';
import 'package:inspector/app/modules/mall/mall_address/address_edit/address_edit_view.dart';
import 'package:inspector/app/modules/mall/mall_address/address_list/address_list_binding.dart';
import 'package:inspector/app/modules/mall/mall_address/address_list/address_list_view.dart';
import 'package:inspector/app/modules/mall/mall_home/mall_home_binding.dart';
import 'package:inspector/app/modules/mall/mall_home/mall_home_view.dart';
import 'package:inspector/app/modules/mall/mall_order/mall_order_confirm_binding.dart';
import 'package:inspector/app/modules/mall/mall_order/mall_order_confirm_view.dart';
import 'package:inspector/app/modules/mall/mall_order/order_detail_binding.dart';
import 'package:inspector/app/modules/mall/mall_order/order_detail_view.dart';
import 'package:inspector/app/modules/mall/mall_order/order_list_binding.dart';
import 'package:inspector/app/modules/mall/mall_order/order_list_view.dart';
import 'package:inspector/app/modules/mall/mall_tab/mall_tab_binding.dart';
import 'package:inspector/app/modules/mall/mall_tab/mall_tab_view.dart';
import 'package:inspector/app/modules/mall/payment/order_pay_result_binding.dart';
import 'package:inspector/app/modules/mall/payment/order_pay_result_view.dart';
import 'package:inspector/app/modules/mall/payment/order_payment_binding.dart';
import 'package:inspector/app/modules/mall/payment/order_payment_view.dart';

part 'mall_routes.dart';

class MallPages {
  static final routes = GetPage(name: _Paths.MALL, page: () => const MallTabView(), binding: MallTabBinding(), children: [
    GetPage(
        name: _Paths.GOODS_DETAIL,
        page: () {
          String? goodsId = Get.parameters['id'];
          String? skuId = Get.parameters['sku_id'];
          String tag = '';
          if (goodsId?.isNotEmpty == true) {
            tag = 'id=$goodsId';
          } else if (skuId?.isNotEmpty == true) {
            tag = 'sku_id=$skuId';
          }
          return GoodsDetailView(tag: tag);
        },
        preventDuplicates: false,
        binding: GoodsDetailBinding()),
    GetPage(
        name: _Paths.MALL_ADDRESS_EDIT,
        page: () {
          String? addressId = Get.parameters['address_id'];
          return MallAddressEditView(tag: addressId);
        },
        binding: MallAddressEditBinding()),
    GetPage(
      name: _Paths.MALL_ADDRESS_LIST,
      page: () {
        return const MallAddressListView();
      },
      binding: MallAddressListBinding(),
    ),
    GetPage(
      name: _Paths.MALL_ORDER_CONFIRM,
      page: () {
        return const MallOrderConfirmView();
      },
      binding: MallOrderConfirmBinding(),
    ),
    GetPage(
        name: _Paths.MALL_ORDER_LIST,
        page: () {
          return const OrderListView();
        },
        binding: OrderListBinding()),
    GetPage(
        name: _Paths.MALL_ORDER_DETAIL,
        page: () {
          String? orderId = Get.parameters['order_num'];
          String? sn = Get.parameters['sn'];
          String tag = '';
          if (orderId?.isNotEmpty == true) {
            tag = 'order_num=$orderId';
          } else if (sn?.isNotEmpty == true) {
            tag = 'sn=$sn';
          }
          return OrderDetailView(tag: tag);
        },
        binding: OrderDetailBinding()),
    GetPage(
        name: _Paths.MALL_PAY_ORDER,
        page: () {
          String? orderId = Get.parameters['sn'];
          return OrderPaymentView(tag: orderId);
        },
        binding: OrderPaymentBinding()),
    GetPage(
        name: _Paths.MALL_PAY_RESULT,
        page: () {
          String? orderId = Get.parameters['sn'];
          return OrderPayResultView(tag: orderId);
        },
        binding: OrderPayResultBinding())
  ]);
}
