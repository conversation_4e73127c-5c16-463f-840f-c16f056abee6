import 'package:inspector/app/config/api.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/tools/public_provider.dart';

class MallService {
  Future<BaseModel<GoodsDetailResp>> getGoodsDetails(String goodsId, String skuId) {
    String urlSuffix = '';
    if (skuId.isNotEmpty) {
      urlSuffix = 'sku_id=$skuId';
    } else if (goodsId.isNotEmpty) {
      urlSuffix = 'id=$goodsId';
    }
    return PublicProvider.request<GoodsDetailResp>(path: '${Api.goodsDetail}?$urlSuffix', isPost: false);
  }

  Future<BaseModel<dynamic>> calculatePrice(String skuId, int count) {
    return PublicProvider.request(path: Api.goodsCalcPrice, params: {'sku_id': skuId, 'count': count}, isPost: true);
  }

  Future<BaseModel<dynamic>> submitOrder(List<OrderSubmitEntity> orders, int addressId) {
    return PublicProvider.request(path: Api.orderCreate, params: {'goods': orders, 'express_id': addressId}, isPost: true);
  }

  Future<BaseModel<MallOrderDetailEntity>> getOrderDetail({String? orderNum, String? orderSn}) {
    if (orderNum?.isNotEmpty == true) {
      return PublicProvider.request<MallOrderDetailEntity>(path: '${Api.mallOrderDetail}?order_num=$orderNum', isPost: false);
    } else if (orderSn?.isNotEmpty == true) {
      return PublicProvider.request<MallOrderDetailEntity>(path: '${Api.mallOrderDetail}?sn=$orderSn', isPost: false);
    } else {
      return Future.value();
    }
  }

  Future<BaseModel<MallOrderListResp>> getOrderList() {
    return PublicProvider.request(path: Api.mallOrderList, isPost: false);
  }

  Future<BaseModel<dynamic>> cancelOrder(String sn) {
    return PublicProvider.request(path: Api.mallOrderCancel, params: {"sn": sn}, isPost: true);
  }

  Future<BaseModel<PaymentResp>> getPaymentInfo(String orderId) {
    return PublicProvider.request<PaymentResp>(path: Api.mallPaymentInfo, params: {'type': 2, 'sn': orderId}, isPost: true);
  }

  Future<BaseModel<dynamic>> getExpressFee(List<String> skuIdList, int addressId) {
    return PublicProvider.request(path: Api.mallExpressFee, params: {'sku_id': skuIdList, 'express_id': addressId}, isPost: true);
  }

  //订单支付
  Future<BaseModel<dynamic>> payOrder(int payType, String orderId) {
    return PublicProvider.request<dynamic>(path: Api.mallPayOrder, params: {'type': 2, 'pay_type': payType, 'sn': orderId});
  }

  Future<BaseModel<dynamic>> payAlipayTest(String payOrderNo) {
    return PublicProvider.request<dynamic>(path: Api.mallPayAlipayTest, params: {'pay_type': 5, 'sn': payOrderNo});
  }

  Future<BaseModel<MallHomeResp>> getHomeList() {
    return PublicProvider.request<MallHomeResp>(path: Api.mallHome, isPost: false);
  }

  Future<BaseModel<dynamic>> confirmOrderReceived(String orderNum) {
    return PublicProvider.request<dynamic>(path: Api.mallOrderReceived, isPost: true, params: {'order_num': orderNum});
  }
}
