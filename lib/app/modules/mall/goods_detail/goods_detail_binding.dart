import 'package:get/get.dart';
import 'package:inspector/app/modules/mall/goods_detail/goods_detail_controller.dart';

class GoodsDetailBinding extends Bindings {
  @override
  void dependencies() {
    String? goodsId = Get.parameters['id'];
    String? skuId = Get.parameters['sku_id'];
    String tag = '';
    if (goodsId?.isNotEmpty == true) {
      tag = 'id=$goodsId';
    } else if (skuId?.isNotEmpty == true) {
      tag = 'sku_id=$skuId';
    }
    Get.lazyPut<GoodsDetailController>(() => GoodsDetailController(), tag: tag);
  }
}
