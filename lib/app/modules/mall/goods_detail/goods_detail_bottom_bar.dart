import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mall/goods_detail/goods_detail_controller.dart';
import 'package:inspector/app/tools/icons.dart';

import '../../../../generated/l10n.dart';

class GoodsDetailBottomBar extends GetView<GoodsDetailController> {
  @override
  final String? tag;
  const GoodsDetailBottomBar({super.key, this.tag});

  @override
  Widget build(BuildContext context) {
    return Container(
      // height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // GestureDetector(
          //   onTap: () {},
          //   child: Column(
          //     mainAxisAlignment: MainAxisAlignment.center,
          //     children: [
          //       AppIcons.icon(0xe620, size: 16, color: MColor.xFFF2591D),
          //       const SizedBox(
          //         height: 4,
          //       ),
          //       Text('首页', style: MFont.regular12.apply(color: MColor.xFF808080))
          //     ],
          //   ),
          // ),
          // const SizedBox(
          //   width: 24,
          // ),
          // GestureDetector(
          //   onTap: () {},
          //   child: Column(
          //     mainAxisAlignment: MainAxisAlignment.center,
          //     children: [
          //       AppIcons.icon(0xe600, size: 16, color: MColor.xFFA6A6A6),
          //       const SizedBox(
          //         height: 4,
          //       ),
          //       Text('收藏', style: MFont.regular12.apply(color: MColor.xFF808080))
          //     ],
          //   ),
          // ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              controller.showSkuPanel(tag);
            },
            child: Container(
                height: 32,
                padding: const EdgeInsets.symmetric(horizontal: 14),
                decoration: BoxDecoration(
                  color: MColor.xFFF2591D,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(16),
                  ),
                ),
                child: Text(
                  S.of(Get.context!).mall_buy_immediate,
                  textAlign: TextAlign.center,
                  style: MFont.medium16.apply(color: MColor.xFFFFFFFF),
                  strutStyle: const StrutStyle(
                    forceStrutHeight: true,
                    leading: 1.0,
                  ),
                )),
          )
        ],
      ),
    );
  }
}
