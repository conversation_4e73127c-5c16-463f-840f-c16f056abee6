import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/mall/goods_detail/goods_detail_bottom_bar.dart';
import 'package:inspector/app/modules/mall/goods_detail/goods_detail_controller.dart';
import 'package:inspector/app/modules/widgets/banner_view.dart';

import '../../../../generated/l10n.dart';

class GoodsDetailView extends GetView<GoodsDetailController> {
  const GoodsDetailView({super.key, this.tag});

  @override
  final String? tag;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).shop_goods_detail_title),
        centerTitle: true,
        elevation: 0,
        // actions: [
        //   GestureDetector(
        //     onTap: () {},
        //     child: const SizedBox(
        //       height: 56,
        //       width: 56,
        //       child: Icon(
        //         Icons.more_vert,
        //       ),
        //     ),
        //   )
        // ],
      ),
      backgroundColor: Colors.white,
      body: SafeArea(
          child: Column(children: [
        Expanded(
          child: Obx(() {
            if (controller.goodsDetailEntity.value == null) {
              return const SizedBox();
            } else {
              return CustomScrollView(
                controller: controller.scrollController,
                slivers: [_headerView, _bodyView],
              );
            }
          }),
        ),
        GoodsDetailBottomBar(tag: tag),
      ])),
    );
  }

  Widget get _headerView {
    return SliverToBoxAdapter(child: Builder(builder: (context) {
      if (controller.goodsDetailEntity.value!.pic.isNotEmpty == true) {
        double width = MediaQuery.of(Get.context!).size.width;
        double height = width * 252 / 393;
        return BannerView(controller.goodsDetailEntity.value!.pic, width, height);
      } else {
        return const SizedBox();
      }
    }));
  }

  Widget get _bodyView {
    return SliverToBoxAdapter(
        child: Column(
      children: [_introView, _producerView, _goodsDetailView],
    ));
  }

  Widget get _introView {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 14.0, right: 14.0, top: 10, bottom: 10),
          child: Column(
            children: [
              Row(
                children: [
                  Builder(builder: (context) {
                    return RichText(
                      textAlign: TextAlign.end,
                      text: TextSpan(
                        text: controller.goodsDetailEntity.value!.accountType.symbol,
                        style: MFont.medium14.apply(color: MColor.xFFF2591D),
                        children: [
                          TextSpan(text: controller.goodsDetailEntity.value!.price, style: MFont.medium24.apply(color: MColor.xFFF2591D)),
                        ],
                      ),
                    );
                  }),
                  const Spacer(),
                  Text(
                    controller.goodsDetailEntity.value!.short_address,
                    style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                  )
                ],
              ),
              const SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Expanded(child: Text(controller.goodsDetailEntity.value!.title, style: MFont.medium18.apply(color: MColor.xFF000000))),
                ],
              ),
              const SizedBox(
                height: 8,
              ),
              Row(
                children: [
                  Expanded(
                      child: Text(
                    controller.goodsDetailEntity.value!.content,
                    style: MFont.regular14.apply(color: MColor.xFFA6A6A6),
                  )),
                ],
              ),
            ],
          ),
        ),
        SizedBox(
          height: 5.0,
          child: Center(
            child: Container(
              color: MColor.xFFF0F0F0,
            ),
          ),
        )
      ],
    );
  }

  Widget get _producerView {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0.0, vertical: 10),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 14.0, right: 14.0, bottom: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '货源信息',
                        style: MFont.medium16.apply(color: MColor.xFF000000),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 4),
                child: Row(
                  children: [
                    Text(
                      '供应商信息',
                      style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                    ),
                    const Spacer(),
                    Text(controller.shopInfo.value!.name, style: MFont.regular12.apply(color: MColor.xFF383838))
                  ],
                ),
              ),
              const Divider(height: 1, color: MColor.xFFF0F0F0),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 4),
                child: Row(
                  children: [
                    Text(
                      '原产地',
                      style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                    ),
                    const Spacer(),
                    Text(controller.goodsDetailEntity.value!.producing_area, style: MFont.regular12.apply(color: MColor.xFF383838))
                  ],
                ),
              ),
              const Divider(height: 1, color: MColor.xFFF0F0F0),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 4),
                child: Row(
                  children: [
                    Text(
                      '库存情况',
                      style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                    ),
                    const Spacer(),
                    Text('剩余${controller.goodsDetailEntity.value!.stock}${controller.goodsDetailEntity.value!.unit}',
                        style: MFont.regular12.apply(color: MColor.xFF383838))
                  ],
                ),
              )
            ],
          ),
        ),
        SizedBox(
          height: 5.0,
          child: Center(
            child: Container(
              color: MColor.xFFF0F0F0,
            ),
          ),
        )
      ],
    );
  }

  Widget get _goodsDetailView {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  '商品详情',
                  style: MFont.medium16.apply(color: MColor.xFF000000),
                ),
              ),
            ],
          ),
        ),
        for (var img in controller.goodsDetailEntity.value!.content_pics) ...{
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14),
              child: ClipRRect(
                child: CachedNetworkImage(
                  imageUrl: img,
                  // width: widget.width,
                  // height: widget.height,
                  fit: BoxFit.cover,
                  placeholder: (ctx, e) {
                    return Container(
                      decoration: const BoxDecoration(
                        color: MColor.xFFEEEEEE,
                      ),
                    );
                  },
                  errorWidget: (ctx, e, x) {
                    return Container(
                      decoration: const BoxDecoration(
                        color: MColor.xFFEEEEEE,
                      ),
                    );
                  },
                ),
              )),
        }
      ],
    );
  }
}
