import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_service/mall_service.dart';
import 'package:inspector/app/modules/mall/sku_panel/sku_panel_view.dart';

class GoodsDetailController extends GetxController {
  final MallService _service = MallService();
  final scrollController = ScrollController();

  final goodsResp = Rxn<GoodsDetailResp>();
  final goodsDetailEntity = Rxn<GoodsDetailEntity>();
  final shopInfo = Rxn<ShopInfoEntity>();
  final skus = RxList<SkuEntity>([]);
  final specs = RxList<SpecAttrEntity>([]);

  late String goodsId;
  late String skuId;
  @override
  void onInit() {
    goodsId = Get.parameters['id'] ?? '';
    skuId = Get.parameters['sku_id'] ?? '';
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    _getGoodsDetails();
  }

  Future<void> _getGoodsDetails() async {
    var result = await _service.getGoodsDetails(goodsId, skuId);
    if (result.isSuccess) {
      var data = result.data;
      if (data != null) {
        goodsResp.value = data;
        goodsDetailEntity.value = data.detail;
        goodsId = data.detail!.id;
        skuId = data.detail!.skuId;
        shopInfo.value = data.shopInfo;
        skus.value = data.skus;
        specs.value = data.specAttrs;
      } else {
        //null
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  void showSkuPanel(String? tag) {
    Get.bottomSheet(SafeArea(child: SkuPanelView(tag: tag)),
        persistent: false,
        isScrollControlled: true,
        ignoreSafeArea: false,
        settings: RouteSettings(name: 'sku-panel', arguments: {'goods_detail': goodsResp.value, 'sku_id': skuId, 'goods_id': goodsId})).then((value) {
      if (value is Map && value.containsKey('isSuccess') && value.containsKey('reply')) {
        var isSuccess = value['isSuccess'] && value['reply'] != null;
        if (isSuccess) {}
      }
    });
  }
}
