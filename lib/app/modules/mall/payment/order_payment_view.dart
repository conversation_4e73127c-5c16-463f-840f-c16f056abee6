import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/payment/order_payment_controller.dart';
import 'package:inspector/app/tools/icons.dart';

import '../../../../generated/l10n.dart';

class OrderPaymentView extends GetView<OrderPaymentController> {
  @override
  final String? tag;
  const OrderPaymentView({super.key, this.tag});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).mall_payment),
        centerTitle: true,
        elevation: 0,
      ),
      backgroundColor: MColor.xFFF2F2F2,
      body: SafeArea(
        child: Column(
          children: [
            _amountView,
            const SizedBox(height: 18),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14.0),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      S.of(Get.context!).mall_payment_methods,
                      style: MFont.regular14.apply(color: MColor.xFF616161),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 6,
            ),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Obx(() {
                  var payment = controller.paymentInfo.value;
                  if (payment == null) {
                    return SizedBox();
                  }
                  return Column(
                    children: [
                      for (int i = 0; i < payment.methods.length; i++) ...{
                        _payItemView(payment.methods[i]),
                        Divider(
                          thickness: 1,
                          height: 0.5,
                          color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
                          indent: 15,
                          endIndent: 15,
                        ),
                      },
                    ],
                  );
                }),
              ),
            ),
            const Spacer(),
            _nextButton,
          ],
        ),
      ),
    );
  }

  Widget get _amountView {
    return Container(
      margin: const EdgeInsets.only(top: 18),
      child: Center(
        child: Builder(builder: (context) {
          return Obx(() {
            if (controller.paymentInfo.value == null) {
              return SizedBox();
            }
            var amount = '${controller.paymentInfo.value!.account.symbol} ${controller.paymentInfo.value!.price}';
            return RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                children: [
                  // TextSpan(text: '共${controller.totalQuantity}件', style: MFont.regular11.apply(color: MColor.xFFF2591D)),
                  const WidgetSpan(
                    child: const SizedBox(
                      width: 3,
                    ),
                  ),
                  TextSpan(text: controller.paymentInfo.value!.account.symbol, style: MFont.medium16.apply(color: MColor.xFFF2591D)),
                  TextSpan(text: controller.paymentInfo.value!.price, style: MFont.medium26.apply(color: MColor.xFFF2591D)),
                ],
              ),
            );
          });
        }),
      ),
    );
  }

  Widget _payItemView(PaymentMethod method) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // controller.index.value = index;
        // controller.checkPayType(index);
        controller.selectedMethod.value = method;
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
        color: Colors.white,
        child: Row(
          children: [
            if (method.image.isNotEmpty) ...{
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: CachedNetworkImage(
                  imageUrl: method.image,
                  width: 24,
                  height: 24,
                  fit: BoxFit.cover,
                  placeholder: (ctx, e) {
                    return Container(
                      decoration: BoxDecoration(
                        color: MColor.xFFEEEEEE,
                        borderRadius: BorderRadius.circular(35),
                      ),
                    );
                  },
                  errorWidget: (ctx, e, x) {
                    return Container(
                      decoration: BoxDecoration(
                        color: MColor.xFFEEEEEE,
                        borderRadius: BorderRadius.circular(35),
                      ),
                    );
                  },
                ),
              )
            } else ...{
              AppIcons.icon(int.tryParse(method.icon, radix: 16), size: 24, color: Color(int.tryParse(method.color, radix: 16) ?? 0xFFFFFFFF))
            },
            const SizedBox(width: 9),
            Expanded(
              child: Text(
                method.name,
                style: MFont.medium16.apply(color: MColor.xFF000000),
              ),
            ),
            if (method.desc.isNotEmpty) ...{
              const SizedBox(
                width: 12,
              ),
              Expanded(
                child: Text(
                  method.desc,
                  style: MFont.regular12.apply(color: MColor.xFFF2591D),
                  textAlign: TextAlign.end,
                ),
              ),
            } else ...{
              const Spacer(),
            },
            Obx(() {
              var selected = controller.selectedMethod.value == method;
              return Container(
                width: 11,
                height: 11,
                decoration: BoxDecoration(
                  color: selected ? MColor.xFFF2591D : Colors.transparent,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    width: 1,
                    color: selected ? MColor.xFFF2591D : MColor.xFF808080,
                    style: BorderStyle.solid,
                  ),
                ),
              );
              // return Icon(
              //   selected ? Icons.radio_button_checked : Icons.radio_button_off,
              //   size: 10,
              //   color: MColor.skin,
              // );
            }),
          ],
        ),
      ),
    );
  }

  Container get _nextButton {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
      child: TextButton(
        onPressed: () {
          controller.fetchPayInfo();
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.xFFE64724),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          S.of(Get.context!).mall_confirm_pay,
          style: MFont.medium16.apply(color: Colors.white),
        ),
      ),
    );
    // return Container(
    //   margin: const EdgeInsets.symmetric(horizontal: 10),
    //   width: double.infinity,
    //   child: TextButton(
    //     style: ButtonStyle(
    //       shape: MaterialStateProperty.all(const StadiumBorder()),
    //       backgroundColor: MaterialStateProperty.all(MColor.skin),
    //       minimumSize: MaterialStateProperty.all(const Size(double.infinity, 44)),
    //     ),
    //     onPressed: () {
    //       controller.fetchPayInfo();
    //     },
    //     child: Text(
    //       S.of(Get.context!).apply_submit,
    //       style: MFont.medium16.apply(color: Colors.white),
    //     ),
    //   ),
    // );
  }
}
