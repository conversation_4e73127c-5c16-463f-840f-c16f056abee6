import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';
import 'package:inspector/app/modules/mall/payment/order_pay_result_controller.dart';
import 'package:inspector/app/tools/icons.dart';

import '../../../../generated/l10n.dart';

class OrderPayResultView extends GetView<OrderPayResultController> {
  @override
  final String? tag;
  const OrderPayResultView({super.key, this.tag});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        elevation: 0,
      ),
      backgroundColor: MColor.xFFFFFFFF,
      body: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 70),
            AppIcons.icon(
              0xe6fd,
              size: 65,
              color: MColor.xFF2A82E4,
            ),
            const SizedBox(
              height: 16,
            ),
            Text(S.of(Get.context!).mall_pay_succeed, style: MFont.regular20.apply(color: MColor.xFF000000)),
            const SizedBox(
              height: 25,
            ),
            Obx(() {
              String totalFee = controller.totalFee.value;
              UserAccountType account = controller.account.value == null ? UserAccountType.CNY : controller.account.value!;
              return RichText(
                textAlign: TextAlign.start,
                text: TextSpan(
                  children: [
                    // TextSpan(text: '共${controller.totalQuantity}件', style: MFont.regular11.apply(color: MColor.xFFF2591D)),
                    const WidgetSpan(
                      child: const SizedBox(
                        width: 3,
                      ),
                    ),
                    TextSpan(text: account.symbol, style: MFont.medium20.apply(color: MColor.xFFF2591D)),
                    TextSpan(text: totalFee, style: MFont.medium36.apply(color: MColor.xFFF2591D)),
                  ],
                ),
              );
            }),
            const SizedBox(
              height: 8,
            ),
            // Text('（积分抵现¥264.00）', style: MFont.regular12.apply(color: MColor.xFFF2591D)),
            const Spacer(),
            _nextButton,
          ],
        ),
      ),
    );
  }

  Container get _nextButton {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 15),
      child: TextButton(
        onPressed: () {
          Get.offAndToNamed(MallRoutes.MALL_ORDER_DETAIL, parameters: {'sn': controller.orderId});
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.xFFF2F2F2),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          S.of(Get.context!).mall_check_order_detail,
          style: MFont.medium13.apply(color: MColor.xFF000000),
        ),
      ),
    );
  }
}
