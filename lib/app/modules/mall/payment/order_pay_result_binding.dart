import 'package:get/get.dart';
import 'package:get/get_instance/src/bindings_interface.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/modules/mall/payment/order_pay_result_controller.dart';

class OrderPayResultBinding extends Bindings {
  @override
  void dependencies() {
    final order_num = Get.parameters['sn'] ?? '';
    Get.lazyPut<OrderPayResultController>(() => OrderPayResultController(), tag: order_num);
  }
}
