import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';
import 'package:inspector/app/modules/mall/mall_service/mall_service.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';
import 'package:tobias/tobias.dart';

import '../../../../generated/l10n.dart';

class OrderPaymentController extends GetxController {
  String orderSn = '';
  MallService _service = MallService();

  final paymentInfo = Rxn<PaymentResp>();

  final selectedMethod = Rxn<PaymentMethod>();

  final index = 0.obs;
  //1-平台 2-paypal 3-支付宝 4-其他
  var payType = 3;

  @override
  void onInit() {
    super.onInit();
    orderSn = Get.parameters['sn'] ?? '';
    if (orderSn.isNotEmpty) {
      getPaymentInfo();
    } else {
      Get.back();
    }
  }

  Future<void> getPaymentInfo() async {
    var result = await _service.getPaymentInfo(orderSn);
    if (result.isSuccess) {
      paymentInfo.value = result.data;
    } else {
      Get.back();
    }
  }

  /// 秒转时分秒
  String second2MS(int sec) {
    String hms = '00:00';
    if (sec > 0) {
      // int h = sec ~/ 3600;
      int m = (sec % 3600) ~/ 60;
      int s = sec % 60;
      hms = '${zeroFill(m)}:${zeroFill(s)}';
    }
    return hms;
  }

  String zeroFill(int i) {
    return i >= 10 ? '$i' : '0$i';
  }

  void payAction(dynamic order) {
    if (index.value == 0) {
      //zfb
      if (order['aliPayurl'] == null) {
        showToast(S.of(Get.context!).failed_get_payment_info);
        return;
      }
      var text = order['aliPayurl'] as String;
      Tobias tobias = Tobias();
      tobias.pay(text, evn: AliPayEvn.online).then((value) {
        if (value['resultStatus'] == '9000') {
          showToast(S.of(Get.context!).pay_result_success);
          Future.delayed(const Duration(seconds: 1), () {
            Get.offAndToNamed(MallRoutes.MALL_PAY_RESULT,
                parameters: {"sn": orderSn}, arguments: {'total_fee': paymentInfo.value!.price, "account": paymentInfo.value!.account});
          });
        } else {
          showToast(S.of(Get.context!).pay_result_failed);
        }
        logger.e('value value');
      });
    } else if (index.value == 1) {
      //paypal
    } else {
      // if (Get.currentRoute.contains(Routes.ORDER_LIST1)) {
      // Get.find<OrderListController>().takePublish(0, 0, 10);
      // Get.find<OrderListController>().fetchPublishList(false);
      // Future.delayed(const Duration(milliseconds: 300), () {
      //   Get.find<OrderListController>(tag: '0').refreshAction();
      // });
      // }
      showToast(S.of(Get.context!).pay_result_success);
      Get.back(result: true);
    }
  }

  void fetchPayInfo() {
    if (selectedMethod.value == null) {
      return;
    }
    EasyLoading.show();
    var payType = selectedMethod.value!.type;
    // if (payType == 5) {
    //   _service.payAlipayTest(orderSn).then((value) => null).whenComplete(() => EasyLoading.dismiss());
    // } else {
    _service.payOrder(payType, orderSn).then((value) {
      if (value.isSuccess) {
        if (payType == 1) {
          Get.offAndToNamed(MallRoutes.MALL_PAY_RESULT,
              parameters: {'sn': orderSn}, arguments: {'total_fee': paymentInfo.value!.price, "account": paymentInfo.value!.account});
        } else {
          payAction(value.data);
        }
      } else {
        showToast(value.message ?? S.of(Get.context!).failed_get_payment_info);
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
    // }
  }
}
