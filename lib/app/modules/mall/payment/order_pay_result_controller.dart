import 'package:get/get.dart';
import 'package:inspector/app/enums/account_type.dart';

class OrderPayResultController extends GetxController {
  late String orderId = '';

  final totalFee = ''.obs;
  final account = Rxn<UserAccountType>();
  @override
  void onInit() {
    super.onInit();
    orderId = Get.parameters['sn'] ?? '';
    if (orderId.isEmpty) {
      Get.back();
    }

    totalFee.value = Get.arguments['total_fee'];
    account.value = Get.arguments['account'];
  }
}
