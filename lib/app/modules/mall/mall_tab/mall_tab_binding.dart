import 'package:get/get.dart';
import 'package:inspector/app/modules/mall/mall_cart/mall_cart_controller.dart';
import 'package:inspector/app/modules/mall/mall_home/mall_home_controller.dart';
import 'package:inspector/app/modules/mall/mall_mine/mall_mine_controller.dart';
import 'package:inspector/app/modules/mall/mall_tab/mall_tab_controller.dart';
import 'package:inspector/app/modules/mall/mall_tab/mall_tab_view.dart';

class MallTabBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<MallTabController>(() => MallTabController());
    Get.lazyPut<MallHomeController>(() => MallHomeController());
    Get.lazyPut<MallCartController>(() => MallCartController());
    Get.lazyPut<MallMineController>(() => MallMineController());
  }
}
