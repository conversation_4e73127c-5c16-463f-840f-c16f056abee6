import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/mall/mall_cart/mall_cart_view.dart';
import 'package:inspector/app/modules/mall/mall_home/mall_home_view.dart';
import 'package:inspector/app/modules/mall/mall_mine/mall_mine_view.dart';
import 'package:inspector/generated/assets.dart';

import '../../../tools/icons.dart';

enum TabPage {
  PageHome(1, '首页', AppIcons.home),
  PageMessage(2, '消息', AppIcons.message),
  PageCart(3, '购物车', AppIcons.shopping_cart),
  PageMine(4, '我的', AppIcons.mine);

  final int pageId;
  final String value;
  final IconData tabIcon;
  const TabPage(this.pageId, this.value, this.tabIcon);
}

class MallTabController extends GetxController {
  var tabPages = [TabPage.PageHome, TabPage.PageCart, TabPage.PageMine];
  final tabViews = [const MallHomeView(), const MallCartView(), const MallMineView()];
  final tabIndex = TabPage.PageHome.obs;

  @override
  void onInit() {
    super.onInit();

    tabIndex.listen((p0) {
      if (p0 == TabPage.PageMine) {
      } else if (p0 == TabPage.PageMessage) {
      } else if (p0 == TabPage.PageCart) {
      } else if (p0 == TabPage.PageHome) {}
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {}

  void navigateTo(TabPage id) {
    tabIndex.value = id;
  }
}
