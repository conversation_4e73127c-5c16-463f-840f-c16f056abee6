import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mall/mall_tab/mall_tab_controller.dart';
import 'package:inspector/app/tools/icons.dart';

class MallTabView extends GetView<MallTabController> {
  const MallTabView({super.key});

  @override
  Widget build(BuildContext context) {
    var tabPages = controller.tabPages;
    return Scaffold(
      body: Obx(() {
        var index = tabPages.indexOf(controller.tabIndex.value);
        return controller.tabViews[index];
      }),
      resizeToAvoidBottomInset: false,
      bottomNavigationBar: Obx(() {
        var index = tabPages.indexOf(controller.tabIndex.value);

        List<BottomNavigationBarItem> items = [];
        for (var i = 0; i < tabPages.length; i++) {
          items.add(_getBottomTabItems(tabPages[i]));
        }
        return BottomNavigationBar(
          // backgroundColor: Colors.white,
          selectedItemColor: MColor.skin,
          unselectedItemColor: context.isDarkMode ? DarkColor.xFF9A9B9C : MColor.xFF9A9B9C,
          selectedLabelStyle: MFont.medium10,
          unselectedLabelStyle: MFont.medium10,
          type: BottomNavigationBarType.fixed,
          currentIndex: index,
          elevation: 8,
          onTap: (index) {
            controller.tabIndex.value = tabPages[index];
          },
          items: items,
        );
      }),
    );
  }

  BottomNavigationBarItem _getBottomTabItems(TabPage tabPage) {
    return BottomNavigationBarItem(
      icon: Icon(tabPage.tabIcon, color: MColor.xFFA6A6A6, size: 24),
      activeIcon: Icon(tabPage.tabIcon, color: MColor.xFFF2591D, size: 24),
      label: tabPage.value,
    );
  }
}
