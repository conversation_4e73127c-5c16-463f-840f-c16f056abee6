import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_order/order_list_controller.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class OrderListView extends GetView<OrderListController> {
  const OrderListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的订单'),
        centerTitle: true,
        elevation: 0,
      ),
      backgroundColor: MColor.xFFF2F2F2,
      body: SafeArea(
          child: Column(children: [
        const SizedBox(
          height: 5,
        ),
        Expanded(
          child: Obx(() {
            return ListView.separated(
              itemCount: controller.orderList.isEmpty ? 1 : controller.orderList.length,
              separatorBuilder: (context, index) {
                return const SizedBox(
                  height: 5,
                );
              },
              itemBuilder: (ctx, index) {
                if (controller.orderList.isEmpty) {
                  return SizedBox(
                    width: double.infinity,
                    height: Get.height / 2,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.hourglass_empty,
                            size: 40,
                            color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                          ),
                          const SizedBox(height: 10),
                          Text(S.of(Get.context!).no_data, style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
                        ],
                      ),
                    ),
                  );
                }
                return _orderItem(index);
              },
            );
          }),
        ),
      ])),
    );
  }

  int? _orderStatus(MallOrderDetailEntity order) {
    return order.goodsList[0].status;
  }

  Widget _orderActions(MallOrderDetailEntity orderInfo) {
    int? orderStatus = orderInfo.goodsList[0].o_status;
    return Row(
      children: [
        const Spacer(),
        // SizedBox(
        //   height: 25,
        //   child: TextButton(
        //     style: ButtonStyle(
        //       textStyle: MaterialStateProperty.all(MFont.regular13),
        //       foregroundColor: MaterialStateProperty.all(MColor.xFF333333),
        //       visualDensity: VisualDensity.compact,
        //       side: MaterialStateProperty.all(BorderSide(color: MColor.xFFA6A6A6, width: 1)),
        //       shape: MaterialStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(13))),
        //     ),
        //     onPressed: () {},
        //     child: Container(
        //       child: Text(
        //         '查看物流',
        //         style: MFont.regular12.apply(color: MColor.xFF000000),
        //         textAlign: TextAlign.right,
        //         strutStyle: StrutStyle.fromTextStyle(
        //           MFont.regular12.apply(color: MColor.xFF000000),
        //           forceStrutHeight: true,
        //           leading: 0.5,
        //         ),
        //       ),
        //     ),
        //   ),
        // ),
        if (orderStatus == 0 || orderStatus == 1 || orderStatus == 2) ...{
          const SizedBox(
            width: 10,
          ),
          SizedBox(
            height: 25,
            child: TextButton(
              style: ButtonStyle(
                  textStyle: MaterialStateProperty.all(MFont.regular13),
                  foregroundColor: MaterialStateProperty.all(MColor.xFF333333),
                  shape: MaterialStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(13))),
                  visualDensity: VisualDensity.compact,
                  side: MaterialStateProperty.all(BorderSide(color: MColor.xFFF2591D, width: 1))),
              onPressed: () {
                if (orderStatus == 0) {
                  Get.toNamed(MallRoutes.MALL_ORDER_DETAIL, parameters: {'sn': orderInfo.payInfo!.sn})?.then((value) {
                    controller.freshData();
                  });
                } else {
                  showCustomDialog('是否确定取消订单', onConfirm: () {
                    controller.confirmOrderReceived(orderInfo);
                  }, cancel: true);
                }
              },
              child: Text(
                orderStatus == 0 ? '继续支付' : '确认收货',
                style: MFont.regular12.apply(color: MColor.xFFF2591D),
                textAlign: TextAlign.right,
                strutStyle: StrutStyle.fromTextStyle(
                  MFont.regular12.apply(color: MColor.xFFF2591D),
                  forceStrutHeight: true,
                  leading: 0.5,
                ),
              ),
            ),
          )
        }
      ],
    );
  }

  Widget _orderItem(int index) {
    MallOrderDetailEntity orderInfo = controller.orderList[index];

    return Builder(builder: (context) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          int? status = orderInfo.goodsList[0].o_status;
          if (status == 0) {
            Get.toNamed(MallRoutes.MALL_ORDER_DETAIL, parameters: {'sn': orderInfo.payInfo!.sn})?.then((value) {
              controller.freshData();
            });
          } else {
            Get.toNamed(MallRoutes.MALL_ORDER_DETAIL, parameters: {'order_num': orderInfo.goodsList[0].order_num})?.then((value) {
              controller.freshData();
            });
          }
        },
        child: Card(
          color: MColor.xFFFFFFFF,
          margin: const EdgeInsets.only(
            left: 14,
            right: 14,
          ),
          elevation: 0,
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
          child: Padding(
            padding: const EdgeInsets.only(left: 14, right: 14, bottom: 10, top: 10),
            child: Column(
              children: [
                for (var goods in orderInfo.goodsList)
                  Builder(builder: (context) {
                    return Column(
                      children: [
                        Row(
                          children: [
                            Expanded(child: Text(goods.shop_name, style: MFont.regular14.apply(color: MColor.xFF383838))),
                            Text(
                              goods.getStatusDesc(),
                              style: MFont.medium16.apply(color: MColor.xFFF2591D),
                            )
                          ],
                        ),
                        const SizedBox(
                          height: 15,
                        ),
                        _goodsView(orderInfo.payInfo!, goods),
                        const SizedBox(
                          height: 15,
                        ),
                        _orderActions(orderInfo)
                      ],
                    );
                  }),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _goodsView(OrderPayEntity? payInfo, OrderGoodsSnapshotEntity goods) {
    return SizedBox(
      height: 90,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Builder(builder: (context) {
            var url = goods.pic.isNotEmpty ? goods.pic[0] : '';
            return ClipRRect(
              borderRadius: BorderRadius.all(Radius.circular(5)),
              child: CachedNetworkImage(
                imageUrl: url,
                width: 90,
                height: 90,
                fit: BoxFit.cover,
                placeholder: (ctx, e) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: MColor.xFFEEEEEE,
                    ),
                  );
                },
                errorWidget: (ctx, e, x) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: MColor.xFFEEEEEE,
                    ),
                  );
                },
              ),
            );
          }),
          const SizedBox(
            width: 14,
          ),
          Expanded(
              child: SizedBox(
            height: 90,
            child: Column(
              children: [
                const SizedBox(
                  height: 5,
                ),
                Row(
                  children: [
                    Expanded(
                        child: Text(
                      goods.title,
                      style: MFont.medium14.apply(color: MColor.xFF000000, heightFactor: 1.2),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ))
                  ],
                ),
                const SizedBox(
                  height: 12,
                ),
                Row(
                  children: [
                    Expanded(
                      child: Builder(builder: (context) {
                        List<String> desc = [];
                        for (var skuAttr in goods.sku) {
                          desc.add('${skuAttr.name}: ${skuAttr.val}');
                        }
                        return Text(
                          desc.join(' '),
                          style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                        );
                      }),
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    Text(
                      'x${goods.count}',
                      style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                      textAlign: TextAlign.end,
                    )
                  ],
                ),
                const Spacer(),
                Builder(builder: (context) {
                  return Row(
                    children: [
                      Expanded(
                        child: RichText(
                          textAlign: TextAlign.start,
                          text: TextSpan(
                            text: goods.account.symbol,
                            style: MFont.regular14.apply(color: MColor.xFF000000),
                            children: [
                              TextSpan(text: goods.total_money, style: MFont.regular14.apply(color: MColor.xFF000000)),
                            ],
                          ),
                        ),
                      ),
                      if (goods.o_status != 0)
                        RichText(
                          textAlign: TextAlign.start,
                          text: TextSpan(
                            text: '实付款',
                            style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                            children: [
                              WidgetSpan(
                                  child: const SizedBox(
                                width: 4,
                              )),
                              TextSpan(text: '${goods.account.symbol}${goods.pay_money}', style: MFont.medium12.apply(color: MColor.xFF000000)),
                            ],
                          ),
                        ),
                    ],
                  );
                }),
                const SizedBox(
                  height: 1,
                ),
              ],
            ),
          )),
          // const SizedBox(
          //   width: 22,
          // ),
          // Row(
          //   children: [Text('x${goods.count}', style: MFont.regular10.apply(color: MColor.xFFA6A6A6)), const Spacer(), Text('实付款 ${goods.total_money}')],
          // )
        ],
      ),
    );
  }
}
