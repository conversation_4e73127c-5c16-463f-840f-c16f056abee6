import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/mall/mall_order/mall_order_confirm_controller.dart';
import 'package:inspector/app/tools/icons.dart';

import '../../../../generated/l10n.dart';

class MallOrderConfirmView extends GetView<MallOrderConfirmController> {
  const MallOrderConfirmView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).mall_order_confirm),
        centerTitle: true,
        elevation: 0,
      ),
      backgroundColor: MColor.xFFF2F2F2,
      body: Safe<PERSON>rea(
          child: Column(children: [
        const SizedBox(
          height: 5,
        ),
        Expanded(
          child: Obx(() {
            List<Widget> items = [];
            items.add(_addressItem);
            items.add(_goodsListView);
            items.add(_priceView);

            return ListView.separated(
              itemCount: items.length,
              separatorBuilder: (context, index) {
                return const SizedBox(
                  height: 5,
                );
              },
              itemBuilder: (ctx, index) {
                return items[index];
              },
            );
          }),
        ),
        Obx(() {
          var price = controller.totalPrice;
          return _textButton;
        }),
      ])),
    );
  }

  Widget get _textButton {
    return Container(
      height: 58,
      decoration: BoxDecoration(color: MColor.xFFFFFFFF),
      child: Row(
        children: [
          const Spacer(),
          RichText(
            textAlign: TextAlign.start,
            text: TextSpan(
              children: [
                TextSpan(text: '共${controller.totalQuantity}件', style: MFont.regular12.apply(color: MColor.xFFF2591D)),
                const WidgetSpan(
                  child: const SizedBox(
                    width: 3,
                  ),
                ),
                TextSpan(text: '¥', style: MFont.medium14.apply(color: MColor.xFFF2591D)),
                TextSpan(text: controller.totalPrice.toStringAsFixed(2), style: MFont.medium20.apply(color: MColor.xFFF2591D)),
              ],
            ),
          ),
          const SizedBox(
            width: 14,
          ),
          Container(
            height: 38,
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
            ),
            decoration: BoxDecoration(
              color: MColor.xFFE64724,
              borderRadius: BorderRadius.circular(35),
            ),
            child: GestureDetector(
              onTap: () {
                controller.submitOrder();
              },
              child: Text(
                S.of(Get.context!).mall_submit_order,
                textAlign: TextAlign.center,
                style: MFont.medium16.apply(color: MColor.xFFFFFFFF),
                strutStyle: const StrutStyle(
                  forceStrutHeight: true,
                  leading: 0.5,
                ),
              ),
            ),
          ),
          const SizedBox(
            width: 14,
          )
        ],
      ),
    );
  }

  Widget get _goodsListView {
    return Column(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(
          controller.goodsList.length,
          (index) => Column(
            children: [
              Card(
                  color: MColor.xFFFFFFFF,
                  margin: EdgeInsets.only(
                    left: 14,
                    right: 14,
                  ),
                  elevation: 0,
                  shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 14, vertical: 14),
                    child: Column(children: [
                      _goodsView(controller.goodsList[index], controller.skuList[index], controller.quantities[index], controller.skuSpecList[index]),
                      const SizedBox(
                        height: 14,
                      ),
                      _remarkView(index),
                    ]),
                  )),
              const SizedBox(
                height: 8,
              ),
            ],
          ),
        ));
  }

  Widget _goodsView(GoodsDetailResp goods, SkuEntity sku, int quantity, List<SpecAttrEntity> skuAttrs) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: Text(
              goods.shopInfo!.name,
              style: MFont.regular14.apply(color: MColor.xFF383838),
            ))
          ],
        ),
        const SizedBox(
          height: 15,
        ),
        SizedBox(
          height: 90,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Builder(builder: (context) {
                var url = sku.pic.isNotEmpty ? sku.pic[0] : goods.detail!.pic[0];
                return ClipRRect(
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                  child: CachedNetworkImage(
                    imageUrl: url,
                    width: 90,
                    height: 90,
                    fit: BoxFit.cover,
                    placeholder: (ctx, e) {
                      return Container(
                        decoration: const BoxDecoration(
                          color: MColor.xFFEEEEEE,
                        ),
                      );
                    },
                    errorWidget: (ctx, e, x) {
                      return Container(
                        decoration: const BoxDecoration(
                          color: MColor.xFFEEEEEE,
                        ),
                      );
                    },
                  ),
                );
              }),
              const SizedBox(
                width: 14,
              ),
              Expanded(
                  child: SizedBox(
                height: 90,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(
                      height: 5,
                    ),
                    Row(
                      children: [
                        Expanded(
                            child: Text(
                          goods.detail!.title,
                          style: MFont.regular14.apply(color: MColor.xFF000000, heightFactor: 1.2),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ))
                      ],
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        Expanded(
                          child: Builder(builder: (context) {
                            List<String> desc = [];
                            for (var skuAttr in skuAttrs) {
                              desc.add('${skuAttr.name}: ${skuAttr.specs[0].name}');
                            }
                            return Text(
                              desc.join(' '),
                              style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                            );
                          }),
                        )
                      ],
                    ),
                    const Spacer(),
                    Builder(builder: (context) {
                      return Row(
                        children: [
                          Expanded(
                            child: RichText(
                              textAlign: TextAlign.start,
                              text: TextSpan(
                                text: sku.accountType.symbol,
                                style: MFont.medium14.apply(color: MColor.xFFF2591D),
                                children: [
                                  TextSpan(text: sku.finalPrice, style: MFont.medium20.apply(color: MColor.xFFF2591D)),
                                ],
                              ),
                            ),
                          ),
                        ],
                      );
                    }),
                    const SizedBox(
                      height: 1,
                    ),
                  ],
                ),
              )),
              const SizedBox(
                width: 22,
              ),
              Text('x$quantity', style: MFont.regular12.apply(color: MColor.xFFA6A6A6))
            ],
          ),
        )
      ],
    );
  }

  Widget _remarkView(int index) {
    return GestureDetector(
      onTap: () {
        controller.remarkController.text = controller.remarks[index];
        Get.bottomSheet(
          SafeArea(
            // and this
            child: _RemarkView(index),
          ),
          isScrollControlled: true,
          ignoreSafeArea: false, // add this
        ).then((value) {
          if (value is Map && value.containsKey('text')) {
            controller.remarks[index] = value['text'];
            controller.remarks.refresh();
          }
        });
      },
      child: Row(
        children: [
          Expanded(flex: 1, child: Text(S.of(Get.context!).mall_order_remark, style: MFont.regular14.apply(color: MColor.xFF383838))),
          Builder(builder: (context) {
            if (controller.remarks[index].isEmpty) {
              return Text(
                S.of(Get.context!).mall_order_remark_input,
                style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                textAlign: TextAlign.end,
              );
            } else {
              return Expanded(
                flex: 2,
                child: Text(
                  controller.remarks[index],
                  style: MFont.regular12.apply(color: MColor.xFF383838),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                ),
              );
            }
          }),
          const SizedBox(
            width: 3,
          ),
          Icon(Icons.arrow_forward_ios, size: 12, color: MColor.xFFA6A6A6)
        ],
      ),
    );
  }

  Widget get _priceView {
    return Card(
      color: MColor.xFFFFFFFF,
      margin: EdgeInsets.only(
        left: 14,
        right: 14,
      ),
      elevation: 0,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14, vertical: 14),
        child: Column(children: [
          Row(
            children: [
              Text(S.of(Get.context!).mall_goods_price, style: MFont.regular14.apply(color: MColor.xFF383838)),
              const Spacer(),
              Text('${UserAccountType.CNY.symbol}${controller.totalGoodsPrice.toStringAsFixed(2)}', style: MFont.regular14.apply(color: MColor.xFF000000))
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            children: [
              Text(S.of(Get.context!).mall_express_price, style: MFont.regular14.apply(color: MColor.xFF383838)),
              const Spacer(),
              Text('${UserAccountType.CNY.symbol}${controller.totalExpressFee.value}', style: MFont.regular14.apply(color: MColor.xFF000000))
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            children: [
              const Spacer(),
              RichText(
                textAlign: TextAlign.start,
                text: TextSpan(
                  text: S.of(Get.context!).mall_price_total,
                  style: MFont.regular12.apply(color: MColor.xFF383838),
                  children: [
                    TextSpan(
                        text: '${UserAccountType.CNY.symbol}${controller.totalPrice.toStringAsFixed(2)}',
                        style: MFont.regular14.apply(color: MColor.xFFF2591D)),
                  ],
                ),
              ),
            ],
          ),
        ]),
      ),
    );
  }

  Widget get _addressItem {
    if (controller.addressInfo.value == null) {
      return SizedBox();
    }
    MallAddressEntity address = controller.addressInfo.value!;

    var name = address.name ?? '';
    var phone = address.phone ?? '';
    var province = address.province ?? '';
    var city = address.city ?? '';
    var area = address.area ?? '';
    var detail = address.address ?? '';
    var text = '$province-$city-$area-$detail';
    return Builder(builder: (context) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {},
        child: Card(
          color: MColor.xFFFFFFFF,
          margin: EdgeInsets.only(
            left: 14,
            right: 14,
          ),
          elevation: 0,
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
          child: Padding(
            padding: const EdgeInsets.only(left: 17, right: 17, bottom: 10, top: 10),
            child: Row(
              children: [
                Icon(AppIcons.location, size: 24, color: MColor.xFFE64724),
                const SizedBox(
                  width: 13,
                ),
                Expanded(
                    child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          '${address.province}${address.city}${address.area}',
                          textAlign: TextAlign.start,
                          style: MFont.regular12.apply(color: MColor.xFF616161),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 2,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          '${address.address}',
                          textAlign: TextAlign.start,
                          style: MFont.medium14.apply(color: MColor.xFF000000),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 2,
                  ),
                  Row(
                    children: [
                      Text(
                        '${address.name}',
                        textAlign: TextAlign.start,
                        style: MFont.regular14.apply(color: MColor.xFF616161),
                      ),
                      const SizedBox(
                        width: 19,
                      ),
                      Text(
                        '${address.phone}',
                        textAlign: TextAlign.start,
                        style: MFont.regular14.apply(color: MColor.xFF616161),
                      ),
                    ],
                  )
                ])),
                const SizedBox(
                  width: 13,
                ),
                // const Icon(
                //   Icons.arrow_forward_ios,
                //   size: 16,
                // )
              ],
            ),
          ),
        ),
      );
    });
  }
}

class _RemarkView extends GetView<MallOrderConfirmController> {
  final int index;
  const _RemarkView(this.index);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.circular(14)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              SizedBox(
                height: 46,
                child: Center(
                  child: Text(S.of(Get.context!).mall_order_remark),
                ),
              ),
              Positioned(
                child: GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: const SizedBox(
                    height: 42,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.close,
                          size: 24,
                        ),
                      ],
                    ),
                  ),
                ),
                right: 12,
              )
            ],
          ),
          const SizedBox(
            height: 6,
          ),
          TextField(
            controller: controller.remarkController,
            // focusNode: controller.inputFocusNode,
            decoration: InputDecoration(
              fillColor: MColor.xFFEEEEEE,
              filled: true,
              contentPadding: const EdgeInsets.only(left: 15, right: 15, top: 12, bottom: 12),
              hintText: S.of(Get.context!).note,
              hintStyle: MFont.regular13.apply(color: MColor.xFF999999),
              labelStyle: MFont.regular13.apply(color: MColor.xFF333333),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: const BorderSide(color: MColor.xFFEEEEEE, width: 0.0),
              ),
              constraints: const BoxConstraints(minHeight: 50, maxHeight: 100),
            ),

            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.search,
            onSubmitted: (value) {},
            onChanged: (val) {},
            minLines: 2,
            maxLines: 6,
            maxLength: 200,
            autofocus: true,
          ),
          Row(
            children: [
              Expanded(
                child: TextButton(
                  style: ButtonStyle(
                    padding: MaterialStateProperty.all(const EdgeInsets.symmetric(vertical: 8)),
                    textStyle: MaterialStateProperty.all(MFont.regular13),
                    backgroundColor: MaterialStateProperty.all(MColor.skin),
                  ),
                  onPressed: () {
                    Get.back(result: {'text': controller.remarkController.text});
                  },
                  child: Text(
                    S.of(Get.context!).public_ok,
                    style: const TextStyle(fontSize: 13, color: MColor.xFFFFFFFF, height: 1),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
