import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_order/order_detail_controller.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class OrderDetailView extends GetView<OrderDetailController> {
  @override
  final String? tag;
  const OrderDetailView({super.key, this.tag});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).order_detail_title),
        centerTitle: true,
        elevation: 0,
      ),
      backgroundColor: MColor.xFFF2F2F2,
      body: SafeArea(
          child: Column(children: [
        const SizedBox(
          height: 5,
        ),
        Expanded(
          child: Obx(() {
            if (controller.orderInfo.value == null) {
              return const SizedBox();
            }
            List<Widget> items = [];
            items.add(_orderStateView);
            items.add(_addressItem);
            items.add(_trackingView);
            items.add(_goodsListView);
            items.add(_priceView);

            return ListView.separated(
              itemCount: items.length,
              separatorBuilder: (context, index) {
                return const SizedBox(
                  height: 5,
                );
              },
              itemBuilder: (ctx, index) {
                return items[index];
              },
            );
          }),
        ),
        Obx(() {
          if (controller.orderInfo.value == null) {
            return const SizedBox();
          }
          return _textButton;
        }),
      ])),
    );
  }

  Widget _remarkView(String remarkText) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(flex: 1, child: Text(S.of(Get.context!).mall_order_remark, style: MFont.regular14.apply(color: MColor.xFF383838))),
        Expanded(
          flex: 2,
          child: Text(
            remarkText,
            style: MFont.regular12.apply(color: MColor.xFF383838),
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.end,
            maxLines: 3,
          ),
        ),
        const SizedBox(
          width: 3,
        ),
      ],
    );
  }

  Widget get _textButton {
    int? orderStatus = controller.orderInfo.value!.goodsList[0].o_status;
    if (orderStatus == 0 || orderStatus == 1 || orderStatus == 2) {
      return Container(
        height: 58,
        decoration: const BoxDecoration(color: MColor.xFFFFFFFF),
        child: Row(
          children: [
            const Spacer(),
            if (orderStatus == 0) ...{
              Container(
                height: 38,
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                ),
                margin: EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  border: Border.all(width: 1, color: MColor.xFFA6A6A6),
                  borderRadius: BorderRadius.circular(35),
                ),
                child: GestureDetector(
                  onTap: () {
                    showCustomDialog(S.of(Get.context!).order_sure_cancel, onConfirm: () {
                      controller.cancelOrder();
                    }, cancel: true);
                  },
                  child: Text(
                    S.of(Get.context!).order_cancel,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500, color: MColor.xFF000000, height: 1.2),
                  ),
                ),
              ),
            },
            if (orderStatus == 0 || orderStatus == 1 || orderStatus == 2) ...{
              Container(
                height: 38,
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                ),
                decoration: BoxDecoration(
                  color: MColor.xFFE64724,
                  borderRadius: BorderRadius.circular(35),
                ),
                child: GestureDetector(
                  onTap: () {
                    if (orderStatus == 0) {
                      Get.offAndToNamed(MallRoutes.MALL_PAY_ORDER, parameters: {'sn': controller.orderSn!});
                    } else {
                      showCustomDialog('是否确定取消订单', onConfirm: () {
                        controller.confirmOrderReceived(controller.orderInfo.value!);
                      }, cancel: true);
                    }
                  },
                  child: Text(
                    orderStatus == 0 ? '继续支付' : '确认收货',
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500, color: Colors.white, height: 1.2),
                  ),
                ),
              ),
            },
            const SizedBox(
              width: 14,
            )
          ],
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  Widget get _goodsListView {
    return Column(
        children: List.generate(
      controller.orderInfo.value!.goodsList.length,
      (index) {
        final goods = controller.orderInfo.value!.goodsList[index];
        return Column(
          children: [
            Card(
              color: MColor.xFFFFFFFF,
              // margin: EdgeInsets.only(
              //   left: 14,
              //   right: 14,
              // ),
              elevation: 0,
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
                child: Builder(builder: (context) {
                  return Column(children: [
                    Row(
                      children: [
                        Expanded(
                            child: Text(
                          goods.shop_name,
                          style: MFont.regular14.apply(color: MColor.xFF000000),
                        )),
                      ],
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    _goodsView(goods),
                    const SizedBox(
                      height: 12,
                    ),
                    if (goods.remark.isNotEmpty) _remarkView(goods.remark)
                  ]);
                }),
              ),
            ),
          ],
        );
      },
    ));
  }

  // Widget _shopView(OrderMallEntity shopEntity) {
  //   return Column(
  //     children: [
  //       Row(
  //         children: [
  //           Expanded(child: Text(shopEntity.name)),
  //         ],
  //       ),
  //       Column(
  //         children: [
  //           const SizedBox(
  //             height: 15,
  //           ),
  //           for (OrderGoodsEntity goods in shopEntity.goods) ...{_goodsView(goods)}
  //         ],
  //       ),
  //     ],
  //   );
  // }

  Widget _goodsView(OrderGoodsSnapshotEntity goods) {
    return SizedBox(
      height: 90,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Builder(builder: (context) {
            var url = goods.pic.isNotEmpty ? goods.pic[0] : '';
            return ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(5)),
              child: CachedNetworkImage(
                imageUrl: url,
                width: 90,
                height: 90,
                fit: BoxFit.cover,
                placeholder: (ctx, e) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: MColor.xFFEEEEEE,
                    ),
                  );
                },
                errorWidget: (ctx, e, x) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: MColor.xFFEEEEEE,
                    ),
                  );
                },
              ),
            );
          }),
          const SizedBox(
            width: 14,
          ),
          Expanded(
              child: SizedBox(
            height: 90,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(
                  height: 5,
                ),
                Row(
                  children: [
                    Expanded(
                        child: Text(
                      goods.title,
                      style: MFont.medium14.apply(color: MColor.xFF000000, heightFactor: 1.2),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ))
                  ],
                ),
                const Spacer(),
                Row(
                  children: [
                    Expanded(
                      child: Builder(builder: (context) {
                        List<String> desc = [];
                        for (var skuAttr in goods.sku) {
                          desc.add('${skuAttr.name}: ${skuAttr.val}');
                        }
                        return Text(
                          desc.join(' '),
                          style: MFont.regular12.apply(color: MColor.xFFA6A6A6),
                        );
                      }),
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Expanded(
                        child: Text('${S.of(Get.context!).mall_goods_count}：${goods.count}',
                            style: MFont.regular12.apply(color: MColor.xFFA6A6A6), textAlign: TextAlign.end))
                  ],
                ),
                const Spacer(),
                Builder(builder: (context) {
                  return Row(
                    children: [
                      Expanded(
                        child: RichText(
                          textAlign: TextAlign.start,
                          text: TextSpan(
                            text: goods.account.symbol,
                            style: MFont.medium14.apply(color: MColor.xFFF2591D),
                            children: [
                              TextSpan(text: goods.total_money, style: MFont.medium20.apply(color: MColor.xFFF2591D)),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      Text(
                        '实付款：${goods.pay_money}',
                        style: MFont.regular12.apply(color: MColor.xFF000000),
                        textAlign: TextAlign.end,
                      )
                    ],
                  );
                }),
                const SizedBox(
                  height: 1,
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget get _priceView {
    return Card(
      color: MColor.xFFFFFFFF,
      // margin: EdgeInsets.only(
      //   left: 14,
      //   right: 14,
      // ),
      elevation: 0,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
        child: Column(children: [
          Row(
            children: [
              Text('实付款', style: MFont.regular12.apply(color: MColor.xFF383838)),
              const Spacer(),
              Text('${controller.orderInfo.value!.payInfo!.account.symbol}${controller.orderInfo.value!.payInfo?.money}',
                  style: MFont.regular12.apply(color: MColor.xFF000000))
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            children: [
              Text('商品总价', style: MFont.regular12.apply(color: MColor.xFF383838)),
              const Spacer(),
              Text('${controller.orderInfo.value!.payInfo!.account.symbol}${controller.orderInfo.value!.payInfo?.total_goods_price}',
                  style: MFont.regular12.apply(color: MColor.xFF858585))
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            children: [
              Text('运费', style: MFont.regular12.apply(color: MColor.xFF383838)),
              const Spacer(),
              Text('${controller.orderInfo.value!.payInfo!.account.symbol}${controller.orderInfo.value!.payInfo?.express_fee}',
                  style: MFont.regular12.apply(color: MColor.xFF858585))
            ],
          ),
          if (controller.orderInfo.value?.payInfo?.pay_type != null && controller.orderInfo.value?.payInfo?.pay_type != 0) ...{
            const SizedBox(
              height: 14,
            ),
            Row(
              children: [
                Text('支付方式', style: MFont.regular12.apply(color: MColor.xFF383838)),
                const Spacer(),
                Text(controller.orderInfo.value!.payInfo?.getPayType() ?? '', style: MFont.regular12.apply(color: MColor.xFF000000))
              ],
            ),
          },
          const SizedBox(
            height: 14,
          ),
          Row(
            children: [
              Text('订单编号', style: MFont.regular12.apply(color: MColor.xFF383838)),
              const Spacer(),
              Text('${controller.orderInfo.value!.payInfo!.sn}', style: MFont.regular12.apply(color: MColor.xFF000000))
            ],
          ),
          const SizedBox(
            height: 14,
          ),
          Row(
            children: [
              Text('下单时间', style: MFont.regular12.apply(color: MColor.xFF383838)),
              const Spacer(),
              Text('${controller.orderInfo.value!.payInfo!.created_at}', style: MFont.regular12.apply(color: MColor.xFF000000))
            ],
          ),
        ]),
      ),
    );
  }

  Widget get _orderStateView {
    String statusStr = controller.orderInfo.value?.goodsList[0].getStatusDesc() ?? '';
    return Padding(
      padding: const EdgeInsets.only(top: 14.0, bottom: 14, right: 14, left: 14),
      child: Text(
        statusStr,
        style: MFont.regular18.apply(color: MColor.xFF000000),
      ),
    );
  }

  Widget get _trackingView {
    if ((controller.orderInfo.value?.goodsList[0].o_status ?? -1) > 1) {
      String url = controller.orderInfo.value?.goodsList[0].express_url ?? '';
      String name = controller.orderInfo.value?.goodsList[0].express_name ?? '';
      String code = controller.orderInfo.value?.goodsList[0].express_code ?? '';
      return GestureDetector(
        onTap: () {
          Get.toNamed(
            Routes.WEB,
            parameters: {'url': url, 'title': '物流信息'},
          );
        },
        child: Card(
          color: MColor.xFFFFFFFF,
          // margin: EdgeInsets.only(
          //   left: 14,
          //   right: 14,
          // ),
          elevation: 0,
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
          child: Padding(
            padding: const EdgeInsets.only(left: 11, right: 11, bottom: 10, top: 10),
            child: Row(
              children: [
                AppIcons.icon(0xe60e, size: 24, color: MColor.xFFE64724),
                const SizedBox(
                  width: 13,
                ),
                Expanded(
                    child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          '物流信息',
                          textAlign: TextAlign.start,
                          style: MFont.regular14.apply(color: MColor.xFF616161),
                        ),
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      Expanded(
                          child: Text(
                        '$name $code',
                        textAlign: TextAlign.end,
                        style: MFont.regular14.apply(color: MColor.xFF616161),
                      ))
                    ],
                  ),
                ])),
                const SizedBox(
                  width: 13,
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  Widget get _addressItem {
    var name = '${controller.orderInfo.value!.goodsList[0].delivery_name} ${controller.orderInfo.value!.goodsList[0].delivery_phone}';
    var detail = '${controller.orderInfo.value!.goodsList[0].delivery_address}';
    return Builder(builder: (context) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {},
        child: Card(
          color: MColor.xFFFFFFFF,
          // margin: EdgeInsets.only(
          //   left: 14,
          //   right: 14,
          // ),
          elevation: 0,
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(14))),
          child: Padding(
            padding: const EdgeInsets.only(left: 11, right: 11, bottom: 10, top: 10),
            child: Row(
              children: [
                Icon(AppIcons.location, size: 24, color: MColor.xFFE64724),
                const SizedBox(
                  width: 13,
                ),
                Expanded(
                    child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          name,
                          textAlign: TextAlign.start,
                          style: MFont.medium16.apply(color: MColor.xFF000000),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 2,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          detail,
                          textAlign: TextAlign.start,
                          style: MFont.regular14.apply(color: MColor.xFF858585),
                        ),
                      ),
                    ],
                  ),
                ])),
                const SizedBox(
                  width: 13,
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
