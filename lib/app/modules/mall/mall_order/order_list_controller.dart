import 'dart:async';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_service/mall_service.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class OrderListController extends GetxController {
  final MallService _service = MallService();

  int page = 0;
  int total = 0;
  final orderList = <MallOrderDetailEntity>[].obs;
  RefreshController refreshController = RefreshController();

  @override
  void onInit() {
    super.onInit();
    fetchList();
  }

  void freshData() {
    page = 0;
    fetchList();
  }

  void loadMore() {
    if (orderList.length >= total) {
      refreshController.loadComplete();
      refreshController.refreshCompleted();
      return;
    } else {
      page++;
      fetchList();
    }
  }

  Future<void> fetchList() async {
    unawaited(EasyLoading.show());
    var result = await _service.getOrderList();
    if (result.isSuccess) {
      orderList.value = result.data?.list ?? [];
      total = result.data?.total ?? 0;
    }

    refreshController.loadComplete();
    refreshController.refreshCompleted();
    unawaited(EasyLoading.dismiss());
  }

  Future<void> confirmOrderReceived(MallOrderDetailEntity orderInfo) async {
    unawaited(EasyLoading.show());
    var result = await _service.confirmOrderReceived(orderInfo.goodsList[0].order_num);
    if (result.isSuccess) {
      // var newStatus = result.data['o_status'];
      // if (newStatus != null) {
      //   orderInfo.goodsList[0].o_status = newStatus;
      //   orderList.refresh();
      // }
      freshData();
    }
    unawaited(EasyLoading.dismiss());
  }
}
