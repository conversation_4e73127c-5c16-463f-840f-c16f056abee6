import 'package:get/get.dart';
import 'package:inspector/app/modules/mall/mall_order/order_detail_controller.dart';

class OrderDetailBinding extends Bindings {
  @override
  void dependencies() {
    String? orderId = Get.parameters['order_num'];
    String? sn = Get.parameters['sn'];
    String tag = '';
    if (orderId?.isNotEmpty == true) {
      tag = 'order_num=$orderId';
    } else if (sn?.isNotEmpty == true) {
      tag = 'sn=$sn';
    }
    Get.lazyPut<OrderDetailController>(() => OrderDetailController(), tag: tag);
  }
}
