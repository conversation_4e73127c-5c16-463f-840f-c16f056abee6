import 'dart:async';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_service/mall_service.dart';

class OrderDetailController extends GetxController {
  final _service = MallService();
  // final List<OrderGoodsSnapshotEntity> orderShops = [];
  // final List<SkuEntity> skuList = [];
  // final List<int> quantities = [];

  final List<List<SpecAttrEntity>> skuSpecList = [];

  final orderInfo = Rxn<MallOrderDetailEntity>();

  String? orderNum;
  String? orderSn;

  @override
  void onInit() {
    super.onInit();

    if (Get.parameters.containsKey('order_num')) {
      orderNum = Get.parameters['order_num']!;
    } else if (Get.parameters.containsKey('sn')) {
      orderSn = Get.parameters['sn']!;
    } else {
      Get.back();
      return;
    }
    getOrderDetail();
  }

  Future<void> getOrderDetail() async {
    var result = await _service.getOrderDetail(orderNum: orderNum, orderSn: orderSn);
    if (result.isSuccess) {
      orderInfo.value = result.data;
      // if (result.data != null) {
      //   orderShops.clear();
      //   orderShops.addAll(result.data!.shops);
      // }
    } else {
      Get.back();
    }
  }

  Future<void> cancelOrder() async {
    unawaited(EasyLoading.show());
    var result = await _service.cancelOrder(orderSn!);
    if (result.isSuccess) {
      await getOrderDetail();
    }
    unawaited(EasyLoading.dismiss());
  }

  Future<void> confirmOrderReceived(MallOrderDetailEntity orderInfo) async {
    unawaited(EasyLoading.show());
    var result = await _service.confirmOrderReceived(orderInfo.goodsList[0].order_num);
    if (result.isSuccess) {
      await getOrderDetail();
    }
    unawaited(EasyLoading.dismiss());
  }
}
