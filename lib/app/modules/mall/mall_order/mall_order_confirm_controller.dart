import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';
import 'package:inspector/app/modules/mall/mall_service/mall_service.dart';
import 'package:inspector/app/modules/mall/order_shop_item.dart';
import 'package:inspector/app/tools/tools.dart';

class MallOrderConfirmController extends GetxController {
  final _service = MallService();
  final List<GoodsDetailResp> goodsList = [];
  final TextEditingController remarkController = TextEditingController();
  final List<SkuEntity> skuList = [];
  final List<int> quantities = [];
  final remarks = <String>[].obs;
  final Map<int, double> expressFeeMap = {};
  final addressInfo = Rxn<MallAddressEntity>();

  // final List<OrderShopItem> orderShops = [];

  final List<List<SpecAttrEntity>> skuSpecList = [];

  final totalExpressFee = 0.0.obs;
  final totalPrice = 0.0.obs;
  final totalGoodsPrice = 0.0.obs;
  late int totalQuantity = 0;

  @override
  void onInit() {
    super.onInit();
    // orderShops.addAll(Get.arguments['goods_list']);
    goodsList.addAll(Get.arguments['goods_list']);
    skuList.addAll(Get.arguments['sku_list']);
    quantities.addAll(Get.arguments['quantities']);

    List<String> tmpRemarks = [];
    for (var _ in goodsList) {
      tmpRemarks.add('');
    }
    remarks.addAll(tmpRemarks);

    addressInfo.value = Get.arguments['address_info'];

    // for (var orderShop in orderShops) {
    //   for (var quantity in orderShop.quanitites) {
    //     totalQuantity += quantity;
    //   }
    //   for (var i = 0; i < orderShop.skuList.length; i++) {
    //     var sku = orderShop.skuList[i];
    //     var quantity = orderShop.quanitites[i];
    //     totalPrice.value = totalPrice.value + (double.tryParse(sku.finalPrice) ?? 0) * quantity;
    //   }
    //   for (var i = 0; i < orderShop.goodsList.length; i++) {
    //     var sku = orderShop.skuList[i];
    //     var specList = <SpecAttrEntity>[];
    //     skuSpecList.add(specList);
    //     for (var specKV in sku.specs) {
    //       for (var specAttr in orderShop.goodsList[i].specAttrs) {
    //         if (specKV.id == specAttr.id) {
    //           for (var spec in specAttr.specs) {
    //             if (spec.id == specKV.vId) {
    //               SpecAttrEntity specAttrEntity = SpecAttrEntity();
    //               specAttrEntity.id = specAttr.id;
    //               specAttrEntity.name = specAttr.name;

    //               SpecEntity specEntity = SpecEntity();
    //               specEntity.id = spec.id;
    //               specEntity.name = spec.name;

    //               specAttrEntity.specs = [specEntity];
    //               specList.add(specAttrEntity);
    //               break;
    //             }
    //           }
    //         }
    //       }
    //     }
    //   }
    // }
    for (var i = 0; i < goodsList.length; i++) {
      totalQuantity += quantities[i];
      totalPrice.value = totalPrice.value + (double.tryParse(skuList[i].finalPrice) ?? 0) * quantities[i];
      totalGoodsPrice.value = totalGoodsPrice.value + (double.tryParse(skuList[i].finalPrice) ?? 0) * quantities[i];

      var sku = skuList[i];
      var specList = <SpecAttrEntity>[];
      skuSpecList.add(specList);
      for (var specKV in sku.specs) {
        for (var specAttr in goodsList[i].specAttrs) {
          if (specKV.id == specAttr.id) {
            for (var spec in specAttr.specs) {
              if (spec.id == specKV.vId) {
                SpecAttrEntity specAttrEntity = SpecAttrEntity();
                specAttrEntity.id = specAttr.id;
                specAttrEntity.name = specAttr.name;

                SpecEntity specEntity = SpecEntity();
                specEntity.id = spec.id;
                specEntity.name = spec.name;

                specAttrEntity.specs = [specEntity];
                specList.add(specAttrEntity);
                break;
              }
            }
          }
        }
      }
    }

    getPrice();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    remarkController.dispose();
    super.onClose();
  }

  Future<void> getPrice() async {
    unawaited(EasyLoading.show());
    var skuIdList = <String>[];
    for (var sku in skuList) {
      skuIdList.add(sku.id);
    }
    var result = await _service.getExpressFee(skuIdList, addressInfo.value!.id);
    if (result.isSuccess) {
      totalExpressFee.value = double.tryParse(result.data['price']) ?? 0.0;
      totalPrice.value = totalPrice.value + totalExpressFee.value;

      expressFeeMap.clear();
      for (dynamic fee in result.data['detail']) {
        var shopId = fee['shop_id'];
        var shopFee = double.tryParse(fee['fee']) ?? 0.0;
        expressFeeMap[shopId] = shopFee;
      }
    }
    unawaited(EasyLoading.dismiss());
  }

  Future<void> submitOrder() async {
    unawaited(EasyLoading.show());
    List<OrderSubmitEntity> entities = [];
    for (var i = 0; i < goodsList.length; i++) {
      OrderSubmitEntity entity = OrderSubmitEntity();
      entity.count = quantities[i];
      entity.skuId = skuList[i].id;
      entity.remark = remarks[i];
      entities.add(entity);
    }
    var result = await _service.submitOrder(entities, addressInfo.value!.id);
    if (result.isSuccess) {
      var orderId = result.data['sn'];
      logger.i('orderId $orderId');
      unawaited(EasyLoading.dismiss());
      unawaited(Get.offAndToNamed(MallRoutes.MALL_PAY_ORDER, parameters: {'sn': orderId}));
    } else {
      unawaited(EasyLoading.dismiss());
    }
  }
}
