import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';

class GoodsCardHView extends StatefulWidget {
  final GoodsDetailEntity goodsDetail;
  const GoodsCardHView(this.goodsDetail, {super.key});
  @override
  State<StatefulWidget> createState() {
    return _GoodsCardHState();
  }
}

class _GoodsCardHState extends State<GoodsCardHView> {
  @override
  Widget build(BuildContext context) {
    double width = (MediaQuery.of(Get.context!).size.width - 28 - 8) / 2;
    double height = width * 90 / 178;
    GoodsDetailEntity entity = widget.goodsDetail;
    return GestureDetector(
      onTap: () {
        var parameters = <String, String>{};
        if (entity.id.isNotEmpty == true) {
          parameters['id'] = entity.id;
        } else if (entity.skuId.isNotEmpty) {
          parameters['sku_id'] = entity.skuId;
        }
        Get.toNamed(MallRoutes.GOODS_DETAIL, parameters: parameters);
      },
      child: Card(
        color: MColor.xFFFFFFFF,
        margin: EdgeInsets.zero,
        elevation: 0,
        child: Container(
          width: 130,
          height: 151,
          decoration: const BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.all(Radius.circular(10))),
          child: Column(children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
              child: CachedNetworkImage(
                imageUrl: entity.pic[1],
                width: 130,
                height: 90,
                fit: BoxFit.cover,
                placeholder: (ctx, e) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: MColor.xFFE5E5E5,
                    ),
                  );
                },
                errorWidget: (ctx, e, x) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: MColor.xFFE5E5E5,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Row(
              children: [
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: Text(
                    entity.title,
                    style: MFont.regular12.apply(color: MColor.xFF000000),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            Row(
              children: [
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      text: entity.accountType.symbol,
                      style: MFont.regular12.apply(color: MColor.xFF000000),
                      children: [
                        TextSpan(text: entity.price, style: MFont.regular14.apply(color: MColor.xFF000000)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
              ],
            ),
            const SizedBox(
              height: 4,
            ),
            Row(
              children: [
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                    child: Text(
                  '剩余库存 ${entity.stock} ${entity.unit}',
                  style: MFont.regular10.apply(color: MColor.xFF808080),
                )),
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: Text(
                    textAlign: TextAlign.end,
                    '${entity.sales}人付款',
                    style: MFont.regular10.apply(color: MColor.xFF808080),
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
              ],
            ),
            const SizedBox(
              height: 7,
            )
          ]),
        ),
      ),
    );
  }
}

class GoodsCardView extends StatefulWidget {
  final GoodsDetailEntity goodsDetail;
  const GoodsCardView(this.goodsDetail, {super.key});

  @override
  State<StatefulWidget> createState() {
    return _GoodsCardState();
  }
}

class _GoodsCardState extends State<GoodsCardView> {
  @override
  Widget build(BuildContext context) {
    double width = (MediaQuery.of(Get.context!).size.width - 28 - 8) / 2;
    double height = width * 90 / 178;
    GoodsDetailEntity entity = widget.goodsDetail;
    return GestureDetector(
      onTap: () {
        var parameters = <String, String>{};
        if (entity.id.isNotEmpty == true) {
          parameters['id'] = entity.id;
        } else if (entity.skuId.isNotEmpty) {
          parameters['sku_id'] = entity.skuId;
        }
        Get.toNamed(MallRoutes.GOODS_DETAIL, parameters: parameters);
      },
      child: Card(
        color: MColor.xFFFFFFFF,
        margin: EdgeInsets.zero,
        elevation: 0,
        child: Container(
          decoration: const BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.all(Radius.circular(10))),
          child: Column(children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: const Radius.circular(10)),
              child: CachedNetworkImage(
                imageUrl: entity.pic[1],
                width: width,
                height: height,
                fit: BoxFit.cover,
                placeholder: (ctx, e) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: MColor.xFFE5E5E5,
                    ),
                  );
                },
                errorWidget: (ctx, e, x) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: MColor.xFFE5E5E5,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Row(
              children: [
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: Text(
                    entity.title,
                    maxLines: 1,
                    style: MFont.regular12.apply(color: MColor.xFF000000),
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            Row(
              children: [
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      text: entity.accountType.symbol,
                      style: MFont.regular12.apply(color: MColor.xFF000000),
                      children: [
                        TextSpan(text: entity.price, style: MFont.regular14.apply(color: MColor.xFF000000)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            Row(
              children: [
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                    flex: 2,
                    child: Text(
                      '剩余${entity.stock}${entity.unit}',
                      maxLines: 1,
                      style: MFont.regular10.apply(color: MColor.xFF808080),
                    )),
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    textAlign: TextAlign.end,
                    '${entity.sales}人付款',
                    style: MFont.regular10.apply(color: MColor.xFF808080),
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
              ],
            ),
            const SizedBox(
              height: 7,
            )
          ]),
        ),
      ),
    );
  }
}
