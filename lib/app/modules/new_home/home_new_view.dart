import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/home_new_resp.dart';
import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/inspect/views/inspect_list_item_view.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';
import 'package:inspector/app/modules/mall/widget/goods_cart_view.dart';
import 'package:inspector/app/modules/purchase/purchase_widgets/purchase_item_view.dart';
import 'package:inspector/app/modules/widgets/banner_view.dart';
import 'package:inspector/app/modules/new_home/home_new_controller.dart';
import 'package:inspector/app/modules/widgets/sliver_sticky_header_delegate.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/widgets/inspection/inspection_mixin.dart';

import 'package:nested_scroll_view_plus/nested_scroll_view_plus.dart';

import '../../../generated/l10n.dart';

class HomeNewView extends GetView<HomeNewController> {
  const HomeNewView({super.key});

  @override
  Widget build(BuildContext context) {
    return getNewBody();
  }

  Widget getBody() {
    return Scaffold(
      // appBar: _HomeAppBar(),
      appBar: AppBar(
          scrolledUnderElevation: 0,
          titleSpacing: 0,
          // title: ,
          // leading: ,
          actions: [
            const SizedBox(
              width: 15,
            ),
            Center(child: AppIcons.icon(0xe615, size: 20, color: MColor.skin)),
            const SizedBox(
              width: 9,
            ),
            Text(S.of(Get.context!).app_name, style: TextStyle(color: MColor.skin, fontSize: 20, fontWeight: FontWeight.w700)),
            const SizedBox(
              width: 40,
            ),
            const Spacer()
            // Expanded(child: _searchView)
          ],
          elevation: 0,
          backgroundColor: MColor.x37FFE9E3),
      backgroundColor: MColor.xFFF6F6F6,
      body: SafeArea(
          child: RefreshIndicator(
        key: controller.indicatorKey,
        onRefresh: () => controller.getHomeList(),
        child: Obx(() {
          bool hasTab = false;
          List<Widget> headers = [];
          if (controller.banners.isNotEmpty) {
            headers.add(_bannerView);
          }
          if (controller.entrances.isNotEmpty) {
            headers.add(SizedBox(
              height: 20,
              child: Container(
                color: MColor.xFFFFFFFF,
              ),
            ));
            headers.add(_topEntranceView);
          }
          if (controller.notices.isNotEmpty) {
            headers.add(separatorView());
            // headers.add(_pushMessageView);
          }
          if (controller.homeInspect.value != null || controller.homePurchase.value != null || controller.homeMall.value != null) {
            headers.add(separatorView());
            headers.add(_tabbarView);
            hasTab = true;
          }
          return NestedScrollViewPlus(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return headers;
            },
            body: !hasTab
                ? _emptyView
                : TabBarView(
                    controller: controller.tabController,
                    physics: const BouncingScrollPhysics(
                      parent: AlwaysScrollableScrollPhysics(),
                    ),
                    children: [
                        if (controller.homeInspect.value != null) ...{
                          _HomeInspectTab(),
                        },
                        if (controller.homePurchase.value != null) ...{
                          _HomePurchaseTab(),
                        },
                        if (controller.homeMall.value != null) ...{_HomeMallTab()}
                      ]),
          );
        }),
      )),
    );
  }

  Widget getNewBody() {
    return Scaffold(
      appBar: AppBar(
          scrolledUnderElevation: 0,
          titleSpacing: 0,
          // title: ,
          // leading: ,
          actions: [
            const SizedBox(
              width: 15,
            ),
            Center(child: AppIcons.icon(0xe615, size: 20, color: MColor.skin)),
            const SizedBox(
              width: 9,
            ),
            Text(S.of(Get.context!).app_name, style: TextStyle(color: MColor.skin, fontSize: 20, fontWeight: FontWeight.w700)),
            const SizedBox(
              width: 40,
            ),
            const Spacer()
            // Expanded(child: _searchView)
          ],
          elevation: 0,
          backgroundColor: MColor.x37FFE9E3),
      backgroundColor: MColor.xFFF6F6F6,
      body: SafeArea(
        child: Obx(() {
          bool hasTab = false;
          List<Widget> headers = [];
          if (controller.banners.isNotEmpty) {
            headers.add(_bannerView);
          }
          if (controller.entrances.isNotEmpty) {
            headers.add(_topEntranceView);
          }
          if (controller.notices.isNotEmpty) {
            headers.add(separatorView());
            headers.add(_pushMessageView);
          }
          if (controller.homeInspect.value != null || controller.homePurchase.value != null || controller.homeMall.value != null) {
            headers.add(separatorView());
            headers.add(_tabbarView);
            hasTab = true;
          }
          if (headers.isEmpty) {
            headers.add(SliverPadding(padding: EdgeInsets.zero));
          }
          return RefreshIndicator(
            key: controller.indicatorKey,
            onRefresh: () => controller.getHomeList(),
            child: CustomScrollView(
              controller: controller.scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                ...headers,
                if (!hasTab)
                  SliverFillRemaining(child: _emptyView)
                else
                  SliverFillRemaining(
                    child: TabBarView(
                      controller: controller.tabController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        if (controller.homeInspect.value != null) ...{
                          _HomeInspectTab(),
                        },
                        if (controller.homePurchase.value != null) ...{
                          _HomePurchaseTab(),
                        },
                        if (controller.homeMall.value != null) ...{_HomeMallTab()}
                      ],
                    ),
                  ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget get _emptyView {
    return SizedBox(
      width: double.infinity,
      height: Get.height / 2,
      child: Stack(
        children: [
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.hourglass_empty,
                  size: 40,
                  color: Get.context!.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                ),
                const SizedBox(height: 10),
                Text(S.of(Get.context!).no_data, style: MFont.regular15.apply(color: Get.context!.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget separatorView() {
    return SliverToBoxAdapter(
      child: SizedBox(
        height: 3,
        child: Container(
          color: MColor.xFFEDEDED,
        ),
      ),
    );
  }

  Widget get _bannerView {
    return SliverToBoxAdapter(
      child: Builder(builder: ((context) {
        if (controller.banners.isEmpty) {
          return const SizedBox();
        } else {
          double width = MediaQuery.of(Get.context!).size.width;
          double height = width * 340 / 726;
          return BannerView(
            controller.banners,
            width,
            height,
            controller: controller.bannerController,
            onPageChanged: (value) {
              controller.bannerScrollIdx = value;
            },
            onPageTapped: (index) {
              RouterJump.jump(controller.banners[index].url);
            },
          );
        }
      })),
    );
  }

  Widget get _topEntranceView {
    double mainAxisSpacing = 4.0;
    var width = (MediaQuery.of(Get.context!).size.width - 15 * 2 - 4 * 3) / 4;
    var height = 36 + 8 + 34;
    return DecoratedSliver(
      decoration: BoxDecoration(
        color: MColor.xFFFFFFFF,
      ),
      sliver: SliverGrid.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4, mainAxisSpacing: mainAxisSpacing, crossAxisSpacing: 5.0, childAspectRatio: width / height),
          itemBuilder: (context, position) {
            HomeEntrance entrance = controller.entrances[position];
            return GestureDetector(
              onTap: () {
                RouterJump.jump(entrance.url);
              },
              child: Container(
                decoration: BoxDecoration(
                  color: MColor.xFFFFFFFF,
                  borderRadius: BorderRadius.circular(8),
                ),
                // margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Builder(builder: (context) {
                      if (entrance.image.isNotEmpty) {
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: CachedNetworkImage(
                            imageUrl: entrance.image,
                            width: 43,
                            height: 43,
                            fit: BoxFit.cover,
                            placeholder: (ctx, e) {
                              return Container(
                                decoration: BoxDecoration(
                                  color: MColor.xFFEEEEEE,
                                  borderRadius: BorderRadius.circular(35),
                                ),
                              );
                            },
                            errorWidget: (ctx, e, x) {
                              return Container(
                                decoration: BoxDecoration(
                                  color: MColor.xFFEEEEEE,
                                  borderRadius: BorderRadius.circular(35),
                                ),
                              );
                            },
                          ),
                        );
                      }
                      return AppIcons.icon(int.tryParse(entrance.icon, radix: 16), size: 36, color: MColor.skin);
                    }),
                    const SizedBox(
                      height: 8,
                    ),
                    Text(
                      entrance.name,
                      style: const TextStyle(fontSize: 14, color: MColor.xFF2A2A2A, height: 1),
                      maxLines: 2,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          },
          itemCount: controller.entrances.length),
    );
  }

  Widget get _pushMessageView {
    return SliverToBoxAdapter(
      child: Builder(builder: (context) {
        return Container(
          color: MColor.xFFFFFFFF,
          height: 35,
          padding: EdgeInsets.symmetric(horizontal: 14),
          child: Row(
            children: [
              AppIcons.icon(0xe62b, size: 14),
              const SizedBox(
                width: 6,
              ),
              Expanded(
                child: PageView.builder(
                    controller: controller.noticeController,
                    scrollDirection: Axis.vertical,
                    itemCount: controller.notices.length,
                    physics: NeverScrollableScrollPhysics(),
                    onPageChanged: (value) {
                      controller.noticeScrollIdx = value;
                    },
                    itemBuilder: (context, index) {
                      var notice = controller.notices[index];
                      return GestureDetector(
                        onTap: () {
                          if (notice.urlType == 0) {
                            RouterJump.jump(notice.url);
                          } else if (notice.urlType == 1) {
                            Get.toNamed(Routes.WEB, parameters: {'url': notice.url});
                          }
                        },
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                notice.title,
                                textAlign: TextAlign.start,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: MFont.regular14.apply(color: MColor.xFF000000),
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
              ),
              AppIcons.icon(0xe88e, size: 14),
            ],
          ),
        );
      }),
    );
  }

  Widget get _tabbarView {
    return SliverPersistentHeader(
      pinned: true,
      floating: true,
      delegate: SliverStickyHeaderDelegate(Builder(builder: (context) {
        return DefaultTabController(
          length: controller.tabController?.length ?? 3,
          child: Container(
            color: MColor.xFFFFFFFF,
            child: TabBar(
              controller: controller.tabController,
              indicatorColor: MColor.xFFF2591D,
              indicatorSize: TabBarIndicatorSize.label,
              indicatorWeight: 2,
              isScrollable: false,
              dividerColor: Colors.transparent,
              labelStyle: TextStyle(color: MColor.xFF000000, fontWeight: FontWeight.w500, fontSize: 14),
              unselectedLabelStyle: TextStyle(color: MColor.xFF383838, fontSize: 14),
              tabs: [
                if (controller.homeInspect.value != null) ...{
                  Container(
                      color: MColor.xFFFFFFFF,
                      child: Tab(
                        child: Text(
                          controller.homeInspect.value!.name,
                          maxLines: 2,
                          textAlign: TextAlign.center,
                        ),
                      ))
                },
                if (controller.homePurchase.value != null) ...{
                  GestureDetector(
                      onDoubleTap: () {},
                      child: Tab(
                          child: Text(
                        controller.homePurchase.value!.name,
                        maxLines: 2,
                        textAlign: TextAlign.center,
                      )))
                },
                if (controller.homeMall.value != null) ...{
                  GestureDetector(
                      onDoubleTap: () {},
                      child: Tab(
                          child: Text(
                        controller.homeMall.value!.name,
                        maxLines: 2,
                        textAlign: TextAlign.center,
                      )))
                },
              ],
            ),
          ),
        );
      }), maxExtend: 45, minExtend: 45),
    );
  }
}

class _HomeInspectTab extends GetView<HomeNewController> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Column(
        children: [
          if (controller.homeInspect.value != null)
            ...controller.homeInspect.value!.inspects.map((model) {
              return InspectListItemView(
                model,
                onItemTap: () {
                  Get.toNamed(Routes.LIST_DETAIL, arguments: model.id, parameters: {'isApply': '1'})?.then((value) {
                    if (value) {
                      // controller.refreshWithPermissionCheck();
                    }
                  });
                },
                onApplyTap: () {
                  if (model.applyStatus == 0 || model.applyStatus == 1) {
                    var first = true;
                    if (model.applyStatus == 1) {
                      first = false;
                    }
                    controller.fetchPrice(model.id ?? 0, first);
                  }
                },
              );
            }),
          Container(
            padding: EdgeInsets.symmetric(vertical: 14),
            child: GestureDetector(
              onTap: () {
                Get.toNamed(Routes.INSP_LIST);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  RichText(
                      text: WidgetSpan(
                          child: Row(children: [
                    Text(S.of(Get.context!).public_see_more, style: TextStyle(color: MColor.xFF000000, fontSize: 14)),
                    const SizedBox(
                      width: 2,
                    ),
                    Icon(Icons.arrow_forward_ios, size: 16)
                  ]))),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _HomePurchaseTab extends GetView<HomeNewController> {
  @override
  Widget build(BuildContext context) {
    if (controller.homePurchase.value?.purchases.isNotEmpty == true) {
      return SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Column(
          children: [
            ...controller.homePurchase.value!.purchases.map((purchase) {
              return PurchaseItemView(
                purchase,
                bgColor: MColor.xFFFFFFFF,
              );
            }),
            Container(
              padding: EdgeInsets.symmetric(vertical: 14),
              child: GestureDetector(
                onTap: () {
                  Get.toNamed(Routes.PURCHASE_LIST);
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    RichText(
                        text: WidgetSpan(
                            child: Row(children: [
                      Text(S.of(Get.context!).public_see_more, style: TextStyle(color: MColor.xFF000000, fontSize: 14)),
                      const SizedBox(
                        width: 2,
                      ),
                      Icon(Icons.arrow_forward_ios, size: 16)
                    ]))),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }
    return const SizedBox();
  }
}

class _HomeMallTab extends GetView<HomeNewController> {
  @override
  Widget build(BuildContext context) {
    if (controller.homeMall.value?.list.isNotEmpty == true) {
      double screenWidth = MediaQuery.of(context).size.width;
      double cardWidth = (screenWidth - 36) / 2;
      return SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Column(
          children: [
            const SizedBox(
              height: 10,
            ),
            Wrap(
              spacing: 8.0,
              runSpacing: 18.0,
              children: [
                for (GoodsDetailEntity entity in controller.homeMall.value!.list) ...{SizedBox(width: cardWidth, child: GoodsCardView(entity))},
              ],
            ),
            // MasonryGridView.count(
            //     padding: EdgeInsets.only(top: 10, bottom: 10, left: 14, right: 14),
            //     crossAxisCount: 2,
            //     crossAxisSpacing: 8,
            //     mainAxisSpacing: 19,
            //     itemCount: controller.homeMall.value!.list.length,
            //     itemBuilder: (context, index) {
            //       GoodsDetailEntity entity = controller.homeMall.value!.list[index];
            //       return GoodsCardView(entity);
            //     }),
            // Container(
            //   padding: EdgeInsets.symmetric(vertical: 14),
            //   child: GestureDetector(
            //     onTap: () {
            //       Get.toNamed(MallRoutes.MALL);
            //     },
            //     child: Row(
            //       mainAxisAlignment: MainAxisAlignment.center,
            //       children: [
            //         RichText(
            //             text: WidgetSpan(
            //                 child: Row(children: [
            //           Text(S.of(Get.context!).public_see_more, style: TextStyle(color: MColor.xFF000000, fontSize: 14)),
            //           const SizedBox(
            //             width: 2,
            //           ),
            //           Icon(Icons.arrow_forward_ios, size: 14)
            //         ]))),
            //       ],
            //     ),
            //   ),
            // )
          ],
        ),
      );
    } else {
      return const SizedBox();
    }
  }
}
