import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/home_new_resp.dart';
import 'package:inspector/app/modules/new_home/home_new_service.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/widgets/inspection/inspection_mixin.dart';

class HomeNewController extends GetxController with GetTickerProviderStateMixin, InspectionMixin {
  final scrollController = ScrollController();
  GlobalKey<RefreshIndicatorState> indicatorKey = GlobalKey<RefreshIndicatorState>();

  final HomeNewService _service = HomeNewService();

  final PageController bannerController = PageController();
  final PageController noticeController = PageController();

  TabController? tabController;

  final entrances = RxList<HomeEntrance>([]);
  final notices = RxList<HomeNotice>([]);
  final banners = RxList<HomeBanner>([]);

  final homeInspect = Rxn<HomeInspect>();
  final homePurchase = Rxn<HomePurchase>();
  final homeMall = Rxn<HomeMall>();

  int bannerScrollIdx = 0;
  int noticeScrollIdx = 0;
  Timer? scrollTimer;
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    indicatorKey.currentState?.show();
  }

  Future<void> getHomeList() async {
    var result = await _service.getHomeList();
    if (result.isSuccess) {
      if (result.data != null) {
        var tabCount = 0;
        if (result.data!.homeInspect != null) {
          tabCount++;
        }
        if (result.data!.homePurchase != null) {
          tabCount++;
        }
        if (result.data!.homeMall != null) {
          tabCount++;
        }
        tabController ??= TabController(length: tabCount, vsync: this);
        entrances.value = result.data!.entrances;
        notices.value = result.data!.notices;
        banners.value = result.data!.banners;
        homeInspect.value = result.data!.homeInspect;
        homePurchase.value = result.data!.homePurchase;
        homeMall.value = result.data!.homeMall;
        scrollTimer?.cancel();
        scrollTimer = Timer.periodic(Duration(seconds: 3), (timer) {
          if (bannerController.hasClients) {
            if (banners.isNotEmpty) {
              bannerScrollIdx = (bannerScrollIdx + 1) % banners.length;
              bannerController.animateToPage(bannerScrollIdx, duration: Duration(milliseconds: 300), curve: Curves.ease);
            } else {
              bannerScrollIdx = 0;
            }
          }
          if (noticeController.hasClients) {
            if (notices.isNotEmpty) {
              noticeScrollIdx = (noticeScrollIdx + 1) % notices.length;
              noticeController.animateToPage(noticeScrollIdx, duration: Duration(milliseconds: 300), curve: Curves.ease);
            } else {
              noticeScrollIdx = 0;
            }
          }
        });
      } else {
        entrances.clear();
        notices.clear();
        banners.clear();
        scrollTimer?.cancel();
        noticeScrollIdx = 0;
        bannerScrollIdx = 0;
        homeInspect.value = null;
        homePurchase.value = null;
        homeMall.value = null;
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
    scrollTimer?.cancel();
    tabController?.dispose();
    scrollController.dispose();
  }

  void updateApply(int orderId, bool isApply) {
    if (homeInspect.value != null) {
      bool found = false;
      for (var element in homeInspect.value!.inspects) {
        if (element.id == orderId) {
          found = true;
          element.applyStatus = isApply ? 1 : 0;
          var applyNum = element.applyNum ?? 0;
          element.applyNum = isApply ? applyNum + 1 : applyNum - 1;
          break;
        }
      }
      if (found) {
        homeInspect.refresh();
      }
    }
  }
}
