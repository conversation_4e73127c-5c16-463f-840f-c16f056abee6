import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/api_service.dart';
import 'package:inspector/app/modules/home/<USER>/supplement_view.dart';
import 'package:inspector/app/modules/message/controllers/message_controller.dart';
import 'package:inspector/app/modules/message/socket/socket_channel.dart';
import 'package:inspector/app/modules/mine/controllers/mine_controller.dart';
import 'package:inspector/app/shortcut/shortcut_controller.dart';
import 'package:inspector/app/tools/global_const.dart';

import '../../../tools/icons.dart';

enum TabPage {
  PageHome(1, 'tab_home', AppIcons.home),
  PageShortcut(2, 'tab_shortcut', AppIcons.menu_manage),
  PagePublish(3, 'tab_publish', AppIcons.message),
  PageMessage(4, 'tab_message', AppIcons.message),
  PageMine(5, 'tab_mine', AppIcons.mine);

  final int pageId;
  final String value;
  final IconData iconCode;
  const TabPage(this.pageId, this.value, this.iconCode);
}

class TabbarController extends GetxController {
  final tabIndex = TabPage.PageHome.obs;

  final unread = RxInt(0);

  @override
  Future<void> onInit() async {
    super.onInit();

    tabIndex.listen((p0) {
      if (p0 == TabPage.PageMine) {
        Get.find<MineController>().loadData();
      } else if (p0 == TabPage.PageShortcut) {
        Get.find<ShortcutController>().getShortcuts();
      } else if (p0 == TabPage.PageMessage) {
        GetInstance().putOrFind<MessageController>(() => MessageController()).fetchList();
        // } else if (p0 == TabPage.PagePurchase) {
        //   Future.delayed(const Duration(milliseconds: 300), () {
        //     Get.find<PurchaseListController>().refreshAction();
        //   });
      } else if (p0 == TabPage.PageHome) {
        Future.delayed(const Duration(milliseconds: 300), () {
          // Get.find<HomeNewController>().refreshWithPermissionCheck();
        });
        // } else if (p0 == TabPage.PageOrder) {
        //   Future.delayed(const Duration(milliseconds: 300), () {
        //     Get.find<OrderController>().refresh();
        //   });
      }
    });

    //这里主要是为了获取到未读数
    unawaited(GetInstance().putOrFind<MessageController>(() => MessageController()).fetchList());

    // ignore: unawaited_futures
    ApiService.getChinaCities().then((value) {
      GlobalConst.chinaAreas = value;
    });

    // SocketListener.start();
    await SocketChannel.startConnect();
  }

  @override
  void onReady() {
    super.onReady();

    if (GlobalConst.userModel?.phone == null || GlobalConst.userModel?.email == null) {
      Get.generalDialog(
        pageBuilder: (ctx, a1, a2) {
          return const SupplementView();
        },
      );
    }
  }

  @override
  void onClose() {}

  void navigateTo(TabPage id) {
    tabIndex.value = id;
  }

  void refreshUnreadMessageCount(int count) {
    unread.value = count;
    if (unread.value < 0) unread.value = 0;
  }
}
