// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/home/<USER>/HomeListView.dart';
import 'package:inspector/app/modules/message/views/message_view.dart';
import 'package:inspector/app/modules/mine/views/mine_view.dart';
import 'package:inspector/app/modules/new_home/home_new_view.dart';
import 'package:inspector/app/modules/tabbar/controllers/tabbar_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/shortcut/shortcut_view.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../generated/l10n.dart';

class CustomTabbarView extends GetView<TabbarController> {
  const CustomTabbarView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var _bodyList = GlobalConst.userModel?.role == UserRole.admin
        ? ([const HomeNewView(), const ShortcutView(), const IMessageView(), const MineView()])
        : ([const HomeNewView(), const ShortcutView(), Container(), const IMessageView(), const MineView()]);
    var _tabPages = GlobalConst.userModel?.role == UserRole.admin
        ? ([TabPage.PageHome, TabPage.PageShortcut, TabPage.PageMessage, TabPage.PageMine])
        : ([TabPage.PageHome, TabPage.PageShortcut, TabPage.PagePublish, TabPage.PageMessage, TabPage.PageMine]);

    return Scaffold(
      body: Obx(() {
        var index = _tabPages.indexOf(controller.tabIndex.value);
        return _bodyList[index];
      }),
      extendBody: true,
      resizeToAvoidBottomInset: false,
      floatingActionButton: GlobalConst.userModel?.role == UserRole.admin
          ? null
          : Container(
              margin: const EdgeInsets.only(top: 35),
              child: GestureDetector(
                onTap: () {
                  _showPublishSheet(context);
                },
                child: Container(
                  padding: const EdgeInsets.only(top: 5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    color: Colors.transparent,
                    boxShadow: [BoxShadow(color: context.isDarkMode ? DarkColor.x80E3E3E3 : MColor.x80E3E3E3, blurRadius: 3)],
                  ),
                  child: Image.asset(Assets.addTask),
                ),
              ),
            ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: Obx(() {
        var index = _tabPages.indexOf(controller.tabIndex.value);

        List<BottomNavigationBarItem> items = [];
        for (var i = 0; i < _tabPages.length; i++) {
          items.add(_getBottomTabItems(context, _tabPages[i]));
        }
        return BottomNavigationBar(
          backgroundColor: Colors.white,
          selectedItemColor: MColor.skin,
          unselectedItemColor: MColor.xFF9A9B9C,
          selectedLabelStyle: MFont.medium10,
          unselectedLabelStyle: MFont.medium10,
          type: BottomNavigationBarType.fixed,
          currentIndex: index,
          elevation: 8,
          onTap: (index) {
            if (index == 2 && GlobalConst.userModel?.role != UserRole.admin) {
              _showPublishSheet(context);
            } else {
              controller.tabIndex.value = _tabPages[index];
            }
          },
          items: items,
        );
      }),
    );
  }

  static void _showPublishSheet(BuildContext context) {
    showCupertinoModalPopup(
        context: context,
        builder: (context) {
          return CupertinoActionSheet(
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(S.of(Get.context!).public_cancel,
                  style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333, fontWeight: FontWeight.bold)),
            ),
            actions: <Widget>[
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  Get.toNamed(Routes.PUBLISH);
                },
                child: Text(
                  S.of(Get.context!).publish_inspection,
                  style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
              ),
              CupertinoActionSheetAction(
                  onPressed: () {
                    Navigator.of(context).pop();
                    // Get.toNamed(Routes.PURCHASE_PUBLISH);
                    // Get.to(() => PurchaseEditView(tag: '0'), preventDuplicates: true, binding: PurchaseEditBinding(tag: '0'));
                    Get.toNamed(Routes.PURCHASE_PUBLISH, parameters: {'is_edit': '0'}, preventDuplicates: true);
                  },
                  child: Text(
                    S.of(Get.context!).publish_purchase,
                    style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                  )),
            ],
          );
        });
  }

  BottomNavigationBarItem _getBottomTabItems(BuildContext context, TabPage tabPage) {
    if (tabPage == TabPage.PageMessage) {
      String unreadStr = '';
      if (controller.unread.value > 99) {
        unreadStr = '99+';
      } else {
        unreadStr = '${controller.unread.value}';
      }
      return BottomNavigationBarItem(
        icon: Badge(
          label: Text(unreadStr),
          isLabelVisible: controller.unread.value > 0,
          child: Icon(tabPage.iconCode, size: 24, color: MColor.xFF9A9B9C),
        ),
        activeIcon: Badge(
          label: Text(unreadStr),
          isLabelVisible: controller.unread.value > 0,
          child: Icon(tabPage.iconCode, size: 24, color: MColor.xFFF2591D),
        ),
        label: S.of(context).tabbar_tab_names(tabPage.value),
      );
    } else {
      return BottomNavigationBarItem(
        icon: Icon(tabPage.iconCode, size: 24, color: MColor.xFF9A9B9C),
        activeIcon: Icon(tabPage.iconCode, size: 24, color: MColor.xFFF2591D),
        label: S.of(context).tabbar_tab_names(tabPage.value),
      );
    }
  }
}
