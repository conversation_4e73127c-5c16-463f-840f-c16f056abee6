import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mine/add_bank/controllers/add_bank_controller.dart';

import '../../../../../generated/l10n.dart';

class AddBankView extends GetView<AddBankController> {
  const AddBankView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).add_bank_title),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                for (int i = 0; i < controller.titles.length; i++) ...{
                  _textView(i),
                },
              ],
            ),
          ),
          _textButton(),
        ],
      ),
    );
  }

  Widget _textView(int index) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Builder(builder: (context) {
            return Text(
              controller.titles[index],
              style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            );
          }),
          _textField(index),
        ],
      ),
    );
  }

  Widget _textField(int index) {
    return Builder(builder: (context) {
      return TextField(
        controller: controller.textControllers[index],
        minLines: 1,
        style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
        textAlign: TextAlign.start,
        scrollPadding: EdgeInsets.zero,
        decoration: InputDecoration(
          hintText: '${S.of(Get.context!).bind_hint}${controller.titles[index]}',
          hintStyle: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
          filled: true,
          contentPadding: const EdgeInsets.only(bottom: 0, top: 10),
          // constraints: BoxConstraints(minHeight: 30, maxHeight: 30),
          fillColor: Get.context!.isDarkMode ? DarkColor.xFFCCCCCC : Colors.white,
          isDense: false,
          focusedBorder: const UnderlineInputBorder(borderSide: BorderSide(color: MColor.skin, width: 1)),
          enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFF9A9B9C : MColor.xFF9A9B9C, width: 1)),
        ),
      );
    });
  }

  Widget _textButton() {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18, bottom: 30),
      child: TextButton(
        onPressed: () {
          controller.fetchAddBank();
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          S.of(Get.context!).publish_submit,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }
}
