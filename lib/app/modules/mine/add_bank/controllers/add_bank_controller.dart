import 'package:flutter/cupertino.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';

class AddBankController extends GetxController {
  MineProvider provider = MineProvider();
  List<TextEditingController> textControllers = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
  ];
  List<String> titles = [
    S.of(Get.context!).add_bank_name,
    S.of(Get.context!).add_bank_branch,
    S.of(Get.context!).add_bank_card,
    S.of(Get.context!).add_bank_address,
    S.of(Get.context!).add_bank_real_name,
  ];

  void fetchAddBank() {
    var bankName = textControllers[0].text;
    if (bankName.isEmpty) {
      showToast('${S.of(Get.context!).please_enter}${titles[0]}');
      return;
    }
    var branch = textControllers[1].text;
    if (branch.isEmpty) {
      showToast('${S.of(Get.context!).please_enter}${titles[1]}');
      return;
    }
    var card = textControllers[2].text;
    if (card.isEmpty) {
      showToast('${S.of(Get.context!).please_enter}${titles[2]}');
      return;
    }
    var realName = textControllers[3].text;
    if (realName.isEmpty) {
      showToast('${S.of(Get.context!).please_enter}${titles[3]}');
      return;
    }
    var address = textControllers[4].text;
    if (address.isEmpty) {
      showToast('${S.of(Get.context!).please_enter}${titles[4]}');
      return;
    }

    EasyLoading.show();
    provider.addBank(address, card, branch, bankName, realName).then((value) async {
      if (value.isSuccess) {
        Get.back();
      }
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  @override
  void onClose() {
    for (var element in textControllers) {
      element.dispose();
    }
  }
}
