import 'package:flutter/material.dart';

import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/data/china_city.dart';
import 'package:inspector/app/modules/address/address_manager.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/modules/mine/address/address_controller.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class AddressView extends GetView<AddressController> {
  const AddressView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(S.of(Get.context!).address_title, style: const TextStyle(color: Colors.white)),
        centerTitle: true,
        backgroundColor: Colors.transparent,
      ),
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: const [0, 0.6],
                colors: [MColor.xFFE95332, context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE],
              ),
            ),
          ),
          SafeArea(
            bottom: false,
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                FocusManager.instance.primaryFocus?.unfocus();
              },
              child: ListView(
                children: [
                  _autoView,
                  _infoView,
                  Obx(() {
                    if (controller.addressList.isNotEmpty && controller.showList.value) {
                      return _addressView;
                    } else {
                      return Container();
                    }
                  }),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Container get _autoView {
    return Container(
      padding: const EdgeInsets.all(15),
      margin: const EdgeInsets.only(top: 5, left: 10, right: 10),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? DarkColor.xFFFFFFFF : MColor.xFFFFFFFF,
        borderRadius: BorderRadius.circular(6),
      ),
      child: _textViewField,
    );
  }

  Container get _textViewField {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
      ),
      child: Builder(builder: (context) {
        return Container(
          padding: const EdgeInsets.only(bottom: 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
          ),
          child: TextField(
            controller: controller.editingController,
            style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
            keyboardType: TextInputType.multiline,
            minLines: 3,
            maxLines: null,
            decoration: InputDecoration(
              hintText: S.of(Get.context!).address_auto_tips,
              hintMaxLines: 3,
              hintStyle: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFFA2A2A2 : MColor.xFFA2A2A2),
              hintTextDirection: TextDirection.ltr,
              filled: true,
              isDense: false,
              counter: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  if (controller.pasteText.value.isNotEmpty) {
                    controller.recognizeAddress(controller.editingController.text);
                  }
                },
                child: Obx(() {
                  return Text(
                    controller.pasteText.value.isEmpty ? S.of(Get.context!).address_paste : S.of(Get.context!).address_ocr,
                    style: MFont.medium15.apply(color: MColor.skin),
                  );
                }),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              fillColor: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
                borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
                borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
                borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
              ),
            ),
            onChanged: (text) {
              controller.pasteText.value = text;
            },
          ),
        );
      }),
    );
  }

  Container get _infoView {
    return Container(
      margin: const EdgeInsets.only(top: 10, left: 10, right: 10),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          for (int i = 0; i < controller.titles.length; i++) ...{
            _textView(i, controller.titles[i], controller.placeHolds[i]),
          },
          _nextView,
        ],
      ),
    );
  }

  Widget _textView(int index, String title, String tips) {
    return Builder(builder: (context) {
      return Container(
        decoration: BoxDecoration(border: Border(bottom: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, width: 0.5))),
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: 84,
              child: index != 3 && index != 5 && index != 1 && index != 0
                  ? RichText(
                      text: TextSpan(
                        text: '*',
                        style: MFont.medium15.apply(color: MColor.skin),
                        children: [
                          const WidgetSpan(child: SizedBox(width: 10)),
                          TextSpan(
                            text: title,
                            style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.right,
                    )
                  : Text(
                      title,
                      style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                      textAlign: TextAlign.right,
                    ),
            ),
            Expanded(
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  if (index == 4) {
                    _pickerAddress();
                  }
                },
                child: TextField(
                  focusNode: index == 5 ? controller.focusNode : null,
                  controller: controller.controllers[index],
                  keyboardType: index == 2
                      ? TextInputType.phone
                      : index == 3
                          ? TextInputType.emailAddress
                          : index == 5
                              ? TextInputType.multiline
                              : TextInputType.text,
                  minLines: 1,
                  maxLines: null,
                  style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
                  textAlign: TextAlign.start,
                  scrollPadding: EdgeInsets.zero,
                  enabled: index == 4 ? false : true,
                  decoration: InputDecoration(
                    hintText: tips,
                    hintStyle: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFFA2A2A2 : MColor.xFFA2A2A2),
                    // hintStyle: MFont.medium15.apply(color: !context.isDarkMode ? DarkColor.xFFD7D9DD : MColor.xFFD7D9DD),
                    filled: true,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 10),
                    fillColor: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6,
                    border: const OutlineInputBorder(borderSide: BorderSide(color: Colors.white)),
                    focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6)),
                    enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6)),
                    disabledBorder: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6)),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Container get _nextView {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 14),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TextButton(
            style: ButtonStyle(
              shape: MaterialStateProperty.all(
                const StadiumBorder(side: BorderSide(color: MColor.xFFD7A17C)),
              ),
              minimumSize: MaterialStateProperty.all(const Size(126, 35)),
            ),
            onPressed: () {
              for (var element in controller.controllers) {
                element.text = '';
              }
              controller.province = '';
              controller.city = '';
              controller.area = '';
              controller.lat = 0;
              controller.lng = 0;
            },
            child: Text(
              S.of(Get.context!).address_clear,
              style: MFont.medium15.apply(color: MColor.xFFD7A17C),
            ),
          ),
          const SizedBox(width: 23),
          TextButton(
            style: ButtonStyle(
              shape: MaterialStateProperty.all(
                const StadiumBorder(side: BorderSide(color: MColor.xFFD7A17C)),
              ),
              minimumSize: MaterialStateProperty.all(const Size(126, 35)),
              backgroundColor: MaterialStateProperty.all(MColor.xFFD7A17C),
            ),
            onPressed: () {
              controller.fetchSaveAddress();
            },
            child: Text(
              S.of(Get.context!).address_submit,
              style: MFont.medium15.apply(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _pickerAddress() {
    return Pickers.showMultiLinkPicker(
      Get.context!,
      data: AddressManager.toMapJson(),
      columeNum: 3,
      selectData: ['', '', ''],
      pickerStyle: PickerStyle(
        // showTitleBar: true,
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
        itemOverlay: Builder(builder: (context) {
          return Container(
            decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
            ),
          );
        }),
      ),
      // initTown: '',
      // addAllItem: false,
      onConfirm: (List p, List i) {
        var province = p[0];
        var city = p[1];
        var area = p[2];
        controller.province = province;
        controller.city = city;
        controller.area = area;
        controller.controllers[4].text = '$province$city$area';
        controller.focusNode.requestFocus();
      },
    );
  }

  Widget get _addressView {
    return Container(
      margin: const EdgeInsets.only(top: 10, left: 10, right: 10),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const BouncingScrollPhysics(),
        itemCount: controller.addressList.length <= 2 ? controller.addressList.length + 1 : 4,
        itemBuilder: (ctx, index) {
          if (index == 0) {
            return Row(
              children: [
                Builder(builder: (context) {
                  return Text(
                    S.of(Get.context!).address_recent,
                    style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFFA2A2A2 : MColor.xFFA2A2A2),
                  );
                }),
                const Spacer(),
                if (controller.addressList.length > 2) ...{
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.toNamed(Routes.ADDRESS_LIST)?.then((value) {
                        if (value == null) {
                          return;
                        }
                        Get.back(result: value);
                      });
                    },
                    child: Builder(builder: (context) {
                      return Container(
                        padding: const EdgeInsets.only(top: 10, bottom: 10),
                        child: Text(
                          S.of(Get.context!).address_more,
                          style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                          textAlign: TextAlign.center,
                        ),
                      );
                    }),
                  ),
                },
                Builder(builder: (context) {
                  return Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 15,
                    color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                  );
                }),
              ],
            );
          } else if (index <= controller.addressList.length && index < 3) {
            return _addressItem(index - 1);
          }

          return Container();
        },
      ),
    );
  }

  Widget _addressItem(int index) {
    AddressRows address = controller.addressList[index];
    var name = address.name ?? '';
    var phone = address.phone ?? '';
    var province = address.province ?? '';
    var city = address.city ?? '';
    var area = address.area ?? '';
    var detail = address.address ?? '';
    var factory = address.factoryName;
    var text = '$province-$city-$area-$detail';
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.back(result: {
          'addressInfo': address,
        });
      },
      child: Builder(builder: (context) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 11),
          decoration: BoxDecoration(
            border: controller.addressList.length <= 2
                ? null
                : Border(bottom: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, width: 0.5)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$name $phone',
                style: MFont.semi_Bold13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              ),
              const SizedBox(height: 4),
              Text(
                text.fixAutoLines(),
                style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              ),
              const SizedBox(height: 4),
              Text(
                '${S.of(Get.context!).address_name}: $factory',
                style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              ),
            ],
          ),
        );
      }),
    );
  }
}
