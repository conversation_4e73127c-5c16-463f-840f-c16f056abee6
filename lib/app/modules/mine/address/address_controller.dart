import 'dart:core';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/const_provider.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/mine/address/address_mixin.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class AddressController extends GetxController with AddressMixin {
  TextEditingController editingController = TextEditingController();
  FocusNode focusNode = FocusNode();
  List<TextEditingController> controllers = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
  ];
  ConstProvider provider = ConstProvider();
  MineProvider mProvider = MineProvider();
  final GlobalKey<FormState> formKey = GlobalKey();
  List<String> titles = [
    S.of(Get.context!).address_name,
    S.of(Get.context!).address_person,
    S.of(Get.context!).address_mobile,
    S.of(Get.context!).address_email,
    S.of(Get.context!).address_area,
    S.of(Get.context!).address_detail
  ];
  List<String> placeHolds = [
    S.of(Get.context!).address_name_tip,
    S.of(Get.context!).address_person_tip,
    S.of(Get.context!).address_mobile_tip,
    S.of(Get.context!).address_email_tip,
    S.of(Get.context!).address_area_tip,
    S.of(Get.context!).address_detail_tip
  ];
  String province = '';
  String city = '';
  String area = '';
  double lat = 0;
  double lng = 0;
  final isSave = true.obs;
  final pasteText = ''.obs;
  int? addressId;
  int? factoryUserId;
  final addressList = <AddressRows>[].obs;
  final showList = true.obs;

  @override
  void onInit() {
    super.onInit();

    if (Get.arguments is AddressRows) {
      var info = Get.arguments as AddressRows;
      addressId = info.id;
      setInfo(info);
    } else {
      showList.value = Get.arguments ?? true;
      if (showList.value) {
        fetchAddressList();
      }
    }
  }

  // @override
  // void onReady() {
  //   readClipboard();
  // }

  // Future<void> readClipboard() async {
  //   ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
  //   if (data != null && data.text != null) {
  //     logger.i(data);
  //     recognizeAddress(data.text!);
  //   }
  //   return Future.value();
  // }

  void recognizeAddress(String text) {
    EasyLoading.show();
    mProvider.recognizeAddress(text).then((value) {
      if (value.isSuccess) {
        AddressRows address = value.data!;
        province = address.province ?? '';
        city = address.city ?? '';
        area = address.area ?? '';
        lat = address.lat ?? 0;
        lng = address.lon ?? 0;
        controllers[0].text = address.factoryName;
        controllers[1].text = address.name ?? '';
        controllers[2].text = address.phone ?? '';
        controllers[3].text = address.email ?? '';
        controllers[4].text = '$province$city$area'.fixAutoLines();
        controllers[5].text = (address.address ?? '').fixAutoLines();
        FocusManager.instance.primaryFocus?.unfocus();
      } else {
        showToast(value.message ?? '');
      }
    }).catchError((error) {
      showToast(error.toString());
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  Future<void> fetchSaveAddress() async {
    for (int i = 0; i < controllers.length; i++) {
      if (controllers[i].text.trim().isEmpty) {
        if (i != 3 && i != 5 && i != 1 && i != 0) {
          showToast(titles[i]);
          return;
        }
      }
    }

    await EasyLoading.show();
    await mProvider
        .addressSave(
            controllers[0].text, controllers[1].text, controllers[2].text, controllers[3].text, province, city, area, controllers[5].text, lat, lng, addressId)
        .then((value) {
      if (value.isSuccess) {
        if (addressId != null) {
          Get.back(result: {'addressInfo': value.data});
        } else {
          Get.back(result: {'addressInfo': value.data});
        }
      }
      showToast(value.message ?? '');
    }).catchError((error) {
      showToast(error.toString());
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void fetchAddressList() {
    mProvider.addressList('', 0, showList.value ? 500 : 10).then((value) {
      if (value.isSuccess) {
        addressList.value = value.data?.rows ?? [];
        var info = addressList.firstWhereOrNull((element) => element.id == addressId);
        setInfo(info);
      }
    });
  }

  void setInfo(AddressRows? info) {
    if (info != null) {
      controllers[0].text = info.factoryName;
      controllers[1].text = info.name ?? '';
      controllers[2].text = info.phone ?? '';
      controllers[3].text = info.email ?? '';
      lat = info.lat ?? 0;
      lng = info.lon ?? 0;
      province = info.province ?? '';
      city = info.city ?? '';
      area = info.area ?? '';
      factoryUserId = info.userId ?? 0;
      controllers[4].text = province + city + area;
      controllers[5].text = info.address ?? '';
    }
  }

  @override
  void onClose() {
    editingController.dispose();
    focusNode.dispose();
    for (var element in controllers) {
      element.dispose();
    }
  }
}
