import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mine/wallet/controllers/wallet_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/wallet/wallet_provider.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../../generated/l10n.dart';

class WalletView extends GetView<WalletController> {
  const WalletView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).wallet_title),
        centerTitle: true,
        actions: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              Get.toNamed(Routes.BILL_LIST);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              alignment: Alignment.center,
              child: Text(
                S.of(Get.context!).wallet_bill,
                style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              ),
            ),
          ),
        ],
      ),
      body: Obx(() {
        bool isRmbDefault = WalletProvider.instance.walletModel.value.userAccount == 1;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _accountView('¥${WalletProvider.instance.walletModel.value.rmbAmount}', isRmbDefault, false),
            _accountView('\$${WalletProvider.instance.walletModel.value.usdAmount}', !isRmbDefault, true),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 18,
                bottom: 18,
              ),
              child: Text(
                S.of(Get.context!).wallet_account_heading,
                textAlign: TextAlign.start,
                style: MFont.semi_Bold17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: controller.icons.length,
                itemBuilder: (ctx, index) {
                  return Obx(() {
                    var icon = controller.icons[index];
                    var title = controller.titles[index];
                    var value = controller.values[index];
                    return _iconText(icon, title, value, index);
                  });
                },
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Row(
                children: [
                  Expanded(
                      child: _textButton(0, S.of(Get.context!).wallet_charge, action: () {
                    Get.toNamed(Routes.CHARGE)?.then((value) {
                      controller.fetchWalletInfo();
                    });
                  })),
                  const SizedBox(width: 20),
                  Expanded(
                    child: _textButton(1, S.of(Get.context!).wallet_cash, action: () {
                      // if (controller.payMotheds.isEmpty && controller.banks.isEmpty) {
                      //   showToast(S.of(Get.context!).withdrawal_method);
                      //   return;
                      // }
                      if (controller.withdrawMethods.containsKey('alipay')) {
                        Get.toNamed(Routes.CASH, arguments: controller.withdrawMethods)?.then((value) {
                          controller.fetchWalletInfo();
                        });
                      } else {
                        showToast(S.of(Get.context!).withdrawal_bind_alipay);
                        Get.toNamed(Routes.BIN_PAY, arguments: 2)?.then((value) {
                          controller.fetchWalletInfo();
                        });
                      }
                    }),
                  )
                ],
              ),
            ),
            const SizedBox(height: 50),
          ],
        );
      }),
    );
  }

  Widget _accountView(String value, bool isDefault, bool isUSD) {
    return Container(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
        top: 18,
        bottom: 0,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Image.asset(isUSD ? Assets.redUsdIcon : Assets.redUsdIcon, width: 16, height: 16),
              const SizedBox(width: 13),
              Text(
                isUSD ? S.of(Get.context!).wallet_usd_account : S.of(Get.context!).wallet_rmb_account,
                style: MFont.medium16.apply(color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              ),
              Expanded(
                child: Text(
                  value,
                  style: MFont.medium16.apply(color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 8,
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              if (!isDefault) {
                controller.setDefaultAccount(isUSD);
              }
            },
            child: Row(
              children: [
                const SizedBox(width: 30),
                Icon(
                  isDefault ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                  size: 18,
                  color: Get.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                ),
                const SizedBox(width: 8),
                Text(
                  !isDefault ? S.of(Get.context!).wallet_set_default_account : S.of(Get.context!).wallet_default_account,
                  style: MFont.medium14.apply(color: Get.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
                  textAlign: TextAlign.start,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _iconText(String icon, String title, String value, int index) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (index == 0) {
          Get.toNamed(Routes.BIND_BANK)?.then((value) {
            controller.fetchWalletInfo();
          });
        } else if (index == 1) {
          Get.toNamed(Routes.BIN_PAY, arguments: 1)?.then((value) {
            controller.fetchWalletInfo();
          });
        } else if (index == 2) {
          Get.toNamed(Routes.BIN_PAY, arguments: 2)?.then((value) {
            controller.fetchWalletInfo();
          });
        } else if (index == 3) {
          Get.toNamed(Routes.BIN_PAY, arguments: 3)?.then((value) {
            controller.fetchWalletInfo();
          });
        }
      },
      child: Builder(builder: (context) {
        return Container(
          padding: const EdgeInsets.only(
            left: 16,
            right: 16,
            top: 16,
            bottom: 16,
          ),
          child: Row(
            children: [
              Image.asset(icon, width: 16, height: 16),
              const SizedBox(width: 13),
              Text(
                title,
                style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              ),
              Expanded(
                child: Text(
                  value,
                  style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                  textAlign: TextAlign.right,
                ),
              ),
              Icon(
                Icons.keyboard_arrow_right_sharp,
                size: 16,
                color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383,
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _textButton(int index, String title, {@required VoidCallback? action}) {
    return TextButton(
      onPressed: () {
        action!();
      },
      style: ButtonStyle(
        backgroundColor: MaterialStateProperty.all(index == 0 ? MColor.xFFD7A17C : Colors.white),
        shape: MaterialStateProperty.all(const StadiumBorder()),
        minimumSize: MaterialStateProperty.all(const Size(double.infinity, 40)),
        visualDensity: VisualDensity.compact,
        maximumSize: MaterialStateProperty.all(const Size(double.infinity, 40)),
        side: MaterialStateProperty.all(const BorderSide(color: MColor.xFFD7A17C, width: 0.5)),
      ),
      child: Text(
        title,
        style: MFont.medium15.apply(color: index == 0 ? Colors.white : MColor.xFFD7A17C),
      ),
    );
  }
}
