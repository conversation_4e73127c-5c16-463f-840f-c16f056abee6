import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/data/bank_entity.dart';
import 'package:inspector/app/data/bind_pay_entity.dart';
import 'package:inspector/app/data/wallet_info_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/wallet/wallet_provider.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../../generated/l10n.dart';

class WalletController extends GetxController {
  final provider = MineProvider();
  final icons = [
    Assets.bankIcon,
    Assets.wechatIcon,
    Assets.alipayIcon,
    Assets.paypalIcon,
  ];
  final titles = [
    S.of(Get.context!).wallet_bank,
    S.of(Get.context!).wallet_wechat,
    S.of(Get.context!).wallet_alipay,
    S.of(Get.context!).pay_paypal,
  ];
  final values = ['', '', '', ''].obs;
  final payMotheds = RxList<BindPayEntity>([]);
  final banks = RxList<BankEntity>([]);

  final withdrawMethods = Map();

  @override
  void onInit() {
    super.onInit();
    fetchWalletInfo();
  }

  void fetchWalletInfo() {
    provider.walletInfo().then((value) async {
      if (value.isSuccess) {
        WalletInfoEntity? walletInfoEntity = value.data;
        if (walletInfoEntity != null) {
          WalletProvider.instance.walletModel.value = walletInfoEntity.wallet!;
          List<BindPayEntity>? payList = walletInfoEntity.payList;
          if (payList != null && payList.isNotEmpty) {
            payMotheds.value = value.data!.payList!;
            walletInfoEntity.payList?.forEach((element) {
              if (element.type == 1) {
                withdrawMethods['wechat'] = element;
                values[1] = element.account ?? '';
              } else if (element.type == 2) {
                withdrawMethods['alipay'] = element;
                values[2] = element.account ?? '';
              } else if (element.type == 3) {
                withdrawMethods['paypal'] = element;
                values[3] = element.account ?? '';
              }
            });
          }
          List<BankEntity>? bankList = walletInfoEntity.bankList;
          if (bankList == null || bankList.isEmpty) {
          } else {
            values[0] = bankList.first.bankName!;
            banks.value = bankList;
            withdrawMethods['bank'] = bankList;
          }
        }
      } else {
        showToast(value.message ?? '');
      }
    });
  }

  void setDefaultAccount(bool isUSD) {
    EasyLoading.show();
    provider.setDefaultAccount(isUSD).then((value) async {
      if (value.isSuccess) {
        fetchWalletInfo();
        EasyLoading.dismiss();
      }
    });
  }

  @override
  void onClose() {}
}
