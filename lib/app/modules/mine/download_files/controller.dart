// ignore_for_file: depend_on_referenced_packages

import 'dart:async';
import 'dart:io';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

import 'package:inspector/app/modules/mine/download_files/state.dart';
import 'package:inspector/app/tools/tools.dart';

class DownloadFilesPageController extends GetxController {
  final state = DownloadFilesPageState();

  @override
  void onInit() {
    _searchFiles();
    super.onInit();
  }

  Future<void> _searchFiles() async {
    unawaited(EasyLoading.show());
    try {
      Directory dir = await getTemporaryDirectory();
      Directory folder = Directory(path.join(dir.path, 'download'));
      if (!folder.existsSync()) return;

      var files = folder.listSync().where((e) => !e.path.endsWith('.DS_Store') && !e.path.endsWith(')')).toList();
      files.sort((a, b) => b.statSync().modified.compareTo(a.statSync().modified));
      state.files.value = files;
    } catch (error) {
      showToast(error.toString());
    } finally {
      state.loading.value = false;
      unawaited(EasyLoading.dismiss());
    }
  }
}
