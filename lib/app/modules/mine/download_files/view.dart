// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:open_file/open_file.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mine/download_files/controller.dart';
import 'package:inspector/app/shared/components/basic/icontainer.dart';
import 'package:inspector/app/shared/services/page_helper.dart';
import 'package:inspector/app/theme/style.dart';

import '../../../../generated/l10n.dart';

class DownloadFilesPage extends GetView<DownloadFilesPageController> {
  const DownloadFilesPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PageHelper.getAppBar(title: S.of(Get.context!).downloadFiles, backgroundColor: DefaultStyle.pageBackground),
      body: Obx(() {
        if (controller.state.loading.value) return const SizedBox();
        return ListView.separated(
          itemCount: controller.state.files.length,
          itemBuilder: (context, index) {
            var file = controller.state.files[index];
            String fileName = file.path.split('/').last;
            return GestureDetector(
              onTap: () {
                OpenFile.open(file.path);
              },
              child: IContainer(
                padding: const EdgeInsets.symmetric(horizontal: DefaultStyle.spaceLarge, vertical: DefaultStyle.spaceLarge),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(file.statSync().modified.toString().substring(0, 19)),
                    Text(fileName, style: MFont.semi_Bold15),
                  ],
                ),
              ),
            );
          },
          separatorBuilder: (context, index) => const Divider(height: 1),
        );
      }),
    );
  }
}
