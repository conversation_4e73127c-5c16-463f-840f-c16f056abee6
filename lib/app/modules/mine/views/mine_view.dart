import 'package:flutter/material.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/mine/controllers/mine_controller.dart';
import 'package:inspector/app/modules/mine/profile/views/input_text_view.dart';
import 'package:inspector/app/routes/app_navigator.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/tools/translation_service.dart';
import 'package:inspector/app/wallet/wallet_provider.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../generated/l10n.dart';
import '../../store/setting_store.dart';

class MineView extends GetView<MineController> {
  const MineView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
      padding: const EdgeInsets.only(bottom: 64.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            _topView,
            const SizedBox(height: 4),
            Offstage(
              offstage: GlobalConst.userModel?.role == UserRole.admin,
              child: _inspectView,
            ),
            Offstage(
              offstage: GlobalConst.userModel?.role == UserRole.admin,
              child: _purchaseView,
            ),
            Offstage(
              offstage: GlobalConst.userModel?.role == UserRole.admin,
              child: _otherView,
            ),
            // Divider(indent: 30, color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, thickness: 0.5),
            // downloadFiles,
            Offstage(
              offstage: GlobalConst.userModel?.role != UserRole.admin,
              child: _itemView(false, Assets.mineSetting, S.of(Get.context!).mine_setting, () {
                Get.toNamed(Routes.SETTING);
              }),
            ),
          ],
        ),
      ),
    ));
  }

  GestureDetector get _topView {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.toNamed(Routes.PROFILE)?.then((value) {
          controller.fetchUserInfo();
        });
      },
      child: Stack(
        children: [
          Image.asset(
            Assets.mineTop,
            width: Get.width,
            height: 235,
            fit: BoxFit.cover,
          ),
          SafeArea(
            bottom: false,
            child: Column(
              children: [
                _authView,
                _infoView,
                if (GlobalConst.userModel?.role != UserRole.admin) ...{
                  const _Wallet(),
                }
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget get _inspectView {
    return Card(
        color: MColor.xFFFFFFFF,
        margin: const EdgeInsets.only(left: 16, right: 16, top: 8),
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                      child: Text(S.of(Get.context!).mine_header_inspect,
                          strutStyle: const StrutStyle(
                            forceStrutHeight: true,
                            leading: 0.5,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.start,
                          style: MFont.bold16.apply(color: MColor.xFF333333))),
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  Expanded(
                      child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.toNamed(Routes.INSP_MY_INSP);
                    },
                    child: Column(
                      children: [
                        AppIcons.icon(
                          0xe60e,
                          size: 24,
                          color: MColor.skin,
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Text(S.of(Get.context!).mine_inspect_mine,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.regular14.apply(color: MColor.xFF333333))
                      ],
                    ),
                  )),
                  Expanded(
                      child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.toNamed(Routes.INSP_ORDER_MANAGE);
                    },
                    child: Column(
                      children: [
                        AppIcons.icon(
                          0xe612,
                          size: 24,
                          color: MColor.skin,
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Text(S.of(Get.context!).mine_inspect_order,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.regular14.apply(color: MColor.xFF333333))
                      ],
                    ),
                  )),
                  Expanded(
                      child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.toNamed(Routes.RECORD);
                    },
                    child: Column(
                      children: [
                        AppIcons.icon(
                          0xe60c,
                          size: 24,
                          color: MColor.skin,
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Text(S.of(Get.context!).mine_inspect_history,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.regular14.apply(color: MColor.xFF333333))
                      ],
                    ),
                  ))
                ],
              )
            ],
          ),
        ));
  }

  Widget get _purchaseView {
    return Card(
        color: MColor.xFFFFFFFF,
        margin: const EdgeInsets.only(left: 16, right: 16, top: 8),
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                      child: Text(S.of(Get.context!).mine_header_purchase,
                          strutStyle: const StrutStyle(
                            forceStrutHeight: true,
                            leading: 0.5,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.start,
                          style: MFont.bold16.apply(color: MColor.xFF333333))),
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  Expanded(
                      child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.toNamed(Routes.PURCHASE_MY_POSTS);
                    },
                    child: Column(
                      children: [
                        AppIcons.icon(
                          0xe610,
                          size: 24,
                          color: MColor.skin,
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Text(S.of(Get.context!).mine_purchase_mine,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.regular14.apply(color: MColor.xFF333333))
                      ],
                    ),
                  )),
                  Expanded(
                      child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.toNamed(Routes.PURCHASE_MY_REPLIES);
                    },
                    child: Column(
                      children: [
                        AppIcons.icon(
                          0xe613,
                          size: 24,
                          color: MColor.skin,
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Text(S.of(Get.context!).mine_purchase_reply,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.regular14.apply(color: MColor.xFF333333))
                      ],
                    ),
                  )),
                  Expanded(
                      child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.toNamed(Routes.PURCHASE_MY_APPEALS);
                    },
                    child: Column(
                      children: [
                        AppIcons.icon(
                          0xe60b,
                          size: 24,
                          color: MColor.skin,
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Text(S.of(Get.context!).mine_purchase_appeal,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.regular14.apply(color: MColor.xFF333333))
                      ],
                    ),
                  ))
                ],
              )
            ],
          ),
        ));
  }

  Widget get _otherView {
    return Card(
        color: MColor.xFFFFFFFF,
        margin: const EdgeInsets.only(left: 16, right: 16, top: 8),
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                      child: Text(S.of(Get.context!).mine_header_other,
                          strutStyle: const StrutStyle(
                            forceStrutHeight: true,
                            leading: 0.5,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.start,
                          style: MFont.bold16.apply(color: MColor.xFF333333))),
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                children: [
                  Expanded(
                      child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.generalDialog(
                        pageBuilder: (ctx, a1, a2) {
                          return InputTextView(S.of(Get.context!).recommended_order, TextInputType.emailAddress,
                              placeHolder: S.of(Get.context!).registry_email_phone_tips);
                        },
                      ).then((value) {
                        var text = (value as String?) ?? '';
                        if (text.isNotEmpty) {
                          controller.fetchRecommend(text);
                        }
                      });
                    },
                    child: Column(
                      children: [
                        AppIcons.icon(
                          0xe609,
                          size: 24,
                          color: MColor.skin,
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Text(S.of(Get.context!).mine_other_recommend,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.regular14.apply(color: MColor.xFF333333))
                      ],
                    ),
                  )),
                  Expanded(
                      child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.toNamed(Routes.ADDRESS_LIST, arguments: true);
                    },
                    child: Column(
                      children: [
                        AppIcons.icon(
                          0xe611,
                          size: 24,
                          color: MColor.skin,
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Text(S.of(Get.context!).mine_other_address,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.regular14.apply(color: MColor.xFF333333))
                      ],
                    ),
                  )),
                  Expanded(
                      child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.toNamed(Routes.SETTING);
                    },
                    child: Column(
                      children: [
                        AppIcons.icon(
                          0xe608,
                          size: 24,
                          color: MColor.skin,
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Text(S.of(Get.context!).mine_other_settings,
                            strutStyle: const StrutStyle(
                              forceStrutHeight: true,
                              leading: 0.5,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.start,
                            style: MFont.regular14.apply(color: MColor.xFF333333))
                      ],
                    ),
                  ))
                ],
              )
            ],
          ),
        ));
  }

  GestureDetector get _authView {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (GlobalConst.userModel?.role != UserRole.admin) {
          Get.toNamed(Routes.APPLY)?.then((value) {
            controller.fetchUserInfo();
          });
        }
      },
      child: GlobalConst.userModel?.role == UserRole.admin
          ? const SizedBox(height: 25)
          : Row(
              children: [
                const Spacer(),
                Container(
                  margin: const EdgeInsets.only(top: 15),
                  height: 25,
                  decoration: const BoxDecoration(
                    color: MColor.xFFEEB697,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.5),
                      bottomLeft: Radius.circular(12.5),
                    ),
                  ),
                  padding: const EdgeInsets.only(left: 15, right: 12),
                  child: Center(
                    child: Obx(() {
                      var checkStatus = controller.infoEntity.value.checkStatus ?? 1;
                      var text = S.of(Get.context!).apply_title;
                      if (checkStatus == 1) {
                        text = S.of(Get.context!).mine_checking;
                      } else if (checkStatus == 2) {
                        if (GlobalConst.userModel?.role != UserRole.inspector) {
                          text = S.of(Get.context!).mine_authed;
                        } else {
                          text = S.of(Get.context!).mine_authed_inspector;
                        }
                      } else if (checkStatus == 3) {
                        text = S.of(Get.context!).mine_check_failed;
                      }
                      return Builder(builder: (context) {
                        return Text(
                          text,
                          style: MFont.medium15.apply(color: MColor.xFF303535),
                        );
                      });
                    }),
                  ),
                ),
              ],
            ),
    );
  }

  Container get _infoView {
    return Container(
      margin: const EdgeInsets.only(top: 23, left: 16, right: 16),
      child: Obx(() {
        var user = controller.infoEntity.value;

        var info = '';
        if ((user.email ?? '').isNotEmpty) {
          info = user.email!;
        }
        if ((user.phone ?? '').isNotEmpty) {
          if (info.isEmpty) {
            info = user.phone!;
          } else {
            info += ' | ${user.phone!}';
          }
        }
        return Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name ?? '-',
                    style: MFont.semi_Bold24.apply(color: Colors.white),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    '${S.of(Get.context!).mine_vip_level}: ${user.grade == 0 ? '-' : user.grade} | ${S.of(Get.context!).mine_credit_quota}: ${WalletProvider.instance.walletModel.value.userAccount == 1 ? '¥${user.quotaRmb}' : '\$${user.quotaUsd}'}',
                    style: MFont.medium13.apply(
                      color: Colors.white.withOpacity(0.75),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    strutStyle: const StrutStyle(
                      forceStrutHeight: true,
                      leading: 0.5,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    info.fixAutoLines(),
                    style: MFont.medium13.apply(
                      color: Colors.white.withOpacity(0.75),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Builder(builder: (context) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(35),
                child: CachedNetworkImage(
                  imageUrl: user.head ?? '',
                  width: 70,
                  height: 70,
                  fit: BoxFit.cover,
                  placeholder: (ctx, e) {
                    return Container(
                      decoration: BoxDecoration(
                        color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                        borderRadius: BorderRadius.circular(35),
                      ),
                    );
                  },
                  errorWidget: (ctx, e, x) {
                    return Container(
                      decoration: BoxDecoration(
                        color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                        borderRadius: BorderRadius.circular(35),
                      ),
                    );
                  },
                ),
              );
            }),
          ],
        );
      }),
    );
  }

  Widget get downloadFiles {
    return Builder(builder: (context) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: AppNavigator.downloadFiles,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 17),
          child: Row(
            children: [
              Icon(Icons.file_download_outlined, size: 22, color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              const SizedBox(width: 18),
              Text(
                S.of(Get.context!).downloadFiles,
                style: MFont.medium16.apply(
                  color: context.isDarkMode
                      ? DarkColor.xFF3D3D3D
                      : context.isDarkMode
                          ? DarkColor.xFF3D3D3D
                          : MColor.xFF3D3D3D,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.keyboard_arrow_right_outlined,
                size: 24,
                color: context.isDarkMode ? DarkColor.xFFBBBBBB : MColor.xFFBBBBBB,
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _itemView(bool needDivider, String icon, String text, VoidCallback action) {
    return Builder(builder: (context) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () => action(),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 17),
              child: Row(
                children: [
                  Image.asset(
                    icon,
                    width: 22,
                    height: 22,
                    color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383,
                  ),
                  const SizedBox(width: 18),
                  Text(
                    text,
                    style: MFont.medium16.apply(
                      color: context.isDarkMode
                          ? DarkColor.xFF3D3D3D
                          : context.isDarkMode
                              ? DarkColor.xFF3D3D3D
                              : MColor.xFF3D3D3D,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.keyboard_arrow_right_outlined,
                    size: 24,
                    color: context.isDarkMode ? DarkColor.xFFBBBBBB : MColor.xFFBBBBBB,
                  ),
                ],
              ),
            ),
            if (needDivider) Divider(indent: 30, color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, thickness: 0.5),
          ],
        ),
      );
    });
  }
}

class _Wallet extends GetView<MineController> {
  const _Wallet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.toNamed(Routes.WALLET),
      child: Container(
        margin: const EdgeInsets.only(left: 16, right: 16, top: 23),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: const LinearGradient(colors: [MColor.xFFFEE0D0, MColor.xFFEEB595]),
        ),
        child: Column(
          children: [
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [title, button]),
            getBalance(),
          ],
        ),
      ),
    );
  }

  Widget get title {
    return Text(S.of(Get.context!).mine_amount, style: MFont.regular18.apply(color: MColor.xFF5C3F2B));
  }

  Widget get button {
    return GestureDetector(
      onTap: () {
        Get.toNamed(Routes.WALLET);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: const LinearGradient(colors: [MColor.xFFE95332, MColor.skin]),
        ),
        width: 100,
        height: 40,
        child: Center(
          child: Builder(builder: (context) {
            return Text(
              SettingStore.to.getCurrentLocale() == const Locale('zh', 'CN') ? S.of(Get.context!).mine_cash : 'Recharge\nWithdrawal',
              style: SettingStore.to.getCurrentLocale() == const Locale('zh', 'CN')
                  ? MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE)
                  : MFont.medium12.apply(color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE),
            );
          }),
        ),
      ),
    );
  }

  Widget getBalance() {
    return Obx(
      () {
        return Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      const Expanded(flex: 1, child: SizedBox()),
                      Expanded(flex: 2, child: Text(S.of(Get.context!).amount_available)),
                      Expanded(flex: 2, child: Text(S.of(Get.context!).amount_blocked)),
                      Expanded(flex: 2, child: Text(S.of(Get.context!).amount_total)),
                    ],
                  ),
                  const Divider(),
                  Row(
                    children: [
                      const Expanded(flex: 1, child: Center(child: Text('￥', style: TextStyle(fontSize: 16.0)))),
                      Expanded(flex: 2, child: Text(WalletProvider.instance.walletModel.value.rmbAmount?.toStringAsFixed(2) ?? '-')),
                      Expanded(flex: 2, child: Text(WalletProvider.instance.walletModel.value.frozenRmbAmount?.toStringAsFixed(2) ?? '-')),
                      Expanded(flex: 2, child: Text(WalletProvider.instance.walletModel.value.zRmbAmount?.toStringAsFixed(2) ?? '-')),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Expanded(flex: 1, child: Center(child: Text('\$', style: TextStyle(fontSize: 16.0)))),
                      Expanded(flex: 2, child: Text(WalletProvider.instance.walletModel.value.usdAmount?.toStringAsFixed(2) ?? '-')),
                      Expanded(flex: 2, child: Text(WalletProvider.instance.walletModel.value.frozenUsdAmount?.toStringAsFixed(2) ?? '-')),
                      Expanded(flex: 2, child: Text(WalletProvider.instance.walletModel.value.zUsdAmount?.toStringAsFixed(2) ?? '-')),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
