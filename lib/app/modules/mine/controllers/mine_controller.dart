import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/data/wallet_entity.dart';
import 'package:inspector/app/modules/auth/account_service.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/wallet/wallet_provider.dart';

class MineController extends GetxController {
  final provider = MineProvider();
  Rx<UserInfoEntity> infoEntity = UserInfoEntity().obs;

  @override
  void onInit() {
    super.onInit();

    if (GlobalConst.userModel != null) {
      infoEntity.value = GlobalConst.userModel!;
    }
  }

  @override
  void onReady() {
    super.onReady();

    loadData();
  }

  void loadData() {
    fetchUserInfo();
    fetchWalletInfo();
  }

  void fetchUserInfo() {
    AccountService.instance.fetchUserInfo().then((value) async {
      if (value.isSuccess) {
        infoEntity.value = GlobalConst.userModel!;
        infoEntity.update((v) {
          v!.checkStatus = value.data?.checkStatus;
          v.head = value.data?.head;
          v.wechatNum = value.data?.wechatNum;
          v.name = value.data?.name;
          v.phone = value.data?.phone;
          v.email = value.data?.email;
          v.quotaId = value.data?.quotaId;
          v.grade = value.data?.grade;
          v.quotaRmb = value.data?.quotaRmb;
          v.quotaUsd = value.data?.quotaUsd;
          v.uid = value.data?.uid;
          v.imUserId = value.data?.imUserId;
        });
      }
    });
  }

  void fetchRecommend(String email) {
    EasyLoading.show();
    provider.takeRecommend(email).then((value) async {
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void fetchWalletInfo() {
    provider.wallet().then((value) async {
      if (value.isSuccess) {
        WalletProvider.instance.walletModel.value = value.data ?? WalletEntity();
      } else {
        showToast(value.message ?? '');
      }
    });
  }

  @override
  void onClose() {}
}
