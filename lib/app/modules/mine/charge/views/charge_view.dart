import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/app_theme.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/modules/mine/charge/controllers/charge_controller.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../../generated/l10n.dart';

class ChargeView extends GetView<ChargeController> {
  const ChargeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
          appBar: AppBar(
            title: Text(S.of(Get.context!).charge_title),
            centerTitle: true,
            actions: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  Get.toNamed(Routes.CHARGE_HISTORY);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  alignment: Alignment.center,
                  child: Text(
                    S.of(Get.context!).charge_history_title,
                    style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                  ),
                ),
              ),
            ],
          ),
          resizeToAvoidBottomInset: false,
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      _accountView,
                      Divider(
                        thickness: 1,
                        color: context.isDarkMode ? DarkColor.xFF333333_60 : MColor.xFF333333_60,
                      ),
                      _textField,
                      Divider(
                        thickness: 1,
                        color: context.isDarkMode ? DarkColor.xFF333333_60 : MColor.xFF333333_60,
                      ),
                      _depositTypeView,
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                _textButton(),
              ],
            ),
          )),
    );
  }

  GestureDetector get _accountView {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        _pickerSingle();
      },
      child: Builder(builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 15),
            Row(
              children: [
                Text(
                  S.of(Get.context!).charge_account,
                  style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                ),
                Icon(Icons.arrow_drop_down_sharp, size: 24, color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              ],
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: _textView,
            ),
            const SizedBox(height: 8),
          ],
        );
      }),
    );
  }

  Widget get _textView {
    return Obx(() {
      int accountType = controller.accountType.value;
      return Builder(builder: (context) {
        return Text(
          accountType == 1 ? S.of(Get.context!).pay_rmb : S.of(Get.context!).pay_usd,
          style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
        );
      });
    });
  }

  Widget get _depositTypeView {
    return Obx(() {
      DepositType dt = controller.depositType.value;
      return Builder(builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 15),
            Text(
              S.of(Get.context!).charge_deposit_type_title,
              style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
            ),
            const SizedBox(height: 8),
            RadioListTile(
                dense: true,
                activeColor: MColor.skin,
                fillColor: MaterialStateProperty.all(MColor.skin),
                visualDensity: const VisualDensity(vertical: -4),
                title: Text(
                  S.of(Get.context!).charge_deposit_type_online,
                  style: MFont.regular16.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
                value: DepositType.online,
                groupValue: dt,
                controlAffinity: ListTileControlAffinity.leading,
                onChanged: (value) {
                  controller.uploadedProofUrl.value = '';
                  controller.depositType.value = value!;
                }),
            Obx(() {
              bool visible = controller.depositType.value == DepositType.online;
              return Builder(builder: (context) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Visibility(visible: visible, child: _payItemView),
                );
              });
            }),
            RadioListTile(
                dense: false,
                activeColor: MColor.skin,
                fillColor: MaterialStateProperty.all(MColor.skin),
                visualDensity: const VisualDensity(vertical: -4),
                title: Text(
                  S.of(Get.context!).charge_deposit_type_offline,
                  style: MFont.regular16.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
                value: DepositType.offline,
                groupValue: dt,
                controlAffinity: ListTileControlAffinity.trailing,
                onChanged: (value) {
                  controller.uploadedProofUrl.value = '';
                  controller.depositType.value = value!;
                }),
            Obx(() {
              bool visible = controller.depositType.value == DepositType.offline;
              return Builder(builder: (context) {
                return Visibility(visible: visible, child: _uploadView);
              });
            }),
            const SizedBox(height: 8),
          ],
        );
      });
    });
  }

  Obx get _textField {
    return Obx(() {
      int accountType = controller.accountType.value;
      return Builder(builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 15),
            Text(
              S.of(Get.context!).charge_money,
              style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 6,
              ),
              child: TextField(
                controller: controller.textController,
                minLines: 1,
                style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                textAlign: TextAlign.start,
                scrollPadding: EdgeInsets.zero,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: S.of(Get.context!).charge_hint,
                  hintStyle: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
                  filled: true,
                  suffixIcon: Text(
                    accountType == 1 ? '￥' : '\$',
                    style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                  ),
                  suffixIconConstraints: const BoxConstraints(
                    maxWidth: 20,
                    minWidth: 20,
                  ),
                  contentPadding: const EdgeInsets.only(left: 10, top: 10),
                  constraints: const BoxConstraints(minHeight: 44, maxHeight: 44),
                  fillColor: context.isDarkMode ? DarkColor.xFFF4F6F9 : MColor.xFFF4F6F9,
                  isDense: false,
                  focusedBorder: OutlineInputBorder(borderSide: BorderSide.none, borderRadius: BorderRadius.circular(8)),
                  enabledBorder: OutlineInputBorder(borderSide: BorderSide.none, borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],
        );
      });
    });
  }

  Widget get _payItemView {
    return Builder(builder: (context) {
      return Row(
        children: [
          Obx(() {
            int accountType = controller.accountType.value;
            return Image.asset(
              accountType == 1 ? Assets.zhifubao : Assets.paypalIcon,
              width: 30,
              height: 30,
            );
          }),
          const SizedBox(width: 4),
          Obx(() {
            int accountType = controller.accountType.value;
            return Builder(builder: (context) {
              return Text(
                accountType == 1 ? S.of(Get.context!).pay_zfb : S.of(Get.context!).pay_paypal,
                style: MFont.medium15.apply(
                  color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333,
                ),
              );
            });
          }),
          const Spacer(),
          Icon(
            Icons.done,
            size: 20,
            color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333,
          ),
        ],
      );
    });
  }

  Widget _textButton() {
    return Container(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextButton(
        onPressed: () {
          controller.fetchCharge();
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 40)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 40)),
        ),
        child: Text(
          S.of(Get.context!).charge_submit,
          style: MFont.medium15.apply(color: Colors.white),
        ),
      ),
    );
  }

  Widget get _uploadView {
    return Obx(() {
      var showUpload = controller.uploadedProofUrl.value.isEmpty;
      return Builder(builder: (context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Visibility(
              visible: showUpload,
              child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    controller.uploadProof();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: SizedBox(
                        height: 80,
                        child: _iconTextView(
                          Icons.camera_alt,
                          S.of(Get.context!).charge_upload_proof,
                          null,
                        )),
                  )),
            ),
            Visibility(visible: !showUpload, child: _imageView(controller.uploadedProofUrl.value)),
            // Visibility(visible: !showUpload, child: _proofView),
          ],
        );
      });
    });
  }

  Widget _iconTextView(IconData icon, String text, String? path) {
    return Builder(builder: (context) {
      return Container(
        decoration: BoxDecoration(
          color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
          borderRadius: BorderRadius.circular(6),
        ),
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383, size: 20),
            const SizedBox(width: 10),
            Text(
              text,
              style: MFont.regular12.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
            ),
          ],
        ),
      );
    });
  }

  void _pickerSingle() {
    var selectData = controller.accountType.value == 1 ? S.of(Get.context!).pay_rmb : S.of(Get.context!).pay_usd;
    return Pickers.showSinglePicker(
      Get.context!,
      selectData: selectData,
      data: [S.of(Get.context!).pay_rmb, S.of(Get.context!).pay_usd],
      pickerStyle: PickerStyle(
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
        itemOverlay: Builder(builder: (context) {
          return Container(
            decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
            ),
          );
        }),
      ),
      onConfirm: (value, index) {
        controller.accountType.value = index == 0 ? 1 : 2;
        controller.payType = index == 0 ? 3 : 2;
      },
    );
  }

  Widget _imageView(String imageUrl) {
    return Container(
        padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
        color: Theme.of(Get.context!).colorScheme.conatiner,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              var imageProvider = Image.network(imageUrl).image;
              // showImageViewer(
              //   Get.context!,
              //   imageProvider,
              //   onViewerDismissed: () {},
              //   immersive: false,
              //   useSafeArea: true,
              // );
            },
            child: Builder(builder: (context) {
              return Container(
                  color: Theme.of(Get.context!).colorScheme.conatiner,
                  child: Stack(
                    children: [
                      CachedNetworkImage(
                        imageUrl: imageUrl,
                        fit: BoxFit.contain,
                        placeholder: (ctx, _) {
                          return const Center(
                            child: SpinKitCircle(
                              color: MColor.skin,
                              size: 40.0,
                            ),
                          );
                        },
                        errorWidget: (ctx, a1, a2) {
                          return const Center(
                            child: SpinKitCircle(
                              color: MColor.skin,
                              size: 40.0,
                            ),
                          );
                        },
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                            onTap: () {
                              controller.uploadedProofUrl.value = '';
                            },
                            child: const Icon(
                              Icons.highlight_remove,
                              color: MColor.skin,
                              size: 25,
                            )),
                      ),
                    ],
                  ));
            }),
          ),
        ));
  }
}
