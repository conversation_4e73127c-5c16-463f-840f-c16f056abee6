import 'package:flutter/cupertino.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:tobias/tobias.dart';

import '../../../../../generated/l10n.dart';
import '../../../../tools/privacy_helper.dart';

enum DepositType { online, offline }

class ChargeController extends GetxController {
  TextEditingController textController = TextEditingController();
  final accountType = 1.obs;
  MineProvider provider = MineProvider();
  //2-paypal 3-支付宝
  var payType = 3;

  // 充值方式 0-线下转账 1-在线充值
  final depositType = DepositType.online.obs;

  final uploadedProofUrl = ''.obs;

  void fetchCharge() {
    var price = double.tryParse(textController.text) ?? 0;
    if (price <= 0) {
      showToast(S.of(Get.context!).charge_hint);
      return;
    }

    if (depositType.value == DepositType.offline && uploadedProofUrl.value.isEmpty) {
      showToast(S.of(Get.context!).charge_offline_nopic_hint);
      return;
    }

    EasyLoading.show();
    provider.recharge(depositType.value == DepositType.offline ? 0 : 1, price, payType, accountType.value, imageUrl: uploadedProofUrl.value).then((value) {
      if (value.isSuccess) {
        if (depositType.value == DepositType.offline) {
          showToast(S.of(Get.context!).pay_result_success);
          Get.back(result: true);
        } else {
          payAction(value.data);
        }
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void payAction(dynamic order) {
    if (payType == 3) {
      //zfb
      if (order['aliPayurl'] == null) {
        showToast(S.of(Get.context!).failed_get_payment_info);
        return;
      }
      var text = order['aliPayurl'] as String;
      Tobias tobias = Tobias();
      tobias.pay(text, evn: AliPayEvn.online).then((value) {
        if (value['resultStatus'] == '9000') {
          showToast(S.of(Get.context!).pay_result_success);
          Get.back(result: true);
        } else {
          showToast(S.of(Get.context!).pay_result_failed);
        }
        logger.e('value $value');
      });
    } else {}
  }

  @override
  void onClose() {
    textController.dispose();
  }

  Future<void> uploadProof() async {
    PermissionStatus ps = await Permission.camera.status;
    if (!ps.isGranted) {
      ps = await Permission.camera.request();
      if (!ps.isGranted) {
        showToast(S.of(Get.context!).enabled_camera);
        return;
      }
    }

    if (!hasShowCameraTips()) {
      await showCameraTips();
    }

    await FilesPicker.openImage(true, enableCrop: false).then((value) {
      // FilesPicker.openCamera().then((value) {
      if (value.isEmpty) {
        return;
      }
      EasyLoading.show();
      PublicProvider.uploadImages(value[0], UploadType.plain).then((url) {
        if (url == null || value.isEmpty) {
          showToast(S.of(Get.context!).profile_info_failed);
          // picturePath.value = '';
          return;
        }
        uploadedProofUrl.value = url;
      }).whenComplete(() {
        EasyLoading.dismiss();
      });
    });
  }
}
