import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mine/setting/controllers/setting_controller.dart';
import 'package:inspector/app/modules/store/setting_store.dart';

import '../../../../../generated/l10n.dart';
import '../../../new_home/home_new_controller.dart';

class LocalePage extends GetView<SettingController> {
  const LocalePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(S.of(Get.context!).language_page_title)),
      body: Builder(builder: (context) {
        List<Widget> items = List.generate(SettingStore.supportedLanguages.length, (index) {
          return ListTile(
            title: Text(SettingStore.supportedLanguages[index]),
            trailing: SettingStore.to.getCurrentLocaleId() == index ? const Icon(Icons.done) : null,
            onTap: () {
              SettingStore.to.updateLocale(index);

              if (GetInstance().isRegistered<HomeNewController>()) {
                GetInstance().find<HomeNewController>().getHomeList();
              }
              Get.back();
            },
          );
        });
        return ListView.separated(
          separatorBuilder: (context, index) {
            return Divider();
          },
          itemBuilder: (context, index) => items[index],
          itemCount: items.length,
        );
      }),
    );
  }
}
