import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mine/setting/controllers/setting_controller.dart';
import 'package:inspector/app/routes/app_navigator.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/UpgradeHelper.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/tools/translation_service.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../../generated/l10n.dart';
import '../../../store/setting_store.dart';

class SettingPage extends GetView<SettingController> {
  const SettingPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var settingCtrl = Get.find<SettingController>();

    return Builder(builder: (context) {
      return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(title: Text(S.of(Get.context!).setting_title)),
        body: Column(
          children: [
            _itemView(
              Assets.settingClearIcon,
              S.of(Get.context!).language_setting,
              [
                Text(
                  SettingStore.supportedLanguages[SettingStore.to.getCurrentLocaleId()],
                  style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                ),
              ],
              () => AppNavigator.locale(),
            ),
            _getDivider(0.5, context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, 15),
            _itemView(Assets.settingClearIcon, S.of(Get.context!).theme_title, [
              Text(
                '',
                style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              )
            ], () {
              AppNavigator.theme();
            }),
            _getDivider(0.5, context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, 15),
            _itemView(Assets.settingClearIcon, S.of(Get.context!).setting_clear_cache, [
              Obx(() {
                return Text(
                  controller.appCacheSize.value,
                  style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                );
              }),
            ], () {
              settingCtrl.clearCacheSize();
            }),
            _getDivider(0.5, context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, 15),
            _itemView(Assets.settingMsgIcon, S.of(Get.context!).setting_receive_msg, [
              CupertinoSwitch(
                  value: controller.receiveMsgSwitch,
                  onChanged: (select) {
                    controller.selectReceiveMsgSwitch();
                  })
            ], () {
              controller.selectReceiveMsgSwitch();
            }),
            _getDivider(0.5, context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, 15),
            _itemView(Assets.settingAboutusIcon, S.of(Get.context!).setting_check_update, [
              Text(
                '${GlobalConst.versionName}(${GlobalConst.versionCode})',
                style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              ),
            ], () {
              UpgradeHelper.alertUpdate(needToast: true);
            }),
            _getDivider(8.0, context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7, 0),
            _itemView(Assets.settingClearIcon, S.of(Get.context!).setting_privacy_policy, [
              Text(
                '',
                style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              ),
            ], () {
              Get.toNamed(
                Routes.WEB,
                parameters: {'url': '${Server.web}/privacy/registerpolicy.html', 'title': S.of(Get.context!).setting_privacy_policy},
              );
            }),
            _getDivider(0.5, context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, 15),
            _itemView(Assets.settingClearIcon, S.of(Get.context!).setting_user_agreement, [
              Text(
                '',
                style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              ),
            ], () {
              Get.toNamed(
                Routes.WEB,
                parameters: {'url': '${Server.web}/privacy/user.html', 'title': S.of(Get.context!).setting_user_agreement},
              );
            }),
            _getDivider(8.0, context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7, 0),
            _itemView(Assets.settingClearIcon, S.of(Get.context!).switch_account, [
              Text(
                '',
                style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              ),
            ], () {
              Get.toNamed(Routes.ACCOUNT_SWITCH);
            }),
            _getDivider(8.0, context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7, 0),
            _itemView(Assets.settingClearIcon, S.of(Get.context!).delete_account, [
              Text(
                '',
                style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
              ),
            ], () {
              showCustomDialog(S.of(Get.context!).setting_delete_account_tips, onConfirm: () {
                settingCtrl.deleteAccount();
              }, cancel: true);
            }),
            _getDivider(8.0, context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7, 0),
            TextButton(
              onPressed: () {
                showCustomDialog(S.of(Get.context!).setting_login_out_tips, onConfirm: () {
                  settingCtrl.loginout();
                }, cancel: true);
              },
              style: ButtonStyle(
                foregroundColor: MaterialStateProperty.all(context.isDarkMode ? DarkColor.xFFE95332 : MColor.xFFE95332),
                minimumSize: MaterialStateProperty.all(const Size.fromHeight(50)),
                textStyle: MaterialStateProperty.all(const TextStyle(fontWeight: FontWeight.bold)),
              ),
              child: Text(S.of(Get.context!).setting_login_out),
            ),
            Expanded(
                child: Container(
              color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
            ))
          ],
        ),
      );
    });
  }

  Widget _getDivider(double hight, Color color, double paddingLeft) {
    return Padding(
      padding: EdgeInsets.fromLTRB(paddingLeft, 0, 0, 0),
      child: Divider(
        color: color,
        thickness: hight,
      ),
    );
  }

  Widget _itemView(String icon, String text, List<Widget> endView, VoidCallback action) {
    return InkWell(
      onTap: () => action(),
      child: Builder(builder: (context) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          child: Row(
            children: [
              // Image.asset(icon, width: 27, height: 27),
              // SizedBox(width: 18),
              Text(
                text,
                style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              ),
              const Spacer(),
              ...endView,
              Icon(
                Icons.keyboard_arrow_right_outlined,
                size: 24,
                color: context.isDarkMode ? DarkColor.xFFBBBBBB : MColor.xFFBBBBBB,
              ),
            ],
          ),
        );
      }),
    );
  }
}
