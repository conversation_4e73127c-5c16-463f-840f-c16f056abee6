// ignore_for_file: always_declare_return_types, no_leading_underscores_for_local_identifiers

import 'dart:async';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:inspector/app/config/design.dart';

import '../../../../../generated/l10n.dart';

class ThemePage extends StatefulWidget {
  const ThemePage({Key? key}) : super(key: key);
  @override
  State<ThemePage> createState() => _ThemePageState();
}

class _ThemePageState extends State<ThemePage> {
  // ignore: prefer_final_fields
  Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
  ThemeMode _themeMode = ThemeMode.system; // 默认为跟随系统

  @override
  void initState() {
    super.initState();
    _getThemeStatus(); // 获取用户之前的主题选择
  }

  _saveThemeStatus() async {
    SharedPreferences pref = await _prefs;
    unawaited(pref.setInt('theme', _themeMode.index)); // 保存主题模式的索引值
  }

  _getThemeStatus() async {
    SharedPreferences pref = await _prefs;
    var _themeIndex = pref.getInt('theme');
    // 根据索引值获取主题模式
    _themeMode = _themeIndex == 0
        ? ThemeMode.system
        : _themeIndex == 1
            ? ThemeMode.light
            : ThemeMode.dark;
    Get.changeThemeMode(_themeMode);
    setState(() {});
  }

  void _changeTheme(ThemeMode? mode) {
    setState(() {
      _themeMode = mode!; // 改变主题模式
      Get.changeThemeMode(_themeMode); // 应用主题模式
      _saveThemeStatus(); // 保存主题模式
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).theme_title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            ListTile(
              title: Text(S.of(Get.context!).theme_auto),
              trailing: _themeMode == ThemeMode.system ? const Icon(Icons.done) : null,
              onTap: () => _changeTheme(ThemeMode.system),
            ),
            const Divider(height: 0.5),
            ListTile(
              title: Text(S.of(Get.context!).theme_light),
              trailing: _themeMode == ThemeMode.light ? const Icon(Icons.done) : null,
              onTap: () => _changeTheme(ThemeMode.light),
            ),
            const Divider(height: 0.5),
            ListTile(
              title: Text(S.of(Get.context!).theme_dark),
              trailing: _themeMode == ThemeMode.dark ? const Icon(Icons.done) : null,
              onTap: () => _changeTheme(ThemeMode.dark),
            ),
            Expanded(child: Container(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7))
          ],
        ),
      ),
    );
  }
}
