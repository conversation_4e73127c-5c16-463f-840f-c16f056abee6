import 'dart:io';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/auth/models/saved_account_entity.dart';
import 'package:inspector/app/modules/message/socket/socket_channel.dart';
import 'package:inspector/app/modules/new_home/home_new_controller.dart';
import 'package:inspector/app/modules/store/setting_store.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/cachUtils.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/tools/translation_service.dart';

import 'package:path_provider/path_provider.dart';

import '../../../../../generated/l10n.dart';
import '../../../../tools/storage_util.dart';
import '../../../auth/account_service.dart';

class SettingController extends GetxController {
  final provider = MineProvider();
  //缓存文件大小
  final appCacheSize = '0.0MB'.obs;

  //接收消息的开关
  var receiveMsgSwitch = true;

  @override
  void onInit() {
    super.onInit();
    receiveMsgSwitch = StorageUtil.getBool(Constant.receiveMsg, defValue: true);
    getCachSize();
  }

  Directory? _tempDir;
  //获取缓存大小
  Future<void> getCachSize() async {
    _tempDir ??= await getTemporaryDirectory();
    num cache = await getTotalSizeOfFilesInDir(_tempDir!);
    appCacheSize.value = renderSize(cache);
  }

  Future<void> clearCacheSize() async {
    await EasyLoading.show();
    await requestPermission(_tempDir ?? await getTemporaryDirectory());
    var file = await getTemporaryDirectory();
    await _delete(file).catchError((e) {});
    await EasyLoading.dismiss();
    showToast(S.of(Get.context!).setting_clear_success);
    await getCachSize();
  }

  /// 递归删除缓存目录和文件
  Future<void> _delete(FileSystemEntity file) async {
    if (file is Directory) {
      List<FileSystemEntity> children = file.listSync();
      for (FileSystemEntity child in children) {
        await _delete(child);
      }
    } else {
      await file.delete();
    }
  }

  ///修改开关状态
  Future<void> selectReceiveMsgSwitch() async {
    receiveMsgSwitch = !receiveMsgSwitch;
    await StorageUtil.setBool(Constant.receiveMsg, receiveMsgSwitch);
    update();
  }

  ///退出登录
  void loginout() {
    int? userId = GlobalConst.tempModel?.uid;
    GlobalConst.tempModel = null;
    StorageUtil.remove(Constant.kUser);
    AccountService.instance.saveToken('');
    if (userId != null) {
      AccountService.instance.removeSavedAccount(userId);
    }
    SocketChannel.stopConnect();
    Get.offAllNamed(Routes.AUTH_LOGIN);
  }

  void deleteAccount() {
    showCustomDialog(
      S.of(Get.context!).delete_account_confirm,
      onConfirm: () async {
        // await AccountService.instance.removeSavedAccount(SavedAccount('', '', '', GlobalConst.userModel!.uid!));
        await MineProvider().deleteAccount();
        showToast(S.of(Get.context!).delete_account_result);
        loginout();
      },
      cancel: true,
    );
  }
}
