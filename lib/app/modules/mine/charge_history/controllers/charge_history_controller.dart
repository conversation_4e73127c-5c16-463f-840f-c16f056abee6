import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:inspector/app/data/charge_history_entity.dart';
import 'package:inspector/app/data/withdraw_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ChargeHistoryController extends GetxController {
  RefreshController refreshController = RefreshController();
  final provider = MineProvider();
  int page = 1;

  final historyList = RxList<ChargeHistoryEntity>([]);
  @override
  void onInit() {
    super.onInit();
    fetchChargeHistory(page);
  }

  void loadMore() {
    page++;
    fetchChargeHistory(page);
  }

  Future<void> fetchChargeHistory(int page) async {
    var size = 0;
    await provider.rechargeList(page).then((value) async {
      if (value.isSuccess) {
        size = value.data?.length ?? 0;
        historyList.addAll(value.data!);
      } else {
        showToast(value.message!);
      }
    }).whenComplete(() {
      refreshController.refreshCompleted();
      if (size == 0) {
        refreshController.loadNoData();
      } else {
        refreshController.loadComplete();
      }
    });
  }

  @override
  void onClose() {
    refreshController.dispose();
  }
}
