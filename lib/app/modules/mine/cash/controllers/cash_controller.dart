import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/data/bank_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/mine/wallet/controllers/wallet_controller.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/wallet/wallet_provider.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../../generated/l10n.dart';

class CashController extends GetxController {
  TextEditingController textController = TextEditingController();
  TextEditingController invoiceController = TextEditingController();
  final accountType = 1.obs;
  final price = 0.0.obs;
  final invoicePrice = 0.0.obs;
  final accountId = 0.obs;
  Rx<BankEntity> bank = BankEntity().obs;
  final invoiceUrls = <String>[].obs;
  //2-PayPai 4-微信 5-支付宝 6银行卡
  final type = 0.obs;
  MineProvider provider = MineProvider();
  final icons = [
    Assets.bankIcon,
    Assets.wechatIcon,
    Assets.alipayIcon,
    Assets.paypalIcon,
  ];
  final titles = [
    S.of(Get.context!).wallet_bank,
    S.of(Get.context!).wallet_wechat,
    S.of(Get.context!).wallet_alipay,
    S.of(Get.context!).pay_paypal,
  ];

  final values = ['0', '', '', '', ''].obs;

  void fetchCsh() {
    if (price <= 0) {
      showToast(S.of(Get.context!).cash_hint);
      return;
    }

    // double balance = (accountType.value == 1 ? WalletProvider.instance.walletModel.value.rmbAmount : WalletProvider.instance.walletModel.value.usdAmount) ?? 0;
    // if (price > balance) {
    //   showToast(S.of(Get.context!).withdrawal_balance);
    //   return;
    // }

    var invoices = jsonEncode(invoiceUrls.value);
    if (invoiceUrls.isNotEmpty) {}
    EasyLoading.show();
    provider
        .takeCash(accountType.value, bank.value.id ?? 0, price.value, type.value, invoices, invoiceUrls.isNotEmpty ? invoicePrice.value : 0.0)
        .then((value) {
      if (value.isSuccess) {
        Get.back();
      }
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  @override
  void onClose() {
    textController.dispose();
  }
}
