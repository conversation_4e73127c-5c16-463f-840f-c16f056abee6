import 'package:flutter/material.dart';

import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/bank_entity.dart';
import 'package:inspector/app/data/bind_pay_entity.dart';
import 'package:inspector/app/modules/widgets/upload/UploadWidget.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/modules/mine/cash/controllers/cash_controller.dart';
import 'package:inspector/app/modules/mine/wallet/controllers/wallet_controller.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/wallet/wallet_provider.dart';

import '../../../../../generated/l10n.dart';

class CashView extends GetView<CashController> {
  const CashView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(S.of(Get.context!).cash_title),
          centerTitle: true,
          actions: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                Get.toNamed(Routes.WITHDRAW_LIST);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                alignment: Alignment.center,
                child: Text(
                  S.of(Get.context!).withdraw_list_title,
                  style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                ),
              ),
            ),
          ],
        ),
        resizeToAvoidBottomInset: false,
        body: Column(
          children: [
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: [
                  const SizedBox(height: 15),
                  _accountView,
                  const SizedBox(height: 14),
                  _textView,
                  const SizedBox(height: 22),
                  Text(
                    S.of(Get.context!).cash_money,
                    style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                  ),
                  const SizedBox(height: 5),
                  _textField(controller.textController, S.of(Get.context!).cash_hint, (text) {
                    controller.price.value = double.tryParse(text) ?? 0;
                  }),
                  const SizedBox(
                    height: 12,
                  ),
                  Text(
                    S.of(Get.context!).cash_invoice_upload,
                    style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                  ),
                  const SizedBox(height: 5),
                  Obx(() {
                    return UploadWidget.generateUploadWidget(controller.invoiceUrls, true, (url) {
                      controller.invoiceUrls.add(url);
                      controller.invoiceUrls.refresh();
                    }, (position) {
                      controller.invoiceUrls.removeAt(position);
                      controller.invoiceUrls.refresh();
                    }, showRemove: true);
                  }),
                  Obx(() {
                    return Visibility(
                        visible: controller.invoiceUrls.isNotEmpty,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(
                              height: 12,
                            ),
                            Text(
                              S.of(Get.context!).cash_invoice_money,
                              style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                            _textField(controller.invoiceController, S.of(Get.context!).cash_invoice_money_hint, (text) {
                              controller.invoicePrice.value = double.tryParse(text) ?? 0;
                            }),
                          ],
                        ));
                  }),
                  const SizedBox(height: 22),
                  _accountList,
                  const SizedBox(height: 22),
                  // for (int i = 0; i < controller.icons.length; i++) ...{
                  //   _iconText(controller.icons[i], controller.titles[i], '', i),
                  // },
                ],
              ),
            ),
            _bottomView,
          ],
        ),
      ),
    );
  }

  GestureDetector get _accountView {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        _pickerSingle();
      },
      child: Builder(builder: (context) {
        return Row(
          children: [
            Text(
              S.of(Get.context!).cash_account,
              style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
            ),
            Icon(Icons.arrow_drop_down_sharp, size: 24, color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
          ],
        );
      }),
    );
  }

  Obx get _textView {
    return Obx(() {
      bool isUSD = controller.accountType.value == 2;
      double balance = isUSD ? WalletProvider.instance.walletModel.value.usdAmount! : WalletProvider.instance.walletModel.value.rmbAmount!;
      Widget current = Text(controller.accountType.value == 1 ? S.of(Get.context!).pay_rmb : S.of(Get.context!).pay_usd);
      current = Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          current,
          Text(balance.toStringAsFixed(2), style: MFont.medium16),
        ],
      );
      return current;
    });
  }

  Widget _textField(TextEditingController textEditingController, String textHint, ValueChanged<String>? valueChanged) {
    return Obx(() {
      bool isRmb = controller.accountType.value == 1;
      return Builder(builder: (context) {
        return TextField(
          controller: textEditingController,
          minLines: 1,
          style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
          textAlign: TextAlign.start,
          scrollPadding: EdgeInsets.zero,
          keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
          inputFormatters: [PrecisionLimitFormatter(2)],
          decoration: InputDecoration(
            hintText: textHint,
            hintStyle: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
            filled: true,
            suffixIcon: Text(
              isRmb ? '￥' : '\$',
              style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            ),
            suffixIconConstraints: const BoxConstraints(
              maxWidth: 20,
              minWidth: 20,
            ),
            contentPadding: const EdgeInsets.only(left: 10, top: 10),
            constraints: const BoxConstraints(minHeight: 44, maxHeight: 44),
            fillColor: context.isDarkMode ? DarkColor.xFFF4F6F9 : MColor.xFFF4F6F9,
            isDense: false,
            focusedBorder: OutlineInputBorder(borderSide: BorderSide.none, borderRadius: BorderRadius.circular(8)),
            enabledBorder: OutlineInputBorder(borderSide: BorderSide.none, borderRadius: BorderRadius.circular(8)),
          ),
          onChanged: valueChanged,
        );
      });
    });
  }

  Widget get _accountList {
    dynamic withdrawMethods = Get.arguments;
    List<BankEntity>? banks = withdrawMethods['bank'];
    BindPayEntity? wechat = withdrawMethods['wechat'];
    BindPayEntity? alipay = withdrawMethods['alipay'];
    BindPayEntity? paypal = withdrawMethods['paypal'];
    return Builder(builder: (context) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(Get.context!).cash_account_list_title,
            style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
          ),
          if (banks != null && banks.isNotEmpty) ...{
            // banks.forEach((bank) {
            //   _accountItem(controller.icons[0], controller.titles[0], controller.values[0]);
            // })
            for (int i = 0; i < banks.length; i++) ...{
              _accountItem(controller.icons[0], controller.titles[0], banks[i].bankCode ?? ''),
            }
          },
          if (wechat != null) ...{
            _accountItem(controller.icons[1], controller.titles[1], wechat.account ?? ''),
          },
          if (alipay != null) ...{
            _accountItem(controller.icons[2], controller.titles[2], alipay.account ?? ''),
          },
          if (paypal != null) ...{
            _accountItem(controller.icons[3], controller.titles[3], paypal.account ?? ''),
          }
        ],
      );
    });
  }

  Widget _accountItem(String icon, String title, String value) {
    return Builder(builder: (context) {
      return Container(
        padding: EdgeInsets.only(
          left: 7,
          right: 16,
          top: 16,
          bottom: 16,
        ),
        child: Row(
          children: [
            Image.asset(icon, width: 16, height: 16),
            const SizedBox(width: 13),
            Text(
              title,
              style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            ),
            Expanded(
              child: Text(
                value,
                style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                textAlign: TextAlign.right,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget get _bottomView {
    return Obx(() {
      bool isRmb = controller.accountType.value == 1;
      return Container(
        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 30),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Builder(builder: (context) {
                  return Text(
                    S.of(Get.context!).cash_amount,
                    style: MFont.medium12.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                  );
                }),
                Obx(() {
                  String amount = controller.price.toStringAsFixed(2);
                  return Builder(builder: (context) {
                    return Text(
                      '${isRmb ? '￥' : '\$'}$amount',
                      style: MFont.medium17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                    );
                  });
                }),
                Builder(builder: (context) {
                  return Text(
                    S.of(Get.context!).cash_other,
                    style: MFont.medium10.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                  );
                }),
              ],
            ),
            const Spacer(),
            TextButton(
              onPressed: () {
                String message = S.of(Get.context!).cash_withdraw_tips1;
                if (controller.accountType.value == 1) {
                  message += S.of(Get.context!).pay_rmb;
                } else {
                  message += S.of(Get.context!).pay_usd;
                }
                message += S.of(Get.context!).cash_withdraw_tips2;
                message += controller.price.value.toString();
                showCustomDialog(
                  message,
                  onConfirm: () {
                    controller.fetchCsh();
                  },
                  cancel: true,
                );
              },
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(MColor.skin),
                shape: MaterialStateProperty.all(const StadiumBorder()),
                minimumSize: MaterialStateProperty.all(const Size(200, 40)),
                visualDensity: VisualDensity.compact,
                maximumSize: MaterialStateProperty.all(const Size(200, 40)),
              ),
              child: Text(
                S.of(Get.context!).charge_submit,
                style: MFont.medium15.apply(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    });
  }

  void showConfirm() {}

  void _pickerSingle() {
    var selectData = controller.accountType.value == 1 ? S.of(Get.context!).pay_rmb : S.of(Get.context!).pay_usd;
    return Pickers.showSinglePicker(
      Get.context!,
      selectData: selectData,
      data: [S.of(Get.context!).pay_rmb, S.of(Get.context!).pay_usd],
      pickerStyle: PickerStyle(
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
        itemOverlay: Builder(builder: (context) {
          return Container(
            decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
            ),
          );
        }),
      ),
      onConfirm: (value, index) {
        controller.accountType.value = index == 0 ? 1 : 2;
      },
    );
  }
}
