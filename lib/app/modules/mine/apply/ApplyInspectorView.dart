// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mine/apply/ApplyInspectorController.dart';
import 'package:inspector/app/modules/widgets/upload/UploadWidget.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/generated/assets.dart';

import '../../../../generated/l10n.dart';

class ApplyInspectorView extends GetView<ApplyInspectorController> {
  //0: 未提交 1:未审核 2：已审核  3:审核被拒
  var checkStatus = GlobalConst.userModel?.checkStatus ?? 0;

  ApplyInspectorView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context);
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(S.of(Get.context!).apply_title),
        centerTitle: true,
      ),
      body: Builder(builder: (context) {
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Stack(
            children: [
              SingleChildScrollView(
                child: SizedBox(
                  height: Get.height - media.padding.top - kToolbarHeight,
                  child: Column(
                    children: [
                      const Spacer(),
                      _nextButton,
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: Get.height - media.padding.top - kToolbarHeight - 84,
                child: Column(
                  children: [
                    if (checkStatus > 0) ...{
                      Container(
                        height: 40,
                        width: double.infinity,
                        color: context.isDarkMode ? DarkColor.x80E3E3E3 : MColor.xFFEEEEEE,
                        child: Center(
                          child: Text(
                            checkStatus == 1
                                ? S.of(Get.context!).apply_checking
                                : checkStatus == 2
                                    ? S.of(Get.context!).apply_check_success
                                    : S.of(Get.context!).apply_check_failed,
                            style: MFont.semi_Bold15.apply(color: checkStatus == 2 ? MColor.xFF25C56F : MColor.skin),
                          ),
                        ),
                      ),
                    },
                    Expanded(
                      child: ListView.separated(
                        shrinkWrap: true,
                        itemBuilder: (ctx, index) {
                          if (index == 5) {
                            return _inputFiled(index);
                          } else if (index == 6) {
                            return _inputFiled(index);
                          }
                          if (index < controller.titles.length) {
                            return _itemView(index, () {
                              if (index == 0) {
                              } else if (index == 1) {
                                _pickerSingle(index, [S.of(Get.context!).male, S.of(Get.context!).female]);
                              } else if (index == 2) {
                                _pickerDate(index);
                              } else if (index == 3) {
                                _pickerSingle(index, [
                                  S.of(Get.context!).elementary,
                                  S.of(Get.context!).junior,
                                  S.of(Get.context!).technical,
                                  S.of(Get.context!).senior,
                                  S.of(Get.context!).college,
                                  S.of(Get.context!).bachelor,
                                  S.of(Get.context!).master,
                                  S.of(Get.context!).doctor,
                                ]);
                              } else if (index == 4) {
                                _pickerAddress(index);
                              } else if (index == 5) {
                              } else if (index == 6) {}
                            });
                          } else if (index == 7) {
                            return _shebaoView;
                          } else if (index == 8) {
                            return _contentView;
                          } else if (index == 9) {
                            return _resumeView;
                          } else {
                            return _cardView;
                          }
                        },
                        separatorBuilder: (ctx, index) {
                          if (index == 8) {
                            return Container();
                          }
                          return Visibility(
                            visible: index < 3 ? false : true,
                            child: Divider(
                              thickness: index == 7 ? 10 : 0.5,
                              color: index == 7
                                  ? context.isDarkMode
                                      ? DarkColor.xFFF4F5F7
                                      : MColor.xFFF4F5F7
                                  : context.isDarkMode
                                      ? DarkColor.xFFE6E6E6
                                      : MColor.xFFE6E6E6,
                              indent: index == 7 ? 0 : 16,
                              height: index == 7 ? 10 : 0.5,
                            ),
                          );
                        },
                        itemCount: controller.titles.length + 4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _itemView(int index, VoidCallback action) {
    return Visibility(
      visible: index < 3 ? false : true,
      child: InkWell(
        onTap: () => action(),
        child: Builder(builder: (context) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                Text(
                  controller.titles[index],
                  style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Obx(() {
                    // final change = controller.valueChange.value;
                    return Text(
                      controller.values[index],
                      style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                      maxLines: 1,
                      textAlign: TextAlign.right,
                    );
                  }),
                ),
                Icon(
                  Icons.keyboard_arrow_right_outlined,
                  size: 24,
                  color: context.isDarkMode ? DarkColor.xFFBBBBBB : MColor.xFFBBBBBB,
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Container get _shebaoView {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Obx(() {
        bool shebao = controller.shebao.value;
        return Builder(builder: (context) {
          return Row(
            children: [
              Expanded(
                child: Text(
                  S.of(Get.context!).apply_shebao,
                  style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  controller.shebao.value = false;
                },
                child: SizedBox(
                  height: 40,
                  child: Row(
                    children: [
                      Icon(
                        shebao ? Icons.radio_button_off : Icons.radio_button_on,
                        color: MColor.skin,
                      ),
                      Builder(builder: (context) {
                        return Text(
                          S.of(Get.context!).no,
                          style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                        );
                      }),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 10),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  controller.shebao.value = true;
                },
                child: SizedBox(
                  height: 40,
                  child: Row(
                    children: [
                      Icon(
                        !shebao ? Icons.radio_button_off : Icons.radio_button_on,
                        color: MColor.skin,
                      ),
                      Builder(builder: (context) {
                        return Text(
                          S.of(Get.context!).yes,
                          style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ],
          );
        });
      }),
    );
  }

  Widget _inputFiled(int index) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Obx(() {
        var isUsd = controller.accountType.value == 1 ? false : true;
        return Builder(builder: (context) {
          return Row(
            children: [
              Text(
                '* ',
                style: MFont.medium16.apply(color: MColor.skin),
              ),
              Text(
                controller.titles[index],
                style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SizedBox(
                  height: 40,
                  child: TextField(
                    controller: index == 5 ? controller.editingController1 : controller.editingController2,
                    minLines: 1,
                    style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
                    textAlign: TextAlign.end,
                    scrollPadding: EdgeInsets.zero,
                    keyboardType: index == 5 ? const TextInputType.numberWithOptions(decimal: true, signed: true) : TextInputType.datetime,
                    decoration: InputDecoration(
                      hintText: '${S.of(Get.context!).bind_hint}${controller.titles[index]}',
                      hintStyle: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
                      filled: true,
                      fillColor: Colors.transparent,
                      isDense: false,
                      focusedBorder: const UnderlineInputBorder(borderSide: BorderSide.none),
                      enabledBorder: const UnderlineInputBorder(borderSide: BorderSide.none),
                    ),
                    onChanged: (text) {
                      controller.values[index] = text;
                    },
                  ),
                ),
              ),
              if (index == 5) ...{
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    controller.accountType.value = 1;
                  },
                  child: SizedBox(
                    height: 40,
                    child: Row(
                      children: [
                        Icon(
                          isUsd ? Icons.radio_button_off : Icons.radio_button_on,
                          color: MColor.skin,
                        ),
                        Builder(builder: (context) {
                          return Text(
                            '￥',
                            style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    controller.accountType.value = 2;
                  },
                  child: SizedBox(
                    height: 40,
                    child: Row(
                      children: [
                        Icon(
                          !isUsd ? Icons.radio_button_off : Icons.radio_button_on,
                          color: MColor.skin,
                        ),
                        Builder(builder: (context) {
                          return Text(
                            '\$',
                            style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              },
            ],
          );
        });
      }),
    );
  }

  Container get _contentView {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Builder(builder: (context) {
            return Text(
              S.of(Get.context!).apply_file,
              style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            );
          }),
          const SizedBox(height: 10),
          _textViewField,
        ],
      ),
    );
  }

  Container get _resumeView {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Builder(builder: (context) {
          return Text(
            S.of(Get.context!).apply_upload_file,
            style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
          );
        }),
        const SizedBox(height: 10),
        Obx(() {
          return UploadWidget.generateUploadWidget(controller.resumes, true, (url) {
            controller.resumes.add(url);
          }, (position) {
            controller.resumes.removeAt(position);
            controller.resumes.refresh();
          }, showRemove: true, uploadType: UploadType.resume);
        }),
      ]),
    );
  }

  Widget get _textViewField {
    return Builder(builder: (context) {
      return TextField(
        controller: controller.editingController,
        style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
        keyboardType: TextInputType.multiline,
        minLines: 3,
        maxLines: null,
        decoration: InputDecoration(
          hintText: S.of(Get.context!).apply_file_tip,
          hintStyle: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFFA2A2A2 : MColor.xFFA2A2A2),
          filled: true,
          isDense: false,
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          fillColor: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
          ),
        ),
        onChanged: (text) {
          controller.content.value = text;
        },
      );
    });
  }

  Widget get _cardView {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '',
            style: MFont.medium16.apply(color: MColor.skin),
          ),
          Builder(builder: (context) {
            return Text.rich(
              TextSpan(text: '* ', style: MFont.medium16.apply(color: MColor.skin), children: [
                TextSpan(
                  text: S.of(Get.context!).apply_upload_card,
                  style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                )
              ]),
            );
          }),
          const SizedBox(height: 10),
          Row(
            children: [
              Expanded(child: _cardImageView(true)),
              const SizedBox(width: 10),
              Expanded(child: _cardImageView(false)),
            ],
          )
        ],
      ),
    );
  }

  Widget _cardImageView(bool front) {
    return GestureDetector(
      onTap: () {
        controller.uploadIdCardImage(front, cropWidth: 155, cropHeight: 96);
      },
      child: AspectRatio(
        aspectRatio: 155.0 / 96,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(5),
          child: Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  front ? Assets.acrdFront : Assets.cardBack,
                ),
                fit: BoxFit.fill,
              ),
            ),
            child: Obx(() {
              var url = '';
              if (front) {
                url = controller.cardFrontUrl.value;
              } else {
                url = controller.cardBackUrl.value;
              }
              if (url.isEmpty) {
                return Builder(builder: (context) {
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_circle,
                        size: 26,
                        color: front
                            ? context.isDarkMode
                                ? DarkColor.xFF838383
                                : MColor.xFF838383
                            : MColor.skin,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        front ? S.of(Get.context!).apply_card_front : S.of(Get.context!).apply_card_back,
                        style: MFont.regular12.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                      ),
                    ],
                  );
                });
              } else if (url.isURL) {
                return CachedNetworkImage(
                  imageUrl: url,
                  fit: BoxFit.cover,
                  placeholder: (ctx, _) {
                    return const Center(
                      child: SpinKitCircle(
                        color: MColor.skin,
                        size: 40.0,
                      ),
                    );
                  },
                  errorWidget: (ctx, a1, a2) {
                    return const Center(
                      child: Icon(
                        Icons.link_off,
                        size: 40,
                        color: MColor.skin,
                      ),
                    );
                  },
                );
              } else {
                return const Center(
                  child: SpinKitCircle(
                    color: MColor.skin,
                    size: 40.0,
                  ),
                );
              }
            }),
          ),
        ),
      ),
    );
  }

  Widget get _nextButton {
    var text = checkStatus == 0 ? S.of(Get.context!).apply_submit : S.of(Get.context!).home_update;

    return Row(
      children: [
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 23, right: 23, bottom: 25, top: 10),
            child: TextButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(const StadiumBorder()),
                backgroundColor: MaterialStateProperty.all(MColor.skin),
                // minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
              ),
              onPressed: controller.fetchApply,
              child: Text(text, style: MFont.medium16.apply(color: Colors.white)),
            ),
          ),
        ),
        if (checkStatus == 2)
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 23, right: 23, bottom: 25, top: 10),
              child: TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all(const StadiumBorder()),
                  backgroundColor: MaterialStateProperty.all(MColor.skin),
                  // minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
                ),
                onPressed: controller.cancelApply,
                child: Text(S.of(Get.context!).revoke_inspector, style: MFont.medium16.apply(color: Colors.white)),
              ),
            ),
          ),
      ],
    );
  }

  void _pickerSingle(int index, List<String> data) {
    return Pickers.showSinglePicker(
      Get.context!,
      data: data,
      pickerStyle: PickerStyle(
        backgroundColor: Get.context!.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
        headDecoration: BoxDecoration(color: Get.context!.isDarkMode ? DarkColor.xFFCFCFCF : MColor.xFFF4F5F7),
        textColor: !Get.context!.isDarkMode ? DarkColor.xFFFFFFFF : MColor.xFFFFFFFF,
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
        itemOverlay: Builder(builder: (context) {
          return Container(
            decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFFCFCFCF : MColor.xFF838383)),
            ),
          );
        }),
      ),
      onConfirm: (value, _) {
        controller.values[index] = value;
      },
    );
  }

  void _pickerDate(int index) {
    return Pickers.showDatePicker(
      Get.context!,
      pickerStyle: PickerStyle(
        itemOverlay: Builder(builder: (context) {
          return Container(
            decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
            ),
          );
        }),
      ),
      onConfirm: (value) {
        controller.values[index] = '${value.year.toString()}-${value.month.toString().padLeft(2, '0')}-${value.day.toString().padLeft(2, '0')}';
      },
    );
  }

  void _pickerAddress(int index) {
    return Pickers.showAddressPicker(
      Get.context!,
      pickerStyle: PickerStyle(
        backgroundColor: Get.context!.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
        headDecoration: BoxDecoration(color: Get.context!.isDarkMode ? DarkColor.xFFCFCFCF : MColor.xFFF4F5F7),
        textColor: !Get.context!.isDarkMode ? DarkColor.xFFFFFFFF : MColor.xFFFFFFFF,
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
        itemOverlay: Builder(builder: (context) {
          return Container(
            decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFFCFCFCF : MColor.xFF838383)),
            ),
          );
        }),
      ),
      initTown: '',
      addAllItem: false,
      onConfirm: (province, city, area) {
        controller.province.value = province;
        controller.city.value = city;
        controller.area.value = area ?? '';
        controller.values[index] = '$province$city$area';
      },
    );
  }
}
