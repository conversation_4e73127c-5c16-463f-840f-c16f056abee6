import 'package:flutter/cupertino.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';
import '../../../tools/privacy_helper.dart';

class ApplyInspectorController extends GetxController {
  TextEditingController editingController = TextEditingController();
  TextEditingController editingController1 = TextEditingController();
  TextEditingController editingController2 = TextEditingController();
  final accountType = 1.obs;

  MineProvider provider = MineProvider();
  List<String> titles = [
    S.of(Get.context!).apply_nick,
    S.of(Get.context!).apply_sex,
    S.of(Get.context!).apply_birthday,
    S.of(Get.context!).apply_education,
    S.of(Get.context!).apply_address,
    S.of(Get.context!).apply_price,
    S.of(Get.context!).apply_id_card,
  ];
  final values = ['', '', '', '', '', '', '', '', '', ''].obs;
  var cardFrontPath = '';
  var cardBackPath = '';
  var content = ''.obs;
  final cardFrontUrl = ''.obs;
  final cardBackUrl = ''.obs;
  final province = ''.obs;
  final city = ''.obs;
  final area = ''.obs;
  final valueChange = false.obs;
  final enable = false.obs;
  final shebao = false.obs;
  final resumes = RxList<String>([]);

  @override
  void onReady() {
    super.onReady();

    fetchApplyInfo();
  }

  void dataState(bool front, String value) {
    if (front) {
      cardFrontPath = value;
      cardFrontUrl.value = value;
    } else {
      cardBackPath = value;
      cardBackUrl.value = value;
    }
  }

  bool validInfo() {
    enable.value = false;
    if (values[5].isEmpty) {
      showToast(S.of(Get.context!).inspector_min_fee);
      return false;
    }
    if (values[6].isEmpty) {
      showToast(S.of(Get.context!).inspector_id_card_required);
      return false;
    }

    if (cardFrontUrl.value.isEmpty || cardBackUrl.value.isEmpty) {
      showToast(S.of(Get.context!).inspector_id_card_upload);
      return false;
    }

    if (!cardFrontUrl.value.isURL || !cardBackUrl.value.isURL) {
      showToast(S.of(Get.context!).inspector_id_card_upload_fail);
      return false;
    }

    enable.value = true;
    return true;
  }

  Future<void> fetchApply() async {
    bool isValid = validInfo();
    if (!isValid) return;

    await provider
        .apply(
      values[0],
      values[1],
      values[2],
      province.value,
      city.value,
      area.value,
      values[5],
      values[3],
      values[6],
      cardFrontUrl.value,
      cardBackUrl.value,
      content.value,
      accountType.value,
      shebao.value,
    )
        .then((value) {
      if (value.isSuccess) {
        Get.back(result: true);
      }
      showToast(value.message ?? '');
    });
  }

  Future<void> cancelApply() async {
    showCustomDialog(S.of(Get.context!).inspector_revoke, onConfirm: () {
      provider.cancel().then((value) {
        showToast(S.of(Get.context!).inspector_revoke_completed);
        Get.back();
      });
    }, cancel: true);
  }

  Future<bool> fetchAuthCard(bool front, String url) async {
    String type = front ? 'front' : 'back';
    var result = await provider.authIDCard(url, type);
    if (result.isSuccess) {
      var card = result.data['id_card_number'] as String?;
      if (front && card != null) {
        editingController2.text = card;
        values[6] = card;
      }
      if ((card == null || card.isEmpty) && front) {
        showToast(S.of(Get.context!).apply_auth_failed);
        return Future.value(false);
      } else {
        return Future.value(true);
      }
    } else {
      showToast(result.message ?? '');
      return Future.value(false);
    }
  }

  void fetchApplyInfo() {
    EasyLoading.show();
    provider.applyInfo().then((value) {
      if (value.isSuccess) {
        province.value = value.data?.province ?? '';
        city.value = value.data?.city ?? '';
        area.value = value.data?.area ?? '';
        values[4] = '${province.value}${city.value}${area.value}';
        values[5] = value.data?.price ?? '';
        values[3] = value.data?.education ?? '';
        values[6] = value.data?.idCardNum ?? '';
        cardFrontUrl.value = value.data?.idCardFront ?? '';
        cardBackUrl.value = value.data?.idCardBack ?? '';
        content.value = value.data?.content ?? '';
        accountType.value = value.data?.accountType ?? 1;
        shebao.value = value.data?.socialSecurity ?? false;
        editingController.text = content.value;
        editingController1.text = value.data?.price ?? '';
        editingController2.text = value.data?.idCardNum ?? '';
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  Future<void> uploadIdCardImage(bool front, {int cropWidth = 1, int cropHeight = 1}) async {
    if (!hasShowCameraTips()) {
      await showCameraTips();
    }
    var value = await FilesPicker.openImage(false, cropWidth: cropWidth, cropHeight: cropHeight);
    if (value.isEmpty) {
      return;
    }
    dataState(front, value[0]);
    var url = await PublicProvider.uploadImages(value[0], UploadType.iden);
    if (url == null || value.isEmpty) {
      showToast(S.of(Get.context!).profile_info_failed);
      dataState(front, '');
      return;
    }
    var tempUrl = url;
    if (front) {
      await fetchAuthCard(front, tempUrl).then((result) {
        if (result) {
          dataState(front, tempUrl);
        } else {
          dataState(front, '');
        }
      });
    } else {
      dataState(front, tempUrl);
    }
  }

  @override
  void onClose() {
    editingController.dispose();
    editingController1.dispose();
    editingController2.dispose();
  }
}

class ResumeInfo {
  String resumeUrl = '';
  String resumeTitle = '';
  ResumeType type = ResumeType.pic;
}

enum ResumeType { pic, doc }
