import 'package:flutter/material.dart';

import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/modules/mine/address_list/address_list_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class AddressListView extends GetView<AddressListController> {
  const AddressListView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).address_list_title),
        centerTitle: true,
      ),
      body: Obx(() {
        return Column(
          children: [
            _searchInput,
            Expanded(
              child: ListView.builder(
                itemCount: controller.addressList.isEmpty ? 1 : controller.addressList.length,
                itemBuilder: (ctx, index) {
                  if (controller.addressList.isEmpty) {
                    return SizedBox(
                      width: double.infinity,
                      height: Get.height / 2,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.hourglass_empty,
                              size: 40,
                              color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                            ),
                            const SizedBox(height: 10),
                            Text(S.of(Get.context!).no_data, style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
                          ],
                        ),
                      ),
                    );
                  }
                  return _addressItem(index);
                },
              ),
            ),
            _textButton,
          ],
        );
      }),
    );
  }

  Widget _addressItem(int index) {
    AddressRows address = controller.addressList[index];
    var name = address.name ?? '';
    var phone = address.phone ?? '';
    // phone = phone.replaceRange(3, 7, '****');
    var province = address.province ?? '';
    var city = address.city ?? '';
    var area = address.area ?? '';
    var factory = address.factoryName;
    var detail = address.address ?? '';
    var text = '$province-$city-$area-$detail';
    return Builder(builder: (context) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Slidable.of(Get.context!)?.close();
          if (controller.isManager) {
            Get.toNamed(Routes.ADDRESS, arguments: address)?.then((value) {
              controller.fetchAddressList();
            });
            return;
          }
          Get.back(result: {'addressInfo': address});
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
          width: double.infinity,
          decoration: BoxDecoration(
            border: controller.addressList.length <= 1
                ? null
                : Border(bottom: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, width: 0.5)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '$name $phone',
                      style: MFont.semi_Bold13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.toNamed(Routes.ADDRESS, arguments: address)?.then((value) {
                        controller.fetchAddressList();
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      child: Icon(
                        Icons.edit_location_outlined,
                        size: 25,
                        color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333,
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      showCustomDialog(S.of(Get.context!).address_delete_tips, onConfirm: () {
                        controller.fetchAddressDelete(address.id ?? 0);
                      }, cancel: true);
                    },
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      child: Icon(
                        Icons.delete_forever_sharp,
                        size: 25,
                        color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                text.fixAutoLines(),
                style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              ),
              const SizedBox(height: 6),
              Text(
                '${S.of(Get.context!).address_name}: $factory',
                style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              ),
            ],
          ),
        ),
      );
    });
  }

  Container get _textButton {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18, bottom: 30),
      child: TextButton(
        onPressed: () {
          Get.toNamed(Routes.ADDRESS, arguments: false)?.then((value) {
            controller.fetchAddressList();
          });
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          S.of(Get.context!).address_insert,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }

  Widget get _searchInput {
    Widget input = Builder(builder: (context) {
      return TextField(
        controller: controller.txtSearchController,
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
          prefixIcon: Icon(
            Icons.search,
            size: 24,
            color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
          ),
          fillColor: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
          filled: true,
          isDense: true,
          hintText: S.of(Get.context!).address_keyword,
          hintStyle: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
          labelStyle: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE, width: 0),
            borderRadius: BorderRadius.circular(5),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE, width: 0),
          ),
          constraints: const BoxConstraints(maxHeight: 40),
        ),
        onChanged: (val) {
          if (val.trim() == '') controller.fetchAddressList();
        },
      );
    });

    Widget button = TextButton(
      onPressed: controller.fetchAddressList,
      style: ButtonStyle(
        backgroundColor: MaterialStateProperty.all(MColor.skin),
        shape: MaterialStateProperty.all(const StadiumBorder()),
        visualDensity: VisualDensity.compact,
        maximumSize: MaterialStateProperty.all(const Size(64.0, 40.0)),
        minimumSize: MaterialStateProperty.all(const Size(64.0, 40.0)),
      ),
      child: Text(S.of(Get.context!).search, style: MFont.medium14.apply(color: Colors.white)),
    );

    return Container(
      margin: const EdgeInsets.fromLTRB(16.0, 12.0, 16.0, 0.0),
      child: Row(children: [Expanded(child: input), const SizedBox(width: 8.0), button]),
    );
  }
}
