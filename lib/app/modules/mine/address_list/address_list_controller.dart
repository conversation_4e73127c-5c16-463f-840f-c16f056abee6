import 'package:flutter/material.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class AddressListController extends GetxController {
  MineProvider provider = MineProvider();
  int page = 0;
  int total = 0;
  final addressList = <AddressRows>[].obs;
  bool isManager = false;
  RefreshController refreshController = RefreshController();
  TextEditingController txtSearchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();

    isManager = Get.arguments ?? false;
    fetchAddressList();
  }

  void freshData() {
    page = 0;
    fetchAddressList();
  }

  void loadMore() {
    if (addressList.length >= total) {
      refreshController.loadComplete();
      refreshController.refreshCompleted();
      return;
    } else {
      page++;
      fetchAddressList();
    }
  }

  void fetchAddressList() {
    EasyLoading.show();
    provider.addressList(txtSearchController.text.trim(), page, 10).then((value) {
      if (value.isSuccess) {
        addressList.value = value.data?.rows ?? [];
        total = value.data?.total ?? 0;
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
      refreshController.loadComplete();
      refreshController.refreshCompleted();
    });
  }

  void fetchAddressDelete(int id) {
    EasyLoading.show();
    provider.addressDelete(id).then((value) {
      if (value.isSuccess) {
        fetchAddressList();
      }
      showToast(value.message ?? '');
    }).catchError((e) {
      showToast(S.of(Get.context!).address_delete_result);
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  @override
  void onClose() {
    refreshController.dispose();
    txtSearchController.dispose();
  }
}
