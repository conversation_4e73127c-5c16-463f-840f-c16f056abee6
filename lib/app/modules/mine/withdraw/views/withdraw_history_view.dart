import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/withdraw_entity.dart';
import 'package:inspector/app/modules/mine/withdraw/controllers/withdraw_history_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../generated/l10n.dart';

class WithdrawHistoryView extends GetView<WithdrawHistoryController> {
  const WithdrawHistoryView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).withdraw_list_title),
        centerTitle: true,
      ),
      body: Obx(() {
        return SmartRefresher(
          controller: controller.refreshController,
          enablePullDown: false,
          onLoading: () => controller.loadMore(),
          enablePullUp: true,
          child: ListView.builder(
            itemCount: controller.withdrawList.length,
            itemBuilder: (ctx, index) {
              return _withdrawItem(controller.withdrawList[index]);
            },
          ),
        );
      }),
    );
  }

  Widget _withdrawItem(WithdrawEntity model) {
    var isRMB = model.type == 1 ? true : false;
    var title = isRMB ? S.of(Get.context!).withdraw_rmb : S.of(Get.context!).withdraw_usd;
    if (model.status == 1) {
      title += ' (${S.of(Get.context!).withdraw_status_checking})';
    } else if (model.status == 3) {
      title += ' (${S.of(Get.context!).withdraw_status_denied})';
    } else if (model.status == 2) {
      title += ' (${S.of(Get.context!).withdraw_status_approved} - ';
      if (model.makeStatus == 1) {
        title += '${S.of(Get.context!).withdraw_cash_status_unfinished})';
      } else if (model.makeStatus == 2) {
        title += '${S.of(Get.context!).withdraw_cash_status_done})';
      } else {
        title += '${S.of(Get.context!).withdraw_cash_status_refused})';
      }
    }

    return Builder(builder: (context) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 11, horizontal: 16),
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, width: 0.5)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: Builder(builder: (context) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            title,
                            style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Text(
                          '${isRMB ? '￥' : '\$'} ${model.amount}',
                          style: MFont.medium16.apply(color: MColor.skin),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          model.createAt ?? '-',
                          style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                        ),
                        Text(
                          '${S.of(Get.context!).wallet_balance} ${isRMB ? '￥' : '\$'} ${model?.balance?.toStringAsFixed(2) ?? ''}',
                          style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                        ),
                      ],
                    ),
                  ],
                );
              }),
            ),
          ],
        ),
      );
    });
  }
}
