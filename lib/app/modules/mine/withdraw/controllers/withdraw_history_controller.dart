import 'package:get/get.dart';

import 'package:inspector/app/data/withdraw_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class WithdrawHistoryController extends GetxController {
  RefreshController refreshController = RefreshController();
  final provider = MineProvider();
  int page = 1;

  final withdrawList = RxList<WithdrawEntity>([]);
  @override
  void onInit() {
    super.onInit();
    fetchWithdrawList(page);
  }

  void loadMore() {
    page++;
    fetchWithdrawList(page);
  }

  Future<void> fetchWithdrawList(int page) async {
    var size = 0;
    await provider.takeCashList(page).then((value) async {
      if (value.isSuccess) {
        size = value.data?.length ?? 0;
        withdrawList.addAll(value.data!);
      } else {
        showToast(value.message!);
      }
    }).whenComplete(() {
      refreshController.refreshCompleted();
      if (size == 0) {
        refreshController.loadNoData();
      } else {
        refreshController.loadComplete();
      }
    });
  }

  @override
  void onClose() {
    refreshController.dispose();
  }
}
