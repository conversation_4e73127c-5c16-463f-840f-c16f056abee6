import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/auth/login/controllers/auth_login_controller.dart';
import 'package:inspector/app/modules/mine/third_login_mgr/third_login_mgr_controller.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/assets.dart';
import '../../../../generated/l10n.dart';

class ThirdLoginMgrView extends GetView<ThirdLoginMgrController> {
  const ThirdLoginMgrView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(S.of(context).profile_bind_manage),
        leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(Icons.arrow_back_ios, size: 18)),
      ),
      body: SafeArea(
          child: Column(
        children: [
          Obx(() {
            return ListTile(
              onTap: () {
                if (controller.thirdAccountGoogle.value != null) {
                  showCustomDialog(S.of(Get.context!).confirm_unbind, onConfirm: () {
                    controller.unbind(1);
                  }, cancel: true);
                } else {
                  controller.bindGoogle();
                }
              },
              title: Text(S.of(context).account_google),
              leading: Image.asset(
                Assets.imageLogoGoogle,
              ),
              trailing: Text(controller.thirdAccountGoogle.value == null ? S.of(context).third_account_bind : S.of(context).third_account_unbind),
            );
          }),
          Divider(
            color: MColor.xFFEEEEEE,
          ),
          Obx(() {
            return ListTile(
              onTap: () {
                if (controller.thirdAccountApple.value != null) {
                  showCustomDialog(S.of(Get.context!).confirm_unbind, onConfirm: () {
                    controller.unbind(2);
                  }, cancel: true);
                } else {
                  if (Platform.isIOS) {
                    controller.bindApple();
                  } else {
                    showToast('Unsupported');
                  }
                }
              },
              title: Text(S.of(context).account_apple),
              leading: Image.asset(
                Assets.imageLogoApple,
              ),
              trailing: Text(controller.thirdAccountApple.value == null ? S.of(context).third_account_bind : S.of(context).third_account_unbind),
            );
          }),
          Divider(
            color: MColor.xFFEEEEEE,
          ),
          Obx(() {
            return ListTile(
              onTap: () {
                if (controller.thirdAccountWechat.value != null) {
                  showCustomDialog(S.of(Get.context!).confirm_unbind, onConfirm: () {
                    controller.unbind(LoginType.wechat.type);
                  }, cancel: true);
                } else {
                  controller.bindWechat();
                }
              },
              title: Text(S.of(context).account_wechat),
              leading: Image.asset(
                Assets.imageLogoWechat,
              ),
              trailing: Text(controller.thirdAccountWechat.value == null ? S.of(context).third_account_bind : S.of(context).third_account_unbind),
            );
          }),
          // Divider(
          //   color: MColor.xFFEEEEEE,
          // ),
          // Obx(() {
          //   return ListTile(
          //     onTap: () {
          //       if (controller.thirdAccountFacebook.value != null) {
          //         showCustomDialog(S.of(Get.context!).confirm_unbind, onConfirm: () {
          //           controller.unbind(4);
          //         }, cancel: true);
          //       } else {
          //         controller.bindFacebook();
          //       }
          //     },
          //     title: Text(S.of(context).account_facebook),
          //     leading: Image.asset(
          //       Assets.imageLogoFacebook,
          //     ),
          //     trailing: Text(controller.thirdAccountFacebook.value == null ? S.of(context).third_account_bind : S.of(context).third_account_unbind),
          //   );
          // })
        ],
      )),
    );
  }
}
