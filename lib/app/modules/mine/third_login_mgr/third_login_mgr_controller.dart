import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:inspector/app/modules/auth/login/controllers/auth_login_controller.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/auth/models/user_model.dart';
import 'package:inspector/app/modules/auth/third_login_store.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../../tools/loading.dart';
import '../../../tools/tools.dart';

class ThirdLoginMgrController extends GetxController implements WechatCallback {
  final thirdAccountGoogle = Rxn<ThirdLoginAccount>();
  final thirdAccountApple = Rxn<ThirdLoginAccount>();
  final thirdAccountWechat = Rxn<ThirdLoginAccount>();

  @override
  void onInit() {
    super.onInit();
    ThirdLoginStore.to.addWechatCallback(this);
    fetchUserInfo();
  }

  @override
  void onClose() {
    logger.i('ThirdLoginMgrController onClose');
    ThirdLoginStore.to.removeWechatCallback(this);
    super.onClose();
  }

  Future<void> fetchUserInfo() async {
    try {
      var resp = await MineProvider.getUserModel();
      if (resp.isSuccess) {
        var userModel = resp.data;
        if (userModel != null) {
          thirdAccountGoogle.value = userModel.loginAccounts?.firstWhereOrNull((element) => element.thirdType == 1);
          thirdAccountApple.value = userModel.loginAccounts?.firstWhereOrNull((element) => element.thirdType == 2);
          thirdAccountWechat.value = userModel.loginAccounts?.firstWhereOrNull((element) => element.thirdType == 3);
        }
      } else {
        showToast(resp.message ?? '');
        Get.back();
      }
    } catch (e) {
      showToast(e.toString());
      Get.back();
    } finally {}
  }

  Future<void> unbind(int type) async {
    Loading.show();
    try {
      var resp = await MineProvider.unbind(type);
      if (resp.isSuccess) {
        await fetchUserInfo();
      }
    } catch (_) {
    } finally {
      Loading.dismiss();
    }
  }

  void bindWechat() {
    ThirdLoginStore.to.signInWithWechat();
  }

  Future<void> bindApple() async {
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );
    await _bindThirdLogin(LoginType.apple.type.toString(), thirdUid: credential.userIdentifier, thirdData: credential.identityToken);
  }

  Future<void> bindGoogle() async {
    await GoogleSignIn().signOut();
    GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
    logger.i('singInWithGoogle user $googleUser');
    // Obtain the auth details from the request
    GoogleSignInAuthentication? googleAuth = await googleUser?.authentication;
    logger.i('singInWithGoogle accessTokenLength ${googleAuth?.accessToken?.length} accessToken:${googleAuth?.accessToken}');
    logger.i('singInWithGoogle idTokenLength ${googleAuth?.idToken?.length} idToken:${googleAuth?.idToken}');
    // Create a new credential
    var credential = GoogleAuthProvider.credential(
      accessToken: googleAuth?.accessToken,
      idToken: googleAuth?.idToken,
    );
    logger.i('singInWithGoogle credential $credential');
    // Once signed in, return the UserCredential
    var userCredential = await FirebaseAuth.instanceFor(app: ThirdLoginStore.to.firebaseApp!).signInWithCredential(credential);
    logger.i('singInWithGoogle ${userCredential.user?.uid} $userCredential');

    await _bindThirdLogin(
      LoginType.google.type.toString(),
      thirdUid: userCredential.user?.uid,
      thirdData: googleAuth?.idToken,
      thirdEmail: userCredential.user?.email,
    );
  }

  Future<void> _bindThirdLogin(String loginType, {String? thirdUid, String? thirdData, String? thirdEmail, String? thirdPhone, String? thirdArea}) async {
    Loading.show();
    try {
      var resp = await MineProvider.bindThird(thirdUid: thirdUid, thirdData: thirdData, thirdType: loginType);
      if (resp.isSuccess) {
        showToast('Bind Success');
        await fetchUserInfo();
      } else if (resp.code == 20006) {
      } else {
        showToast(resp.message ?? 'Failed');
      }
    } catch (_) {
    } finally {
      Loading.dismiss();
    }
  }

  @override
  void onLoginSuccess({required String thirdUid, required String thirdData}) {
    _bindThirdLogin(
      LoginType.wechat.type.toString(),
      thirdUid: thirdUid,
      thirdData: thirdData,
    );
  }
}
