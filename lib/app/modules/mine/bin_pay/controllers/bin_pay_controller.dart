import 'package:flutter/cupertino.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/data/bind_pay_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';
import '../../../../tools/privacy_helper.dart';

class BinPayController extends GetxController {
  final provider = MineProvider();
  TextEditingController editingController1 = TextEditingController();
  TextEditingController editingController2 = TextEditingController();
  //1-微信 2-支付宝 3-paypal
  final type = 1.obs;
  final imagePath = ''.obs;
  final imageUrl = ''.obs;
  final payInfo = BindPayEntity().obs;

  @override
  void onInit() {
    super.onInit();

    type.value = Get.arguments ?? 1;
    fetchPayInfo();
  }

  Future<void> uploadImage() async {
    if (!hasShowCameraTips()) {
      await showCameraTips();
    }
    var value = await FilesPicker.openImage(false);
    if (value.isEmpty) {
      return;
    }
    imagePath.value = value[0];

    var urls = await PublicProvider.uploadImages(value[0], UploadType.plain);
    if (urls == null || urls.isEmpty) {
      showToast(S.of(Get.context!).profile_info_failed);
      imagePath.value = value[0];
      return;
    }
    imageUrl.value = urls;
  }

  void fetchBind() {
    if (editingController1.text.isEmpty) {
      showToast('${S.of(Get.context!).please_enter}${S.of(Get.context!).bind_account}');
      return;
    }
    if (editingController2.text.isEmpty) {
      showToast('${S.of(Get.context!).please_enter}${S.of(Get.context!).bind_name}');
      return;
    }
    if ((type.value == 1 || type.value == 2) && imageUrl.value.isEmpty) {
      showToast('${S.of(Get.context!).please_enter}${S.of(Get.context!).bind_image}');
      return;
    }

    EasyLoading.show();
    provider.bindPay(editingController1.text, imageUrl.value, editingController2.text, type.value).then((value) async {
      if (value.isSuccess) {
        Get.back();
      }
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void fetchPayInfo() {
    provider.bindPayInfo().then((value) async {
      if (value.isSuccess) {
        payInfo.value = value.data?.firstWhere((element) => element.type == type.value, orElse: () => BindPayEntity()) ?? BindPayEntity();
        var isUrl = payInfo.value.image?.isURL ?? false;
        if (isUrl) {
          imagePath.value = '';
        }
        imageUrl.value = payInfo.value.image ?? '';
        editingController1.text = payInfo.value.account ?? '';
        editingController2.text = payInfo.value.name ?? '';
      } else {
        showToast(value.message ?? '');
      }
    });
  }

  @override
  void onClose() {
    editingController1.dispose();
    editingController2.dispose();
  }
}
