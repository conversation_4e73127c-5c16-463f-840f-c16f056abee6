import 'package:flutter/material.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mine/bin_pay/controllers/bin_pay_controller.dart';

import '../../../../../generated/l10n.dart';

class BinPayView extends GetView<BinPayController> {
  const BinPayView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).bind_title(
              controller.type.value == 1
                  ? S.of(Get.context!).wallet_wechat
                  : controller.type.value == 2
                      ? S.of(Get.context!).wallet_alipay
                      : S.of(Get.context!).pay_paypal,
            )),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                _textView('${S.of(Get.context!).bind_account}:', 0),
                _textView('${S.of(Get.context!).bind_name}:', 1),
                Container(
                  margin: const EdgeInsets.only(top: 20),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${S.of(Get.context!).bind_image}:',
                        style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                      ),
                      const SizedBox(height: 15),
                      _imageView,
                    ],
                  ),
                ),
              ],
            ),
          ),
          _textButton(),
        ],
      ),
    );
  }

  Widget _textView(String title, int index) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 50,
            child: Builder(builder: (context) {
              return Text(
                title,
                style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              );
            }),
          ),
          _textField(index),
        ],
      ),
    );
  }

  Widget _textField(int index) {
    return Builder(builder: (context) {
      return TextField(
        controller: index == 0 ? controller.editingController1 : controller.editingController2,
        minLines: 1,
        style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
        textAlign: TextAlign.start,
        scrollPadding: EdgeInsets.zero,
        decoration: InputDecoration(
          hintText: '${S.of(Get.context!).bind_hint}${index == 0 ? S.of(Get.context!).bind_account : S.of(Get.context!).bind_name}',
          hintStyle: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
          filled: true,
          contentPadding: const EdgeInsets.only(bottom: 0, top: 10),
          // constraints: BoxConstraints(minHeight: 30, maxHeight: 30),
          fillColor: Get.context!.isDarkMode ? DarkColor.xFFCCCCCC : Colors.white,
          isDense: false,
          focusedBorder: const UnderlineInputBorder(borderSide: BorderSide(color: MColor.skin, width: 1)),
          enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFF9A9B9C : MColor.xFF9A9B9C, width: 1)),
        ),
      );
    });
  }

  Widget get _imageView {
    return Obx(() {
      String path = controller.imagePath.value;
      String url = controller.imageUrl.value;

      return Stack(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              controller.uploadImage();
            },
            child: SizedBox(
              height: Get.width / 2,
              width: Get.width / 2,
              child: url.isURL
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: CachedNetworkImage(
                        imageUrl: url,
                        fit: BoxFit.cover,
                        placeholder: (ctx, a1) {
                          return const Center(
                            child: SpinKitCircle(
                              color: MColor.skin,
                              size: 40.0,
                            ),
                          );
                        },
                        errorWidget: (ctx, a1, a2) {
                          return const Icon(
                            Icons.error,
                            color: MColor.skin,
                            size: 60,
                          );
                        },
                      ),
                    )
                  : Builder(builder: (context) {
                      return DottedBorder(
                        borderType: BorderType.RRect,
                        color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFCCCCCC,
                        radius: const Radius.circular(6),
                        child: Center(child: Icon(Icons.add, size: 20, color: context.isDarkMode ? DarkColor.xFFABABAC : MColor.xFFABABAC)),
                      );
                    }),
            ),
          ),
          Visibility(
            visible: url.isEmpty ? false : true,
            child: SizedBox(
              height: Get.width / 2,
              width: Get.width / 2,
              child: Align(
                alignment: Alignment.topRight,
                child: GestureDetector(
                  onTap: () {
                    controller.imageUrl.value = '';
                    controller.imagePath.value = '';
                  },
                  child: Builder(builder: (context) {
                    return Container(
                      decoration: BoxDecoration(
                        color: context.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      width: 20,
                      height: 20,
                      child: const Icon(
                        Icons.close,
                        size: 20,
                        color: Colors.white,
                      ),
                    );
                  }),
                ),
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _textButton() {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18, bottom: 30),
      child: TextButton(
        onPressed: () {
          controller.fetchBind();
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          S.of(Get.context!).publish_submit,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }
}
