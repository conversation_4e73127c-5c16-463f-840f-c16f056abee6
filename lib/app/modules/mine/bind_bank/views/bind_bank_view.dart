import 'package:flutter/material.dart';

import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mine/bind_bank/controllers/bind_bank_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/style.dart';

import '../../../../../generated/l10n.dart';

class BindBankView extends GetView<BindBankController> {
  const BindBankView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).bank_title),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: Obx(() {
              return ListView.builder(
                itemCount: controller.bankList.length,
                itemBuilder: (ctx, index) {
                  return _cardView(index);
                },
              );
            }),
          ),
          _nextButton,
        ],
      ),
    );
  }

  Widget _cardView(int index) {
    var bank = controller.bankList[index];
    return Slidable(
      key: const ValueKey(0),
      endActionPane: ActionPane(
        dragDismissible: false,
        motion: const ScrollMotion(),
        dismissible: DismissiblePane(onDismissed: () {}),
        children: [
          SlidableAction(
            onPressed: (_) {
              controller.fetchDeleteBank(bank.id ?? 0);
            },
            backgroundColor: const Color(0xFFFE4A49),
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: S.of(Get.context!).address_delete,
          ),
        ],
      ),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          if (controller.needCheck) {
            Get.back(result: bank);
          }
        },
        child: Container(
          width: double.infinity,
          margin: const EdgeInsets.only(left: 16, right: 16, top: 16),
          padding: const EdgeInsets.only(left: 22, right: 22, top: 20, bottom: 13),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            gradient: const LinearGradient(colors: [MColor.xFFD95F66, MColor.xFFEA6A46]),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                bank.bankName ?? '-',
                style: MFont.medium18.apply(color: Colors.white),
              ),
              const SizedBox(height: 3),
              Text(S.of(Get.context!).deposit_card, style: MFont.regular12.apply(color: Colors.white)),
              const SizedBox(height: 11),
              Text(
                bank.bankCode ?? '-',
                style: MFont.medium18.apply(color: Colors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget get _nextButton {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.toNamed(Routes.ADD_BANK)?.then((value) {
          controller.fetchBankList();
        });
      },
      child: Builder(builder: (context) {
        return Container(
          margin: const EdgeInsets.only(left: 23, right: 23, top: 10, bottom: 37),
          decoration: BoxDecoration(
            color: DefaultStyle.primary,
            borderRadius: BorderRadius.circular(25),
            boxShadow: [BoxShadow(color: context.isDarkMode ? DarkColor.xFFCFCFCF : MColor.xFFCFCFCF, offset: const Offset(0, 1), blurRadius: 6)],
          ),
          height: 50,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.add, size: 20, color: DarkColor.xFF000000),
              const SizedBox(width: 4),
              Builder(builder: (context) {
                return Text(
                  S.of(Get.context!).bank_add,
                  style: MFont.medium18.apply(color: DarkColor.xFF000000),
                );
              }),
            ],
          ),
        );
      }),
    );
  }
}
