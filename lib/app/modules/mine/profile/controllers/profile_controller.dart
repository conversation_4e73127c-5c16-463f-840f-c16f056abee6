import 'package:get/get.dart';

import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/wallet/wallet_provider.dart';

import '../../../../../generated/l10n.dart';

class ProfileController extends GetxController {
  MineProvider provider = MineProvider();
  List<String> titles = [
    S.of(Get.context!).profile_avatar,
    S.of(Get.context!).profile_name,
    S.of(Get.context!).profile_mobile,
    S.of(Get.context!).profile_email,
    S.of(Get.context!).profile_wechat,
    S.of(Get.context!).profile_bind_manage,
  ];
  final values = ['', '', '', '', '', ''].obs;
  final image = ''.obs;
  final valueChange = false.obs;
  Rx<String> phone = ''.obs;
  Rx<String> email = ''.obs;
  Rx<String> name = ''.obs;
  Rx<String> avatar = ''.obs;
  Rx<String> wechat = ''.obs;

  @override
  void onInit() {
    super.onInit();

    email.value = GlobalConst.userModel?.email ?? '';
    phone.value = GlobalConst.userModel?.phone ?? '';
    name.value = GlobalConst.userModel?.name ?? '';
    avatar.value = GlobalConst.userModel?.head ?? '';
    wechat.value = GlobalConst.userModel?.wechatNum ?? '';
    image.value = avatar.value;
    setData();
  }

  void setData() {
    values[0] = avatar.value;
    values[1] = name.value;
    values[2] = phone.value;
    values[3] = email.value;
    values[4] = wechat.value;
    valueChange.value = !valueChange.value;
  }

  void fetchEditInfo() {
    provider.editInfo(phone.value, wechat.value, email.value, avatar.value, name.value).then((value) {
      setData();
      showToast(value.message ?? '');
    });
  }
}
