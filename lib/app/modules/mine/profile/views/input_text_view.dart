import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';

import '../../../../../generated/l10n.dart';

class InputTextView extends GetView {
  final String title;
  final TextInputType type;
  final String? placeHolder;
  final TextEditingController editingController = TextEditingController();
  InputTextView(this.title, this.type, {Key? key, this.placeHolder}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      resizeToAvoidBottomInset: true,
      body: Stack(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              Get.back();
            },
            child: Container(),
          ),
          Center(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: context.isDarkMode ? Colors.black : Colors.white,
              ),
              margin: const EdgeInsets.only(left: 40, right: 40, bottom: 150),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
              width: double.infinity,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  _textField,
                  const SizedBox(height: 20),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            Get.back();
                          },
                          style: ButtonStyle(
                            shape: MaterialStateProperty.all(
                              const StadiumBorder(side: BorderSide(color: MColor.skin)),
                            ),
                          ),
                          child: Text(
                            S.of(Get.context!).public_cancel,
                            style: MFont.medium15.apply(color: MColor.skin),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            Get.back(result: editingController.text);
                          },
                          style: ButtonStyle(
                            shape: MaterialStateProperty.all(const StadiumBorder()),
                            backgroundColor: MaterialStateProperty.all(MColor.skin),
                          ),
                          child: Text(
                            S.of(Get.context!).public_ok,
                            style: MFont.medium15.apply(color: Colors.white),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  TextField get _textField {
    return TextField(
      controller: editingController,
      style: MFont.regular15.apply(color: Get.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
      textAlign: TextAlign.center,
      keyboardType: type,
      decoration: InputDecoration(
        hintText: placeHolder ?? title,
        hintStyle: MFont.regular15.apply(color: Get.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
        filled: true,
        isDense: false,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        constraints: const BoxConstraints(maxHeight: 40, minHeight: 35),
        fillColor: Get.context!.isDarkMode ? DarkColor.xFFCCCCCC : Colors.white,
        border: OutlineInputBorder(borderSide: BorderSide(color: Get.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80)),
        focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Get.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80)),
        enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Get.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80)),
      ),
    );
  }
}
