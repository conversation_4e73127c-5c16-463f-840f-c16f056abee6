import 'package:flutter/material.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mine/profile/controllers/profile_controller.dart';
import 'package:inspector/app/modules/mine/profile/views/input_text_view.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/privacy_helper.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';
import '../../../../routes/app_pages.dart';

class ProfileView extends GetView<ProfileController> {
  const ProfileView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(S.of(Get.context!).profile_title),
        centerTitle: true,
      ),
      body: ListView.separated(
        itemBuilder: (ctx, index) {
          var phone = GlobalConst.userModel?.phone ?? '';
          var email = GlobalConst.userModel?.email ?? '';
          var hold = GlobalConst.userModel?.emailHold ?? false;
          var showMore = true;
          if (phone.isNotEmpty && index == 2 && !hold) {
            showMore = false;
          } else if (email.isNotEmpty && index == 3 && hold) {
            showMore = false;
          }

          return _itemView(index, () {
            if (index == 0) {
              if (hasShowCameraTips()) {
                _openImage();
              } else {
                showCameraTips().then((_) {
                  _openImage();
                });
              }
            } else if (index == 5) {
              Get.toNamed(Routes.THIRD_LOGIN_MANAGE);
            } else {
              if (!showMore) {
                return;
              }
              Get.generalDialog(
                pageBuilder: (ctx, a1, a2) {
                  return InputTextView(
                    controller.titles[index],
                    index == 2 ? TextInputType.number : TextInputType.text,
                  );
                },
              ).then((value) {
                if (value is String && value.isNotEmpty) {
                  if (index == 1) {
                    controller.name.value = value;
                  } else if (index == 2) {
                    controller.phone.value = value;
                  } else if (index == 3) {
                    controller.email.value = value;
                  } else if (index == 4) {
                    controller.wechat.value = value;
                  }
                  controller.fetchEditInfo();
                }
              });
            }
          });
        },
        separatorBuilder: (ctx, index) {
          return Divider(
            thickness: 0.5,
            color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6,
            indent: 16,
            height: 0,
          );
        },
        itemCount: controller.titles.length,
      ),
    );
  }

  Future<void> _openImage() async {
    if (!hasShowCameraTips()) {
      await showCameraTips();
    }
    var value = await FilesPicker.openImage(false);
    if (value.isEmpty) {
      return;
    }
    controller.image.value = value[0];
    var url = await PublicProvider.uploadImages(value[0], UploadType.plain);
    if (url == null || url.isEmpty) {
      showToast(S.of(Get.context!).profile_info_failed);
      return;
    }
    controller.avatar.value = url;
    controller.fetchEditInfo();
  }

  Widget _itemView(int index, VoidCallback action) {
    var phone = GlobalConst.userModel?.phone ?? '';
    var email = GlobalConst.userModel?.email ?? '';
    var hold = GlobalConst.userModel?.emailHold ?? false;
    var showMore = true;
    if (phone.isNotEmpty && index == 2 && !hold) {
      showMore = false;
    } else if (email.isNotEmpty && index == 3 && hold) {
      showMore = false;
    }

    return InkWell(
      onTap: () => action(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            Builder(builder: (context) {
              return Text(
                controller.titles[index],
                style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              );
            }),
            const SizedBox(width: 12),
            if (index == 0) ...{
              const Spacer(),
              Obx(() {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(25),
                  child: CachedNetworkImage(
                    imageUrl: controller.image.value,
                    width: 50,
                    height: 50,
                    fit: BoxFit.cover,
                    placeholder: (ctx, a1) {
                      return const Center(
                        child: SpinKitCircle(
                          color: MColor.skin,
                          size: 40.0,
                        ),
                      );
                    },
                    errorWidget: (ctx, a1, a2) {
                      return Builder(builder: (context) {
                        return Container(
                          decoration: BoxDecoration(
                            color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                            borderRadius: BorderRadius.circular(25),
                          ),
                        );
                      });
                    },
                  ),
                );
              })
            } else ...{
              Expanded(
                child: Builder(builder: (context) {
                  return Text(
                    controller.values[index],
                    style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                    maxLines: 1,
                    textAlign: TextAlign.right,
                  );
                }),
              ),
            },
            if (showMore) ...{
              Icon(
                Icons.keyboard_arrow_right_outlined,
                size: 24,
                color: Get.isDarkMode ? DarkColor.xFFBBBBBB : MColor.xFFBBBBBB,
              ),
            },
          ],
        ),
      ),
    );
  }
}
