import 'package:get/get.dart';

import 'package:inspector/app/data/bill_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';

class BillListController extends GetxController {
  final provider = MineProvider();
  final month = DateTime.now().obs;
  var startTime = 0;
  var endTime = 0;
  final billInfo = BillEntity().obs;

  final billType = 0.obs;
  final billTypeTitles = [
    S.of(Get.context!).bill_all,
    S.of(Get.context!).bill_income,
    S.of(Get.context!).bill_outcome,
    S.of(Get.context!).bill_freeze,
    S.of(Get.context!).bill_unfreeze,
    S.of(Get.context!).bill_withdraw
  ];

  @override
  void onInit() {
    super.onInit();

    takeTime(DateTime.now());
  }

  void takeTime(DateTime time) {
    month.value = time;
    billType.value = 0;
    var firstDay = DateTime(time.year, time.month); //本月第一天
    var nextFirstDay = DateTime(time.year, time.month + 1); //下月第一天
    var lastDay = nextFirstDay.subtract(const Duration(seconds: 1));

    startTime = firstDay.millisecondsSinceEpoch ~/ 1000;
    endTime = lastDay.millisecondsSinceEpoch ~/ 1000;
    fetchBillList();
  }

  void fetchBillList() {
    provider.takeBillList(startTime, endTime, billType.value).then((value) async {
      if (value.isSuccess) {
        billInfo.value = value.data ?? BillEntity();
      } else {
        showToast(value.message ?? '');
      }
    });
  }

  @override
  void onClose() {}
}
