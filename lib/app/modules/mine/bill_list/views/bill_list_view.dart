import 'package:flutter/material.dart';

import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_pickers/time_picker/model/date_mode.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/mine/bill_list/controllers/bill_list_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/tools/translation_service.dart';

import 'package:inspector/app/theme/style.dart';

import '../../../../../generated/l10n.dart';
import '../../../store/setting_store.dart';

class BillListView extends GetView<BillListController> {
  const BillListView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).bill_title),
        centerTitle: true,
      ),
      body: Obx(() {
        var rmbOut = controller.billInfo.value.rmbExpenditureAccount ?? 0;
        var rmbIn = controller.billInfo.value.rmbIncomeAccount ?? 0;
        var usdOut = controller.billInfo.value.usaExpenditureAccount ?? 0;
        var usdIn = controller.billInfo.value.usaIncomeAccount ?? 0;
        var capitalModelList = controller.billInfo.value.capitalModelList ?? [];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6, width: 0.5),
                ),
              ),
              padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      _pickerDate();
                    },
                    child: Obx(
                      () => Text.rich(
                        TextSpan(
                          text: Helper.parseMonth(controller.month.value.month),
                          style: MFont.medium30.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                          children: [
                            if (SettingStore.to.getCurrentLocale().languageCode == 'zh')
                              TextSpan(
                                  text: S.of(Get.context!).bill_month,
                                  style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D)),
                            WidgetSpan(
                                child: Icon(
                              Icons.arrow_drop_down_sharp,
                              size: 30,
                              color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                            )),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 7),
                  Text(
                    '${S.of(Get.context!).bill_out} ¥$rmbOut  ${S.of(Get.context!).bill_in} ¥$rmbIn',
                    style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    '${S.of(Get.context!).bill_out}\$$usdOut  ${S.of(Get.context!).bill_in}\$$usdIn',
                    style: MFont.medium13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                  ),
                  const SizedBox(height: 5),
                  GestureDetector(
                    onTap: () {
                      _pickerBillType();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24),
                        color: Colors.white,
                      ),
                      child: Row(mainAxisSize: MainAxisSize.min, children: [
                        Text(
                          controller.billTypeTitles[controller.billType.value],
                          style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(width: 2),
                        Icon(
                          Icons.arrow_drop_down_sharp,
                          size: 16,
                          color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                        )
                      ]),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ListView.separated(
                  itemCount: capitalModelList.isEmpty ? 1 : capitalModelList.length,
                  itemBuilder: (ctx, index) {
                    if (capitalModelList.isEmpty) {
                      return SizedBox(
                        width: double.infinity,
                        height: Get.height / 2,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.hourglass_empty,
                                size: 40,
                                color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                              ),
                              const SizedBox(height: 10),
                              Text(S.of(Get.context!).no_data,
                                  style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
                            ],
                          ),
                        ),
                      );
                    }
                    return _itemView(index);
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    if (capitalModelList.isEmpty) {
                      return SizedBox();
                    } else {
                      return SizedBox(height: 10);
                    }
                  },
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _itemView(int index) {
    var model = controller.billInfo.value.capitalModelList?[index];
    if (model == null) return const SizedBox();
    var isAdd = model.type == 1 ? true : false;
    var isRMB = model.userAccount == 1 ? true : false;

    var balance = '${isRMB ? '¥' : '\$'}${model.account?.toStringAsFixed(2) ?? ''}';
    // if (model.frozenBalance != null) {
    //   balance = '$balance | ${isRMB ? '¥' : '\$'}';
    // }
    var orderId = model.orderId;
    var typeDesc = model.getTypeDesc();
    var typeColor = model.getTypeDescColor();
    return GestureDetector(
      onTap: () {
        if (orderId != null && orderId != 0) {
          Get.toNamed(Routes.LIST_DETAIL, arguments: orderId);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 11, horizontal: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          color: Colors.white,
        ),
        child: Stack(children: [
          Positioned(top: 0, right: 0, child: Text(typeDesc, style: MFont.bold20.apply(color: typeColor))),
          Builder(builder: (context) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Row(
                  children: [
                    Text(
                      S.of(Get.context!).general_date,
                      style: MFont.medium13.apply(
                        color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(width: 10),
                    Text(
                      model.time ?? '-',
                      style: MFont.semi_Bold13.apply(
                        color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                      ),
                      overflow: TextOverflow.ellipsis,
                    )
                  ],
                ),
                const SizedBox(
                  height: 4,
                ),
                Row(
                  children: [
                    Text(
                      S.of(Get.context!).general_desc,
                      style: MFont.medium13.apply(
                        color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(width: 10),
                    Text(
                      model.title ?? '-',
                      style: MFont.semi_Bold13.apply(
                        color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                      ),
                      overflow: TextOverflow.ellipsis,
                    )
                  ],
                ),
                const SizedBox(
                  height: 4,
                ),
                Row(
                  children: [
                    Text(
                      S.of(Get.context!).general_amount,
                      style: MFont.medium13.apply(
                        color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(width: 10),
                    Text(
                      balance,
                      style: MFont.semi_Bold13.apply(
                        color: typeColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    )
                  ],
                ),
                if (model.orderInfo != null) ...{
                  const SizedBox(
                    height: 4,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(Get.context!).tab_order,
                        style: MFont.medium13.apply(color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(width: 10),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (model.orderInfo?.city != null) ...{
                            Row(
                              children: [
                                Text(
                                  S.of(Get.context!).order_detail_inspection_city,
                                  style: MFont.medium13.apply(
                                    color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(width: 10),
                                Text(
                                  model.orderInfo!.city!,
                                  style: MFont.medium13.apply(
                                    color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          },
                          if (model.orderInfo?.inspectTime != null) ...{
                            Row(
                              children: [
                                Text(
                                  S.of(Get.context!).order_detail_inspection_time,
                                  style: MFont.medium13.apply(
                                    color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(width: 10),
                                Text(
                                  model.orderInfo!.inspectTime!,
                                  style: MFont.medium13.apply(
                                    color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          },
                          if (model.orderInfo?.productName != null) ...{
                            Row(
                              children: [
                                Text(
                                  S.of(Get.context!).order_detail_inspection_product,
                                  style: MFont.medium13.apply(
                                    color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(width: 10),
                                Text(
                                  model.orderInfo!.productName!,
                                  style: MFont.medium13.apply(
                                    color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          },
                          Text(
                            '[ ${S.of(Get.context!).view_order} ]',
                            style: MFont.medium13.apply(color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      )
                    ],
                  ),
                }
              ],
            );
          })
        ]),
      ),
    );
  }

  void _pickerDate() {
    return Pickers.showDatePicker(
      Get.context!,
      mode: DateMode.YM,
      pickerStyle: PickerStyle(
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
        itemOverlay: Builder(builder: (context) {
          return Container(
            decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
            ),
          );
        }),
      ),
      onConfirm: (value) {
        var time = DateTime(value.year!, value.month!);
        controller.takeTime(time);
      },
    );
  }

  void _pickerBillType() {
    // 1-收入，2-支出，3-冻结，4-解冻，5-提现
    return Pickers.showSinglePicker(
      Get.context!,
      selectData: controller.billTypeTitles[controller.billType.value],
      data: controller.billTypeTitles,
      pickerStyle: PickerStyle(
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
        itemOverlay: Builder(builder: (context) {
          return Container(
            decoration: BoxDecoration(
              border: Border.symmetric(horizontal: BorderSide(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
            ),
          );
        }),
      ),
      onConfirm: (value, index) {
        controller.billType.value = index;
        controller.fetchBillList();
      },
    );
  }
}
