import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/auth/login/controllers/auth_login_controller.dart';

import '../../../../generated/l10n.dart';

class AreaCodeView extends GetView<AuthLoginController> {
  AreaCodeView({Key? key}) : super(key: key) {
    controller.fetchAreaList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).login_area_selected),
      ),
      body: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            const SizedBox(height: 10),
            _searchView,
            Expanded(child: _listView),
          ],
        ),
      ),
    );
  }

  Widget get _searchView {
    return Builder(builder: (context) {
      return TextField(
        controller: controller.searchController,
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
          prefixIcon: Icon(
            Icons.search,
            size: 24,
            color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
          ),
          fillColor: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
          filled: true,
          isDense: true,
          hintText: S.of(Get.context!).login_area_selected,
          hintStyle: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999),
          labelStyle: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE, width: 0),
            borderRadius: BorderRadius.circular(5),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE, width: 0),
          ),
          constraints: const BoxConstraints(maxHeight: 40),
        ),
        onChanged: (text) {
          controller.searchWords();
        },
      );
    });
  }

  Widget get _listView {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Obx(() {
        return ListView.builder(
          itemCount: controller.areaList.length,
          itemBuilder: (ctx, index) {
            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                var code = controller.areaList[index];
                Get.back(result: code);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 5),
                child: Row(
                  children: [
                    Text(
                      controller.areaList[index].name,
                      style: MFont.medium18.apply(color: Colors.black),
                    ),
                    const Spacer(),
                    Text(
                      '+ ${controller.areaList[index].code}',
                      style: MFont.medium18.apply(color: Colors.black),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }),
    );
  }
}
