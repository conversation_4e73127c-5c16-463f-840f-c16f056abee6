import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/auth/account_service.dart';
import 'package:inspector/app/modules/auth/models/saved_account_entity.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/shared/widgets/avatar.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/loading.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

class AccountSwitchPage extends StatefulWidget {
  const AccountSwitchPage({super.key});

  @override
  State<StatefulWidget> createState() => _AccountSwitchState();
}

class _AccountSwitchState extends State<AccountSwitchPage> {
  late List<SavedAccount> savedAccounts = [];
  bool isManaging = false;

  @override
  void initState() {
    savedAccounts = AccountService.instance.getSavedAccount();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(S.of(context).switch_account),
        actions: [
          TextButton(
              onPressed: () {
                setState(() {
                  isManaging = !isManaging;
                });
              },
              child: Text(
                isManaging ? S.of(context).public_cancel : S.of(context).public_manage,
                style: TextStyle(fontSize: 14),
              ))
        ],
        leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(Icons.arrow_back_ios, size: 18)),
      ),
      body: SafeArea(
          child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: ListView.separated(
          shrinkWrap: true,
          itemBuilder: (context, index) {
            if (index == savedAccounts.length) {
              return _addView;
            }
            return _itemView(context, savedAccounts[index]);
          },
          separatorBuilder: (context, index) {
            return Divider();
          },
          itemCount: savedAccounts.length + 1,
        ),
      )),
    );
  }

  Widget get _addView {
    return GestureDetector(
        onTap: () {
          Get.toNamed(Routes.AUTH_LOGIN);
        },
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(15), border: Border.all(color: MColor.xFFE5E5E5), color: MColor.xFFFFFFFF),
          height: 64,
          padding: EdgeInsets.all(8),
          child: Row(
            children: [
              Container(
                width: 46,
                height: 46,
                decoration: BoxDecoration(color: MColor.xFFE5E5E5),
                child: const Icon(Icons.add),
              ),
              const SizedBox(
                width: 8,
              ),
              Text(S.of(context).add_account)
            ],
          ),
        ));
  }

  Widget _itemView(BuildContext context, SavedAccount account) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (GlobalConst.userModel!.uid != account.userId) {
          showCustomDialog(S.of(Get.context!).switch_account_confirm_tips, onConfirm: () {
            _loginWithToken(account.token);
          }, cancel: true);
        }
      },
      child: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(15), border: Border.all(color: MColor.xFFE5E5E5), color: MColor.xFFFFFFFF),
        height: 64,
        padding: EdgeInsets.all(8),
        child: Row(
          children: [
            Avatar(
              size: 46,
              url: account.avatar,
              displayName: account.account,
            ),
            const SizedBox(
              width: 8,
            ),
            Text(account.account),
            const Spacer(),
            if (isManaging) ...{
              IconButton(
                  onPressed: () {
                    setState(() {
                      AccountService.instance.removeSavedAccount(account.userId);
                      savedAccounts = AccountService.instance.getSavedAccount();
                    });
                  },
                  icon: Icon(Icons.close, color: MColor.xFF000000, size: 16))
            } else if (GlobalConst.userModel!.uid == account.userId) ...{
              IconButton(onPressed: () {}, icon: Icon(Icons.check_circle, color: MColor.xFF000000, size: 16))
            }
          ],
        ),
      ),
    );
  }

  Future<void> _loginWithToken(String token) async {
    Loading.show();
    try {
      await AccountService.instance.saveToken(token);
      await AccountService.instance.fetchUserInfo();
      if (GlobalConst.userModel?.uid != null) {
        await AccountService.instance.saveAccount(GlobalConst.userModel!.name!, GlobalConst.userModel!.head!, token, GlobalConst.userModel!.uid!);
      }
      unawaited(Get.offAllNamed(Routes.TABBAR));
    } catch (_) {
    } finally {
      Loading.dismiss();
    }
  }
}
