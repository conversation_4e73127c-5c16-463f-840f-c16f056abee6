import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:inspector/app/config/api.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/data/apply_info_entity.dart';
import 'package:inspector/app/data/area_list_entity.dart';
import 'package:inspector/app/data/bank_entity.dart';
import 'package:inspector/app/data/bill_entity.dart';
import 'package:inspector/app/data/bind_pay_entity.dart';
import 'package:inspector/app/data/charge_history_entity.dart';
import 'package:inspector/app/data/insp_order_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/data/user_profile_resp.dart';
import 'package:inspector/app/data/wallet_entity.dart';
import 'package:inspector/app/data/wallet_info_entity.dart';
import 'package:inspector/app/data/withdraw_entity.dart';
import 'package:inspector/app/tools/public_provider.dart';

import '../../data/base_entity.dart';
import '../../tools/http.dart';
import 'models/user_model.dart';

class MineProvider {
  //获取验证码
  Future<BaseModel<dynamic>> takeCode(bool isEmail, String account, String? area) {
    Map<String, dynamic> params = {'phoneOrEmail': account};
    if (area != null) {
      params['areaCode'] = area;
    }
    return PublicProvider.request<dynamic>(path: isEmail ? Api.takeEmailCode : Api.takeSmsCode, params: params);
  }

  //登录
  Future<BaseModel<UserInfoEntity>> loginAccount<T>(String email, String code, int? areaCode, bool isEmail) {
    if (isEmail) {
      return PublicProvider.request<UserInfoEntity>(path: Api.emailLogin, params: {'code': code, 'email': email});
    } else {
      return PublicProvider.request<UserInfoEntity>(path: Api.mobileLogin, params: {'phone': email, 'code': code, 'areaCode': areaCode});
    }
    // return PublicProvider.request<UserInfoEntity>(
    //     path: isEmail ? Api.emailLogin : Api.mobileLogin,
    //     params: {'email': email, 'code': code, 'areaCode': areaCode});
  }

  static Future<BaseEntity<UserModel>> getUserModel() async {
    var response = await HttpUtil().get(Api.mineInfo);
    return BaseEntity.fromJson(response, (json) => UserModel.fromJson(json));
  }

  static Future<BaseEntity<dynamic>> userLogin(
      {String? user, String? pwd, String? code, String? thirdUid, String? thirdData, String? areaCode, String? thirdType}) async {
    var data = {};
    if (user?.isNotEmpty == true) {
      data['user'] = user;
    }
    if (pwd?.isNotEmpty == true) {
      data['pwd'] = md5.convert(utf8.encode(pwd!)).toString();
    }
    if (code?.isNotEmpty == true) {
      data['code'] = code;
    }
    if (thirdData?.isNotEmpty == true) {
      data['third_data'] = thirdData;
    }
    if (thirdUid?.isNotEmpty == true) {
      data['third_uid'] = thirdUid;
    }
    if (areaCode?.isNotEmpty == true) {
      data['area_code'] = areaCode;
    }
    if (thirdType?.isNotEmpty == true) {
      data['third_type'] = thirdType;
    }
    var response = await HttpUtil().post(Api.newUserLogin, data: data);
    return BaseEntity.fromJson(response, (json) => json);
  }

  static Future<BaseEntity<dynamic>> registerWithThird(String scene,
      {String? user, String? pwd, String? code, String? thirdUid, String? thirdData, String? areaCode, String? thirdType}) async {
    var data = {};
    if (user?.isNotEmpty == true) {
      data['user'] = user;
    }
    if (pwd?.isNotEmpty == true) {
      data['pwd'] = md5.convert(utf8.encode(pwd!)).toString();
    }
    if (code?.isNotEmpty == true) {
      data['code'] = code;
    }
    if (thirdData?.isNotEmpty == true) {
      data['third_data'] = thirdData;
    }
    if (thirdUid?.isNotEmpty == true) {
      data['third_uid'] = thirdUid;
    }
    if (areaCode?.isNotEmpty == true) {
      data['area_code'] = areaCode;
    }
    if (thirdType?.isNotEmpty == true) {
      data['third_type'] = thirdType;
    }
    data['scene'] = scene;
    var resp = await HttpUtil().post(Api.registerBindThird, data: data);
    return BaseEntity.fromJson(resp, (json) => json);
  }

  static Future<BaseEntity<dynamic>> bindThird(
      {String? user, String? pwd, String? code, String? thirdUid, String? thirdData, String? areaCode, String? thirdType}) async {
    var data = {};
    if (user?.isNotEmpty == true) {
      data['user'] = user;
    }
    if (pwd?.isNotEmpty == true) {
      data['pwd'] = md5.convert(utf8.encode(pwd!)).toString();
    }
    if (code?.isNotEmpty == true) {
      data['code'] = code;
    }
    if (thirdData?.isNotEmpty == true) {
      data['third_data'] = thirdData;
    }
    if (thirdUid?.isNotEmpty == true) {
      data['third_uid'] = thirdUid;
    }
    if (areaCode?.isNotEmpty == true) {
      data['area_code'] = areaCode;
    }
    if (thirdType?.isNotEmpty == true) {
      data['third_type'] = thirdType;
    }
    data['scene'] = '4';
    var resp = await HttpUtil().post(Api.bindThird, data: data);
    return BaseEntity.fromJson(resp, (json) => json);
  }

  //重置密码
  Future<BaseModel<T?>> resetPassword<T>(String email, String password, String code) {
    return PublicProvider.request<T>(
      path: Api.resetPassword,
      params: {'user': email, 'pwd': md5.convert(utf8.encode(password)).toString(), 'code': code},
    );
  }

  //国区列表
  Future<BaseModel<List<AreaListEntity>>> areaList<T>() {
    return PublicProvider.request<List<AreaListEntity>>(
      path: Api.areaList,
      isPost: false,
    );
  }

  //个人信息
  Future<BaseModel<UserInfoEntity>> takeMineInfo() {
    return PublicProvider.request<UserInfoEntity>(path: Api.mineInfo, isPost: false);
  }

  Future<BaseModel<UserProfileResp>> takeUserProfile(int userId) {
    return PublicProvider.request<UserProfileResp>(path: '${Api.userProfile}/$userId', isPost: false);
  }

  Future<BaseModel<dynamic>> deleteAccount() {
    return PublicProvider.request<UserInfoEntity>(path: 'user/setStatus', isPost: false);
  }

  //推荐
  Future<BaseModel<dynamic>> takeRecommend(String email) {
    return PublicProvider.request<dynamic>(path: Api.recommend, params: {'email': email});
  }

  //绑定手机号
  Future<BaseModel<UserInfoEntity>> bindMobile(String mobile, String areaCode, String code) {
    var params = {};
    params['phoneOrEmail'] = mobile;
    params['areaCode'] = areaCode;
    params['code'] = code;
    return PublicProvider.request<UserInfoEntity>(path: Api.bindMobile, params: params);
  }

  //更新用户信息
  Future<BaseModel<dynamic>> editInfo(String phone, String wechatNum, String email, String head, String name) {
    var params = {};
    if (phone.isNotEmpty) {
      params['phone'] = phone;
    }
    if (wechatNum.isNotEmpty) {
      params['wechatNum'] = wechatNum;
    }
    if (email.isNotEmpty) {
      params['email'] = email;
    }
    if (head.isNotEmpty) {
      params['head'] = head;
    }
    if (name.isNotEmpty) {
      params['name'] = name;
    }
    return PublicProvider.request<dynamic>(path: Api.editInfo, params: params);
  }

  //审核员验证
  Future<BaseModel<dynamic>> apply(
    String nick,
    String sex,
    String birthday,
    String province,
    String city,
    String area,
    String price,
    String education,
    String idCardNum,
    String idCardFront,
    String idCardBack,
    String content,
    int accountType,
    bool socialSecurity,
  ) {
    return PublicProvider.request<dynamic>(path: Api.apply, params: {
      'province': province,
      'city': city,
      'area': area,
      'price': price,
      'education': education,
      'idCardNum': idCardNum,
      'idCardFront': idCardFront,
      'idCardBack': idCardBack,
      'content': content,
      'accountType': accountType,
      'socialSecurity': socialSecurity,
    });
  }

  //撤销验货员资格
  Future<BaseModel<dynamic>> cancel() {
    return PublicProvider.request<dynamic>(path: 'user/inspector/cancel', isPost: false, isDelete: false);
  }

  //身份证验证
  Future<BaseModel<dynamic>> authIDCard(String url, String type) {
    return PublicProvider.request<dynamic>(path: Api.authIDCard, params: {'imgUrl': url, 'type': type});
  }

  //身份证验证
  Future<BaseModel<ApplyInfoEntity>> applyInfo() {
    return PublicProvider.request<ApplyInfoEntity>(path: Api.applyIno, isPost: false);
  }

  Future<BaseModel<AddressRows>> recognizeAddress(String text) {
    return PublicProvider.request<AddressRows>(path: Api.addressRecognize, params: {'address': text}, isPost: true);
  }

  Future<BaseModel<dynamic>> refreshToken() {
    return PublicProvider.request(path: Api.refreshToken, isPost: false);
  }

  //保存地址
  Future<BaseModel<AddressRows>> addressSave(
    String factoryName,
    String name,
    String phone,
    String? email,
    String province,
    String city,
    String area,
    String address,
    double? lat,
    double? lon,
    int? addressId,
  ) {
    Map<String, dynamic> params = {
      'factoryName': factoryName,
      'name': name,
      'phone': phone,
      'province': province,
      'city': city,
      'area': area,
      'address': address,
    };
    if (email != null) {
      params['email'] = email;
    }
    if (lat != null) {
      params['lat'] = lat;
    }
    if (lon != null) {
      params['lon'] = lon;
    }
    if (addressId != null && addressId != 0) {
      params['id'] = addressId;
      return PublicProvider.request<AddressRows>(path: Api.addressEdit, params: params);
    } else {
      return PublicProvider.request<AddressRows>(path: Api.addressSave, params: params);
    }
  }

  //地址列表
  Future<BaseModel<AddressEntity>> addressList(String keywords, int page, int limit) {
    return PublicProvider.request<AddressEntity>(path: Api.addressList, params: {'keyword': keywords, 'limit': limit, 'page': page});
  }

  //删除地址
  Future<BaseModel<dynamic>> addressDelete(int id) {
    return PublicProvider.request<dynamic>(path: '${Api.addressDelete}/$id', isPost: false);
  }

  //编辑地址
  Future<BaseModel<dynamic>> addressEdit(int id) {
    return PublicProvider.request<dynamic>(path: Api.addressEdit, params: {'id': id});
  }

  //获取钱包信息
  Future<BaseModel<WalletEntity>> wallet() {
    return PublicProvider.request<WalletEntity>(path: Api.wallet, isPost: false);
  }

  Future<BaseModel<WalletInfoEntity>> walletInfo() {
    return PublicProvider.request(path: Api.walletInfo, isPost: true);
  }

  //获取支付绑定信息
  Future<BaseModel<List<BindPayEntity>>> bindPayInfo() {
    return PublicProvider.request<List<BindPayEntity>>(path: Api.bindPay, isPost: false);
  }

  Future<BaseModel<dynamic>> setDefaultAccount(bool isUSD) {
    return PublicProvider.request(path: Api.defaultAccount, params: {'user_account': isUSD ? 2 : 1}, isPost: true);
  }

  //支付绑定
  Future<BaseModel<dynamic>> bindPay(String account, String image, String name, int type) {
    return PublicProvider.request<dynamic>(path: Api.bindPay, params: {'account': account, 'image': image, 'name': name, 'type': type});
  }

  //获取银行卡列表
  Future<BaseModel<List<BankEntity>>> bankList() {
    return PublicProvider.request<List<BankEntity>>(path: Api.bankList);
  }

  //删除银行卡
  Future<BaseModel<dynamic>> deleteBank(int id) {
    return PublicProvider.request<dynamic>(path: '${Api.deleteBank}/$id', isPost: false);
  }

  //删除银行卡
  Future<BaseModel<dynamic>> addBank(String bankAddress, String bankCode, String bankHang, String bankName, String userName) {
    return PublicProvider.request<dynamic>(
        path: Api.addBank, params: {'bankAddress': bankAddress, 'bankCode': bankCode, 'bankHang': bankHang, 'bankName': bankName, 'userName': userName});
  }

  //充值订单
  // 充值方式 rechargeMode 1-系统充值 2-PayPai 3-Veem 4-微信 5-支付宝 6-其他
  Future<BaseModel<dynamic>> recharge(int chargeType, num price, int rechargeMode, int userAccount, {String? imageUrl}) {
    if (chargeType == 0) {
      return PublicProvider.request<dynamic>(
          path: Api.chargeOrder, params: {'price': price, 'rechargeMode': rechargeMode, 'userAccount': userAccount, 'type': 0, 'image_url': imageUrl});
    } else {
      return PublicProvider.request<dynamic>(
          path: Api.chargeOrder, params: {'price': price, 'rechargeMode': rechargeMode, 'userAccount': userAccount, 'type': 1});
    }
  }

  //充值
  Future<BaseModel<dynamic>> payRecharge(String orderId, int payType) {
    return PublicProvider.request<dynamic>(path: Api.payCharge, params: {'orderId': orderId, 'payType': payType});
  }

  //充值列表
  Future<BaseModel<List<ChargeHistoryEntity>>> rechargeList(int page) {
    return PublicProvider.request<List<ChargeHistoryEntity>>(path: Api.chargeList, params: {'limit': 20, 'page': page});
  }

  //提现
  Future<BaseModel<dynamic>> takeCash(int accountType, int bankId, double price, int type, String invoiceUrls, double invoicePrice) {
    var params = {};
    // if (accountId > 0) {
    // params['accountId'] = '0';
    // }
    params['accountType'] = accountType;
    if (bankId > 0) {
      params['bankId'] = bankId;
    }
    params['price'] = price;
    params['type'] = type;
    params['invoice_file'] = invoiceUrls;
    params['invoice_money'] = invoicePrice;
    return PublicProvider.request<dynamic>(path: Api.cash, params: params);
  }

  //提现记录
  Future<BaseModel<List<WithdrawEntity>>> takeCashList(int page) {
    return PublicProvider.request<List<WithdrawEntity>>(path: Api.cashList, params: {'limit': 20, 'page': page});
  }

  //账单记录
  Future<BaseModel<BillEntity>> takeBillList(int startTime, int endTime, int type) {
    return PublicProvider.request<BillEntity>(path: Api.billList, params: {'startTime': startTime, 'endTime': endTime, 'type': type});
  }

  Future<BaseModel<InspOrderResp>> getInspOrders(int page, int pageSize, int userId) {
    return PublicProvider.request<InspOrderResp>(path: Api.inspOrder, isPost: true, params: {'page': page, 'page_size': pageSize, 'user_id': userId});
  }

  static Future<BaseEntity<dynamic>> checkBindAvailable(String thirdType, String user) async {
    var resp = await HttpUtil().post(Api.checkBindAvailable, data: {'third_type': thirdType, 'user': user});
    return BaseEntity.fromJson(resp, (json) => json);
  }

  static Future<BaseEntity<dynamic>> unbind(int type) async {
    var resp = await HttpUtil().post(Api.unbindThird, data: {'third_type': type});
    return BaseEntity.fromJson(resp, (json) => json);
  }

  static Future<BaseEntity<dynamic>> userParsing(String type, String code) async {
    var resp = await HttpUtil().post(Api.userParsing, data: {'third_type': type, 'code': code});
    return BaseEntity.fromJson(resp, (json) => json);
  }
}
