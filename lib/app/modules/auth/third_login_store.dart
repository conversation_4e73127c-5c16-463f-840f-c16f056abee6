import 'dart:convert';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:fluwx/fluwx.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/store/privacy_store.dart';
import 'package:inspector/app/tools/loading.dart';
import 'package:inspector/app/tools/privacy_helper.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/firebase_options.dart';
import 'package:inspector/flavors.dart';

abstract class WechatCallback {
  void onLoginSuccess({required String thirdUid, required String thirdData});
}

class ThirdLoginStore extends GetxController {
  static ThirdLoginStore get to => Get.find();

  final Fluwx fluwx = Fluwx();

  bool isWechatInstalled = Platform.isAndroid;
  bool isInitSdk = false;

  FirebaseApp? firebaseApp;

  List<WechatCallback> wechatCallbacks = [];

  @override
  void onInit() {
    logger.i('ThirdLoginStore init');
    initSdk();
    super.onInit();
  }

  Future<void> initSdk() async {
    if (!isInitSdk && PrivacyStore.to.isPrivacyAgreed()) {
      isInitSdk = true;
      await Future.wait([_initFirebase(), _initFluwx()]);
    }
  }

  Future<void> _initFirebase() async {
    logger.i('ThirdLoginStore initFirebase');
    if (Firebase.apps.isEmpty) {
      firebaseApp = await Firebase.initializeApp(
        name: F.appFlavor == Flavor.dev ? 'inspector-dev' : 'inspector',
        options: DefaultFirebaseOptions.currentPlatform,
      );
      logger.i('Firebase Initialized: $firebaseApp ${firebaseApp?.options}');
    } else {
      firebaseApp = Firebase.apps[0];
    }
  }

  void addWechatCallback(WechatCallback callback) {
    wechatCallbacks.add(callback);
  }

  void removeWechatCallback(WechatCallback callback) {
    wechatCallbacks.remove(callback);
  }

  Future<void> _initFluwx() async {
    logger.i('ThirdLoginStore initFluwx');
    // AppSecret:c746c4076bb59d95f8a6fa71420a5470
    await fluwx.registerApi(
        appId: 'wx003a8baa47475fd4', //传入注册的应用id
        doOnAndroid: true, //在android上运行
        doOnIOS: true, // 在ios上运行
        universalLink: 'https://www.inspector.ltd');
    // if (Platform.isAndroid) {
    // } else if (Platform.isIOS) {

    // }
    if (Platform.isAndroid) {
      isWechatInstalled = true;
    } else {
      isWechatInstalled = await fluwx.isWeChatInstalled;
    }
    update();
    if (isWechatInstalled) {
      fluwx.addSubscriber((response) {
        if (response is WeChatAuthResponse) {
          // setState(() {
          logger.i('state :${response.state} \n code:${response.code}');
          // });
          if (response.state == 'wechat_sdk_login' && response.code?.isNotEmpty == true) {
            _userLoginByWechat(response.code!);
          }
        }
      });
    }
  }

  Future<void> _userLoginByWechat(String code) async {
    logger.i('ThirdLoginStore _userLoginByWechat $code');
    try {
      var resp = await MineProvider.userParsing('3', code);
      if (resp.isSuccess) {
        var uid = resp.data['uid'];
        for (var callback in wechatCallbacks) {
          callback.onLoginSuccess(thirdUid: uid, thirdData: json.encode(resp.data));
        }
      }
    } catch (e) {
      logger.e('_userLoginByWechat error $e');
    } finally {}
  }

  void signInWithWechat() {
    fluwx.authBy(which: NormalAuth(scope: 'snsapi_userinfo', state: 'wechat_sdk_login'));
  }
}
