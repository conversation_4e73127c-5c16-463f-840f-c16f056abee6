import 'dart:convert';

import 'package:get/get.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/modules/auth/login/controllers/auth_login_controller.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/auth/models/saved_account_entity.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/json/base/json_convert_content.dart';

import '../../tools/storage_util.dart';

class AccountService {
  factory AccountService() => _getInstance();

  static AccountService get instance => _getInstance();

  // 静态变量_instance，存储唯一对象
  static final AccountService _instance = AccountService._internal();

  // 获取对象
  static AccountService _getInstance() {
    return _instance;
  }

  AccountService._internal();

  Future<void> saveAccount(String account, String avatar, String token, int userId) async {
    SavedAccount thisAccount = SavedAccount(account, avatar, token, userId);
    logger.i('save account $thisAccount');
    List<SavedAccount> savedAccount = getSavedAccount();
    savedAccount.remove(thisAccount);
    savedAccount.insert(0, thisAccount);
    String encodedAccount = jsonEncode(savedAccount);
    await StorageUtil.setString(Constant.kSavedAccount, encodedAccount);
  }

  List<SavedAccount> getSavedAccount() {
    try {
      String savedAccount = StorageUtil.getString(Constant.kSavedAccount);
      List<SavedAccount> accounts = jsonConvert.convertListNotNull<SavedAccount>(jsonDecode(savedAccount)) ?? [];
      for (var account in accounts) {
        logger.i('getSavedAccount $account');
      }
      return accounts;
    } catch (e) {
      return [];
    }
  }

  Future<void> removeSavedAccount(int userId) async {
    logger.i('remove account $userId');
    List<SavedAccount> savedAccount = getSavedAccount();
    savedAccount.removeWhere((test) => test.userId == userId);
    String encodedAccount = jsonEncode(savedAccount);
    await StorageUtil.setString(Constant.kSavedAccount, encodedAccount);
  }

  Future<void> saveToken(String data) async {
    await StorageUtil.setString(Constant.kSavedToken, data);
  }

  String getToken() {
    //print('getToken=='+StorageUtil.getString(Constant.kSavedToken));
    return StorageUtil.getString(Constant.kSavedToken);
  }

  Future<BaseModel<UserInfoEntity>> fetchUserInfo() async {
    var value = await MineProvider().takeMineInfo();
    if (value.isSuccess) {
      GlobalConst.tempModel = null;
      await GlobalConst.saveUser(value.data!);
    } else {
      showToast(value.message ?? '');
    }
    return value;
  }

  Future<void> onTokenExpired() async {
    int? userId = GlobalConst.tempModel?.uid;
    if (userId != null) {
      await removeSavedAccount(userId);
    }
    GlobalConst.tempModel = null;
    StorageUtil.remove(Constant.kUser);
    await saveToken('');
    if (Get.currentRoute == Routes.AUTH_LOGIN) {
      return;
    }
    await Get.offAllNamed(Routes.AUTH_LOGIN);
  }
}
