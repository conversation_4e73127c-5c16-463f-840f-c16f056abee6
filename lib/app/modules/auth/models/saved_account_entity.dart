class SavedAccount {
  String account;
  String avatar;
  String token;
  int userId;

  SavedAccount(this.account, this.avatar, this.token, this.userId);

  SavedAccount.fromJson(Map<String, dynamic> json)
      : account = json['account'],
        avatar = json['avatar'],
        token = json['token'],
        userId = json['userId'];

  Map<String, dynamic> toJson() => {'account': account, 'avatar': avatar, 'userId': userId, 'token': token};

  @override
  String toString() {
    return toJson().toString();
  }

  @override
  int get hashCode => userId;

  @override
  bool operator ==(Object other) {
    return other is SavedAccount && other.userId == userId;
  }
}
