import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel extends Object {
  @Json<PERSON>ey(name: 'userId')
  int? userId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'nick')
  String? nick;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'phone')
  String? phone;

  @Json<PERSON><PERSON>(name: 'email')
  String? email;

  @<PERSON>son<PERSON><PERSON>(name: 'head')
  String? head;

  @Json<PERSON>ey(name: 'realName')
  String? realName;

  @Json<PERSON>ey(name: 'name')
  String? name;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'sex')
  int? sex;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'idCardNum')
  String? idCardNum;

  @Json<PERSON>ey(name: 'idCardFront')
  String? idCardFront;

  @Json<PERSON>ey(name: 'idCardBack')
  String? idCardBack;

  @<PERSON>son<PERSON>ey(name: 'idCardHand')
  String? idCardHand;

  @<PERSON>son<PERSON><PERSON>(name: 'country')
  String? country;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'countryEn')
  String? countryEn;

  @<PERSON>son<PERSON>ey(name: 'province')
  String? province;

  @Json<PERSON><PERSON>(name: 'provinceEn')
  String? provinceEn;

  @Json<PERSON><PERSON>(name: 'city')
  String? city;

  @JsonKey(name: 'cityEn')
  String? cityEn;

  @JsonKey(name: 'area')
  String? area;

  @JsonKey(name: 'address')
  String? address;

  @JsonKey(name: 'status')
  int? status;

  @JsonKey(name: 'registerTime')
  int? registerTime;

  @JsonKey(name: 'lastLoginTime')
  int? lastLoginTime;

  @JsonKey(name: 'lastLoginIp')
  String? lastLoginIp;

  @JsonKey(name: 'role')
  int? role;

  @JsonKey(name: 'inspAge')
  int? inspAge;

  @JsonKey(name: 'adminId')
  int? adminId;

  @JsonKey(name: 'isAuth')
  int? isAuth;

  @JsonKey(name: 'isFollow')
  int? isFollow;

  @JsonKey(name: 'sparePhone')
  String? sparePhone;

  @JsonKey(name: 'quotaId')
  int? quotaId;

  @JsonKey(name: 'facebook')
  String? facebook;

  @JsonKey(name: 'skype')
  String? skype;

  @JsonKey(name: 'twitter')
  String? twitter;

  @JsonKey(name: 'instgram')
  String? instgram;

  @JsonKey(name: 'tube')
  String? tube;

  @JsonKey(name: 'chatId')
  String? chatId;

  @JsonKey(name: 'workStatus')
  int? workStatus;

  @JsonKey(name: 'onlyWifi')
  int? onlyWifi;

  @JsonKey(name: 'remind')
  int? remind;

  @JsonKey(name: 'notice')
  int? notice;

  @JsonKey(name: 'lon')
  double? lon;

  @JsonKey(name: 'lat')
  double? lat;

  @JsonKey(name: 'remark')
  String? remark;

  @JsonKey(name: 'activeTime')
  int? activeTime;

  @JsonKey(name: 'discern')
  int? discern;

  @JsonKey(name: 'resume')
  String? resume;

  @JsonKey(name: 'certificationStatus')
  int? certificationStatus;

  @JsonKey(name: 'onLine')
  int? onLine;

  @JsonKey(name: 'inspCount')
  int? inspCount;

  @JsonKey(name: 'demoStatus')
  int? demoStatus;

  @JsonKey(name: 'downApp')
  int? downApp;

  @JsonKey(name: 'redDot')
  int? redDot;

  @JsonKey(name: 'differenceDays')
  int? differenceDays;

  @JsonKey(name: 'createdAt')
  String? createdAt;

  @JsonKey(name: 'updatedAt')
  String? updatedAt;

  @JsonKey(name: 'isUpdate')
  int? isUpdate;

  @JsonKey(name: 'usePub')
  int? usePub;

  @JsonKey(name: 'cateExh')
  String? cateExh;

  @JsonKey(name: 'fixedPhone')
  String? fixedPhone;

  @JsonKey(name: 'isSour')
  int? isSour;

  @JsonKey(name: 'exhName')
  String? exhName;

  @JsonKey(name: 'postCode')
  String? postCode;

  @JsonKey(name: 'estaDate')
  String? estaDate;

  @JsonKey(name: 'regCap')
  String? regCap;

  @JsonKey(name: 'scale')
  String? scale;

  @JsonKey(name: 'enteType')
  String? enteType;

  @JsonKey(name: 'tradePatte')
  String? tradePatte;

  @JsonKey(name: 'exhHist')
  String? exhHist;

  @JsonKey(name: 'entePro')
  String? entePro;

  @JsonKey(name: 'payWay')
  int? payWay;

  @JsonKey(name: 'regDevice')
  int? regDevice;

  @JsonKey(name: 'emailStatus')
  int? emailStatus;

  @JsonKey(name: 'purVerifyTs')
  int? purVerifyTs;

  @JsonKey(name: 'exemptReview')
  int? exemptReview;

  @JsonKey(name: 'purAuth')
  int? purAuth;

  @JsonKey(name: 'receiveEmail')
  int? receiveEmail;

  @JsonKey(name: 'sendEmail')
  int? sendEmail;

  @JsonKey(name: 'smsNoticeSwitch')
  int? smsNoticeSwitch;

  @JsonKey(name: 'checkStatus')
  int? checkStatus;

  @JsonKey(name: 'grade')
  int? grade;

  @JsonKey(name: 'roleMsg')
  String? roleMsg;

  @JsonKey(name: 'popBtn')
  String? popBtn;

  @JsonKey(name: 'popMsg')
  String? popMsg;

  @JsonKey(name: 'quotaRmb')
  String? quotaRmb;

  @JsonKey(name: 'quotaUsd')
  String? quotaUsd;

  @JsonKey(name: 'login_accounts')
  List<ThirdLoginAccount>? loginAccounts;

  @JsonKey(name: 'im_user_id')
  int? imUserId;

  UserModel(
    this.userId,
    this.nick,
    this.phone,
    this.email,
    this.head,
    this.realName,
    this.name,
    this.sex,
    this.idCardNum,
    this.idCardFront,
    this.idCardBack,
    this.idCardHand,
    this.country,
    this.countryEn,
    this.province,
    this.provinceEn,
    this.city,
    this.cityEn,
    this.area,
    this.address,
    this.status,
    this.registerTime,
    this.lastLoginTime,
    this.lastLoginIp,
    this.role,
    this.inspAge,
    this.adminId,
    this.isAuth,
    this.isFollow,
    this.sparePhone,
    this.quotaId,
    this.facebook,
    this.skype,
    this.twitter,
    this.instgram,
    this.tube,
    this.chatId,
    this.workStatus,
    this.onlyWifi,
    this.remind,
    this.notice,
    this.lon,
    this.lat,
    this.remark,
    this.activeTime,
    this.discern,
    this.resume,
    this.certificationStatus,
    this.onLine,
    this.inspCount,
    this.demoStatus,
    this.downApp,
    this.redDot,
    this.differenceDays,
    this.createdAt,
    this.updatedAt,
    this.isUpdate,
    this.usePub,
    this.cateExh,
    this.fixedPhone,
    this.isSour,
    this.exhName,
    this.postCode,
    this.estaDate,
    this.regCap,
    this.scale,
    this.enteType,
    this.tradePatte,
    this.exhHist,
    this.entePro,
    this.payWay,
    this.regDevice,
    this.emailStatus,
    this.purVerifyTs,
    this.exemptReview,
    this.purAuth,
    this.receiveEmail,
    this.sendEmail,
    this.smsNoticeSwitch,
    this.checkStatus,
    this.grade,
    this.roleMsg,
    this.popBtn,
    this.popMsg,
    this.quotaRmb,
    this.quotaUsd,
    this.loginAccounts,
    this.imUserId,
  );

  factory UserModel.fromJson(Map<String, dynamic> srcJson) => _$UserModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}

@JsonSerializable()
class ThirdLoginAccount extends Object {
  @JsonKey(name: 'user')
  String? user;

  @JsonKey(name: 'type')
  String? type;

  @JsonKey(name: 'third_type')
  int? thirdType;

  @JsonKey(name: 'nick')
  String? nick;

  @JsonKey(name: 'avatar')
  String? avatar;

  ThirdLoginAccount(
    this.user,
    this.type,
    this.thirdType,
    this.nick,
    this.avatar,
  );

  factory ThirdLoginAccount.fromJson(Map<String, dynamic> srcJson) => _$ThirdLoginAccountFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ThirdLoginAccountToJson(this);
}
