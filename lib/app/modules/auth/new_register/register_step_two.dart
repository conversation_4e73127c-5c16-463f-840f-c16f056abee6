import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';
import '../../../config/design.dart';
import '../../../tools/global_const.dart';
import '../../../widgets/send_verification_code_view.dart';
import '../account_service.dart';

class RegisterStepTwoController extends GetxController {
  MineProvider provider = MineProvider();

  //controller
  TextEditingController emailController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  TextEditingController passController = TextEditingController();

  final isLengthMatched = false.obs;
  final hasLowerCase = false.obs;
  final hasDigit = false.obs;
  final hasSpecialCharacter = false.obs;

  final pageType = 0.obs;

  final needVerifyCode = true.obs;
  final needPassword = true.obs;

  final accountFieldEnabled = true.obs;
  final isButtonEnabled = false.obs;

  final isPasswordVisible = false.obs;

  final isRegistering = false.obs;

  String? thirdUid;
  String? thirdData;
  String? thirdType;
  String? errorType;
  String? thirdEmail;
  String? scene;

  late bool accountCanEdit = true;

  @override
  void onInit() {
    super.onInit();
    var params = Get.parameters;
    logger.i('RegisterStepTwoController params $params');
    if (params.isNotEmpty && params.containsKey('error_type')) {
      thirdUid = params['third_uid'];
      thirdData = params['third_data'];
      thirdType = params['third_type'];
      thirdEmail = params['third_email'];
      errorType = params['error_type'];
      scene = params['scene'];
      if (scene == 'register' || scene == 'bind') {
        if (scene == 'register') {
          needPassword.value = true;
        } else {
          needPassword.value = false;
        }
        if (errorType == '0') {
          if (thirdEmail?.isNotEmpty == true) {
            pageType.value = 0;
            emailController.text = thirdEmail!;
            accountFieldEnabled.value = false;
            needVerifyCode.value = false;
            return;
          }
        } else if (errorType == '2') {
          if (thirdEmail?.isNotEmpty == true) {
            pageType.value = 2;
            emailController.text = thirdEmail!;
            accountFieldEnabled.value = false;
            needVerifyCode.value = true;
            return;
          }
        }
      }
    }
    Get.back();
  }

  @override
  void onClose() {
    emailController.dispose();
    codeController.dispose();
    passController.dispose();
    super.onClose();
  }

  void validatePassword(String value) {
    hasLowerCase.value = value.contains(RegExp(r'[a-z]'));
    hasDigit.value = value.contains(RegExp(r'\d'));
    hasSpecialCharacter.value = value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    isLengthMatched.value = value.length >= 8 && value.length <= 32;

    checkButtonEnabled();
  }

  void checkButtonEnabled() {
    isButtonEnabled.value =
        (!needPassword.value || (needPassword.value && hasLowerCase.value && hasDigit.value && hasSpecialCharacter.value && isLengthMatched.value)) &&
            (!needVerifyCode.value || (needVerifyCode.value && codeController.text.isNotEmpty));
  }

  Future<void> register() async {
    isRegistering.value = true;
    try {
      var registerScene = '';
      if (errorType == '0') {
        registerScene = '2';
      } else if (errorType == '2') {
        if (scene == 'register') {
          registerScene = '3';
        } else if (scene == 'bind') {
          registerScene = '5';
        }
      }
      var resp = await MineProvider.registerWithThird(registerScene,
          user: emailController.text, pwd: passController.text, code: codeController.text, thirdUid: thirdUid, thirdType: thirdType, thirdData: thirdData);
      if (resp.isSuccess) {
        await AccountService.instance.saveToken(resp.data);
        var value = await AccountService.instance.fetchUserInfo();
        if (value.isSuccess && GlobalConst.userModel?.uid != null) {
          await AccountService.instance.saveAccount(GlobalConst.userModel!.name!, GlobalConst.userModel!.head!, resp.data, GlobalConst.userModel!.uid!);
          await Get.offAllNamed(Routes.TABBAR);
        }
      } else {
        showToast(resp.message ?? 'Failed');
      }
    } catch (_) {
    } finally {
      isRegistering.value = false;
    }
  }
}

class RegisterStepTwoBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<RegisterStepTwoController>(
      () => RegisterStepTwoController(),
    );
  }
}

class RegisterStepTwoView extends GetView<RegisterStepTwoController> {
  const RegisterStepTwoView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(controller.scene == 'bind' ? S.of(context).third_account_bind : S.of(context).register),
        leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(Icons.arrow_back_ios, size: 18)),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15.0),
          child: SingleChildScrollView(
            child: Obx(() {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 50),
                  _accountTextField,
                  if (controller.needVerifyCode.value) ...{
                    const SizedBox(height: 15),
                    _verificationCodeView,
                  },
                  const SizedBox(height: 15),
                  if (controller.needPassword.value) ...{
                    _passwordView,
                    const SizedBox(height: 15),
                    Text(
                      S.of(context).password_must_have,
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Column(
                      children: [
                        Obx(() {
                          return _buildRequirement(S.of(context).password_must_have_1, controller.isLengthMatched.value);
                        }),
                        Obx(() {
                          return _buildRequirement(S.of(context).password_must_have_2, controller.hasLowerCase.value);
                        }),
                        Obx(() {
                          return _buildRequirement(S.of(context).password_must_have_3, controller.hasDigit.value);
                        }),
                        Obx(() {
                          return _buildRequirement(S.of(context).password_must_have_4, controller.hasSpecialCharacter.value);
                        }),
                      ],
                    ),
                  },
                  const SizedBox(height: 15),
                  _nextButton,
                  const SizedBox(height: 20),
                ],
              );
            }),
          ),
        ),
      ),
    );
  }

  Widget get _verificationCodeView {
    String hintText = S.of(Get.context!).login_verify_tips;

    return Builder(builder: (context) {
      return TextField(
        controller: controller.codeController,
        cursorColor: MColor.skin,
        style: MFont.regular14.apply(color: Colors.black),
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: MFont.regular14.apply(color: MColor.xFFA6A6A6),
          labelStyle: MFont.regular12.apply(color: Colors.white),
          suffixIcon: SendVerificationCodeView(true, controller.emailController.text, '', '', true),
          filled: true,
          isDense: false,
          contentPadding: const EdgeInsets.only(left: 17),
          fillColor: MColor.xFFFFFFFF,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.skin),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
        ),
        onChanged: (text) {
          controller.checkButtonEnabled();
        },
      );
    });
  }

  Widget get _accountTextField {
    return Obx(() {
      return TextField(
        controller: controller.emailController,
        cursorColor: MColor.skin,
        keyboardType: TextInputType.emailAddress,
        style: MFont.regular14.apply(color: Colors.black),
        decoration: InputDecoration(
          hintText: S.of(Get.context!).registry_email_phone_tips,
          hintStyle: MFont.regular14.apply(color: MColor.xFFA6A6A6),
          labelStyle: MFont.regular12.apply(color: MColor.xFF000000),
          labelText: S.of(Get.context!).new_register_bind_field_account_tips,
          filled: true,
          isDense: false,
          enabled: controller.accountFieldEnabled.value,
          contentPadding: const EdgeInsets.only(left: 17),
          fillColor: MColor.xFFFFFFFF,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.skin),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
        ),
        onChanged: (text) {},
      );
    });
  }

  Widget get _passwordView {
    String hintText = S.of(Get.context!).new_password;

    return Obx(() {
      return TextField(
        controller: controller.passController,
        cursorColor: MColor.skin,
        style: MFont.regular14.apply(color: MColor.xFF000000),
        keyboardType: TextInputType.text,
        obscureText: controller.isPasswordVisible.value,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: MFont.regular14.apply(color: MColor.xFFA6A6A6),
          labelStyle: MFont.regular12.apply(color: MColor.xFF000000),
          labelText: S.of(Get.context!).password_login,
          filled: true,
          isDense: false,
          suffixIcon: IconButton(
            icon: Icon(
              controller.isPasswordVisible.value ? Icons.visibility : Icons.visibility_off,
              size: 18,
            ),
            onPressed: () {
              controller.isPasswordVisible.value = !controller.isPasswordVisible.value;
            },
          ),
          contentPadding: const EdgeInsets.only(left: 17),
          fillColor: MColor.xFFFFFFFF,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.skin),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
        ),
        onChanged: (text) {
          controller.validatePassword(text);
        },
      );
    });
  }

  Widget get _nextButton {
    return Obx(() {
      return TextButton(
        style: ButtonStyle(
          shape: WidgetStateProperty.all(const StadiumBorder()),
          backgroundColor: WidgetStateProperty.all(controller.isButtonEnabled.value ? MColor.skin : MColor.skin.withOpacity(0.2)),
          minimumSize: WidgetStateProperty.all(const Size(double.infinity, 49)),
        ),
        onPressed: () {
          if (controller.isButtonEnabled.value && !controller.isRegistering.value) {
            controller.register();
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              controller.scene == 'bind' ? S.of(Get.context!).third_account_bind : S.of(Get.context!).register,
              style: MFont.regular14.apply(color: Colors.white),
            ),
            if (controller.isRegistering.value) ...{
              const SizedBox(
                width: 8,
              ),
              SpinKitCircle(
                color: MColor.skin,
                size: 14,
              )
            }
          ],
        ),
      );
    });
  }

  Widget _buildRequirement(String text, bool isMet) {
    return Row(
      children: <Widget>[
        Icon(
          isMet ? Icons.check_circle_outline : Icons.cancel_outlined,
          color: isMet ? Colors.green : Colors.red,
          size: 18,
        ),
        SizedBox(width: 8),
        Text(
          text,
          style: TextStyle(
            color: isMet ? Colors.green : Colors.red,
          ),
        ),
      ],
    );
  }
}
