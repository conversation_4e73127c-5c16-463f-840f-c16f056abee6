import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/auth/new_register/new_register_controller.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/assets.dart';
import '../../../../generated/l10n.dart';
import '../../../config/design.dart';
import '../../../routes/app_pages.dart';

class NewRegisterView extends GetView<NewRegisterController> {
  const NewRegisterView({super.key});

  @override
  Widget build(BuildContext context) {
    String? registerType = Get.parameters['error_type'];
    return Scaffold(
        appBar: AppBar(
          leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: Icon(Icons.arrow_back_ios, size: 18)),
        ),
        body: SafeArea(
          child: Padding(padding: const EdgeInsets.symmetric(horizontal: 15.0), child: _pageType0View(registerType)),
        ));
  }

  Widget _pageType0View(String? registerType) {
    return Column(
      children: [
        const SizedBox(
          height: 40,
        ),
        Image.asset(Assets.loginLogo),
        const SizedBox(
          height: 30,
        ),
        Row(
          children: [
            Expanded(child: Builder(builder: (context) {
              if (registerType == '0' && controller.thirdEmail.isNotEmpty == true) {
                return Text(
                  textAlign: TextAlign.center,
                  S.of(Get.context!).register_and_bind_email0(controller.thirdEmail),
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                );
              }
              if (registerType == '1' && controller.thirdEmail.isNotEmpty == true) {
                return Text(
                  textAlign: TextAlign.center,
                  S.of(Get.context!).register_and_bind_email1(controller.thirdEmail),
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                );
              }
              if (registerType == '2' || controller.thirdEmail.isNotEmpty == false) {
                return Text(
                  textAlign: TextAlign.center,
                  S.of(Get.context!).register_and_bind_email2,
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                );
              }
              return const SizedBox();
            }))
          ],
        ),
        const SizedBox(
          height: 16,
        ),
        if (registerType == '2' || controller.thirdEmail.isNotEmpty == false) _accountTextField,
        const SizedBox(
          height: 16,
        ),
        TextButton(
            style: ButtonStyle(
                shape: WidgetStateProperty.all(const StadiumBorder()),
                backgroundColor: WidgetStateProperty.all(MColor.skin),
                minimumSize: WidgetStateProperty.all(const Size(double.infinity, 49)),
                textStyle: WidgetStateProperty.all(const TextStyle(fontSize: 14))),
            onPressed: () {
              var params = <String, String>{
                'third_uid': controller.thirdUid,
                'third_data': controller.thirdData,
                'third_type': controller.thirdType,
                'error_type': controller.errorType
              };
              if (registerType == '0' && controller.thirdEmail.isNotEmpty == true) {
                params['third_email'] = controller.thirdEmail;
                params['scene'] = 'register';
                Get.toNamed(Routes.REGISTER_TWO, parameters: params);
              } else if (registerType == '1' && controller.thirdEmail.isNotEmpty == true) {
                controller.directlyBindExistAccount();
              } else if (registerType == '2' || controller.thirdEmail.isNotEmpty == false) {
                controller.checkEmailAvailable().then((status) {
                  //0:可绑定，1：不可绑定，2：未注册
                  if (status == 0 || status == 2) {
                    params['third_email'] = controller.emailController.text;
                    params['scene'] = status == 2 ? 'register' : 'bind';
                    params['error_type'] = '2';
                    unawaited(Get.toNamed(Routes.REGISTER_TWO, parameters: params));
                  } else if (status == 1) {
                    showToast('该账号已被绑定，如需绑定请先登录解绑后重新绑定');
                  }
                });
              }
            },
            child: Text(S.of(Get.context!).register, style: TextStyle(color: MColor.xFFFFFFFF))),
      ],
    );
  }

  Widget get _accountTextField {
    return SizedBox(
      height: 53,
      child: TextField(
        controller: controller.emailController,
        cursorColor: MColor.skin,
        keyboardType: TextInputType.emailAddress,
        style: MFont.regular14.apply(color: Colors.black),
        decoration: InputDecoration(
          hintText: S.of(Get.context!).login_email_tips,
          hintStyle: MFont.regular14.apply(color: MColor.xFFA6A6A6),
          labelStyle: MFont.regular12.apply(color: MColor.xFF000000),
          labelText: S.of(Get.context!).login_email_tips,
          filled: true,
          isDense: false,
          enabled: true,
          contentPadding: const EdgeInsets.only(left: 17),
          fillColor: MColor.xFFFFFFFF,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.skin),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
        ),
        onChanged: (text) {},
      ),
    );
  }
}
