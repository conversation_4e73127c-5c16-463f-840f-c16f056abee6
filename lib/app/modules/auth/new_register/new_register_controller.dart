import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../tools/global_const.dart';
import '../../../tools/loading.dart';
import '../account_service.dart';

class NewRegisterController extends GetxController {
  final emailController = TextEditingController();

  late String thirdUid;
  late String thirdData;
  late String thirdType;
  late String thirdEmail;
  /**
   * 0代表 该帐号未注册且没有邮箱号对应的账户，可以通过邮箱号直接进行绑定
1代表该第三方帐号未注册，且对应的邮箱已注册，可以直接绑定并登录
2代表第三方账号未注册且对应的邮箱已注册但绑定了其他第三方账号
   */
  late String errorType;

  @override
  void onInit() {
    errorType = Get.parameters['error_type']!;
    thirdUid = Get.parameters['third_uid']!;
    thirdData = Get.parameters['third_data']!;
    thirdType = Get.parameters['third_type']!;
    thirdEmail = Get.parameters['third_email'] ?? '';
    logger.i('NewRegisterController params: ${Get.parameters}');
    super.onInit();
  }

  @override
  void onClose() {
    emailController.dispose();
    super.onClose();
  }

  Future<int> checkEmailAvailable() async {
    Loading.show();
    try {
      var resp = await MineProvider.checkBindAvailable('1', emailController.text);
      if (resp.isSuccess) {
        //0:可绑定，1：不可绑定，2：未注册
        return resp.data['status'];
      } else {
        showToast(resp.message ?? '');
        return -1;
      }
    } catch (e) {
      showToast(e.toString());
      return -1;
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> directlyBindExistAccount() async {
    Loading.show();
    try {
      var resp = await MineProvider.registerWithThird('4', user: thirdEmail, thirdUid: thirdUid, thirdData: thirdData, thirdType: thirdType);
      if (resp.isSuccess) {
        await AccountService.instance.saveToken(resp.data);
        var value = await AccountService.instance.fetchUserInfo();
        if (value.isSuccess) {
          if (GlobalConst.userModel?.uid != null) {
            await AccountService.instance.saveAccount(GlobalConst.userModel!.name!, GlobalConst.userModel!.head!, resp.data, GlobalConst.userModel!.uid!);
            unawaited(Get.offAllNamed(Routes.TABBAR));
          }
        }
      } else {
        showToast(resp.message ?? 'Failed');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      Loading.dismiss();
    }
  }
}
