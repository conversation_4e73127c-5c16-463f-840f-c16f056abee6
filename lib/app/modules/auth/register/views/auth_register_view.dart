import 'dart:ui';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/auth/register/controllers/auth_register_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/privacy_helper.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

class AuthRegisterView extends GetView<AuthRegisterController> {
  const AuthRegisterView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    return Scaffold(
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          controller.pageType == BindPageType.register ? S.of(Get.context!).login_register : S.of(Get.context!).login_forget_password,
          style: MFont.medium18.apply(color: MColor.xFF000000),
        ),
        leading: IconButton(onPressed: () {
          Get.back();
        }, icon: Icon(Icons.arrow_back_ios, size: 18,color: MColor.xFF000000),),
      ),
      body: SafeArea(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 22.5),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 40),
              _textField(0),
              const SizedBox(height: 15),
              _textField(1),
              const SizedBox(height: 15),
              _textField(2),
              const SizedBox(height: 15),
              _nextButton,
              const SizedBox(height: 20),
              if (controller.pageType == BindPageType.register) ...{
                _bottomView,
              }
            ],
          ),
        ),
      ),
    );
  }

  // get _titleView {
  //   return Container(
  //     alignment: Alignment.center,
  //     child: Text(
  //       S.of(Get.context!).login_register,
  //       style: MFont.semi_Bold24.apply(color: Colors.white),
  //     ),
  //   );
  // }

  Widget _textField(int index) {
    String hintText = '';
    // Widget? rightView;
    if (index == 0) {
      hintText = S.of(Get.context!).registry_email_phone_tips;
    } else if (index == 1) {
      hintText = S.of(Get.context!).login_verify_tips;
    } else {
      hintText = S.of(Get.context!).new_password;
    }

    return Builder(builder: (context) {
      return TextField(
        controller: index == 0
            ? controller.emailController
            : index == 1
                ? controller.codeController
                : controller.passController,
        cursorColor: MColor.skin,
        style: MFont.regular14.apply(color: MColor.xFF000000),
        obscureText: index == 2,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: MFont.regular14.apply(color: MColor.xFFA6A6A6),
          suffixIcon: index == 0 ? _sendButton : null,
          filled: true,
          isDense: false,
          contentPadding: const EdgeInsets.only(left: 17),
          constraints: const BoxConstraints(maxHeight: 48, minHeight: 48),
          fillColor: MColor.xFFFFFFFF,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: MColor.xFFE5E5E5),
          ),
        ),
        onChanged: (text) {
          if (index == 0) {
            controller.email.value = text;
          } else if (index == 1) {
            controller.code.value = text;
          } else {
            controller.password.value = text;
          }
        },
      );
    });
  }

  Widget get _sendButton {
    return Builder(builder: (context) {
      return TextButton(
        style: ButtonStyle(
          textStyle: MaterialStateProperty.all(MFont.regular13),
          foregroundColor: MaterialStateProperty.all(context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
          minimumSize: MaterialStateProperty.all(const Size(10, 20)),
          visualDensity: VisualDensity.compact,
        ),
        onPressed: () {
          if (!controller.isCheck.value) {
            showPrivacySheet(onConfirm: () {
              controller.isCheck.value = true;
              controller.takeCode();
            });
            return;
          }
          controller.takeCode();
        },
        child: Container(
          padding: const EdgeInsets.only(right: 0),
          child: Obx(() {
            var time = controller.time.value;
            return Text(
              time == 61 ? S.of(Get.context!).login_take_code : '$time',
              style: MFont.regular14.apply(color: MColor.skin),
              textAlign: TextAlign.right,
            );
          }),
        ),
      );
    });
  }

  // get _eyesButton {
  //   return IconButton(
  //     padding: EdgeInsets.only(top: 20),
  //     onPressed: () {
  //       controller.isSee.value = !controller.isSee.value;
  //     },
  //     icon: Obx(() {
  //       return Image.asset(
  //         controller.isSee.value
  //             ? Assets.imagesLoginLogo
  //             : Assets.imagesLoginLogo,
  //         width: 20,
  //         height: 20,
  //         color: Colors.white,
  //       );
  //     }),
  //   );
  // }

  Widget get _nextButton {
    return TextButton(
      style: ButtonStyle(
        shape: MaterialStateProperty.all(const StadiumBorder()),
        backgroundColor: MaterialStateProperty.all(MColor.skin),
        minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
      ),
      onPressed: () {
        if (controller.pageType == BindPageType.bindMobile) {
          controller.bindAction();
        } else if (controller.pageType == BindPageType.reset) {
          controller.resetPassAction();
        } else if (controller.pageType == BindPageType.register) {
          if (!controller.isCheck.value) {
            showPrivacySheet(onConfirm: () {
              controller.isCheck.value = true;
              controller.registerAction();
            });
            return;
          }
          controller.registerAction();
        }
      },
      child: Text(
        S.of(Get.context!).public_ok,
        style: MFont.regular14.apply(color: Colors.white),
      ),
    );
  }

  Widget get _bottomView {
    return Builder(builder: (context) {
      return Container(
        margin: EdgeInsets.only(bottom: 42.0.pixRatio),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                controller.isCheck.value = !controller.isCheck.value;
              },
              child: Obx(() {
                return Container(
                  padding: const EdgeInsets.only(left: 0, right: 10),
                  child: Icon(
                      controller.isCheck.value
                          ? Icons.check_box_outlined
                          : Icons.check_box_outline_blank,
                      size: 15,
                      color: MColor.skin),
                );
              }),
            ),
            Expanded(
              child: RichText(
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  softWrap: true,
                  text: TextSpan(
                      text: S.of(Get.context!).setting_ihavereadandagreed,
                      style: MFont.regular13.apply(color: MColor.xFF000000),
                      //手势监听
                      // recognizer: ,
                      children: [
                        TextSpan(
                            text:
                            '${S.of(Get.context!).setting_user_agreement} ',
                            style: MFont.regular13.apply(color: MColor.skin),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                Get.toNamed(
                                  Routes.WEB,
                                  parameters: {
                                    'url': '${Server.web}/privacy/user.html',
                                    'title': S
                                        .of(Get.context!)
                                        .setting_user_agreement
                                  },
                                );
                              }),
                        TextSpan(
                          text: S.of(Get.context!).public_and,
                          style: MFont.regular13.apply(color: MColor.xFF000000),
                        ),
                        TextSpan(
                            text:
                            ' ${S.of(Get.context!).setting_privacy_policy}',
                            style: MFont.regular13.apply(color: MColor.skin),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                Get.toNamed(
                                  Routes.WEB,
                                  parameters: {
                                    'url':
                                    '${Server.web}/privacy/registerpolicy.html',
                                    'title': S
                                        .of(Get.context!)
                                        .setting_privacy_policy
                                  },
                                );
                              }),
                      ])),
            ),
          ],
        ),
      );
    });
  }
}
