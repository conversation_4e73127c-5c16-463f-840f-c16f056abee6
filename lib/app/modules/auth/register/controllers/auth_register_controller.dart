import 'dart:async';

import 'package:flutter/material.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/modules/auth/account_service.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';

enum BindPageType { register, reset, bindEmail, bindMobile }

class AuthRegisterController extends GetxController {
  MineProvider provider = MineProvider();
  //controller
  TextEditingController emailController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  TextEditingController passController = TextEditingController();
  //state
  RxBool codeEnable = false.obs;
  RxBool buttonEnable = false.obs;
  //data
  RxString email = ''.obs;
  RxString code = ''.obs;
  RxString password = ''.obs;
  RxInt time = 61.obs;
  Timer? _timer;
  final isSee = true.obs;
  final isCheck = false.obs;
  var pageType = BindPageType.register;

  @override
  void onInit() {
    super.onInit();

    pageType = Get.arguments;
  }

  void takeCode() {
    Get.showOverlay(asyncFunction: () => Future.value(), loadingWidget: loadingWidget);
    if (!GetUtils.isEmail(email.value) && !GetUtils.isPhoneNumber(email.value)) {
      showToast(S.of(Get.context!).registry_email_phone_tips);
      return;
    }
    if (time.value != 61) {
      return;
    }
    _timer?.cancel();
    _timer = null;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (time.value == 0) {
        _timer?.cancel();
        _timer = null;
        time.value = 61;
        return;
      }
      time.value = time.value - 1;
    });
    provider.takeCode(true, email.value, null).then((value) {
      if (!value.isSuccess) {
        _timer?.cancel();
        _timer = null;
        time.value = 61;
      }
      showToast(value.message ?? '');
    });
  }

  Future<void> registerAction() async {
    if (!GetUtils.isEmail(email.value) && !GetUtils.isPhoneNumber(email.value)) {
      showToast(S.of(Get.context!).registry_email_phone_tips);
      return;
    }

    if (password.value.isEmpty) {
      showToast(S.of(Get.context!).login_password_tips);
      return;
    }

    if (code.value.isEmpty) {
      showToast(S.of(Get.context!).login_verify_tips);
      return;
    }

    unawaited(EasyLoading.show());
    try {
      var resp = await MineProvider.registerWithThird('1', user: email.value, pwd: password.value, code: code.value);
      if (resp.isSuccess) {
        await AccountService.instance.saveToken(resp.data);
        var value = await AccountService.instance.fetchUserInfo();
        if (value.isSuccess && GlobalConst.userModel?.uid != null) {
          await AccountService.instance.saveAccount(GlobalConst.userModel!.name!, GlobalConst.userModel!.head!, resp.data, GlobalConst.userModel!.uid!);
          unawaited(Get.offAllNamed(Routes.TABBAR));
        }
      } else {
        showToast(resp.message ?? '');
      }
    } catch (e) {
      showToast(e.toString());
    } finally {
      unawaited(EasyLoading.dismiss());
    }
  }

  void bindAction() {
    if (!GetUtils.isEmail(email.value) && !GetUtils.isPhoneNumber(email.value)) {
      showToast(S.of(Get.context!).registry_email_phone_tips);
      return;
    }

    if (code.value.isEmpty) {
      showToast(S.of(Get.context!).login_verify_tips);
      return;
    }
    EasyLoading.show();
    provider.bindMobile(email.value, '0', code.value).then((value) async {
      if (value.isSuccess) {
        Get.back();
      }
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void resetPassAction() {
    if (!GetUtils.isEmail(email.value) && !GetUtils.isPhoneNumber(email.value)) {
      showToast(S.of(Get.context!).registry_email_phone_tips);
      return;
    }

    if (password.value.isEmpty) {
      showToast(S.of(Get.context!).login_password_tips);
      return;
    }

    if (code.value.isEmpty) {
      showToast(S.of(Get.context!).login_verify_tips);
      return;
    }

    EasyLoading.show();
    provider.resetPassword(email.value, password.value, code.value).then((value) async {
      if (value.isSuccess) {
        Get.back();
      }
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  @override
  void onClose() {}
}
