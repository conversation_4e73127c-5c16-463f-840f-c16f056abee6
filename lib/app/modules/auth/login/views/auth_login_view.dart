import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';

import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/area_list_entity.dart';
import 'package:inspector/app/modules/auth/login/controllers/auth_login_controller.dart';
import 'package:inspector/app/modules/auth/models/saved_account_entity.dart';
import 'package:inspector/app/modules/auth/register/controllers/auth_register_controller.dart';
import 'package:inspector/app/modules/auth/third_login_store.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/shared/widgets/avatar.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/icons.dart';
import 'package:inspector/app/tools/privacy_helper.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/flavors.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../../../tools/storage_util.dart';
import '../../account_service.dart';

class AuthLoginView extends GetView<AuthLoginController> {
  const AuthLoginView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        extendBodyBehindAppBar: true,
        body: SafeArea(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            decoration: BoxDecoration(color: MColor.xFFFFFFFF),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                const SizedBox(
                  height: 83,
                ),
                Image.asset(Assets.loginLogo),
                Expanded(
                  child: ListView(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    children: [
                      SizedBox(height: 40.0),
                      _accountField,
                      SizedBox(height: 15.0),
                      _passwordField,
                      Obx(() {
                        if (controller.loginType.value == LoginType.code) {
                          return SizedBox(height: 15.0);
                        } else {
                          return Column(
                            children: [
                              SizedBox(
                                height: 9,
                              ),
                              _forgotPwdView,
                              SizedBox(
                                height: 9,
                              )
                            ],
                          );
                        }
                      }),
                      _loginButton,
                      SizedBox(height: 23.0),
                      _otherView,
                      SizedBox(height: 100.0),
                      _googleView,
                    ],
                  ),
                ),
                _bottomView,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget get _googleView {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // if (F.appFlavor == Flavor.dev || F.appFlavor == Flavor.gp) ...{
        IconButton(
          onPressed: () {
            if (controller.isCheck.value) {
              controller.signInWithGoogle();
            } else {
              showPrivacySheet(onConfirm: () {
                controller.isCheck.value = true;
                controller.signInWithGoogle();
              });
            }
          },
          icon: Image.asset(
            Assets.imageLogoGoogle,
          ),
          iconSize: 47,
        ),
        if (Platform.isIOS) ...{
          IconButton(
            onPressed: () {
              if (controller.isCheck.value) {
                controller.signInWithApple();
              } else {
                showPrivacySheet(onConfirm: () {
                  controller.isCheck.value = true;
                  controller.signInWithApple();
                });
              }
            },
            icon: Image.asset(Assets.imageLogoApple),
            iconSize: 47,
          )
        },
        GetBuilder<ThirdLoginStore>(builder: (thirdLoginStore) {
          if (thirdLoginStore.isWechatInstalled) {
            return IconButton(
              onPressed: () {
                if (controller.isCheck.value) {
                  thirdLoginStore.signInWithWechat();
                } else {
                  showPrivacySheet(onConfirm: () {
                    controller.isCheck.value = true;
                    thirdLoginStore.signInWithWechat();
                  });
                }
              },
              icon: Image.asset(
                Assets.imageLogoWechat,
              ),
              iconSize: 47,
            );
          } else {
            return const SizedBox();
          }
        })
      ],
    );
  }

  Widget get _accountField {
    return Obx(() {
      bool isEmail = controller.isEmailInput.value;

      String hintText = '';
      Widget? rightView;
      if (controller.showClear.value) {
        rightView = IconButton(
            padding: const EdgeInsets.all(4),
            visualDensity: VisualDensity.compact,
            onPressed: () {
              controller.emailController.clear();
              controller.validateAccount('');
            },
            icon: Icon(
              Icons.cancel_outlined,
              size: 16,
              color: MColor.xFFA6A6A6,
            ));
      } else if (controller.savedAccounts.isNotEmpty) {
        rightView = IconButton(
            padding: const EdgeInsets.all(4),
            visualDensity: VisualDensity.compact,
            onPressed: () {
              Get.bottomSheet(
                SafeArea(child: AccountHistoryView()),
                persistent: false,
                isScrollControlled: true,
                ignoreSafeArea: false,
              ).then((value) {
                if (value is Map && value.containsKey('isSuccess') && value['isSuccess']) {}
              });
            },
            icon: const Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: MColor.xFFA6A6A6,
            ));
      }
      hintText = S.of(Get.context!).registry_email_phone_tips;

      return Builder(builder: (context) {
        return SizedBox(
          height: 53,
          child: TextField(
            controller: controller.emailController,
            cursorColor: MColor.skin,
            style: MFont.regular14.apply(color: MColor.xFF000000),
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: MFont.regular14.apply(color: MColor.xFFA6A6A6),
              // suffix: rightView,
              suffixIcon: rightView,
              prefixIcon: !isEmail ? _areaButton : null,
              filled: true,
              isDense: false,
              contentPadding: const EdgeInsets.symmetric(horizontal: 17),
              fillColor: MColor.xFFFFFFFF,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: MColor.xFFE5E5E5),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: MColor.skin),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: MColor.xFFE5E5E5),
              ),
            ),
            onChanged: (text) {
              controller.validateAccount(text);
            },
          ),
        );
      });
    });
  }

  Widget get _passwordField {
    return Obx(() {
      bool isCode = controller.loginType.value == LoginType.code;
      bool hidePassword = controller.hidePassword.value;

      String hintText = '';
      Widget? rightView;

      if (isCode) {
        hintText = S.of(Get.context!).login_verify_tips;
        rightView = _sendButton;
      } else {
        hintText = S.of(Get.context!).login_password_tips;
        rightView = IconButton(
            padding: const EdgeInsets.all(4),
            visualDensity: VisualDensity.compact,
            onPressed: () {
              controller.hidePassword.value = !controller.hidePassword.value;
            },
            icon: Icon(hidePassword ? Icons.visibility_off : Icons.visibility, size: 16, color: MColor.xFFA6A6A6));
      }

      TextInputType type = TextInputType.text;
      if (controller.loginType.value == LoginType.code) {
        type = TextInputType.number;
      } else if (controller.loginType.value == LoginType.password) {
        type = TextInputType.text;
      }

      return Builder(builder: (context) {
        return SizedBox(
          height: 53,
          child: TextField(
            controller: controller.codeController,
            cursorColor: MColor.skin,
            style: MFont.regular14.apply(color: MColor.xFF000000),
            //obscureText: isEmail && !isCode && index == 1,
            //mydev
            obscureText: !isCode && hidePassword,
            keyboardType: type,
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: MFont.regular14.apply(color: MColor.xFFA6A6A6),
              // suffix: rightView,
              suffixIcon: rightView,
              filled: true,
              isDense: false,
              contentPadding: const EdgeInsets.symmetric(horizontal: 17),
              fillColor: MColor.xFFFFFFFF,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: MColor.xFFE5E5E5),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: MColor.skin),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: MColor.xFFE5E5E5),
              ),
            ),
            onChanged: (text) {
              controller.checkLoginEnabled();
            },
          ),
        );
      });
    });
  }

  Widget get _sendButton {
    return Builder(builder: (context) {
      return GestureDetector(
        onTap: () {
          controller.takeCode();
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Container(
              color: MColor.xFFE5E5E5,
              margin: const EdgeInsets.fromLTRB(15, 12, 15, 10),
              width: 1,
            ),
            Obx(() {
              var time = controller.time.value;
              return Text(
                time == 61 ? S.of(Get.context!).login_take_code : '$time',
                style: MFont.regular14.apply(color: MColor.skin),
                textAlign: TextAlign.right,
              );
            }),
            const SizedBox(
              width: 16,
            )
          ],
        ),
      );
    });
  }

  Widget get _areaButton {
    return GestureDetector(
      onTap: () {
        Get.toNamed(Routes.AREALIST)?.then((value) {
          AreaListEntity entity = value;
          controller.areaCode.value = entity.code;
        });
      },
      child: SizedBox(
        height: 30,
        child: Container(
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: Builder(builder: (context) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Obx(() {
                  return Text(
                    '+${controller.areaCode.value}',
                    textAlign: TextAlign.center,
                    style: MFont.regular14.apply(color: MColor.xFF000000),
                  );
                }),
                const SizedBox(width: 3),
                Icon(Icons.arrow_drop_down, size: 15, color: MColor.xFF000000),
                const SizedBox(
                  width: 10,
                ),
                Container(
                  color: MColor.xFFE5E5E5,
                  margin: const EdgeInsets.fromLTRB(0, 12, 0, 12),
                  width: 1,
                ),
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget get _loginButton {
    return Obx(() {
      return SizedBox(
        height: 53,
        child: TextButton(
          style: ButtonStyle(
            shape: WidgetStateProperty.all(const StadiumBorder()),
            backgroundColor: WidgetStateProperty.all(controller.loginEnable.value ? MColor.skin : MColor.skin_20),
          ),
          onPressed: () {
            if (controller.loginEnable.value) {
              if (controller.isCheck.value) {
                controller.signInWithAccount();
              } else {
                showPrivacySheet(onConfirm: () {
                  controller.isCheck.value = true;
                  controller.signInWithAccount();
                });
              }
            }
          },
          child: Text(
            S.of(Get.context!).login_submit,
            style: MFont.medium18.apply(color: MColor.xFFFFFFFF),
          ),
        ),
      );
    });
  }

  Widget get _forgotPwdView {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            Get.toNamed(Routes.Register, arguments: BindPageType.reset);
          },
          child: Text(
            '${S.of(Get.context!).login_forget_password}?',
            style: MFont.regular14.apply(color: MColor.skin),
          ),
        ),
        const SizedBox(width: 7),
      ],
    );
  }

  Widget get _otherView {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        const SizedBox(
          width: 7,
        ),
        Expanded(child: Container()),
        Expanded(
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              controller.codeController.text = '';
              if (controller.loginType.value == LoginType.code) {
                controller.loginType.value = LoginType.password;
              } else {
                controller.loginType.value = LoginType.code;
              }
            },
            child: Obx(() {
              return Text(
                controller.loginType.value == LoginType.code ? S.of(Get.context!).login_password_login : S.of(Get.context!).login_verify_login,
                style: MFont.regular14.apply(color: MColor.skin),
              );
            }),
          ),
        ),
        Expanded(
            child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            Get.toNamed(Routes.Register, arguments: BindPageType.register);
          },
          child: Text(
            textAlign: TextAlign.end,
            S.of(Get.context!).login_register,
            style: MFont.regular14.apply(color: MColor.skin),
          ),
        )),
        const SizedBox(width: 7),
      ],
    );
  }

  Widget get _bottomView {
    return Builder(builder: (context) {
      return Container(
        margin: EdgeInsets.only(bottom: 42.0.pixRatio),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                controller.isCheck.value = !controller.isCheck.value;
              },
              child: Obx(() {
                return Container(
                  padding: const EdgeInsets.only(left: 0, right: 10),
                  child: Icon(controller.isCheck.value ? Icons.check_box_outlined : Icons.check_box_outline_blank, size: 15, color: MColor.skin),
                );
              }),
            ),
            Expanded(
              child: RichText(
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  softWrap: true,
                  text: TextSpan(
                      text: S.of(Get.context!).setting_ihavereadandagreed,
                      style: MFont.regular13.apply(color: MColor.xFF000000),
                      //手势监听
                      // recognizer: ,
                      children: [
                        TextSpan(
                            text: '${S.of(Get.context!).setting_user_agreement} ',
                            style: MFont.regular13.apply(color: MColor.skin),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                Get.toNamed(
                                  Routes.WEB,
                                  parameters: {'url': '${Server.web}/privacy/user.html', 'title': S.of(Get.context!).setting_user_agreement},
                                );
                              }),
                        TextSpan(
                          text: S.of(Get.context!).public_and,
                          style: MFont.regular13.apply(color: MColor.xFF000000),
                        ),
                        TextSpan(
                            text: ' ${S.of(Get.context!).setting_privacy_policy}',
                            style: MFont.regular13.apply(color: MColor.skin),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                Get.toNamed(
                                  Routes.WEB,
                                  parameters: {'url': '${Server.web}/privacy/registerpolicy.html', 'title': S.of(Get.context!).setting_privacy_policy},
                                );
                              }),
                      ])),
            ),
          ],
        ),
      );
    });
  }
}

class AccountHistoryView extends GetView<AuthLoginController> {
  const AccountHistoryView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.fromLTRB(15, 0, 15, 20),
        decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16))),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            headerView,
            Container(
              padding: EdgeInsets.fromLTRB(12, 8, 12, 8),
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(14), border: Border.all(color: MColor.xFFE5E5E5)),
              child: Obx(() {
                return ListView.separated(
                  separatorBuilder: (context, index) => const SizedBox(
                    height: 12,
                  ),
                  itemBuilder: (context, index) {
                    return _accountItemView(controller.savedAccounts[index]);
                  },
                  itemCount: controller.savedAccounts.length,
                  shrinkWrap: true,
                );
              }),
            )
          ],
        ));
  }

  Widget get headerView {
    return Column(
      children: [
        const SizedBox(
          height: 16,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 4,
              width: 32,
              child: Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(2), color: MColor.xFFA6A6A6),
              ),
            )
          ],
        ),
        const SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Text(S.of(Get.context!).choose_account_to_login, style: TextStyle(color: MColor.xFF000000, fontSize: 14)),
            const Spacer(),
            TextButton(
                style: TextButton.styleFrom(
                    minimumSize: Size(0, 0),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    padding: EdgeInsets.symmetric(vertical: 4),
                    textStyle: const TextStyle(fontSize: 14)),
                onPressed: () {
                  controller.isEditingSavedAccounts.value = !controller.isEditingSavedAccounts.value;
                },
                child: Obx(() {
                  return Text(controller.isEditingSavedAccounts.value ? S.of(Get.context!).done : S.of(Get.context!).edit,
                      style: TextStyle(color: MColor.xFF000000));
                }))
          ],
        ),
        const SizedBox(
          height: 8,
        ),
      ],
    );
  }

  Widget _accountItemView(SavedAccount account) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        controller.loginWithToken(account.token);
      },
      child: Container(
        color: MColor.xFFFFFFFF,
        height: 46,
        child: Row(
          children: [
            Avatar(
              size: 46,
              url: account.avatar,
              displayName: account.account,
            ),
            const SizedBox(
              width: 8,
            ),
            Text(account.account),
            const Spacer(),
            Obx(() {
              if (controller.isEditingSavedAccounts.value) {
                return IconButton(
                    onPressed: () {
                      controller.removeSavedAccount(account);
                    },
                    icon: Icon(Icons.close, color: MColor.xFF000000, size: 16));
              } else {
                return SizedBox();
              }
            })
          ],
        ),
      ),
    );
  }
}
