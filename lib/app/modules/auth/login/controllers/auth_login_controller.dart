import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluwx/fluwx.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';

import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/data/area_list_entity.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/modules/auth/account_service.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/auth/models/saved_account_entity.dart';
import 'package:inspector/app/modules/auth/third_login_store.dart';
import 'package:inspector/app/modules/store/privacy_store.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/loading.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

import 'package:shared_preferences/shared_preferences.dart';

import 'package:inspector/app/tools/privacy_helper.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../../../tools/storage_util.dart';
import '../../register/controllers/auth_register_controller.dart';

enum LoginType {
  code(-1),
  password(-1),
  google(1),
  apple(2),
  wechat(3);

  final int type;

  const LoginType(this.type);
}

class AuthLoginController extends GetxController implements WechatCallback {
  MineProvider provider = MineProvider();

  //controller
  TextEditingController emailController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  TextEditingController searchController = TextEditingController();

  //state
  final loginType = LoginType.password.obs;
  final isEmailInput = true.obs;
  final areaCode = '86'.obs;
  var areaList = <AreaListEntity>[].obs;
  var areaCopyList = <AreaListEntity>[];

  final hidePassword = true.obs;
  final showClear = false.obs;

  final savedAccounts = <SavedAccount>[].obs;
  final isEditingSavedAccounts = false.obs;

  RxBool loginEnable = false.obs;

  //data
  RxInt time = 61.obs;
  Timer? _timer;
  final isCheck = false.obs;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();

    if (!PrivacyStore.to.isPrivacyAgreed()) {
      showPrivacyDialog();
    }
    isCheck.listen((val) {
      if (val) {
        ThirdLoginStore.to.initSdk();
      }
    });
    ThirdLoginStore.to.addWechatCallback(this);
    _loadSavedAccounts();
  }

  void _loadSavedAccounts() {
    savedAccounts.value = AccountService.instance.getSavedAccount();
  }

  void takeCode() {
    if ((isEmailInput.value && !emailController.text.isEmail) || (!isEmailInput.value && emailController.text.length <= 5)) {
      showToast(S.of(Get.context!).registry_email_phone_tips);
      return;
    }
    if (time.value != 61) {
      return;
    }

    Get.showOverlay(
      asyncFunction: () => Future.value(),
      loadingWidget: loadingWidget,
    );
    _timer?.cancel();
    _timer = null;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (time.value == 0) {
        _timer?.cancel();
        _timer = null;
        time.value = 61;
        return;
      }
      time.value = time.value - 1;
    });
    provider.takeCode(isEmailInput.value, emailController.text, isEmailInput.value ? null : areaCode.value).then((value) {
      if (!value.isSuccess) {
        _timer?.cancel();
        _timer = null;
        time.value = 61;
      }
      showToast(value.message ?? '');
    });
  }

  Future<void> signInWithAccount() async {
    await tryLogin(loginType.value);
  }

  Future<void> tryLogin(LoginType loginType, {String? thirdUid, String? thirdData, String? thirdEmail}) async {
    Loading.show();
    logger.i('tryLogin $loginType');
    try {
      var resp = await MineProvider.userLogin(
          user: emailController.text,
          pwd: loginType == LoginType.password ? codeController.text : null,
          code: loginType == LoginType.code ? codeController.text : null,
          thirdUid: thirdUid,
          thirdData: thirdData,
          areaCode: !isEmailInput.value && loginType == LoginType.code ? areaCode.value : null,
          thirdType: loginType == LoginType.password || loginType == LoginType.code ? null : loginType.type.toString());
      if (resp.isSuccess) {
        // await GlobalConst.saveUser(value.data!);
        await AccountService.instance.saveToken(resp.data);
        await AccountService.instance.fetchUserInfo();
        if (GlobalConst.userModel?.uid != null) {
          await AccountService.instance.saveAccount(GlobalConst.userModel!.name!, GlobalConst.userModel!.head!, resp.data, GlobalConst.userModel!.uid!);
        }
        unawaited(Get.offAllNamed(Routes.TABBAR));
      } else if (resp.code == 20006) {
        //第三方登录，账号未在数据控中查到，需跳转注册
        logger.i('resp code == 20006 ${resp.data}');
        var params = <String, String>{};
        if (resp.data is Map<String, dynamic> && resp.data.containsKey('error_type')) {
          var errorType = resp.data['error_type'];
          // var errorType = 1;
          params['third_uid'] = thirdUid!;
          params['third_data'] = thirdData!;
          params['error_type'] = errorType.toString();
          params['third_type'] = loginType.type.toString();
          if (thirdEmail?.isNotEmpty == true) {
            params['third_email'] = thirdEmail!;
          }
          if (errorType == 0 || errorType == 1) {
            //0 该帐号未注册且没有邮箱号对应的账户，可以通过手机号/邮箱号直接进行绑定
            //1 该第三方帐号未注册，且对应的邮箱已注册，可以直接绑定并登录
            // if (thirdEmail?.isNotEmpty == true) {
            unawaited(Get.toNamed(Routes.NewRegister, parameters: params));
            // } else {
            //   showToast('Failed');
            // }
          } else if (errorType == 2) {
            //第三方账号未注册且对应邮箱已注册但绑定了其他第三方账号
            unawaited(Get.toNamed(Routes.NewRegister, parameters: params));
          }
        } else {
          showToast(resp.message ?? 'Failed');
        }
      } else {
        showToast(resp.message ?? 'Failed');
      }
    } catch (e) {
      logger.e('tryLogin error $e');
    } finally {
      Loading.dismiss();
    }
  }

  Future<void> removeSavedAccount(SavedAccount item) async {
    await AccountService.instance.removeSavedAccount(item.userId);
    _loadSavedAccounts();
  }

  void fetchAreaList() {
    EasyLoading.show();
    provider.areaList().then((value) async {
      if (value.isSuccess) {
        areaList.value = value.data ?? [];
        areaCopyList = List.from(areaList);
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void searchWords() {
    var text = searchController.text.trim();
    if (text.isEmpty) {
      areaList.value = areaCopyList;
      return;
    }
    List<AreaListEntity> temp = List.from(areaCopyList);
    temp.retainWhere((element) => element.name.contains(text));
    areaList.value = temp;
  }

// Google登录
  Future<void> signInWithGoogle() async {
    GoogleSignInAccount? googleUser1 = await GoogleSignIn().signOut();
    GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
    logger.i('singInWithGoogle user $googleUser');
    // Obtain the auth details from the request
    GoogleSignInAuthentication? googleAuth = await googleUser?.authentication;
    logger.i('singInWithGoogle accessTokenLength ${googleAuth?.accessToken?.length} accessToken:${googleAuth?.accessToken}');
    logger.i('singInWithGoogle idTokenLength ${googleAuth?.idToken?.length} idToken:${googleAuth?.idToken}');
    // Create a new credential
    var credential = GoogleAuthProvider.credential(
      accessToken: googleAuth?.accessToken,
      idToken: googleAuth?.idToken,
    );
    logger.i('singInWithGoogle credential $credential');
    // Once signed in, return the UserCredential
    var userCredential = await FirebaseAuth.instanceFor(app: ThirdLoginStore.to.firebaseApp!).signInWithCredential(credential);
    logger.i('singInWithGoogle ${userCredential.user?.uid} $userCredential');

    await tryLogin(LoginType.google, thirdUid: userCredential.user?.uid, thirdData: googleAuth?.idToken, thirdEmail: userCredential.user?.email);
  }

  Future<void> signInWithApple() async {
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );

    logger.i(credential);
    await tryLogin(LoginType.apple, thirdUid: credential.userIdentifier, thirdData: credential.identityToken);
  }

  @override
  void onClose() {
    super.onClose();
    emailController.dispose();
    codeController.dispose();
    searchController.dispose();
    ThirdLoginStore.to.removeWechatCallback(this);
  }

  void validateAccount(String text) {
    var isMobile = false;
    if (text.isNotEmpty) {
      isMobile = true;
      for (var char in text.characters) {
        if (!char.isNum) {
          isMobile = false;
          break;
        }
      }
    }
    isEmailInput.value = !isMobile;
    showClear.value = text.isNotEmpty;
    checkLoginEnabled();
  }

  void checkLoginEnabled() {
    if (codeController.text.isNotEmpty) {
      if (isEmailInput.value) {
        loginEnable.value = emailController.text.isEmail && codeController.text.isNotEmpty;
      } else {
        loginEnable.value = emailController.text.length > 5 && codeController.text.isNotEmpty;
      }
    } else {
      loginEnable.value = false;
    }
  }

  Future<void> loginWithToken(String token) async {
    unawaited(EasyLoading.show());
    try {
      await AccountService.instance.saveToken(token);
      await AccountService.instance.fetchUserInfo();
      if (GlobalConst.userModel?.uid != null) {
        await AccountService.instance.saveAccount(GlobalConst.userModel!.name!, GlobalConst.userModel!.head!, token, GlobalConst.userModel!.uid!);
      }
      unawaited(Get.offAllNamed(Routes.TABBAR));
    } catch (_) {
    } finally {
      unawaited(EasyLoading.dismiss());
    }
  }

  Future<void> _userLoginByWechat(String code) async {
    try {
      Loading.show();
      var resp = await MineProvider.userParsing('3', code);
      if (resp.isSuccess) {
        var uid = resp.data['uid'];
        await tryLogin(LoginType.wechat, thirdUid: uid, thirdData: json.encode(resp.data));
      }
    } catch (e) {
      logger.e('_userLoginByWechat error $e');
    } finally {
      Loading.dismiss();
    }
  }

  @override
  void onLoginSuccess({required String thirdUid, required String thirdData}) {
    tryLogin(LoginType.wechat, thirdUid: thirdUid, thirdData: thirdData);
  }
}
