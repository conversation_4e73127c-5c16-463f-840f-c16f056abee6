import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/insp_order_entity.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/profile/user_profile/user_profile_controller.dart';
import 'package:inspector/app/modules/purchase/purchase_complaint/purchase_complaint_view.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/app_theme.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

const int TYPE_HEAD = 0;
const int TYPE_EMAIL = 1;
const int TYPE_PHONE = 2;
const int TYPE_COUNTRY = 3;
const int TYPE_CITY = 4;
const int TYPE_INSP_FEE = 5;
const int TYPE_CHAT = 6;
const int TYPE_INSP_ITEM = 7;
const int TYPE_COUNT = TYPE_INSP_ITEM + 1;

class UserProfileView extends GetView<UserProfileController> {
  @override
  final String? tag;

  const UserProfileView({Key? key, this.tag}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          title: Text(S.of(Get.context!).profile_title),
          centerTitle: true,
          actions: [
            Obx(
              () {
                if (controller.userInfo.value?.uid != null)
                  return IconButton(
                      onPressed: () {
                        if (controller.userInfo.value?.uid == null) {
                          return;
                        }
                        Get.bottomSheet(
                          SafeArea(
                            child: BottomSheet(
                              onClosing: () {},
                              enableDrag: false,
                              builder: (context) {
                                return PurchaseComplaintView(controller.userInfo.value!.uid!, 2, tag: tag);
                              },
                            ),
                          ),
                          isScrollControlled: true,
                          ignoreSafeArea: false,
                          persistent: false,
                        ).then((value) {
                          if (value is bool && value == true) {
                            //刷新
                          }
                        });
                      },
                      icon: Icon(Icons.report_outlined));
                else
                  return const SizedBox();
              },
            )
          ],
        ),
        body: Obx(() {
          var user = controller.userInfo.value;
          if (user == null) {
            return const SizedBox();
          }
          var count = controller.histories.length;
          return ListView.separated(
            itemBuilder: (context, index) {
              logger.i('itemBuilder index $index');
              if (_itemType(index) == TYPE_HEAD) {
                return _headView;
              } else if (_itemType(index) == TYPE_EMAIL) {
                return _emailView;
              } else if (_itemType(index) == TYPE_PHONE) {
                return _phoneView;
              } else if (_itemType(index) == TYPE_COUNTRY) {
                return _countryView;
              } else if (_itemType(index) == TYPE_CITY) {
                return _cityView;
              } else if (_itemType(index) == TYPE_INSP_FEE) {
                return _inspFeeView;
              } else if (_itemType(index) == TYPE_CHAT) {
                return _messageView;
              } else if (_itemType(index) == TYPE_INSP_ITEM) {
                return _historyView(controller.histories[index - TYPE_INSP_ITEM]);
              } else {
                return const SizedBox();
              }
            },
            separatorBuilder: (context, index) {
              logger.i('separator index $index');
              if (_itemType(index) == TYPE_CHAT) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  child: Row(children: [
                    Expanded(
                        child: Text(
                      '以下为最近的${controller.histories.length}条验货记录',
                      style: MFont.semi_Bold16.apply(
                        color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ))
                  ]),
                );
              }
              return const Divider(height: 1);
            },
            itemCount: _itemCount,
          );
        }));
  }

  int get _itemCount {
    UserInfoEntity? userInfo = controller.userInfo.value;
    if (userInfo == null) {
      return 0;
    } else if (GlobalConst.userModel?.role == UserRole.admin && !controller.isAdmin) {
      //如果当前是管理员，可以看到用户的头像+昵称/邮箱/手机号
      return TYPE_COUNT + controller.histories.length - 1;
    } else {
      return 2;
    }
  }

  int _itemType(index) {
    if (GlobalConst.userModel?.role == UserRole.admin) {
      //如果当前是管理员，可以看到用户的头像+昵称/邮箱/手机号
      if (index == 0) {
        return TYPE_HEAD;
      } else if (index == 1) {
        return TYPE_EMAIL;
      } else if (index == 2) {
        return TYPE_PHONE;
      } else if (index == 3) {
        return TYPE_COUNTRY;
      } else if (index == 4) {
        return TYPE_CITY;
      } else if (index == 5) {
        return TYPE_INSP_FEE;
      } else if (index == 6) {
        return TYPE_CHAT;
      }
      return TYPE_INSP_ITEM;
    } else {
      if (index == 0) {
        return TYPE_HEAD;
      }
      return TYPE_CHAT;
    }
  }

  Widget _historyView(InspOrderEntity model) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Get.toNamed(Routes.LIST_DETAIL, arguments: model.orderId, parameters: {'isApply': '1'});
      },
      child: Builder(builder: (context) {
        return Container(
          margin: const EdgeInsets.only(left: 12, right: 12, top: 10),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.conatiner,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.only(top: 16, left: 12),
                    child: Row(
                      children: [
                        Text(
                          Helper.parseChinaArea(model.ordersNum),
                          style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                        ),
                        const SizedBox(width: 4),
                      ],
                    ),
                  ),
                  const Spacer(),
                ],
              ),
              Visibility(
                visible: model.inspAddress.isNotEmpty,
                child: Container(
                  padding: const EdgeInsets.only(left: 12, right: 12, top: 8),
                  child: Text(
                    model.inspAddress,
                    style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 13, left: 12, right: 12),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                height: 44,
                color: context.isDarkMode ? DarkColor.xFFF7F8F9 : MColor.xFFF7F8F9,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: Text(
                        model.inspectTime,
                        style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(color: context.isDarkMode ? DarkColor.xFFDDDDDD : MColor.xFFDDDDDD, width: 1, height: 24),
                    Expanded(
                      child: Text(
                        Helper.parseOrderType(model.type ?? 0),
                        style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.only(left: 12, right: 12, top: 6, bottom: 2),
                child: Row(
                  children: [
                    Text(
                      S.of(Get.context!).home_product_tip,
                      style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383),
                    ),
                    Expanded(
                      child: Text(
                        model.productName.join(', '),
                        style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget get _inspFeeView {
    var user = controller.userInfo.value;
    var inspectorCheck = controller.inspectorCheck.value;
    if (user == null || inspectorCheck == null || inspectorCheck.accountType == null) {
      return const SizedBox();
    }

    var accountType = inspectorCheck.accountType!;
    var inspFee = '';
    if (accountType == UserAccountType.CNY && (inspectorCheck.initialPrice?.isNotEmpty ?? false)) {
      inspFee = '${accountType.symbol}${inspectorCheck.initialPrice}';
    } else if (accountType == UserAccountType.USD && (inspectorCheck.initialMoney?.isNotEmpty ?? false)) {
      inspFee = '${accountType.symbol}${inspectorCheck.initialMoney}';
    } else {
      return const SizedBox();
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      child: Row(children: [
        SizedBox(width: 70, child: Text(S.of(Get.context!).order_detail_inspection_cost)),
        const SizedBox(width: 12),
        Expanded(
            child: Text(
          inspFee,
          style: MFont.semi_Bold16.apply(
            color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
          ),
          overflow: TextOverflow.ellipsis,
        ))
      ]),
    );
  }

  Widget get _messageView {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18),
      child: TextButton(
        onPressed: () {
          controller.sendMessage();
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          S.of(Get.context!).chat_send_message,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }

  Widget get _headView {
    var user = controller.userInfo.value;
    if (user == null) {
      return const SizedBox();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(35),
            child: CachedNetworkImage(
              imageUrl: user.head ?? '',
              width: 70,
              height: 70,
              fit: BoxFit.cover,
              placeholder: (ctx, e) {
                return Container(
                  decoration: BoxDecoration(
                    color: Get.context!.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                    borderRadius: BorderRadius.circular(35),
                  ),
                );
              },
              errorWidget: (ctx, e, x) {
                return Container(
                  decoration: BoxDecoration(
                    color: Get.context!.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                    borderRadius: BorderRadius.circular(35),
                  ),
                );
              },
            ),
          ),
          const SizedBox(
            width: 12,
          ),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              '${user.nick ?? '-'} (${user.role?.name})',
              style: MFont.semi_Bold16.apply(
                color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
              ),
              overflow: TextOverflow.ellipsis,
            ),
            if (user.name != null && user.name!.isNotEmpty && GlobalConst.userModel?.role == UserRole.admin) ...{
              const SizedBox(
                height: 4,
              ),
              Text(
                '${S.of(Get.context!).profile_real_name}: ${user.name!}',
                style: MFont.semi_Bold16.apply(
                  color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
                ),
                overflow: TextOverflow.ellipsis,
              )
            }
          ])
        ],
      ),
    );
  }

  Widget get _emailView {
    var user = controller.userInfo.value;
    if (user == null) {
      return const SizedBox();
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      child: Row(children: [
        SizedBox(width: 70, child: Text(S.of(Get.context!).profile_email)),
        const SizedBox(width: 12),
        Expanded(
            child: Text(
          user.email ?? '-',
          style: MFont.semi_Bold16.apply(
            color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
          ),
          overflow: TextOverflow.ellipsis,
        ))
      ]),
    );
  }

  Widget get _phoneView {
    var user = controller.userInfo.value;
    if (user == null) {
      return const SizedBox();
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      child: Row(children: [
        SizedBox(width: 70, child: Text(S.of(Get.context!).profile_mobile)),
        const SizedBox(width: 12),
        Expanded(
          child: Text(user.phone ?? '-',
              style: MFont.semi_Bold16.apply(
                color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
              ),
              overflow: TextOverflow.ellipsis),
        )
      ]),
    );
  }

  Widget get _countryView {
    var user = controller.userInfo.value;
    if (user == null) {
      return const SizedBox();
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      child: Row(children: [
        SizedBox(width: 70, child: Text(S.of(Get.context!).profile_country)),
        const SizedBox(width: 12),
        Expanded(
          child: Text(user.country ?? '-',
              style: MFont.semi_Bold16.apply(
                color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
              ),
              overflow: TextOverflow.ellipsis),
        )
      ]),
    );
  }

  Widget get _cityView {
    var user = controller.userInfo.value;
    if (user == null) {
      return const SizedBox();
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      child: Row(children: [
        SizedBox(width: 70, child: Text(S.of(Get.context!).profile_city)),
        const SizedBox(width: 12),
        Expanded(
          child: Text(user.city ?? '-',
              style: MFont.semi_Bold16.apply(
                color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D,
              ),
              overflow: TextOverflow.ellipsis),
        )
      ]),
    );
  }
}
