import 'dart:async';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/insp_order_entity.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/data/user_profile_resp.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/message/chat/chat_binding.dart';
import 'package:inspector/app/modules/message/chat/chat_view.dart';
import 'package:inspector/app/modules/message/chat_provider.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';

class UserProfileController extends GetxController {
  final userInfo = Rxn<UserInfoEntity>(null);
  final inspectorCheck = Rxn<InspectorProfile>(null);

  final histories = <InspOrderEntity>[].obs;

  bool isAdmin = false;

  @override
  void onInit() {
    super.onInit();
    var userId = Get.arguments['userId'];
    isAdmin = Get.arguments['isAdmin'];
    if (isAdmin) {
      UserInfoEntity user = UserInfoEntity();
      user.nick = Get.arguments['name'];
      user.head = Get.arguments['head'];
      user.imUserId = Get.arguments['imUserId'];
      userInfo.value = user;
    } else {
      _getUserInfo(userId);
    }

    if (GlobalConst.userModel?.role == UserRole.admin) {
      _getInspOrders(userId);
    }
  }

  void _getInspOrders(int userId) {
    EasyLoading.show();
    MineProvider().getInspOrders(1, 10, userId).then((value) {
      if (value.isSuccess) {
        if (value.data != null) {
          histories.clear();
          histories.addAll(value.data!.list);
        }
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void _getUserInfo(int userId) {
    EasyLoading.show();
    MineProvider().takeUserProfile(userId).then((value) {
      if (value.isSuccess) {
        userInfo.value = value.data?.userInfo;
        inspectorCheck.value = value.data?.inspectorProfile;
      }
    }).whenComplete(() => EasyLoading.dismiss());
  }

  void sendMessage() {
    int imUserId = userInfo.value?.imUserId ?? 0;
    if (imUserId != 0) {
      EasyLoading.show();
      ChatProvider().createConv(imUserId).then((value) {
        if (value.isSuccess) {
          var gid = value.data['cid'] as int?;
          if (gid != null) {
            unawaited(Get.to(() => ChatView(tag: gid.toString()), arguments: {'cid': gid, 'showSendOrder': false}, binding: ChatBinding(tag: gid.toString())));
          }
        } else {
          showToast(value.message ?? '');
        }
      }).whenComplete(() {
        EasyLoading.dismiss();
      });
    }
  }
}
