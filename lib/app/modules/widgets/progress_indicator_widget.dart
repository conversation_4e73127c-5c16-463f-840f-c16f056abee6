import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ProgressIndicatorWidget extends StatefulWidget {
  final bool isShowLoading;

  final Widget? emptyView;

  const ProgressIndicatorWidget(this.isShowLoading, {super.key, this.emptyView});

  @override
  State<StatefulWidget> createState() {
    return _ProgressIndicatorWidgetState();
  }
}

class _ProgressIndicatorWidgetState extends State<ProgressIndicatorWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isShowLoading) {
      return widget.emptyView ?? const SizedBox();
    }
    return Padding(
        padding: const EdgeInsets.fromLTRB(0.0, 14.0, 0.0, 14.0),
        child: Opacity(
            opacity: widget.isShowLoading ? 1.0 : 0.0,
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                SpinKitChasingDots(color: Colors.blueAccent, size: 26.0),
                Padding(padding: EdgeInsets.fromLTRB(10.0, 0.0, 0.0, 0.0), child: Text('正在加载中...'))
              ],
            )));
  }
}
