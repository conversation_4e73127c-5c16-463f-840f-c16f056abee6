import 'package:flutter/widgets.dart';

class SliverStickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  final double? maxExtend;
  final double? minExtend;
  SliverStickyHeaderDelegate(this.child, {this.maxExtend, this.minExtend});

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => maxExtend ?? 42;

  @override
  double get minExtent => minExtend ?? 42;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
