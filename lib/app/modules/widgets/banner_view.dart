import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/tools/tools.dart';

class BannerView extends StatefulWidget {
  final List<dynamic> banners;
  final double width;
  final double height;
  final PageController? controller;
  final ValueChanged<int>? onPageChanged;
  final ValueChanged<int>? onPageTapped;
  BannerView(this.banners, this.width, this.height, {super.key, this.controller, this.onPageChanged, this.onPageTapped});

  @override
  State<StatefulWidget> createState() => _BannerState();
}

class _BannerState extends State<BannerView> {
  int bannerIdx = 0;
  @override
  Widget build(BuildContext context) {
    return Builder(builder: ((context) {
      return Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            height: widget.height,
            child: PageView.builder(
                controller: widget.controller,
                itemCount: widget.banners.length,
                onPageChanged: (value) {
                  widget.onPageChanged?.call(value);
                  setState(() {
                    bannerIdx = value;
                  });
                },
                itemBuilder: (context, index) {
                  var url = widget.banners[index].toString();
                  return GestureDetector(
                    onTap: () {
                      widget.onPageTapped?.call(index);
                    },
                    child: CachedNetworkImage(
                      imageUrl: url,
                      width: widget.width,
                      height: widget.height,
                      fit: BoxFit.cover,
                      placeholder: (ctx, e) {
                        return Container(
                          decoration: const BoxDecoration(
                            color: MColor.xFFEEEEEE,
                          ),
                        );
                      },
                      errorWidget: (ctx, e, x) {
                        return Container(
                          decoration: const BoxDecoration(
                            color: MColor.xFFEEEEEE,
                          ),
                        );
                      },
                    ),
                  );
                }),
          ),
          if (widget.banners.length > 1) ...{_bannerIndicatorView}
        ],
      );
    }));
  }

  Widget get _bannerIndicatorView {
    return Positioned(
      bottom: 5,
      child: Container(
        height: 10,
        padding: const EdgeInsets.only(left: 8, right: 8, top: 2, bottom: 2),
        decoration: const BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(10))),
        child: Row(
            children: List.generate(widget.banners.length, (index) {
          return Row(
            children: [
              Container(
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: bannerIdx != index ? const Color.fromARGB(255, 0, 0, 0) : Color.fromARGB(255, 230, 16, 16))),
              if (index != widget.banners.length - 1)
                const SizedBox(
                  width: 4,
                )
            ],
          );
        })),
      ),
    );
  }
}
