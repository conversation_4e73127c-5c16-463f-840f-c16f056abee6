import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/tools/privacy_helper.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../generated/l10n.dart';

typedef OnRemoved = Function(int position);
typedef OnUploaded = Function(String url);

class UploadWidget {
  static Widget generateUploadWidget(List<String> attachments, bool showAddView, OnUploaded onUploaded, OnRemoved onRemoved,
      {bool? showFile = true, bool? showRemove = true, UploadType? uploadType = UploadType.plain, Color? color}) {
    return _cardView(
        GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
            ),
            itemBuilder: (context, position) {
              if (position == attachments.length) {
                return _addView(onUploaded, uploadType ?? UploadType.plain, showFile ?? true);
              } else {
                return _attachmentView(position, attachments[position], onRemoved, showRemove: showRemove);
              }
            },
            itemCount: attachments.length + (showAddView ? 1 : 0)),
        color: color);
  }

  static Widget _cardView(Widget itemViews, {Color? color}) {
    return Builder(builder: (context) {
      return Container(
        decoration: BoxDecoration(
          color: color != null ? color : MColor.xFFF4F5F7,
          borderRadius: BorderRadius.circular(6),
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        child: itemViews,
      );
    });
  }

  static Widget _addView(OnUploaded onUploaded, UploadType uploadType, bool showFile) {
    return Builder(builder: (context) {
      return DottedBorder(
        borderType: BorderType.RRect,
        color: Get.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC,
        radius: const Radius.circular(6),
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            if (hasShowCameraTips()) {
              showCuportionDialog(context, onUploaded, uploadType, showFile);
            } else {
              showCameraTips().then((_) {
                showCuportionDialog(context, onUploaded, uploadType, showFile);
              });
            }
          },
          child: Builder(builder: (context) {
            return Center(
              child: Icon(
                Icons.add,
                size: 48,
                color: context.isDarkMode ? DarkColor.xFFABABAC : MColor.xFFABABAC,
              ),
            );
          }),
        ),
      );
    });
  }

  static Widget _attachmentView(int index, String url, OnRemoved onRemoved, {bool? showRemove}) {
    return Stack(
      children: [
        Row(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                if (Uri.parse(url).isAbsolute) {
                  if (url.isImageFileName) {
                    var imageProvider = Image.network(url).image;
                    showImageViewer(
                      Get.context!,
                      imageProvider,
                      onViewerDismissed: () {},
                      immersive: false,
                      useSafeArea: true,
                    );
                  } else {
                    // goto download
                    launchUrlString(url, mode: LaunchMode.externalApplication);
                  }
                }
              },
              child: AspectRatio(
                  aspectRatio: 1.0,
                  child: Uri.parse(url).isAbsolute ? _picAttachmentView(url) : _circleView),
            ),
          ],
        ),
        Visibility(
          visible: showRemove ?? false,
          child: Align(
            alignment: Alignment.topRight,
            child: GestureDetector(
              onTap: () {
                onRemoved.call(index);
              },
              child: Builder(builder: (context) {
                return Container(
                  decoration: BoxDecoration(
                    color: Colors.black26,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  width: 24,
                  height: 24,
                  child: Icon(
                    Icons.close,
                    size: 16,
                    color: context.isDarkMode ? DarkColor.xFFE95332 : MColor.xFFE95332,
                  ),
                );
              }),
            ),
          ),
        ),
      ],
    );
  }

  static Widget _picAttachmentView(String attachment) {
    if (Uri.parse(attachment).isAbsolute) {
      if (attachment.isImageFileName) {
        return _picImageView(attachment);
      } else {
        String basename = File(attachment).path.split('/').last;
        return DottedBorder(
            borderType: BorderType.RRect,
            color: Get.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC,
            radius: const Radius.circular(6),
            child: Center(
              child: Text(
                basename,
                style: MFont.semi_Bold12
                    .apply(color: Get.context!.isDarkMode ? DarkColor.xFFABABAC : MColor.xFFABABAC),
                overflow: TextOverflow.ellipsis,
                maxLines: 3,
                softWrap: true,
                textAlign: TextAlign.center,
              ),
            ));
      }
    } else {
      return const Center(
        child: SpinKitCircle(
          color: MColor.skin,
          size: 40.0,
        ),
      );
    }
  }

  static DottedBorder get _circleView {
    return DottedBorder(
      borderType: BorderType.RRect,
      color: Get.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC,
      radius: const Radius.circular(6),
      child: const AspectRatio(
        aspectRatio: 1,
        child: SpinKitCircle(
          color: MColor.skin,
          size: 40.0,
        ),
      ),
    );
  }

  static Widget _picImageView(String url) {
    return Builder(builder: (context) {
      return Container(
        decoration: BoxDecoration(
          color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
          borderRadius: BorderRadius.circular(6),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              var image = url;
              if (image.isURL) {
                var imageProvider = Image.network(image).image;
                showImageViewer(Get.context!, imageProvider,
                    immersive: false, useSafeArea: true, onViewerDismissed: () {});
              }
            },
            child: Container(
              color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
              child: CachedNetworkImage(
                imageUrl: url,
                fit: BoxFit.fill,
                placeholder: (ctx, _) {
                  return const Center(
                    child: SpinKitCircle(
                      color: MColor.skin,
                      size: 40.0,
                    ),
                  );
                },
                errorWidget: (ctx, a1, a2) {
                  return const Center(
                    child: Icon(
                      Icons.error,
                      size: 40,
                      color: MColor.skin,
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      );
    });
  }

  static Future<void> _uploadCamera(OnUploaded onUploaded, UploadType uploadType) async {
    PermissionStatus ps = await Permission.camera.status;
    if (!ps.isGranted) {
      ps = await Permission.camera.request();
      if (!ps.isGranted) {
        showToast(S.of(Get.context!).enabled_camera);
        return;
      }
    }
    await FilesPicker.openCamera().then((value) {
      // FilesPicker.openCamera().then((value) {
      if (value == null || value.isEmpty) {
        return;
      }
      EasyLoading.show();
      PublicProvider.uploadImages(value, uploadType).then((url) {
        if (url == null || value.isEmpty) {
          showToast(S.of(Get.context!).profile_info_failed);
          // picturePath.value = '';
          return;
        }
        onUploaded.call(url);
      }).whenComplete(() {
        EasyLoading.dismiss();
      });
    });
  }

  static Future<void> _uploadImage(OnUploaded onUploaded, UploadType uploadType,
      {int selectCount = 100}) async {
    PermissionStatus ps = await Permission.camera.status;
    if (!ps.isGranted) {
      ps = await Permission.camera.request();
      if (!ps.isGranted) {
        showToast(S.of(Get.context!).enabled_camera);
        return;
      }
    }
    await FilesPicker.openImage(false, enableCrop: false, selectCount: selectCount).then((images) {
      if (images.isEmpty) {
        return;
      }
      EasyLoading.show();
      images.forEach((element) async {
        var result = await PublicProvider.uploadImages(element, uploadType);
        if (result != null && result.isNotEmpty) {
          onUploaded.call(result);
        }
      });
      EasyLoading.dismiss();
    });
  }

  static void _uploadFile(OnUploaded onUploaded, UploadType uploadType) {
    FilesPicker.openFile().then((fileList) {
      if (fileList.isEmpty) {
        return;
      }
      EasyLoading.show();
      fileList.forEach((element) async {
        var result = await PublicProvider.uploadImages(element, uploadType);
        if (result != null && result.isNotEmpty) {
          onUploaded.call(result);
        }
      });
      EasyLoading.dismiss();
      // PublicProvider.uploadImages(value, uploadType).then((url) {
      //   if (url == null || value.isEmpty) {
      //     showToast(S.of(Get.context!).profile_info_failed);
      //     return;
      //   }
      //   onUploaded.call(url);
      // }).whenComplete(() {
      //   EasyLoading.dismiss();
      // });
    });
  }

  static void showCuportionDialog(
      BuildContext context, OnUploaded onUploaded, UploadType uploadType, bool showFile,
      {int selectCount = 100}) {
    showCupertinoModalPopup(
        context: context,
        builder: (context) {
          return CupertinoActionSheet(
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(S.of(Get.context!).public_cancel,
                  style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333, fontWeight: FontWeight.bold)),
            ),
            actions: <Widget>[
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  _uploadCamera(onUploaded, uploadType);
                },
                child: Text(
                  S.of(Get.context!).publish_camera,
                  style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
              ),
              CupertinoActionSheetAction(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _uploadImage(onUploaded, uploadType, selectCount: selectCount);
                  },
                  child: Text(
                    S.of(Get.context!).upload_image,
                    style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                  )),
              if (showFile)
                CupertinoActionSheetAction(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _uploadFile(onUploaded, uploadType);
                    },
                    child: Text(
                      S.of(Get.context!).upload_file,
                      style: TextStyle(fontSize: 16.0, color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                    )),
            ],
          );
        });
  }
}
