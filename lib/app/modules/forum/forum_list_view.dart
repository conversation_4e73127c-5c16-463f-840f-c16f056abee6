import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/forum/forum_list_controller.dart';

import '../../../generated/l10n.dart';

class ForumListView extends GetView<ForumListController> {
  const ForumListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(S.of(Get.context!).tab_purchase), centerTitle: true, elevation: 0),
      backgroundColor: Colors.white,
    );
  }
}
