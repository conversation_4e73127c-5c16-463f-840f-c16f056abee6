import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/tools/global_const.dart';

import '../../tools/storage_util.dart';

class ExchangeRate {
  final String name;
  final double rate;

  ExchangeRate(this.name, this.rate);
}

class SettingStore extends GetxController {
  static SettingStore get to => Get.find();

  static const List<Locale> supportedLocales = [
    Locale('zh', 'CN'),
    Locale('zh', 'HK'),
    Locale('en'),
    Locale('id'),
    Locale('ja'),
    Locale('ko'),
    Locale('fr'),
    Locale('es')
  ];

  static List<String> supportedLanguages = ['简体中文', '繁体中文', 'English', 'Indonesia', '日本語', '한국인', 'Français', 'Español'];

  ExchangeRate defaultRate = ExchangeRate('USD', 1.0);

  List<ExchangeRate> supportedCurrencies = [];

  late Timer globalTimer;

  late int themeMode;

  @override
  void onInit() {
    super.onInit();
    globalTimer = Timer.periodic(Duration(seconds: 1), (_) {
      update(['msg_timer_update']);
    });
    _loadSettings();
  }

  bool updateLocale(int index) {
    if (index >= 0 && index < supportedLocales.length) {
      StorageUtil.setInt(StorageKey.kLocale, index);
      Get.updateLocale(supportedLocales[index]);
      update(['msg_locale_updated']);
      return true;
    }
    return false;
  }

  int getCurrentLocaleId() {
    int index = StorageUtil.getInt(StorageKey.kLocale, defValue: -1);
    if (index == -1 || index >= supportedLocales.length) {
      index = _nearestLocale(Get.deviceLocale);
      StorageUtil.setInt(StorageKey.kLocale, index);
      return index;
    } else {
      return index;
    }
  }

  // String getThemeModeDesc(context) {
  //   switch (getThemeMode().index) {
  //     case 2:
  //       return S.of(Get.context!).theme_dark;
  //     case 1:
  //       return S.of(Get.context!).theme_light;
  //     default:
  //       return S.of(Get.context!).theme_follow;
  //   }
  // }

  // ThemeMode getThemeMode() {
  //   themeMode = StorageUtil.getInt(StorageKey.kThemeMode, defValue: 0);
  //   return ThemeMode.values[themeMode];
  // }

  // void setThemeMode(int mode) {
  //   themeMode = mode;
  //   StorageUtil.setInt(StorageKey.kThemeMode, mode);
  //   Get.changeThemeMode(ThemeMode.values[mode]);
  //   update(['msg_theme_mode_updated']);
  // }

  Locale getCurrentLocale() {
    return supportedLocales[getCurrentLocaleId()];
  }

  ///获取最相近的Locale
  int _nearestLocale(Locale? locale) {
    if (locale == null) {
      //默认返回英文
      return 2;
    } else {
      int index = 0;
      //先判断languageCode\countryCode都相同
      for (var l in supportedLocales) {
        if (l.languageCode == locale.languageCode && l.countryCode == locale.countryCode) {
          return index;
        }
        index++;
      }
      //再判断只languageCode相同
      index = 0;
      for (var l in supportedLocales) {
        if (l.languageCode == locale.languageCode) {
          return index;
        }
        index++;
      }
    }
    return 2;
  }

  void _loadSettings() {
    try {
      var currency = json.decode(
        StorageUtil.getString(StorageKey.kCurrency),
      );
      defaultRate = ExchangeRate(currency['name'], currency['rate']);
      update(['msg_currency_updated']);
    } catch (_) {}
  }

  // void changeExchangeRate(ExchangeRate er) {
  //   defaultRate = er;
  //   StorageUtil.setString(StorageKey.kCurrency, json.encode({'name': er.name, 'rate': er.rate}));
  //   update(['msg_currency_updated']);
  // }

  @override
  void dispose() {
    super.dispose();
  }
}
