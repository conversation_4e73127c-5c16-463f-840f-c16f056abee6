import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/checkout/ai_defect_add_label_view.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_image_selected_button.dart';
import 'package:inspector/generated/l10n.dart';

///缺陷记录输入项
class AiInputDefectTile extends StatefulWidget {
  final AiDetailDefectInfo info;
  final ChangeLabel? changeLabel;
  final AddLabel? addLabel;
  final List<String> labels;
  const AiInputDefectTile(this.info,
      {required this.labels, super.key, this.changeLabel, this.addLabel});

  @override
  State<AiInputDefectTile> createState() => _AiInputDefectTileState();

  static Future<AiDetailDefectInfo?> inputDefectView(
    BuildContext context,
    AiDetailDefectInfo info, {
    required List<String> labels,
    ChangeLabel? changeLabel,
    AddLabel? addLabel,
  }) async {
    bool isNew = info.label == null && info.level == null;
    AiDetailDefectInfo? result = await showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return AiInputDefectTile(
          info,
          changeLabel: changeLabel,
          addLabel: addLabel,
          labels: labels,
        );
      },
    );

    if (isNew) return result;
    //修改不需要值
    return null;
  }
}

class _AiInputDefectTileState extends State<AiInputDefectTile> {
  late final isEdit = widget.info.totalNum?.isEmpty ?? true;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(S.of(context).ai_defect_record),
        centerTitle: true,
      ),
      body: KeyboardDismissOnTap(
        child: Container(
          width: Get.width,
          padding: EdgeInsets.fromLTRB(20, 20, 20, 0),
          decoration: BoxDecoration(
              color: MColor.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(10),
              )),
          child: StatefulBuilder(builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SizedBox(width: 80, child: Text(S.of(context).ai_defect_photo)),
                    AiImageSelectedButton(manager: widget.info),
                  ],
                ),
                AiFormAssetsListView(
                  manager: widget.info,
                  padding: EdgeInsets.only(top: 20),
                ),
                SizedBox(height: 20),
                _buildRowCell(
                    title: S.of(context).ai_defect_description,
                    child: widget.info.label != null
                        ? Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(child: Text(widget.info.label!)),
                              GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () async {
                                  await showCupertinoModalPopup(
                                    context: context,
                                    builder: (context) {
                                      return AiDefectAddLabelView(
                                        info: widget.info,
                                        changeLabel: widget.changeLabel,
                                        addLabel: widget.addLabel,
                                        labels: widget.labels,
                                      );
                                    },
                                  );
                                  setState(() {
                                    widget.info.refreshEditTime();
                                  });
                                },
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Container(
                                    height: 27.0,
                                    width: 80,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(5),
                                        border: Border.all(color: MColor.aiMain)),
                                    child: Text(
                                      S.of(context).ai_change_description,
                                      style: MFont.medium14.copyWith(color: MColor.aiMain),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          )
                        : GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () async {
                              await showCupertinoModalPopup(
                                context: context,
                                builder: (context) {
                                  return AiDefectAddLabelView(
                                    info: widget.info,
                                    changeLabel: widget.changeLabel,
                                    addLabel: widget.addLabel,
                                    labels: widget.labels,
                                  );
                                },
                              );
                              setState(() {
                                widget.info.refreshEditTime();
                              });
                            },
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Container(
                                height: 27.0,
                                width: 80,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5),
                                    border: Border.all(color: MColor.aiMain)),
                                child: Text(
                                  S.of(context).ai_add_plus,
                                  style: MFont.medium14.copyWith(color: MColor.aiMain),
                                ),
                              ),
                            ),
                          ),
                    bottom: 10),
                _buildRowCell(
                    title: S.of(context).ai_defect_level,
                    bottom: 30,
                    child: Wrap(
                      runSpacing: 10,
                      children: [
                        for (int i = 0; i < AiCheckoutDefectLevelType.values.length; i++)
                          Padding(
                            padding: EdgeInsets.only(
                                right: (AiCheckoutDefectLevelType.values.length - 1) != i ? 30 : 0),
                            child: AiCheckButton(
                              title: AiCheckoutDefectLevelType.values[i].name,
                              selected:
                                  widget.info.level == AiCheckoutDefectLevelType.values[i].value,
                              onPressed: () {
                                setState(() {
                                  widget.info.level = AiCheckoutDefectLevelType.values[i].value;
                                });
                                widget.info.refreshEditTime();
                              },
                            ),
                          ),
                      ],
                    )),
                _buildRowCell(
                    title: S.of(context).ai_found_quantity,
                    axis: CrossAxisAlignment.center,
                    bottom: 30,
                    child: AiAddSubButton(
                      value: int.parse(widget.info.totalNum ?? '0'),
                      onChange: (value) {
                        setState(() {
                          widget.info.totalNum = value.toString();
                        });
                        widget.info.refreshEditTime();
                      },
                    )),
                _buildRowCell(
                    title: S.of(context).ai_handling_method,
                    child: AiCheckoutActionResult(widget.info)),
                SizedBox(height: 10),
                if (isEdit) bottomButton()
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget bottomButton() {
    return Container(
      padding: EdgeInsets.only(top: 50.0, bottom: 30),
      child: AiModalBottomButton(
        confirm: () {
          if (widget.info.label?.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_add}${S.of(context).ai_defect_description}');
            return;
          }
          if (widget.info.level?.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_select}${S.of(context).ai_defect_level}');
            return;
          }
          if (widget.info.totalNum?.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_fill_in}${S.of(context).ai_found_quantity}');
            return;
          }
          if (widget.info.actionType?.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_select}${S.of(context).ai_handling_method}');
            return;
          }
          Get.back(result: widget.info);
        },
      ),
    );
  }

  Widget _buildRowCell(
      {required String title,
      required Widget child,
      double? bottom,
      CrossAxisAlignment axis = CrossAxisAlignment.start}) {
    Widget result;
    result = Row(
      crossAxisAlignment: axis,
      children: [
        SizedBox(width: 80, child: Text(title)),
        Expanded(child: child),
      ],
    );
    if (bottom != null) {
      result = Padding(
        padding: EdgeInsets.only(bottom: bottom),
        child: result,
      );
    }
    return result;
  }
}

///弹出的底部双按钮
class AiModalBottomButton extends StatelessWidget {
  const AiModalBottomButton({required this.confirm, this.confirmTitle, super.key});

  // final VoidCallback cancel;
  final String? confirmTitle;
  final VoidCallback confirm;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              // style: OutlinedButton.styleFrom(
              //   side: BorderSide(width: 1.0, color: MColor.aiMain),
              // ),
              style: ButtonStyle(
                  shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                      side: BorderSide(
                        color: MColor.aiMain,
                        width: 1,
                      )))),
              child: Text(
                S.of(context).ai_cancel_action,
                style: TextStyle(color: MColor.aiMain),
              ),
            ),
          ),
          SizedBox(width: 20),
          Expanded(
            child: TextButton(
              onPressed: confirm,
              style: ButtonStyle(
                  backgroundColor: WidgetStatePropertyAll(MColor.aiMain),
                  shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                      side: BorderSide(color: MColor.aiMain)))),
              child: Text(
                confirmTitle ?? S.of(context).ai_confirm_action,
                style: TextStyle(color: MColor.white),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

///单选中按钮
class AiCheckButton extends StatelessWidget {
  const AiCheckButton({
    required this.title,
    required this.onPressed,
    this.selected = false,
    super.key,
  });

  final bool selected;
  final String title;
  final VoidCallback? onPressed;

  Color get color => selected ? MColor.aiMain : MColor.xFFA6A6A6;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            selected ? Icons.check_circle : Icons.circle_outlined,
            size: 20,
            color: color,
          ),
          SizedBox(
            width: 4,
          ),
          Text(
            title,
            style: TextStyle(color: color),
          ),
        ],
      ),
    );
  }
}

///加减按钮
class AiAddSubButton extends StatefulWidget {
  const AiAddSubButton({
    required this.value,
    required this.onChange,
    super.key,
    this.min = 1,
    this.max,
  });

  final int min;
  final int? max;
  final int value;
  final ValueChanged<int> onChange;

  @override
  State<AiAddSubButton> createState() => _AiAddSubButtonState();
}

class _AiAddSubButtonState extends State<AiAddSubButton> {
  late int _min = widget.min;
  late int? _max = widget.max;
  late int _value = widget.value;

  bool get thanEqualMax => _max != null && _value >= _max!;

  bool get lessEqualMin => _value <= _min;

  Color color(bool value) => value ? MColor.xFFE5E5E5 : MColor.xFFA6A6A6;
  int _step = 1;

  @override
  void didUpdateWidget(covariant AiAddSubButton oldWidget) {
    _min = widget.min;
    _max = widget.max;
    _value = widget.value;
    super.didUpdateWidget(oldWidget);
  }

  void subOne() {
    if (lessEqualMin) return;
    update(-_step);
  }

  void addOne() {
    if (thanEqualMax) return;
    update(_step);
  }

  void update(int value) {
    setState(() {
      _value = _value + value;
    });
    widget.onChange.call(_value);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: subOne,
          behavior: HitTestBehavior.translucent,
          child: Padding(
            padding: const EdgeInsets.only(right: 10),
            child: Icon(
              CupertinoIcons.minus_square,
              color: color(lessEqualMin),
            ),
          ),
        ),
        SizedBox(width: 20, child: Center(child: Text(_value.toString()))),
        GestureDetector(
          onTap: addOne,
          behavior: HitTestBehavior.translucent,
          child: Padding(
            padding: const EdgeInsets.only(left: 10),
            child: Icon(
              Icons.add_box_outlined,
              color: color(thanEqualMax),
            ),
          ),
        )
      ],
    );
  }
}

///检测结果的处理方式
class AiCheckoutActionResult extends StatefulWidget {
  final AiDetailDefectInfo info;

  const AiCheckoutActionResult(this.info, {super.key});

  @override
  State<AiCheckoutActionResult> createState() => _AiCheckoutActionResultState();
}

class _AiCheckoutActionResultState extends State<AiCheckoutActionResult> {
  late final Map<String, int> _record = {};

  @override
  void initState() {
    super.initState();
    for (var e in AiCheckoutDefectActionType.values) {
      _record[e.value] =
          e.value == widget.info.actionType ? (int.tryParse(widget.info.actionNum ?? '0') ?? 0) : 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (int i = 0; i < AiCheckoutDefectActionType.values.length; i++)
          Builder(builder: (context) {
            final type = AiCheckoutDefectActionType.values[i];
            return Padding(
              padding: EdgeInsets.only(
                  bottom: (AiCheckoutDefectActionType.values.length - 1) != i ? 18 : 0),
              child: _buildRowCell(
                type: type,
                selected: widget.info.actionType == type.value,
              ),
            );
          }),
      ],
    );
  }

  Widget _buildRowCell({required bool selected, required AiCheckoutDefectActionType type}) {
    int a = int.tryParse(widget.info.actionNum ?? '') ?? 0;
    int b = int.tryParse(widget.info.totalNum ?? '') ?? 0;
    if (a > b && selected) {
      widget.info.actionNum = widget.info.totalNum;
      _record[type.value] = b;
    }

    return Row(
      children: [
        Expanded(
          flex: 1,
          child: AiCheckButton(
            title: type.name,
            selected: selected,
            onPressed: () {
              setState(() {
                final count = _record[type.value] ?? 0;
                if (count == 0 && widget.info.actionType != type.value) {
                  widget.info.actionNum = widget.info.totalNum?.toString() ?? '0';
                  _record[type.value] = int.tryParse(widget.info.actionNum ?? '0') ?? 0;
                }
                widget.info.actionType = type.value;
              });
              widget.info.refreshEditTime();
            },
          ),
        ),
        Expanded(
          flex: 1,
          child: AiAddSubButton(
            value: _record[type.value] ?? 0,
            min: 0,
            max: int.tryParse(widget.info.totalNum ?? '') ?? 0,
            onChange: (value) {
              _record[type.value] = value;
              if (value == 0) {
                if (widget.info.actionType == type.value) {
                  setState(() {
                    widget.info.actionType = '';
                    widget.info.actionNum = '';
                  });
                }
                return;
              }
              setState(() {
                widget.info.actionType = type.value;
                widget.info.actionNum = value.toString();
              });
              widget.info.refreshEditTime();
            },
          ),
        ),
      ],
    );
  }
}
