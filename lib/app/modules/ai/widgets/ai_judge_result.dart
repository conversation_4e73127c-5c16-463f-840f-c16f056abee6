import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/const_data/ai_const_data.dart';
import 'package:inspector/app/modules/ai/widgets/ai_widget_style.dart';
import 'package:inspector/generated/l10n.dart';

class AiJudgeView extends StatefulWidget {
  const AiJudgeView({
    required this.noteChange,
    required this.resultChange,
    this.isSame,
    super.key,
  });

  final int? isSame;
  final ValueChanged<String> noteChange;
  final ValueChanged<int> resultChange;

  @override
  State<AiJudgeView> createState() => _AiJudgeState();
}

class _AiJudgeState extends State<AiJudgeView> {
  late int? _isSame = widget.isSame;

  @override
  void didUpdateWidget(covariant AiJudgeView oldWidget) {
    _isSame = widget.isSame;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final width = 89.0;
    return Container(
      width: Get.width,
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      color: MColor.white,
      child: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextButton.icon(
                  onPressed: () => changeResult(1),
                  label: Text(
                    S.of(context).approve,
                    style: MFont.medium14.copyWith(color: MColor.black),
                  ),
                  icon: getIcon(_isSame == 1),
                ),
                TextButton.icon(
                  onPressed: () => changeResult(3),
                  label: Text(
                    S.of(context).ai_wait,
                    style: MFont.medium14.copyWith(color: MColor.black),
                  ),
                  icon: getIcon(_isSame == 3),
                ),
                TextButton.icon(
                  onPressed: () => changeResult(2),
                  label: Text(
                    S.of(context).ai_default_config_fail,
                    style: MFont.medium14.copyWith(color: MColor.black),
                  ),
                  icon: getIcon(_isSame == 2),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Icon getIcon(bool value) {
    return Icon(
      value ? Icons.check_circle : Icons.circle_outlined,
      color: MColor.aiMain,
      size: 20,
    );
  }

  void changeResult(int value) {
    setState(() {
      _isSame = value;
      widget.resultChange.call(value);
    });
  }

  void changeNote(String value) {
    widget.noteChange.call(value);
  }
}
