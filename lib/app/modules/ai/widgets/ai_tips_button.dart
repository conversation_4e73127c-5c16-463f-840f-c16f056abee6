import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';

class AiTipsButton extends StatefulWidget {
  const AiTipsButton({
    this.images,
    this.richText,
    this.isInfo = false,
    this.padding = const EdgeInsets.only(left: 20),
    super.key,
  });
  final List<String>? images;
  final List<String>? richText;
  final bool isInfo;
  final EdgeInsetsGeometry padding;

  @override
  State<AiTipsButton> createState() => _AiTipsButtonState();
}

class _AiTipsButtonState extends State<AiTipsButton> {
  bool get hasTips => widget.richText?.isNotEmpty ?? false;
  bool get hasImage => widget.images?.isNotEmpty ?? false;

  late bool _isTips = hasTips;
  String name(bool value) => value
      ? S.of(context).ai_action_guidance_instructions
      : S.of(context).ai_action_standard_instructions;
  String get title => name(_isTips);
  String get actionName => name(!_isTips);

  void toggle() => _isTips = !_isTips;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          Navigator.push(context, CupertinoModalPopupRoute(
            builder: (context) {
              return StatefulBuilder(builder: (context, setState) {
                return Scaffold(
                    backgroundColor: MColor.backgroundColor,
                    appBar: AppBar(
                      title: Text(title),
                      centerTitle: true,
                      actions: [
                        TextButton.icon(
                            onPressed: () {
                              setState(() {
                                toggle();
                              });
                            },
                            label: Image.asset(Assets.aiToggleAction),
                            icon: Text(
                              actionName,
                              style: MFont.medium14.copyWith(color: MColor.aiMain),
                            ))
                      ],
                    ),
                    body: DefaultTextStyle(
                      style: MFont.medium14.copyWith(color: MColor.black),
                      child: _isTips ? tipsView : requiredView,
                    ));
              });
            },
          ));
        },
        child: Padding(
          padding: widget.padding,
          child: widget.isInfo
              ? Icon(
                  Icons.info,
                  size: 16,
                )
              : Image.asset(
                  Assets.aiCheckoutLight,
                ),
        ));
  }

  Widget get tipsView {
    if (!hasTips) return Center(child: Text(S.of(context).ai_no_guidance_instructions));
    return Html(data: widget.richText!.first);
  }

  Widget get requiredView {
    if (!hasImage) return Center(child: Text(S.of(context).ai_no_standard_instructions));
    return SingleChildScrollView(
      child: CachedNetworkImage(imageUrl: widget.images!.first),
    );
  }
}
