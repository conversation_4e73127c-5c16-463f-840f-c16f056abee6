import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/generated/l10n.dart';

class AiSubmitButton extends StatelessWidget {
  const AiSubmitButton({
    required this.onPressed,
    required this.name,
    this.margin,
    this.isSave =false,
    super.key,
  });

  static AiSubmitButton next({required VoidCallback onPressed}) => AiSubmitButton(
        onPressed: onPressed,
        name: S.current.ai_next_item,
      );
  static AiSubmitButton submit({required VoidCallback onPressed}) => AiSubmitButton(
        onPressed: onPressed,
        name: S.current.ai_submit,
      );
  static AiSubmitButton save({required VoidCallback onPressed}) => AiSubmitButton(
    onPressed: onPressed,
    name: S.current.date_save,
      isSave:true
  );
  static AiSubmitButton add({required VoidCallback onPressed}) => AiSubmitButton(
      onPressed: onPressed,
      name: S.current.ai_add_all,
      isSave:true
  );
  static AiSubmitButton go({required VoidCallback onPressed,required String name}) => AiSubmitButton(
      onPressed: onPressed,
      name: name,
      isSave:true
  );
  static AiSubmitButton done({required VoidCallback onPressed}) => AiSubmitButton(
        onPressed: onPressed,
        name: S.current.ai_complete,
      );
  static AiSubmitButton use({required VoidCallback onPressed}) => AiSubmitButton(
        onPressed: onPressed,
        name: S.current.ai_default_config_use,
      );
  static AiSubmitButton confirm({required VoidCallback onPressed}) => AiSubmitButton(
        onPressed: onPressed,
        name: S.current.ai_confirm_action,
      );
  static AiSubmitButton rest({required VoidCallback onPressed}) => AiSubmitButton(
        onPressed: onPressed,
        name: S.current.public_reset,
      );
  final VoidCallback onPressed;
  final String name;
  final EdgeInsets? margin;
  final bool isSave;
  @override
  Widget build(BuildContext context) {
    return KeyboardVisibilityBuilder(
      builder: (p0, isKeyboardVisible) {
        if (isKeyboardVisible) return SizedBox.shrink();
        return FutureBuilder(
          future: Future.delayed(
            Duration(milliseconds: 200),
            () => isKeyboardVisible,
          ),
          builder: (BuildContext context, AsyncSnapshot<dynamic> snapshot) {
            if (snapshot.hasData) {
              return Row(
                children: [
                  Expanded(
                    child: SafeArea(
                      top: false,
                      child: Container(
                        margin: margin ?? EdgeInsets.symmetric(horizontal: 20),
                        child: OutlinedButton(
                            style: ButtonStyle(
                                backgroundColor: WidgetStatePropertyAll( isSave?MColor.white: MColor.aiMain),
                                side: WidgetStatePropertyAll(BorderSide(color: MColor.aiMain)),
                                shape: WidgetStatePropertyAll(
                                  RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                )),
                            onPressed: onPressed,
                            child: Text(
                              name,
                              textAlign: TextAlign.center,
                              style: MFont.medium14.copyWith(color:isSave?MColor.aiMain: MColor.white),
                            )),
                      ),
                    ),
                  ),
                ],
              );
            }
            return SizedBox.shrink();
          },
        );
      },
    );
  }
}
