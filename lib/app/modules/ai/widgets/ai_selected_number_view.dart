import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/const_data/ai_const_data.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';

class AiSelectedNumberView extends StatefulWidget {
  const AiSelectedNumberView({
    this.max = 450,
    this.step = 100,
    this.initial = const [],
    super.key,
  });
  final int max;
  final int step;
  final List<String> initial;
  @override
  State<AiSelectedNumberView> createState() => _AiSelectedNumberViewState();
}

class _AiSelectedNumberViewState extends State<AiSelectedNumberView> {
  late final buttonCount = (widget.max + step - 1) ~/ step;
  late int step = widget.step;
  int rangeIndex = 0;
  int start([int? from]) => (from ?? rangeIndex) * step + 1;
  int end([int? from]) => min(((from ?? rangeIndex) + 1) * step, widget.max);
  late List<String> selectedList = List.from(widget.initial);

  void tapNum(String value, {bool onlyAdd = false}) {
    if (onlyAdd) {
      if (!selectedList.contains(value)) {
        selectedList.add(value);
      }
      return;
    }
    if (selectedList.contains(value)) {
      selectedList.remove(value);
    } else {
      selectedList.add(value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: EdgeInsets.only(top: 15),
      decoration: BoxDecoration(
          color: MColor.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(10),
          )),
      child: Column(
        children: [
          Text(S.of(context).ai_sampling_number, style: MFont.medium18),
          // Divider(
          //   height: 36,
          // ),
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 10, 10, 20),
            child: buildInput(),
          ),
          SizedBox(
            height: 40,
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: 10),
              scrollDirection: Axis.horizontal,
              children: [
                for (int i = 0; i < buttonCount; i++)
                  _AiNumberButton(
                    status: rangeIndex == i,
                    num: '${start(i)}-${end(i)}',
                    onChanged: (value) {
                      if (i == rangeIndex) return;
                      setState(() {
                        rangeIndex = i;
                      });
                    },
                  )
              ],
            ),
          ),
          SizedBox(height: 20),
          Expanded(
              child: GridView.builder(
            padding: EdgeInsets.symmetric(horizontal: 20),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 14,
                mainAxisSpacing: 15,
                childAspectRatio: 78 / 38),
            itemCount: end() - start() + 1,
            itemBuilder: (context, index) {
              final value = (start() + index).toString();
              final contains = selectedList.contains(value);
              return _AiNumberTile(
                status: contains,
                num: value,
                onChanged: tapNum,
              );
            },
          )),
          AiSubmitButton(
              onPressed: () {
                Navigator.pop(context, selectedList);
              },
              name: S.of(context).ai_confirm_action),
        ],
      ),
    );
  }

  TextEditingController editingController = TextEditingController();
  Widget buildInput() {
    Widget input = TextField(
      style: MFont.medium14.copyWith(color: MColor.xFF777777),
      controller: editingController,
      cursorColor: MColor.xFFF2591D,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp('[0-9]')),
        LengthLimitingTextInputFormatter(AiConstData.numberMaxLength),
      ],
      decoration: InputDecoration(
        hintText: S.of(context).ai_input_range_number('1～${widget.max}'),
        focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: MColor.aiMain)),
        contentPadding: EdgeInsets.zero,
        constraints: BoxConstraints.tight(const Size.fromHeight(40)),
      ),
    );
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Expanded(child: input),
        TextButton(
            onPressed: () {
              final n = int.tryParse(editingController.text);
              if (n != null && n >= 1 && n <= widget.max) {
                tapNum(editingController.text, onlyAdd: true);
                EasyLoading.showToast(S.of(context).ai_selected_status,
                    duration: Duration(milliseconds: 500));
                editingController.text = '';
              } else {
                EasyLoading.showError(S.of(context).ai_input_range_number('1～${widget.max}'));
              }
            },
            style: ButtonStyle(padding: WidgetStatePropertyAll(EdgeInsets.zero)),
            child: Text(
              S.of(context).ai_selected,
              style: MFont.medium14.copyWith(
                color: MColor.aiMain,
              ),
            )),
      ],
    );
  }
}

class _AiNumberTile extends StatefulWidget {
  const _AiNumberTile({
    required this.status,
    required this.num,
    required this.onChanged,
    super.key,
  });
  final bool status;
  final String num;
  final ValueChanged<String> onChanged;

  @override
  State<_AiNumberTile> createState() => _AiNumberTileState();
}

class _AiNumberTileState extends State<_AiNumberTile> {
  late bool status = widget.status;
  late String num = widget.num;

  Color get color => status ? MColor.aiMain : MColor.xFFA6A6A6;

  @override
  void didUpdateWidget(covariant _AiNumberTile oldWidget) {
    status = widget.status;
    num = widget.num;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => setState(() {
        status = !status;
        widget.onChanged.call(num);
      }),
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            border: Border.all(color: status ? MColor.aiMain : MColor.xFFE5E5E5)),
        child: Text(
          num,
          textAlign: TextAlign.center,
          style: MFont.medium16.copyWith(color: color),
        ),
      ),
    );
  }
}

class _AiNumberButton extends StatelessWidget {
  const _AiNumberButton({
    required this.status,
    required this.num,
    required this.onChanged,
    super.key,
  });
  final bool status;
  final String num;
  final ValueChanged<String> onChanged;
  Color get color => status ? MColor.aiMain : MColor.xFFA6A6A6;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => onChanged.call(num),
      child: Container(
        alignment: Alignment.center,
        height: 38,
        margin: EdgeInsets.only(right: 8),
        padding: EdgeInsets.symmetric(horizontal: 8),
        constraints: BoxConstraints(minWidth: 92),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(19),
            border: Border.all(color: status ? MColor.aiMain : MColor.xFFE5E5E5)),
        child: Text(
          num,
          textAlign: TextAlign.center,
          style: MFont.medium16.copyWith(color: color),
        ),
      ),
    );
  }
}
