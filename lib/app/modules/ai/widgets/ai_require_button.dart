import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';

class AiRequireButton extends StatefulWidget {
  const AiRequireButton({
    this.images,
    this.isInfo = false,
    this.padding = const EdgeInsets.only(left: 20),
    super.key,
  });
  final List<String>? images;
  final bool isInfo;
  final EdgeInsetsGeometry padding;

  @override
  State<AiRequireButton> createState() => _AiTipsButtonState();
}

class _AiTipsButtonState extends State<AiRequireButton> {
  bool get hasImage => widget.images?.isNotEmpty ?? false;

  String name(bool value) => value
      ? S.of(context).ai_action_guidance_instructions
      : S.of(context).ai_action_standard_instructions;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          Navigator.push(context, CupertinoModalPopupRoute(
            builder: (context) {
              return StatefulBuilder(builder: (context, setState) {
                return Scaffold(
                    backgroundColor: MColor.backgroundColor,
                    body: DefaultTextStyle(
                      style: MFont.medium14.copyWith(color: MColor.black),
                      child:  requiredView,
                    ));
              });
            },
          ));
        },
        child: Padding(
          padding: widget.padding,
          child: widget.isInfo
              ?  Container(
            child: Image.asset(
              Assets.productPic,
            )
          ):Container() ,
        ));
  }


  Widget get requiredView {
    if (!hasImage) return Center(child: Text(S.of(context).ai_no_standard_instructions));
    return SingleChildScrollView(
      child: CachedNetworkImage(imageUrl: widget.images!.first),
    );
  }
}
