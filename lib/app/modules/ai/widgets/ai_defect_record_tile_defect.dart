import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/checkout/ai_defect_add_label_view.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/widgets/ai_input_devect_tile.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

import '../entiy/ai_detail_checout.dart';
import '../entiy/ai_detail_checout_record.dart';
import '../entiy/ai_detail_defect_checout_info.dart';
import '../entiy/image_info_entity.dart';

///缺陷记录
class AiDefectRecordTileDefect extends StatefulWidget {
  final List<AiCheckOutDefectInfo> defect;
  final int index;
  final List<String> labels;
  final ChangeLabel? changeLabel;
  final AddLabel? addLabel;
  final bool readOnly;
  final ValueChanged<AiCheckOutInfo>? deleteCall; //TODO:AI 下策

  const AiDefectRecordTileDefect({
    required this.defect,
    required this.index,
    required this.labels,
    this.readOnly = false,
    this.deleteCall,
    this.changeLabel,
    this.addLabel,
    super.key,
  });

  @override
  State<AiDefectRecordTileDefect> createState() => _AiDefectRecordTileState();
}

class _AiDefectRecordTileState extends State<AiDefectRecordTileDefect> {

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: widget.readOnly ? 0 : 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for(var parent  in widget.defect)...{
            for(var item  in parent.records!)...{
              Container(
                padding: EdgeInsets.fromLTRB(20, 8, 20, 13),
                decoration: BoxDecoration(
                    color: Colors.white
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Text(
                          S.of(context).ai_simple_record,
                          style: MFont.medium16,
                        ),
                        Spacer(),
                      ],
                    ),
                    AiFormAssetsListView(
                      manager: item ,
                      readOnly: widget.readOnly,
                      padding: EdgeInsets.only(top: 10),
                    ),
                    Container(height: 3,),
                    Container(
                        alignment: Alignment.topLeft,
                        padding: EdgeInsets.fromLTRB(0, 0, 20, 13),
                        decoration: BoxDecoration(
                            color: Colors.white
                        ),
                        child: _buildRowText(
                            title: S.of(context).ai_simple_dsec, value: item.label ?? 'aaa')
                    ),

                  ],
                ),
              ),
              Obx(() {
                if (item.editTimeTitleObs.isEmpty) return SizedBox.shrink();
                return Container(
                  alignment: Alignment.centerRight,
                  padding: EdgeInsets.only(top: 10,right: 20),
                  child: Text(
                    item.editTimeTitleObs.value,
                    style: MFont.medium12.copyWith(color: MColor.xFF808080),
                  ),
                );
              }),
            },
            _builTable( title: S.of(context).ai_defect_level, value: parent.levelName ?? '',isFirst:true),
            _builTable( title: S.of(context).ai_found_quantity, value: parent.totalNum ?? '',isFirst:false),
            _builTable( title: S.of(context).ai_pick_out, value: parent.tiaoChu ?? '',isFirst:false),
            _builTable( title: S.of(context).ai_rework, value: parent.fanGong ?? '',isFirst:false),
            _builTable( title: S.of(context).ai_replace, value: parent.tiHuan ?? '',isFirst:false),
            SizedBox(height: 10,)
          },

          SizedBox(height:15 ,),
        ],
      ),
    );
  }


  Widget _builTable({required String title, required String value,required bool isFirst}){
    return Container(
      height: 60,
      decoration: BoxDecoration(
          color: Colors.white,

          border: isFirst? Border.all(width: 1,color:MColor.xFFE5E5E5 ):
          Border(
            left: BorderSide(
              width: 1,//宽度
              color:MColor.xFFE5E5E5, //边框颜色
            ),
            bottom: BorderSide(
              width: 1,//宽度
              color:MColor.xFFE5E5E5, //边框颜色
            ),
            right: BorderSide(
              width: 1,//宽度
              color:MColor.xFFE5E5E5, //边框颜色
            ),
          )
      ),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            width: 115,
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1,//宽度
                  color:MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(title),
          ),
          Container(
            alignment: Alignment.center,
            width: Get.width-115-3,
            height: 60,
            decoration: BoxDecoration(
            ),
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildRowText({required String title, required String value}) {
    return Padding(
      padding: EdgeInsets.only(
        top: 8,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(title),
          ),
          SizedBox(width: 25,),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
