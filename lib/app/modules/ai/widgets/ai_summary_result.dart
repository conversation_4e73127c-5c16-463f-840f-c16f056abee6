import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/const_data/ai_const_data.dart';
import 'package:inspector/app/modules/ai/widgets/ai_widget_style.dart';
import 'package:inspector/generated/l10n.dart';

class AiSummaryResultView extends StatefulWidget {
  const AiSummaryResultView({
    required this.noteChange,
    required this.resultChange,
    this.isSame,
    this.note,
    super.key,
  });

  final String? note;
  final int? isSame;
  final ValueChanged<String> noteChange;
  final ValueChanged<int> resultChange;

  @override
  State<AiSummaryResultView> createState() => _AiSummaryResultViewState();
}

class _AiSummaryResultViewState extends State<AiSummaryResultView> {

  late int? _isSame = widget.isSame;

  @override
  void didUpdateWidget(covariant AiSummaryResultView oldWidget) {
    _isSame = widget.isSame;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final width = 89.0;
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      color: MColor.white,
      child: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 15),
              child: Text(
                S.of(context).ai_overall_conclusion,
                style: MFont.medium18,
              ),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: width,
                  child: Text('* ${S.of(context).ai_simple_result}'),
                ),
                TextButton.icon(
                  onPressed: () => changeResult(1),
                  label: Text(
                    S.of(context).approve,
                    style: MFont.medium14.copyWith(color: MColor.black),
                  ),
                  icon: getIcon(_isSame == 1),
                ),
                TextButton.icon(
                  onPressed: () => changeResult(3),
                  label: Text(
                    S.of(context).ai_wait,
                    style: MFont.medium14.copyWith(color: MColor.black),
                  ),
                  icon: getIcon(_isSame == 3),
                ),
                TextButton.icon(
                  onPressed: () => changeResult(2),
                  label: Text(
                    S.of(context).ai_default_config_fail,
                    style: MFont.medium14.copyWith(color: MColor.black),
                  ),
                  icon: getIcon(_isSame == 2),
                ),
              ],
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: width,
                  child: Text('  ${S.of(context).ai_remarks}'),
                ),
                Expanded(
                    child: TextField(
                  controller: TextEditingController(text: widget.note),
                  minLines: 3,
                  maxLines: 5,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(AiConstData.textMaxLength),
                  ],
                  decoration: const InputDecoration(
                      enabledBorder: AiWidgetStyle.enabledBorder,
                      focusedBorder: AiWidgetStyle.focusedBorder,
                      contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 8)),
                  onChanged: changeNote,
                ))
              ],
            ),
          ],
        ),
      ),
    );
  }

  Icon getIcon(bool value) {
    return Icon(
      value ? Icons.check_circle : Icons.circle_outlined,
      color: MColor.aiMain,
      size: 20,
    );
  }

  void changeResult(int value) {
    setState(() {
      _isSame = value;
      widget.resultChange.call(value);
    });
  }

  void changeNote(String value) {
    widget.noteChange.call(value);
  }
}
