import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/ai/attachment/ai_attachment_controller.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_category_info.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/checkout_result/ai_checkout_result_view.dart';
import 'package:inspector/app/modules/ai/const_data/ai_const_data.dart';
import 'package:inspector/app/modules/ai/entiy/ai_checkout_sample_aql_data.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/extension/table_border_extension.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_image_selected_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_defect_record_tile_defect_title.dart';
import 'package:inspector/app/modules/ai/widgets/ai_widget_style.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/theme/app_theme.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';
import 'package:signature/signature.dart';

class AiAttachmentSecondView extends GetView<AiAttachmentController> {
  const AiAttachmentSecondView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(S.of(context).ai_report_summary),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
          style: MFont.medium14.copyWith(color: MColor.black),
          child: StatefulBuilder(builder: (context, setState) {
            return KeyboardDismissOnTap(
              child: Column(
                children: [
                  Expanded(
                    flex: 1,
                    child: Obx(() {
                      return AiEmptyView(
                        baseResult: controller.baseResult,
                        child: SingleChildScrollView(
                            child: Column(
                          children: [
                            _AiResultStatusView(
                              pass: controller.endResult,
                            ),
                            Container(
                              padding: const EdgeInsets.only(
                                  top: 10, left: 20, right: 20, bottom: 10),
                              margin: EdgeInsets.only(top: 10),
                              color:
                                  Theme.of(Get.context!).colorScheme.conatiner,
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 80,
                                    child: Text(
                                      '${S.of(Get.context!).order_detail_inspection_type}:',
                                      style: MFont.bold16
                                          .apply(color: MColor.xFFA6A6A6),
                                    ),
                                  ),
                                  Expanded(
                                    child: Text(
                                      Helper.parseOrderType(
                                          AiCategoryController.to.order.type ??
                                              0),
                                      style: MFont.bold18,
                                    ),
                                  )
                                ],
                              ),
                            ),
                            _AiSummarizeHeaderView(),
                            _AiSummarizeView(),
                            AiProductSelectBar(loader: controller),
                            SizedBox(
                              height: 10,
                            ),
                            _AiQualityJudgeResultView(),
                            _AiQualityDefectResultView(),
                            SizedBox(
                              height: 10,
                            ),
                            buildAllDefects(),
                            SizedBox(
                              height: 10,
                            ),
                            _AiCheckoutJudgeResultView(),
                            SizedBox(
                              height: 10,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(
                                      left: 20, top: 10, bottom: 10),
                                  child: Text(
                                    S.current.ai_product_report_sign,
                                    style: MFont.semi_Bold17,
                                  ),
                                )
                              ],
                            ),
                            Stack(
                              children: [
                                Signature(
                                  controller: controller.singControllerShow,
                                  width: Get.width,
                                  height: 180,
                                  backgroundColor: Colors.white,
                                ),
                                InkWell(
                                  onTap: () {},
                                  child: Container(
                                    width: Get.width,
                                    height: 180,
                                  ),
                                )
                              ],
                            )
                          ],
                        )),
                      );
                    }),
                  ),
                  Container(
                      height: 60,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                              width: Get.width / 2 - 20,
                              child: AiSubmitButton(
                                onPressed: () {
                                  Get.toNamed(AiRoutes.ATTACHMENT_SIGN)
                                      ?.then((value) {
                                    if (value == -1) {
                                      setState(() {
                                        controller.decodeSign();
                                      });
                                    }
                                  });
                                  // controller.saveSign(context);
                                },
                                name: S.current.ai_product_report_sign,
                                isSave: true,
                              )),
                          Container(
                              width: Get.width / 2 - 20,
                              child: AiSubmitButton(
                                onPressed: () {
                                  controller.toNext();
                                },
                                name: S.current.ai_complete,
                              ))
                        ],
                      ))
                ],
              ),
            );
          })),
    );
  }

  Widget buildAllDefects() {
    return Obx(() {
      if (controller.allDefects.isEmpty) return SizedBox.shrink();
      return Container(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        for (var form in controller.allDefects) ...{
          Container(
            margin: EdgeInsets.only(left: 20, top: 10, bottom: 10),
            child: Text(
              form.name!,
              style: MFont.bold16,
            ),
          ),
          for (var defect in form.data!.defectCheckOut?.values ?? []) ...[
            for (var item in defect.recordsList!) ...{
              AiDefectRecordTileDefectTitle(
                  defect: item.defectsList!,
                  index: 1,
                  readOnly: true,
                  addLabel: (value) {
                    // setState(() {
                    // });
                  },
                  changeLabel: (oldValue, newValue) {
                    // setState(() {
                    // });
                  },
                  labels: [],
                  deleteCall: (value) {
                    // setState(() {
                    // });
                  })
            },
          ],
        }
      ]));
    });
  }

  Widget buildAddButton() {
    return Container(
      color: MColor.white,
      padding: EdgeInsets.fromLTRB(20, 10, 20, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            S.current.ai_special_note,
            style: MFont.bold18,
          ),
          Spacer(),
          DottedBorder(
            color: MColor.aiMain,
            borderType: BorderType.RRect,
            radius: Radius.circular(5),
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                controller.addDefect(AiDetailDefectInfo());
              },
              child: Container(
                width: 80,
                height: 27,
                alignment: Alignment.center,
                child: Text(
                  S.current.ai_add_plus,
                  style: TextStyle(color: MColor.aiMain),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _AiResultNotice extends StatefulWidget {
  const _AiResultNotice(this.defect, {this.removeDefectAction});

  final AiDetailDefectInfo defect;
  final ValueChanged<AiDetailDefectInfo>? removeDefectAction;

  @override
  State<_AiResultNotice> createState() => _AiResultNoticeState();
}

class _AiResultNoticeState extends State<_AiResultNotice> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
      color: MColor.white,
      child: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller:
                        TextEditingController(text: widget.defect.label),
                    minLines: 3,
                    maxLines: 5,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(
                          AiConstData.textMaxLength),
                    ],
                    decoration: const InputDecoration(
                        enabledBorder: AiWidgetStyle.enabledBorder,
                        focusedBorder: AiWidgetStyle.focusedBorder,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 8)),
                    onChanged: changeNote,
                  ),
                ),
                TextButton(
                    onPressed: () {
                      showCustomDialog(
                        S.of(context).ai_confirm_delete(''),
                        cancel: true,
                        onConfirm: () {
                          widget.removeDefectAction?.call(widget.defect);
                        },
                      );
                    },
                    child: Text(
                      S.of(context).ai_delete,
                      style: MFont.medium14.copyWith(color: MColor.aiMain),
                    )),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(top: 10),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AiImageSelectedButton(manager: widget.defect, big: true),
                  Expanded(
                    child: AiFormAssetsListView(manager: widget.defect),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Icon getIcon(bool value) {
    return Icon(
      value ? Icons.check_circle : Icons.circle_outlined,
      color: MColor.aiMain,
      size: 20,
    );
  }

  void changeResult(String value) {
    setState(() {
      widget.defect.actionType = value;
    });
  }

  void changeNote(String value) {
    widget.defect.label = value;
  }
}

class _AiResultStatusView extends GetView<AiAttachmentController> {
  const _AiResultStatusView({required this.pass, super.key});

  final bool pass;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      margin: EdgeInsets.only(top: 10),
      color: MColor.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 20, top: 10),
            child: Text(
              S.of(context).ai_overall_conclusion_2,
              style: MFont.bold18,
            ),
          ),
          Expanded(
            child: Center(
              child: Container(
                width: 200,
                height: 60,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: pass ? MColor.pass : MColor.failed,
                    borderRadius: BorderRadius.circular(10.0)),
                child: Text(
                  pass.title,
                  textAlign: TextAlign.center,
                  style: MFont.bold20,
                ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 20, top: 10, right: 20, bottom: 10),
            child: Text(
              S.of(context).inspection_report_note,
              style: MFont.medium16,
            ),
          ),
        ],
      ),
    );
  }
}

class _AiSummarizeHeaderView extends GetView<AiAttachmentController> {
  OrderDetailEntity? get order => AiCategoryController.to.order;
  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(top: 10),
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                decoration: BoxDecoration(color: MColor.xFFFFF3F0),
                padding: EdgeInsets.only(left: 20, top: 10, bottom: 15),
                child: Row(
                  children: [
                    Text(S.of(context).summarize, style: MFont.bold18),
                  ],
                )),
            Table(
              border: TableBorder.all(color: MColor.xFFE5E5E5),
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              columnWidths: const {
                0: FixedColumnWidth(130),
                1: FlexColumnWidth(),
              },
              children: [
                TableRow(children: [
                  aiResultCell(name: S.of(context).po_number, height: 38),
                  aiResultCell(
                      name: order?.reportPoNums == null ||
                              order!.reportPoNums!.isEmpty
                          ? '---'
                          : order!.reportPoNums!,
                      height: 38),
                ]),
                TableRow(children: [
                  aiResultCell(
                      name: S.of(context).product_quantity, height: 38),
                  aiResultCell(
                      name: order?.reportTotalAmout == null ||
                              order!.reportTotalAmout!.isEmpty
                          ? '---'
                          : order!.reportTotalAmout!,
                      height: 38),
                ]),
                TableRow(children: [
                  aiResultCell(name: S.of(context).customer_name, height: 38),
                  aiResultCell(
                      name: order?.name == null || order!.name!.isEmpty
                          ? '---'
                          : order!.name!)
                ]),
                TableRow(children: [
                  aiResultCell(name: S.of(context).supplier_name, height: 38),
                  aiResultCell(
                    name: order?.factoryName == null ||
                            order!.factoryName!.isEmpty
                        ? '---'
                        : order!.factoryName!,
                  )
                ]),
                TableRow(children: [
                  aiResultCell(name: S.of(context).inspection_date, height: 38),
                  aiResultCell(
                    name: order?.inspectTime == null ||
                            order!.inspectTime!.isEmpty
                        ? '---'
                        : order!.inspectTime!,
                  )
                ]),
                TableRow(children: [
                  aiResultCell(name: S.of(context).arrival_time, height: 38),
                  aiResultCell(
                      name: order?.firstArrivalTime == null ||
                              order!.firstArrivalTime!.isEmpty
                          ? '---'
                          : order!.firstArrivalTime!)
                ]),
                TableRow(children: [
                  aiResultCell(name: S.of(context).completion_time, height: 38),
                  aiResultCell(
                      name: order?.successTime == null ||
                              order!.successTime!.isEmpty
                          ? '---'
                          : order!.successTime!)
                ]),
                TableRow(children: [
                  aiResultCell(
                      name: S.of(context).inspection_address, height: 38),
                  aiResultCell(
                      name: order?.address == null || order!.address!.isEmpty
                          ? '---'
                          : order!.address!)
                ]),
                TableRow(children: [
                  aiResultCell(name: S.of(context).inspector, height: 38),
                  aiResultCell(
                      name: order?.inspectorNames == null ||
                              order!.inspectorNames!.isEmpty
                          ? '---'
                          : order!.inspectorNames!)
                ]),
              ],
            )
          ],
        ));
  }
}

class _AiSummarizeView extends GetView<AiAttachmentController> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: 20),
      margin: EdgeInsets.only(top: 10),
      color: MColor.white,
      child: Obx(() {
        return Table(
          border: TableBorder.all(color: MColor.xFFE5E5E5),
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: [
            TableRow(
                decoration: BoxDecoration(color: MColor.xFFFFF3F0),
                children: [
                  aiResultCell(
                      name: S.of(context).ai_category_name_table, height: 38),
                  aiResultCell(name: S.of(context).ai_compliance, height: 38),
                  aiResultCell(name: S.of(context).ai_remarks_2, height: 38),
                ]),
            for (AiCategoryInfo info
                in (controller.categoryData.value.data ?? []))
              if (info.categoryType != AiCategoryType.attachment)
                TableRow(children: [
                  aiResultCell(name: info.categoryType.name),
                  _cell(info.isPass),
                  aiResultCell(name: info.remark ?? ''),
                ])
          ],
        );
      }),
    );
  }

  TableRow summaryRow(String title, String value) {
    return TableRow(children: [
      Container(
        height: 38,
        alignment: Alignment.center,
        child: Text(title),
      ),
      Padding(
          padding: const EdgeInsets.only(left: 8),
          child: Text(
            value,
            style: MFont.regular14,
          )),
    ]);
  }

  Widget _cell(bool? done) {
    Widget icon(bool? selected) =>
        Icon(selected == true ? Icons.check_circle : Icons.circle_outlined,
            size: 20, color: MColor.xFFA6A6A6);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        icon(done),
        SizedBox(width: 2),
        Text('YES'),
        SizedBox(width: 10),
        icon(done == null ? null : !done!),
        SizedBox(width: 2),
        Text('NO'),
      ],
    );
  }
}

class _AiQualityDefectResultView extends GetView<AiAttachmentController> {
  const _AiQualityDefectResultView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      color: MColor.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: Text(
              S.of(context).ai_defect_list,
              style: MFont.medium18,
            ),
          ),
          Obx(() {
            return Table(
              border: TableBorder.all(color: MColor.xFFE5E5E5),
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              columnWidths: const {
                0: FlexColumnWidth(),
                1: FixedColumnWidth(57),
                2: FixedColumnWidth(57),
                3: FixedColumnWidth(57),
              },
              children: [
                TableRow(
                    decoration: BoxDecoration(color: MColor.xFFFFF3F0),
                    children: [
                      for (var o in [
                        S.of(context).ai_defect_description,
                        S.of(context).ai_critical,
                        S.of(context).ai_important,
                        S.of(context).ai_minor,
                      ])
                        aiResultCell(
                          name: o,
                          height: 38,
                        ),
                    ]),
                for (int i = 0;
                    i <
                        (controller.qualityCategoryConfig.value.forms ?? [])
                            .length;
                    i++)
                  if (controller.qualityCategoryConfig.value.forms![i].data !=
                      null)
                    // if(controller.inputList?[i].data!=null && controller.inputList?[i].data!.defectCheckOut!=null&&controller.inputList?[i].data!.defectCheckOut!.values!=null
                    //     && controller.inputList![i].data!.defectCheckOut!.values!.isNotEmpty
                    //     && controller.inputList![i].data!.defectCheckOut!.values!.first.recordsList!.isNotEmpty
                    // )
                    TableRow(children: [
                      aiResultCell(
                          name: controller
                                  .qualityCategoryConfig.value.forms![i].name ??
                              ''),
                      aiResultCell(
                          name: controller.qualityCountList[i]['aql_1'] ?? '0'),
                      aiResultCell(
                          name: controller.qualityCountList[i]['aql_2'] ?? '0'),
                      aiResultCell(
                          name: controller.qualityCountList[i]['aql_3'] ?? '0'),
                    ]),
              ],
            );
          }),
        ],
      ),
    );
  }
}

class _AiQualityJudgeResultView extends GetView<AiAttachmentController> {
  const _AiQualityJudgeResultView({super.key});

  @override
  Widget build(BuildContext context) {
    final sampleLevelObs = controller
        .qualityCategoryConfig.value.table?.forms?.first.sampleLevelObs;
    final sampleCountObs =
        controller.qualityCategoryConfig.value.table?.forms?.first.sampleNumObs;

    final level = sampleLevelObs?.value ?? '';
    print('level======$level');
    if (level.isEmpty || level == AICheckoutSampleLevelType.custom.value) {
      // return SizedBox.shrink();
    }
    final color = MColor.xFFE5E5E5;

    final noBottomBorder = TableBorderExt.without(color: color, bottom: true);
    final noHBorder =
        TableBorderExt.without(color: color, right: true, left: true);
    final titleHeight = 74.0;
    final borderAll = TableBorder.all(color: color);
    final rowDecoration = BoxDecoration(color: MColor.xFFFFF3F0);

    Map<int, FlexColumnWidth> titleWidths = {
      0: FlexColumnWidth(1),
      1: FlexColumnWidth(1),
      2: FlexColumnWidth(1),
      3: FlexColumnWidth(1),
      4: FlexColumnWidth(1),
      5: FlexColumnWidth(1),
    };
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      color: MColor.white,
      child: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 15),
              child: Text(
                S.current.process_appearance_judgment,
                style: MFont.medium18,
              ),
            ),
            //标题
            Table(
              border: noBottomBorder,
              columnWidths: {
                0: titleWidths[0]!,
                1: FlexColumnWidth(titleWidths[1]!.value +
                    titleWidths[2]!.value +
                    titleWidths[3]!.value +
                    titleWidths[4]!.value),
                2: titleWidths[5]!,
              },
              children: [
                TableRow(decoration: rowDecoration, children: [
                  aiResultCell(name: ''),
                  Table(
                    children: [
                      TableRow(decoration: rowDecoration, children: [
                        aiResultCell(
                            name:
                                '${S.of(context).ai_standard}ANSl/ASQZ1.4(MIL-STD-105E)',
                            height: titleHeight / 2)
                      ]),
                      TableRow(decoration: rowDecoration, children: [
                        Table(
                          border: TableBorderExt.only(color: color, top: true),
                          children: [
                            TableRow(children: [
                              aiResultCell(
                                  name: S.of(context).ai_sampling_level,
                                  height: titleHeight / 2),
                              aiResultCell(
                                  name: S.of(context).ai_sample_count,
                                  height: titleHeight / 2),
                              aiResultCell(
                                  name: 'AQL', height: titleHeight / 2),
                              aiResultCell(
                                  name:
                                      S.of(context).ai_maximum_allowable_value,
                                  height: titleHeight / 2),
                            ])
                          ],
                        )
                      ])
                    ],
                  ),
                  aiResultCell(
                      name: S.of(context).ai_result, height: titleHeight),
                ]),
              ],
            ),
            //值
            Table(
              border: borderAll,
              columnWidths: {
                0: titleWidths[0]!,
                1: FlexColumnWidth(
                    titleWidths[1]!.value + titleWidths[2]!.value),
                2: FlexColumnWidth(titleWidths[3]!.value +
                    titleWidths[4]!.value +
                    titleWidths[5]!.value),
              },
              children: [
                TableRow(children: [
                  //值标题
                  Table(border: noHBorder, columnWidths: {
                    0: titleWidths[0]!,
                  }, children: [
                    for (int i = 0;
                        i < AiCheckoutDefectLevelType.values.length;
                        i++)
                      TableRow(children: [
                        aiResultCell(
                            name: AiCheckoutDefectLevelType.values[i].name,
                            pass: controller.isPassLeve(i)),
                      ]),
                  ]),
                  //值 样本等级/样本数
                  Table(border: noHBorder, columnWidths: {
                    0: titleWidths[1]!,
                    1: titleWidths[2]!,
                  }, children: [
                    TableRow(children: [
                      Obx(() {
                        return buildCell<String>(
                            onSelected: (value) {
                              controller.qualityCategoryConfig.value.table
                                  ?.forms?.first
                                  .changeSampleLevel(
                                value,
                                callback: () {
                                  for (int i = 0;
                                      i <
                                          AiCheckoutDefectLevelType
                                              .values.length;
                                      i++) {
                                    controller.qualityCategoryConfig.value.table
                                        ?.getAqlAcNum(
                                            i,
                                            controller.qualityCategoryConfig
                                                    .value.table?.aqlList?[i] ??
                                                '',
                                            sampleLevelObs?.value ?? '')
                                        .then((value) {
                                      bool isSucc = controller.isPassLeve(i);
                                      controller.qualityCategoryConfig.value
                                          .result?.status = isSucc ? 1 : 2;
                                      print('isSucc===$isSucc');
                                      controller.aiDetail.refresh();
                                    });
                                  }
                                },
                              );
                            },
                            name: AICheckoutSampleLevelType.getName(
                                sampleLevelObs?.value ?? ''),
                            labels: (List<AICheckoutSampleLevelType>.from(
                                    AICheckoutSampleLevelType.values)
                                  ..removeLast())
                                .map((e) => e.name)
                                .toList(),
                            values: (List<AICheckoutSampleLevelType>.from(
                                    AICheckoutSampleLevelType.values)
                                  ..removeLast())
                                .map((e) => e.value)
                                .toList(),
                            height: 43 * 3);
                      }),
                      Obx(() {
                        return aiResultCell(
                            name: sampleCountObs?.value ?? '',
                            pass: true,
                            height: 43 * 3);
                      })
                    ]),
                  ]),
                  //值 aql/最大值/结果
                  Table(border: noHBorder, columnWidths: {
                    0: titleWidths[3]!,
                    1: titleWidths[4]!,
                    2: titleWidths[5]!,
                  }, children: [
                    for (int i = 0;
                        i < AiCheckoutDefectLevelType.values.length;
                        i++)
                      TableRow(children: [
                        buildAqlCell(
                            onSelected: (value) {
                              controller.qualityCategoryConfig.value.table
                                  ?.getAqlAcNum(
                                      i, value, sampleLevelObs?.value ?? '')
                                  .then(
                                (value) {
                                  // bool isSucc=      controller.isPassLeve(i);
                                  // controller.result?.status = isSucc?1:2;
                                  // print('isSucc==='+isSucc.toString());
                                  controller.aiDetail.refresh();
                                },
                              );
                            },
                            name: controller.qualityCategoryConfig.value.table
                                    ?.aqlList?[i] ??
                                '',
                            pass: controller.isPassLeve(i)),
                        aiResultCell(
                            name: controller.qualityCategoryConfig.value.table
                                    ?.aqlAcNumList?[i] ??
                                '',
                            pass: controller.isPassLeve(i)),
                        aiResultCell(
                            // name: controller.getTotalMap[AiCheckoutDefectLevelType.values[i].value]
                            //         ?.toString() ??
                            //     '0',
                            name: controller.qualityResultList[i]['aql']
                                    ?.toString() ??
                                '0',
                            pass: controller.isPassLeve(i)),
                      ]),
                  ]),
                ])
              ],
            )
          ],
        );
      }),
    );
  }

  Widget buildAqlCell({
    required String name,
    required ValueChanged<String> onSelected,
    required bool pass,
  }) {
    final aqlList = [
      "0",
      "0.65",
      "1.0",
      "1.5",
      "2.5",
      "4.0",
      "0.010",
      "0.015",
      "0.025",
      "0.04",
      "0.065",
      "0.1",
      "0.15",
      "0.25",
      "0.4",
      "6.5",
      "10",
      "15",
      "25",
      "40",
      "65",
      "100",
      "150",
      "250",
      "400",
      "650",
      "1000",
    ];
    return buildCell<String>(
      name: name,
      onSelected: onSelected,
      pass: pass,
      labels: aqlList,
      values: aqlList,
    );
  }

  Widget buildCell<T>({
    required String name,
    required ValueChanged<T> onSelected,
    required List<String> labels,
    required List<T> values,
    double? height,
    bool pass = true,
  }) {
    return SizedBox(
        height: height,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            aiResultCell(
              name: name,
              pass: pass,
            ),
          ],
        ));
    return SizedBox(
      height: height,
      child: PopupMenuButton<T>(
        constraints: BoxConstraints(maxWidth: 100, maxHeight: 200),
        onSelected: onSelected,
        color: MColor.xFFFAFAFA,
        itemBuilder: (context) {
          return <PopupMenuEntry<T>>[
            for (var i = 0; i < values.length; ++i) ...[
              PopupMenuItem<T>(value: values[i], child: Text(labels[i])),
              PopupMenuDivider(height: 1),
            ]
          ]..removeLast();
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            aiResultCell(
              name: name,
              pass: pass,
            ),
            Icon(
              Icons.arrow_drop_down_outlined,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}

class _AiCheckoutJudgeResultView extends GetView<AiAttachmentController> {
  const _AiCheckoutJudgeResultView({super.key});

  @override
  Widget build(BuildContext context) {
    final color = MColor.xFFE5E5E5;
    final titleHeight = 57.0;
    final borderAll = TableBorder.all(color: color);
    final rowDecoration = BoxDecoration(color: MColor.xFFFFF3F0);

    final widths = {
      0: FixedColumnWidth(80),
      1: FixedColumnWidth(40),
      2: FixedColumnWidth(40),
      6: FixedColumnWidth(55),
    };
    return Obx(() => Container(
          padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
          color: MColor.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 15),
                child: Text(
                  S.of(context).test_validation_judgment,
                  style: MFont.medium18,
                ),
              ),
              Table(
                columnWidths: {0: FixedColumnWidth(186)},
                border: TableBorderExt.without(color: color, bottom: true),
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                children: [
                  TableRow(children: [
                    //空合并
                    Container(
                      padding: EdgeInsets.only(left: 10),
                      height: 43,
                      decoration: rowDecoration,
                      child: Text(
                        '${S.of(context).ai_measurement_standard}:ANSl/ASQZ1.4(MIL-STD-105E);${S.of(context).sampling_plan}:${S.of(context).single},${S.of(context).normal}',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                    //等级行列
                    Table(
                      border: TableBorderExt.without(
                          color: color, left: true, bottom: true),
                      children: [
                        TableRow(decoration: rowDecoration, children: [
                          for (final type in AiCheckoutDefectLevelType.values)
                            aiResultCell(name: type.name)
                        ]),
                      ],
                    ),
                  ]),
                ],
              ),
              //值
              Table(
                border: borderAll,
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                columnWidths: {
                  0: FixedColumnWidth(67),
                  1: FixedColumnWidth(60),
                  2: FixedColumnWidth(59),
                },
                children: [
                  TableRow(children: [
                    aiResultCell(name: S.of(context).ai_sampling_level),
                    aiResultCell(name: S.of(context).ai_sample_count),
                    aiResultCell(name: 'AQL'),
                    aiResultCell(
                        name:
                            controller.checkCategoryConfig.value.table?.aql1 ??
                                ''),
                    aiResultCell(
                        name:
                            controller.checkCategoryConfig.value.table?.aql2 ??
                                ''),
                    aiResultCell(
                        name:
                            controller.checkCategoryConfig.value.table?.aql3 ??
                                ''),
                  ]),
                  //动态
                  for (AiItemForms form in controller.checkStandardList)
                    TableRow(children: [
                      aiResultCell(
                          name: AICheckoutSampleLevelType.getName(
                              form.sampleLevelObs.value)),
                      aiResultCell(name: form.sampleNumObs.value),
                      aiResultCell(
                          name: ' ${S.of(context).ai_maximum_allowable_value}'),
                      for (AiCheckoutSampleQalInfo qal
                          in form.sampleQal?.values ?? [])
                        aiResultCell(name: qal.aqlAcNum?.toString() ?? ''),
                    ])
                ],
              ),
              SizedBox(height: 20),
              //标题
              Table(
                border: TableBorderExt.without(color: color, bottom: true),
                columnWidths: {
                  0: widths[0]!,
                  1: widths[1]!,
                  2: widths[2]!,
                  4: widths[6]!,
                },
                children: [
                  TableRow(decoration: rowDecoration, children: [
                    aiResultCell(
                        name: S.of(context).ai_test_item_name,
                        height: titleHeight),
                    aiResultCell(
                        name: S.of(context).ai_sampling_level,
                        height: titleHeight),
                    aiResultCell(
                        name: S.of(context).ai_sample_count,
                        height: titleHeight),
                    Table(
                      children: [
                        TableRow(decoration: rowDecoration, children: [
                          aiResultCell(
                              name: S.of(context).ai_test_result,
                              height: titleHeight / 2)
                        ]),
                        TableRow(decoration: rowDecoration, children: [
                          Table(
                            border:
                                TableBorderExt.only(color: color, top: true),
                            children: [
                              TableRow(children: [
                                aiResultCell(
                                    name: S.of(context).ai_critical,
                                    height: titleHeight / 2),
                                aiResultCell(
                                    name: S.of(context).ai_important,
                                    height: titleHeight / 2),
                                aiResultCell(
                                    name: S.of(context).ai_minor,
                                    height: titleHeight / 2),
                              ])
                            ],
                          )
                        ])
                      ],
                    ),
                    aiResultCell(
                        name: S.of(context).ai_conclusion, height: titleHeight),
                  ]),
                ],
              ),
              //值
              Table(
                border: borderAll,
                columnWidths: widths,
                children: [
                  for (int i = 0;
                      i < (controller.checkInputList ?? []).length;
                      i++)
                    TableRow(children: [
                      aiResultCell(
                          name: controller.checkInputList![i].name ?? '',
                          pass: controller.isCheckFitStandard(
                              controller.checkInputList![i])),
                      aiResultCell(
                          name: controller.getCheckLevelReal(controller
                                  .checkInputList![i]
                                  .sampleLevel
                                  ?.values
                                  ?.firstOrNull ??
                              ''),
                          pass: formIsPass(controller.checkInputList![i])),
                      aiResultCell(
                          name:
                              controller.checkInputList![i].sampleNumObs.value,
                          pass: controller.isCheckFitStandard(
                              controller.checkInputList![i])),
                      if (controller.checkCountList.isNotEmpty) ...{
                        aiResultCell(
                            name: controller.checkCountList[i]['aql_1'] ?? '0',
                            pass: formIsPass(controller.checkInputList![i])),
                        aiResultCell(
                            name: controller.checkCountList[i]['aql_2'] ?? '0',
                            pass: formIsPass(controller.checkInputList![i])),
                        aiResultCell(
                            name: controller.checkCountList[i]['aql_3'] ?? '0',
                            pass: formIsPass(controller.checkInputList![i])),
                      },
                      aiResultCell2(
                          name: controller.getCheckStatus(
                              controller.checkInputList![i].status, context),
                          last: true,
                          pass: controller.checkInputList![i].status!),
                    ]),
                ],
              )
            ],
          ),
        ));
  }

  bool formIsPass(AiItemForms form) {
    return form.sampleLevelObs.value == AICheckoutSampleLevelType.custom.value
        ? true
        : controller.isCheckFitStandard(form);
  }
}
