import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_category_info.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_checkout_sample_aql_data.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_checout_single.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:signature/signature.dart';

import '../../../../generated/l10n.dart';

class AiAttachmentController extends GetxController implements AiDataFeature {
  final aiDetail = AiSummaryDetailData(
          id: 0,
          ruleOrderId: 0,
          orderId: 0,
          productId: 0,
          modelId: 0,
          type: 8,
          status: 0)
      .obs;

  List<AiItemForms>? get inputList => aiDetail.value.details?.config?.forms;
  final categoryData = AiBaseEntity<List<AiCategoryInfo>>(data: []).obs;
  late final defects = <AiDetailDefectInfo>[AiDetailDefectInfo()].obs;
  late var baseResult = AiBaseEntity();

  bool get endResult {
    if (inputList == null) return false;
    bool result = true;
    for (AiItemForms e in inputList ?? []) {
      result = result && (e.isPassInLast ?? false);
      if (!result) return false;
    }
    aiDetail.value.details?.config?.result?.status = result ? 1 : 1;
    return result;
  }

  @override
  void onInit() {
    loadData();
    super.onInit();
  }

  void removeDefect(AiDetailDefectInfo defect) {
    defects.remove(defect);
  }

  void addDefect(AiDetailDefectInfo defect) {
    defects.insert(0, defect);
  }

  void toNext() {
    uploadData().then((value) {
      Get.back(result: -1);
    });
  }

  final SignatureController singController = SignatureController(
    penStrokeWidth: 1,
    penColor: Colors.red,
    exportBackgroundColor: Colors.white,
  );

  final SignatureController singControllerShow = SignatureController(
    penStrokeWidth: 1,
    penColor: Colors.red,
    exportBackgroundColor: Colors.white,
  );

  void onlySave() {
    uploadData().then((value) {
      Get.toNamed(AiRoutes.ATTACHMENT_SECOND)?.then((value) {
        Get.back();
      });
    });
  }

  void decodeSign() {
    print('111111111');
    if (defects[0].signature != null) {
      print('222222222');
      singControllerShow.clear();
      List<dynamic> points = jsonDecode(defects[0].signature!);
      points.forEach((element) {
        List<String> d = element.toString().split(r',');
        singControllerShow.addPoint(Point(
            Offset(double.parse(d[1]), double.parse(d[2])),
            d[0] == 'PointType.tap' ? PointType.tap : PointType.move,
            1));
      });
    }
  }

  void saveSign(context) async {
    if (singController.points.isEmpty) {
      showToast(S.of(context).ai_product_report_sign_done);
      return;
    }
    var json = jsonEncode(singController.points
        .map((e) => '${e.type},${e.offset.dx},${e.offset.dy}')
        .toList());
    defects[0].signature = json;

    Get.back(result: -1);
    // String? aa = await singController.toRawSVG();
    // if(aa!=null){
    //   defects[0].signature = aa;
    // }
    // showToast(S.of(context).ai_product_report_sign_done);
    // await singController.toPngBytes().then((value) async {
    //   var tempDir = await getTemporaryDirectory();
    //   String fileName = 'image_${DateTime.now().millisecond}.jpg';
    //   var file =
    //       await File('${tempDir.path}/$fileName')
    //       .create();
    //   await file.writeAsBytes(value as List<int>).then((value) async {
    //     print('    file.length()=='+    value.toString());
    //     String? url = await startUpload(fileName,value);
    //   });
    //
    //
    //
    //
    // });

    // await singController.toImage().then((value){
    //   print(value.toString());
    // });
  }

  @override
  void loadData() {
    AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: AiCategoryType.attachment.id,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      baseResult = value;
      if (value.data != null) {
        aiDetail.value = value.data!;
        if ((aiDetail.value.details?.config?.defects?.length ?? 0) != 0) {
          defects.clear();
          defects.addAll(aiDetail.value.details!.config!.defects!);
        } else {
          defects.clear();
          List<AiDetailDefectInfo> aa = [AiDetailDefectInfo.empty('')];
          defects.addAll(aa);
        }
      }
    });
    AiProvider()
        .getReportRuleLists(
      orderId: AiCategoryController.to.orderId,
      productId: AiCategoryController.to.productId,
    )
        .then(
      (value) {
        categoryData.value = value;
      },
    );
    loadDefects();
  }

  @override
  Future<AiBaseEntity> uploadData() {
    if (aiDetail.value.details == null) {
      return Future.value(AiBaseEntity(code: 200));
    }
    aiDetail.value.details?.config?.defects = defects
      ..removeWhere(
          (element) => element.label == null && element.actionType == null);
    aiDetail.value.details?.config?.uploadImageIfNeed();
    return AiProvider()
        .uploadConfig(id: aiDetail.value.id, details: aiDetail.value.details!);
  }

  final allDefects = <AiItemForms>[].obs;
  var qualityCategoryConfig = AiCategoryConfig().obs;
  var checkCategoryConfig = AiCategoryConfig().obs;
  List<AiItemForms> get checkStandardList =>
      checkCategoryConfig.value.table?.forms ?? [];
  List<AiItemForms>? get checkInputList => checkCategoryConfig.value.forms;
  var qualityCountList = <Map>[].obs;
  var checkCountList = <Map>[].obs;
  var qualityResultList = <Map>[].obs;

  Future<void> loadDefects() async {
    await AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: AiCategoryType.checkout.id,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      final config = value.data?.details?.config;
      if (config != null) {
        checkCategoryConfig.value = config;
        checkCountList.clear();
        for (AiItemForms o in config.forms ?? []) {
          if (o.data == null || o.data!.defectCheckOut == null) {
            checkCountList.add({
              'aql_1': '0',
              'aql_2': '0',
              'aql_3': '0',
            });
          } else {
            checkCountList
                .add(calCount(o.data!.defectCheckOut!.values![0].recordsList!));
          }
          // final l = o.data?.defectCheckOut?.values;
          // if (l?.isNotEmpty ?? false) {
          //   allDefects.addAll(l!);
          // }
        }
      }
    });
    await AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: AiCategoryType.quality.id,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      final config = value.data?.details?.config;
      if (config != null) {
        qualityCategoryConfig.value = config;
        allDefects.clear();
        qualityCountList.clear();
        qualityResultList.clear();
        for (AiItemForms o in config.forms ?? []) {
          if (o.data == null || o.data!.defectCheckOut == null) {
            qualityCountList.add({
              'aql_1': '0',
              'aql_2': '0',
              'aql_3': '0',
            });
          } else {
            qualityCountList
                .add(calCount(o.data!.defectCheckOut!.values![0].recordsList!));
          }
          if (o.data != null &&
              o.data!.defectCheckOut != null &&
              o.data?.defectCheckOut?.values != null &&
              o.data!.defectCheckOut!.values!.isNotEmpty) {
            bool has = false;
            for (var item in o.data!.defectCheckOut!.values!) {
              if (item.recordsList!.isNotEmpty) {
                has = true;
                break;
              }
            }
            if (has) {
              allDefects.add(o);
            }
          }
        }
        getResult();
        // final l = config.data?.defectCheckOut?.values;
        // if (l?.isNotEmpty ?? false) {
        //   allDefects.addAll(l!);
        // }
      }
    });
  }

  Map calCount(List<AiCheckOutInfoSingle> countItem) {
    Map aa = {
      'aql_1': '0',
      'aql_2': '0',
      'aql_3': '0',
    };
    if (countItem == null || countItem.isEmpty) {
      return aa;
    }

    int totalNum1 = 0;
    int totalNum2 = 0;
    int totalNum3 = 0;
    for (var parent in countItem) {
      for (var item in parent.defectsList!) {
        if (item.level == 'aql_1') {
          // 关键
          totalNum1 = totalNum1 +
              (item.totalNum == null ? 0 : int.parse(item.totalNum!));
          aa['aql_1'] = totalNum1.toString();
        } else if (item.level == 'aql_2') {
          // 关键
          totalNum2 = totalNum2 +
              (item.totalNum == null ? 0 : int.parse(item.totalNum!));
          aa['aql_2'] = totalNum2.toString();
        } else if (item.level == 'aql_3') {
          // 关键
          totalNum3 = totalNum3 +
              (item.totalNum == null ? 0 : int.parse(item.totalNum!));
          aa['aql_3'] = totalNum3.toString();
        }
      }
    }
    return aa;
  }

  getResult() {
    int aql_1 = 0;
    num aql_2 = 0;
    num aql_3 = 0;
    for (var item in qualityCountList) {
      aql_1 += int.tryParse(item['aql_1']?.toString() ?? '0') ?? 0;
      aql_2 += num.tryParse(item['aql_2']?.toString() ?? '0') ?? 0;
      aql_3 += num.tryParse(item['aql_3']?.toString() ?? '0') ?? 0;
    }
    qualityResultList.assignAll([
      {
        'aql': aql_1,
      },
      {
        'aql': aql_2,
      },
      {
        'aql': aql_3,
      },
    ]);
  }

  bool isPassLeve(int index) {
    int aql1 = 0;
    int aql2 = 0;
    int aql3 = 0;
    for (Map map in qualityCountList) {
      aql1 = aql1 + int.parse(map['aql_1']);
      aql2 = aql2 + int.parse(map['aql_2']);
      aql3 = aql3 + int.parse(map['aql_3']);
    }
    int diffNum = 0;
    if (index == 0) {
      diffNum = aql1;
    } else if (index == 1) {
      diffNum = aql2;
    } else if (index == 2) {
      diffNum = aql3;
    }
    bool isSucc = (int.tryParse(
                qualityCategoryConfig.value.table?.aqlAcNumList?[index] ??
                    '0') ??
            0) >=
        diffNum;

    return isSucc;
  }

  bool isCheckFitStandard(AiItemForms form) {
    final level = form.sampleLevel?.values?.first;
    final standard = checkCategoryConfig.value.table?.forms?.firstWhereOrNull(
      (element) => element.sampleLevel?.values?.first == level,
    );
    bool result = true;
    for (AiCheckoutSampleQalInfo o in standard?.sampleQal?.values ?? []) {
      final formAql = form.sampleQal?.values
          ?.firstWhereOrNull((element) => element.key == o.key);
      result = result &&
          (int.tryParse(o.aqlAcNum) ?? 0) >=
              (int.tryParse(formAql?.aqlAcNum ?? '0') ?? 0);
    }
    return result;
  }

  String getCheckLevelReal(value) {
    for (final item in AICheckoutSampleLevelType.values) {
      if (value == item.value) {
        return item.name;
      }
    }
    if (value == '无' || value == '') {
      return '/';
    }
    return value;
  }

  String getCheckStatus(status, context) {
    if (status == 1) {
      return S.of(context).approve;
    }
    if (status == 2) {
      return S.of(context).ai_default_config_fail;
    }
    if (status == 3) {
      return S.of(context).ai_wait;
    }
    return '';
  }
}
