import 'dart:math';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/modules/ai/attachment/ai_attachment_controller.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/checkout_result/ai_checkout_result_view.dart';
import 'package:inspector/app/modules/ai/const_data/ai_const_data.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_image_selected_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_defect_record_tile.dart';
import 'package:inspector/app/modules/ai/widgets/ai_widget_style.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';
import 'package:signature/signature.dart';

import '../ai_net/ai_server.dart';
import '../photo_selected/action_picture/ai_image_info.dart';
import '../photo_selected/image_browse_page.dart';
import '../photo_selected/picker_method.dart';

class AiAttachmentSignView extends GetView<AiAttachmentController> {
  const AiAttachmentSignView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(S.of(context).ai_report_summary),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: KeyboardDismissOnTap(
          child: Column(
            children: [
              Expanded(
                child: AiEmptyView(
                    baseResult: controller.baseResult,
                    child: ListView(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              margin: EdgeInsets.only(left: 20,top: 10,bottom: 10),
                              child: Text(S.current.ai_product_report_sign,style: MFont.semi_Bold17,),
                            )
                          ],
                        ),
                        Signature(
                          controller: controller.singController,
                          width: Get.width,
                          height: 180,
                          backgroundColor: Colors.white,
                        )
                      ],
                    ),
                  )
              ),

              Container(
                  height: 60,
                  child:  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                          width: Get.width/2 - 20 ,
                          child:  AiSubmitButton(
                            onPressed: (){
                              controller.saveSign(context);
                            },
                            name: S.current.date_save,
                            isSave: true,
                          )
                      ),
                      Container(
                          width: Get.width/2 - 20 ,
                          child:
                          AiSubmitButton(
                            onPressed: (){
                              controller.singController.clear();
                            },
                            name: S.current.address_clear,
                          ))
                    ],
                  )
              ),
            ],
          ),
        ),
      ),
    );
  }


  Widget buildAddButton() {
    return Container(
      color: MColor.white,
      padding: EdgeInsets.fromLTRB(20, 10, 20, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            S.current.ai_special_note,
            style: MFont.bold18,
          ),
          Spacer(),
        ],
      ),
    );
  }
}

class _AiResultNotice extends StatefulWidget {
  const _AiResultNotice(this.defect, {this.removeDefectAction});

  final AiDetailDefectInfo defect;
  final ValueChanged<AiDetailDefectInfo>? removeDefectAction;

  @override
  State<_AiResultNotice> createState() => _AiResultNoticeState();
}

class _AiResultNoticeState extends State<_AiResultNotice> {

     Future<String?> startUpload(asset) async {
    final url = await AiProvider().uploadAsset(
      asset,
      onSendProgress: (count, total) {
      },
    ).then(
          (value) {
        return value;
      },
    );

    return url;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [

            _first(),
            Container(
              padding: EdgeInsets.fromLTRB(20, 10, 20, 0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    S.current.ai_special_note,
                    style: MFont.bold18,
                  ),
                  Spacer(),
                ],
              ),
            ),
          Container(
            margin: EdgeInsets.only(top: 10),
              padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
            color: Colors.white,
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: TextEditingController(text: widget.defect.label),
                        minLines: 3,
                        maxLines: 5,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(AiConstData.textMaxLength),
                        ],
                        decoration:  InputDecoration(
                          hintStyle: TextStyle(
                            color: MColor.xFF808080
                          ),
                            hintText: S.current.ai_product_report_special,
                            enabledBorder: AiWidgetStyle.enabledBorder,
                            focusedBorder: AiWidgetStyle.focusedBorder,
                            contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 8)),
                        onChanged: changeNote,
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: EdgeInsets.only(top: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      AiImageSelectedButton(manager: widget.defect, big: true),
                      Expanded(
                        child: AiFormAssetsListView(manager: widget.defect),
                      ),
                    ],
                  ),
                ),
              ],
            )
          )

          ],
        ),
      ),
    );
  }



  Widget _first(){
       widget.defect.covers ??= [];
    return Container(
      padding: EdgeInsets.only(top: 10,bottom: 10,left: 20),
      color: Colors.white,
      child: Row(
        children: [
          Text('*'+S.current.ai_product_first,style: MFont.medium18,),
          SizedBox(width: 20,),
         widget.defect.covers!=null &&widget.defect.covers!.isNotEmpty
                ? Container(
              width: 68, //68与图片大小一致
              height: 68,
              decoration: BoxDecoration(
                border: Border.all(color: MColor.xFFE5E5E5),
                borderRadius: BorderRadius.circular(8),
              ),
              margin: EdgeInsets.only(right: 8),
              child: Container(
                width: 68, //68与图片大小一致
                height: 68,
                child: Stack(
                  children: [
                    InkWell(
                      onTap: (){
                        List<AiImageInfo> aiInfo = AiImageInfo.fromList(urlList:widget.defect.covers,assets:[]);
                        Navigator.push(context, MaterialPageRoute(
                          builder: (context) {
                            return ImageBrowsePage(title: S.of(context).ai_inspection_image, selected: 0, images: aiInfo);
                          },
                        ));
                      },
                      child:  ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child:  Image.network( widget.defect.covers![0],width: 68,height: 68,fit: BoxFit.cover,)
                      )
                    )
                   ,
                    Positioned(
                        right: 0,
                        child: InkWell(
                      onTap: (){
                        setState(() {
                          widget.defect.covers?.clear();
                        });

                      },
                      child: Icon(Icons.close,  color:  MColor.xFFC4C4C4)
                    ))
                  ],
                ),
              ) ,
            )
                : InkWell(
              onTap: () async {
                final list = await PickMethod(
                  maxAssetsCount: 1,
                ).forCamera(context);
                if(list!.isNotEmpty){
                  String?  url= await startUpload(list[0]);
                  setState(() {
                    if(widget.defect.covers!.isNotEmpty){
                      widget.defect.covers?[0] = url!;
                    }else{
                      widget.defect.covers?.add(url!);
                    }

                  });

                }
              },
              child: Container(
                  width: 68, //68与图片大小一致
                  height: 68,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: MColor.xFFC4C4C4)
                  ),
                  child: Icon(Icons.camera_alt,  color:  MColor.xFFC4C4C4)
              )
            )  ,

        ],
      ),
    );
  }

  Icon getIcon(bool value) {
    return Icon(
      value ? Icons.check_circle : Icons.circle_outlined,
      color: MColor.aiMain,
      size: 20,
    );
  }

  void changeResult(String value) {
    setState(() {
      widget.defect.actionType = value;
    });
  }

  void changeNote(String value) {
    widget.defect.label = value;
  }
}

class _AiResultStatusView extends GetView<AiAttachmentController> {
  const _AiResultStatusView({required this.pass, super.key});

  final bool pass;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 136,
      margin: EdgeInsets.only(top: 10),
      color: MColor.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 20, top: 10),
            child: Text(
              S.of(context).ai_overall_conclusion_2,
              style: MFont.bold18,
            ),
          ),
          Expanded(
            child: Center(
              child: Container(
                width: 200,
                height: 60,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: pass ? MColor.pass : MColor.failed,
                    borderRadius: BorderRadius.circular(10.0)),
                child: Text(
                  pass.title,
                  textAlign: TextAlign.center,
                  style: MFont.bold20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _AiSummarizeView extends GetView<AiAttachmentController> {
  OrderDetailEntity? get order => AiCategoryController.to.order;

  AiCategoryNameModel? get model => AiCategoryController.to.currentModel.value;

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(bottom: 20),
        margin: EdgeInsets.only(top: 10),
        color: MColor.white,
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Padding(
            padding: EdgeInsets.only(left: 20, top: 10, bottom: 15),
            child: Text(S.of(context).ai_summary, style: MFont.bold18),
          ),
          Obx(() {
            return Table(
              border: TableBorder.all(color: MColor.xFFE5E5E5),
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: [
                TableRow(decoration: BoxDecoration(color: MColor.xFFFFF3F0), children: [
                  aiResultCell(name: S.of(context).ai_category_name_table, height: 38),
                  aiResultCell(name: S.of(context).ai_compliance, height: 38),
                  aiResultCell(name: S.of(context).ai_remarks_2, height: 38),
                ]),
                for (AiItemForms form in (controller.inputList ?? []))
                  TableRow(children: [
                    aiResultCell(name: form.name ?? ''),
                    _cell(form.isPassInLast),
                    aiResultCell(name: form.remark ?? ''),
                  ]),
              ],
            );
          }),
        ]));
  }

  TableRow summaryRow(String title, String value) {
    return TableRow(children: [
      Container(
        height: 38,
        alignment: Alignment.center,
        child: Text(title),
      ),
      Padding(
          padding: const EdgeInsets.only(left: 8),
          child: Text(
            value,
            style: MFont.regular14,
          )),
    ]);
  }

  Widget _cell(bool? done) {
    Widget icon(bool? selected) =>
        Icon(selected == true ? Icons.check_circle : Icons.circle_outlined,
            size: 20, color: MColor.xFFA6A6A6);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        icon(done),
        SizedBox(width: 2),
        Text('YES'),
        SizedBox(width: 10),
        icon(done == null ? null : !done!),
        SizedBox(width: 2),
        Text('NO'),
      ],
    );
  }
}
