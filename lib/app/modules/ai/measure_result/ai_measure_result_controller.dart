import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';

class AiMeasureResultController extends GetxController implements AiDataFeature {
  final aiDetail = AiSummaryDetailData(
          id: 0, ruleOrderId: 0, orderId: 0, productId: 0, modelId: 0, type: 7, status: 0)
      .obs;

  List<AiItemForms>? get inputList => aiDetail.value.details?.config?.forms ?? [];
  List<AiItemEvent>? get eventList => aiDetail.value.details?.config?.event ?? [];
  AiSummaryResult? get result => aiDetail.value.details?.config?.result;
  late var baseResult = AiBaseEntity();

  @override
  void onInit() {
    loadData();
    super.onInit();
  }

  void toNext() {
    uploadData().then((value) {
      Get.offAndToNamed(
        AiCategoryController.to.nextType(fromType: AiCategoryType.measure.id).location,
        parameters: Get.parameters as Map<String, String>,
      );
    });
  }

  void goback() {
    uploadData().then((value) {
      Get.offAndToNamed(
        AiCategoryController.to.nextType(fromType: AiCategoryType.measure.id).location,
        parameters: Get.parameters as Map<String, String>,
      );
    });
  }

  @override
  void loadData() {
    AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: AiCategoryType.measure.id,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      baseResult = value;
      if (value.data != null) {
        aiDetail.value = value.data!;
      }
    });
  }

  @override
  Future<AiBaseEntity> uploadData() {
    if (aiDetail.value.details == null) {
      return Future.value(AiBaseEntity(code: 200));
    }
    aiDetail.value.details?.config?.uploadImageIfNeed();
    return AiProvider().uploadConfig(id: aiDetail.value.id, details: aiDetail.value.details!);
  }
}
