import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/checkout_result/ai_checkout_result_view.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/measure_result/ai_measure_result_controller.dart';
import 'package:inspector/app/modules/ai/widgets/ai_summary_result.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';

class AiMeasureResultViewView extends GetView<AiMeasureResultController> {
  const AiMeasureResultViewView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(AiCategoryType.measure.name),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: Obx(() {
          return KeyboardDismissOnTap(
            child: Column(children: [
              AiProductSelectBar(loader: controller),
              Expanded(
                child: AiEmptyView(
                  baseResult: controller.baseResult,
                  child: ListView(
                    children: [
                      _AiMeasureJudgeResultView(),
                      AiSummaryResultView(
                        isSame: controller.result?.isSame,
                        note: controller.result?.remark,
                        noteChange: (value) => controller.result?.remark = value,
                        resultChange: (value) => controller.result?.changeResult(value),
                      )
                    ],
                  ),
                ),
              ),
              AiSubmitButton.next(
                onPressed: controller.toNext,
              ),
            ]),
          );
        }),
      ),
    );
  }
}

class _AiMeasureJudgeResultView extends GetView<AiMeasureResultController> {
  const _AiMeasureJudgeResultView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      color: MColor.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: Text(
              S.of(context).ai_judgment,
              style: MFont.medium18,
            ),
          ),
          Table(
              border: TableBorder.all(color: MColor.xFFE5E5E5),
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              columnWidths: {
                0: FixedColumnWidth(50),
                // 1: FixedColumnWidth(50),
                // 2: FixedColumnWidth(50),
                3: FixedColumnWidth(40),
                4: FixedColumnWidth(45),
              },
              children: [
                TableRow(
                    decoration: BoxDecoration(color: MColor.xFFFFF3F0),
                    children: [
                      S.of(context).ai_judgment_item,
                      S.of(context).ai_standard,
                      S.of(context).ai_result,
                      S.of(context).ai_allowable_error,
                      S.of(context).ai_conclusion
                    ]
                        .map(
                          (e) => aiResultCell(name: e, height: 45),
                        )
                        .toList()),
                for (int i = 0; i < (controller.inputList?.length ?? 0); ++i)
                  TableRow(children: [
                    aiResultCell(
                        name: controller.inputList![i].name ?? '',
                        pass: controller.inputList![i].isMeasurePass),
                    aiResultCell(
                        name: controller.inputList![i].measureStandardData?.values?.firstOrNull
                                ?.measureValueName ??
                            '',
                        pass: controller.inputList![i].isMeasurePass),
                    // aiResultCell(
                    //     name: controller.inputList![i].measureData?.allValueString ?? '',
                    //     pass: controller.inputList![i].isMeasurePass,
                    //     height: null),
                    aiResultCell(
                        name: controller.inputList![i].measureAllowNum?.values?.firstOrNull ?? '',
                        pass: controller.inputList![i].isMeasurePass),
                    aiResultCell(
                        name: controller.inputList![i].isMeasurePass.title,
                        last: true,
                        pass: controller.inputList![i].isMeasurePass),
                  ])
              ]),
        ],
      ),
    );
  }
}
