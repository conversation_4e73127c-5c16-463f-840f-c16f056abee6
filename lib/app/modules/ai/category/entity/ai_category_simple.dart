import 'package:inspector/app/modules/ai/category/type/category_type.dart';

class AiCategorySimple {
  int sampleNum;
  String level;
  int aqlAcNum;

  AiCategorySimple({
    required this.sampleNum,
    required this.level,
    required this.aqlAcNum,
  });

  factory AiCategorySimple.fromJson(Map<String, dynamic> json) {
    return AiCategorySimple(
      sampleNum: json['sample_num'] ?? 0,
      level: json['level'] ?? '',
      aqlAcNum: json['aql_ac_num'] ?? '',
    );
  }


}
