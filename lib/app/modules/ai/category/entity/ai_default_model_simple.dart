class AiDefaultModelSimple {
  AiDefaultModelSimple({
      this.modelId, 
      this.adminId, 
      this.status, 
      this.statusText, 
      this.reportStatusText,});

  AiDefaultModelSimple.fromJson(dynamic json) {
    modelId = json['model_id'];
    adminId = json['admin_id'];
    status = json['status'];
    statusText = json['status_text'];
    reportStatusText = json['report_status_text'];
  }
  int? modelId;
  int? adminId;
  int? status;
  String? statusText;
  String? reportStatusText;
  bool get fromDefault => (adminId ?? 0) > 0;
}