class AiClassifyInfo {
  int id;
  int pid;
  String name;
  String nameEn;
  int level;
  List<AiClassifyInfo>? child;

  AiClassifyInfo({
    required this.id,
    required this.pid,
    required this.name,
    required this.nameEn,
    required this.level,
    this.child = const [],
  });

  factory AiClassifyInfo.fromJson(Map<String, dynamic> json) {
    return AiClassifyInfo(
      id: json['id'] ?? 0,
      pid: json['pid'] ?? 0,
      name: json['name'] ?? '',
      nameEn: json['name_en'] ?? '',
      level: json['level'] ?? 0,
      child: json['child'] != null
          ? (json['child'] as List).map((i) => AiClassifyInfo.fromJson(i)).toList()
          : null,
    );
  }

  bool get hasNext => child?.isNotEmpty ?? false;


  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AiClassifyInfo &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          level == other.level;

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ level.hashCode;
}
