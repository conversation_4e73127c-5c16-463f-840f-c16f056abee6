class AiTemplateInfoData {
  List<AiTemplateInfo> list;
  int total;

  AiTemplateInfoData({required this.list, required this.total});

  factory AiTemplateInfoData.fromJson(dynamic json) {
    List<AiTemplateInfo>? list = json['list'] != null
        ? (List.from((json['list'] as List).map(
            (e) => AiTemplateInfo.fromJson(e),
          )))
        : null;
    final total = json['total'] ?? 0;
    return AiTemplateInfoData(list: list ?? [], total: total);
  }
}

class AiTemplateInfo {
  int id;
  String name;

  AiTemplateInfo({
    required this.id,
    required this.name,
  });

  factory AiTemplateInfo.fromJson(dynamic json) {
    final id = json['id'];
    final name = json['name'];
    return AiTemplateInfo(id: id, name: name);
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    return map;
  }
}
