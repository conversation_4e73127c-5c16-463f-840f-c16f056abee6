import 'package:inspector/app/modules/ai/category/type/category_type.dart';

class AiRecmendData {
  int id;
  int categoryId;
  int modelId;
  int type;
  String itemName;
  String remark;
  int hot;
  bool isSelected = false;

  AiRecmendData({
    required this.id,
    required this.categoryId,
    required this.modelId,
    required this.type,
    required this.itemName,
    required this.remark,
    required this.hot,

  });

  factory AiRecmendData.fromJson(Map<String, dynamic> json) {
    return AiRecmendData(
      id: json['id'] ?? 0,
      categoryId: json['category_id'] ?? '',
      modelId: json['model_id'] ?? '',
      type: json['type'] ?? 0,
      itemName: json['item_name'] ?? '',
      remark: json['remark'] ?? '',
      hot: json['hot'] ?? 0,

    );
  }


}
