class AiModelItemData {
  int id;
  String name;
  String image;

  AiModelItemData({
    required this.id,
    required this.name,
    this.image = '',
  });

  factory AiModelItemData.fromJson(dynamic json) {
    final id = json['id'];
    final name = json['name'];
    final image = json['image'];
    return AiModelItemData(id: id, name: name, image: image);
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    map['image'] = image;
    return map;
  }
}
