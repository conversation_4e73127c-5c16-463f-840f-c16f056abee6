import 'package:inspector/app/modules/ai/category/type/category_type.dart';

class AiCategoryInfo {
  AiCategoryInfo({
    required this.type,
    required this.status,
    required this.statusText,
    required this.modelId,
    required this.remark,
  });

  AiCategoryInfo.fromJson(dynamic json) {
    type = json['type'] ?? 0;
    status = json['status'] ?? 0;
    statusText = json['status_text'] ?? '';
    modelId = json['model_id'] ?? 0;
    remark = json['remark'] ?? '';
  }
  AiCategoryInfo.attachment()
      : type = 8,
        status = 2,
        statusText = '';
  late int type;
  late num status;
  late String statusText;
  late int modelId;
  late String remark;
  late final AiCategoryType categoryType = AiCategoryType.from(type);

  bool get isPass => status == 1;
  bool get hasTag => status != 0;

  AiCategoryInfo copyWith({
    int? type,
    num? status,
    String? statusText,
    int? modelId,
    String? remark,
  }) =>
      AiCategoryInfo(
        type: type ?? this.type,
        status: status ?? this.status,
        statusText: statusText ?? this.statusText,
        modelId: modelId ?? this.modelId,
        remark: remark ?? this.remark,
      );
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['type'] = type;
    map['status'] = status;
    map['status_text'] = statusText;
    map['model_id'] = modelId;
    map['remark'] = remark;
    return map;
  }
}
