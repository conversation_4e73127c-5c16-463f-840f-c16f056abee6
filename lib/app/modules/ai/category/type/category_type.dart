import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';

enum AiCategoryType {
  number(
    id: 1,
    iconPath: Assets.aiNumberIcon,
    location: AiRoutes.NUMBER,
  ),
  pack(
    id: 2,
    iconPath: Assets.aiPackageIcon,
    location: AiRoutes.PACKAGE,
  ),
  mark(
    id: 3,
    iconPath: Assets.aiMarkIcon,
    location: AiRoutes.MARK,
  ),
  style(
    id: 4,
    iconPath: Assets.aiStyleIcon,
    location: AiRoutes.STYLE,
  ),
  quality(
    id: 5,
    iconPath: Assets.aiQualityIcon,
    location: AiRoutes.QUALITY,
  ),
  checkout(
    id: 6,
    iconPath: Assets.aiCheckoutIcon,
    location: AiRoutes.CHECKOUT,
  ),
  measure(
    id: 7,
    iconPath: Assets.aiMeasureIcon,
    location: AiRoutes.MEASURE,
  ),
  attachment(
    id: 8,
    iconPath: Assets.aiAttachmentIcon,
    location: AiRoutes.ATTACHMENT,
  );

  const AiCategoryType({required this.id, required this.iconPath, required this.location});
  final int id; //type
  final String iconPath;

  ///route name
  final String location;

  String get name {
    switch (this) {
      case AiCategoryType.number:
        return S.current.ai_quantity;
      case AiCategoryType.pack:
        return S.current.ai_packaging;
      case AiCategoryType.mark:
        return S.current.ai_shipping_mark;
      case AiCategoryType.style:
        return S.current.ai_product_style;
      case AiCategoryType.quality:
        return S.current.ai_craftsmanship;
      case AiCategoryType.checkout:
        return S.current.ai_test_verification;
      case AiCategoryType.measure:
        return S.current.ai_category_measure;
      case AiCategoryType.attachment:
        return S.current.ai_spare_parts;
    }
  }

  static AiCategoryType from(int type) => AiCategoryType.values.firstWhere(
        (e) => e.id == type,
      );
}
