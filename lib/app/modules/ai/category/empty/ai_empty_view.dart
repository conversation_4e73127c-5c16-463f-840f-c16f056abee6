import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_default_config_controller.dart';
import 'package:inspector/app/modules/ai/category/view/ai_default_config_view.dart';

class AiEmptyView extends StatefulWidget {
  final Widget child;
  final AiBaseEntity baseResult;
  final Widget? placeholder;
  const AiEmptyView({
    required this.baseResult,
    required this.child,
    this.placeholder,
    super.key,
  });

  @override
  State<AiEmptyView> createState() => _AiEmptyViewState();
}

class _AiEmptyViewState extends State<AiEmptyView> {
  @override
  Widget build(BuildContext context) {
    if (widget.baseResult.isGood) return widget.child;
    if (widget.baseResult.isLoading) return SizedBox.shrink();
    if (widget.baseResult.isBad) {
      if (widget.baseResult.templateEmpty) {
        return GetBuilder(
            init: AiDefaultConfigController(), builder: (controller) => AiDefaultConfigView());
      }
      return widget.placeholder ?? Center(child: Text(widget.baseResult.message ?? '没有信息'));
    }
    return SizedBox.shrink();
  }
}
