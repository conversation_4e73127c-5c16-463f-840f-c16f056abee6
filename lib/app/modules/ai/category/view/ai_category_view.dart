import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_default_config_controller.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_category_info.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/view/ai_default_config_view.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

import '../../entiy/ai_envent_refresh.dart';
import '../../product/ai_product_list_view.dart';

class AiCategoryView extends GetView<AiCategoryController> {
  AiCategoryView({super.key});

  final GlobalKey<DropdownButton2State> globalKey = GlobalKey();
  var categoryController;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).ai_category_name),
        centerTitle: true,
        actions: [
          /*GetBuilder(
              init: AiDefaultConfigController(),
              builder: (controller) => TextButton(
                  onPressed: () async {
                    await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AiDefaultTemplateView(),
                        ));
                  },
                  child: Text(
                    S.of(context).ai_default_config_template,
                    style: MFont.medium14.copyWith(
                      color: MColor.black,
                    ),
                  ))),*/
          GetBuilder(
            init: AiDefaultConfigController(),
            builder: (controller) => DropdownButton2<int>(
                key: globalKey,
                underline: Container(),
                customButton: IconButton(
                  icon: Icon(Icons.more_horiz_rounded),
                  onPressed: (){
                    globalKey.currentState?.callTap();
                  },
                ),
                dropdownStyleData: DropdownStyleData(
                    padding: const EdgeInsets.all(0),
                    offset: const Offset(0, -4),
                    elevation: 2,
                    width: 130,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4)
                    )
                ),
                onChanged: (value){
                  if(value == 1){
                    showToast(S.of(context).select_template_config_tip);
                    /*Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AiDefaultTemplateView(),
                        ));*/
                  }
                  if(value == 2){
                    Get.to(ProductListView(id:super.controller.orderId))?.then((value){
                      controller.loadModelList();
                      AiCategoryController.to.getCategoryResult();
                      AiCategoryController.to.loadOrdersData();
                      AiCategoryController.to.reload();

                    });
                  }
                },
                items: [
                  DropdownMenuItem<int>(
                    value: 1,
                    child: Row(
                      children: [
                        Icon(Icons.settings,size: 16,),
                        SizedBox(width: 8,),
                        Text(S.of(context).ai_default_config_select_template,style: TextStyle(fontSize: 14),)
                      ],
                    ),
                  ),
                  DropdownMenuItem<int>(
                    value: 2,
                    child: Row(
                      children: [
                        Icon(Icons.edit,size: 16,),
                        SizedBox(width: 8,),
                        Text(S.of(context).ai_default_config_product_edit,style: TextStyle(fontSize: 14),)
                      ],
                    ),
                  ),
                ]
            )
          )
        ],
      ),
      backgroundColor: MColor.backgroundColor,
      body: Column(
        children: const [
          AiProductSelectBar(),
          Expanded(child: AiCategoryListView()),
        ],
      ),
    );
  }
}

class AiCategoryListView extends GetView<AiCategoryController> {
  const AiCategoryListView({super.key});

  @override
  Widget build(BuildContext context) {

    return Obx(() {
      final count = controller.categoryList.length;
      return LayoutBuilder(builder: (context, constraints) {
        if (controller.categoryData.value.templateEmpty) {
          return GetBuilder(
              init: AiDefaultConfigController(), builder: (controller) => AiDefaultConfigView());
        }
        if (controller.errorInfo.value) {
          return GetBuilder(
              init: AiDefaultConfigController(), builder: (controller) => AiFullConfigView());
        }
        return AiEmptyView(
          baseResult: controller.categoryData.value,
          child: GridView.builder(
            padding: const EdgeInsets.fromLTRB(10, 0, 10, 10),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 23,
                mainAxisSpacing: 20,
                // childAspectRatio: 165 / 150,
                mainAxisExtent: max(constraints.maxHeight / 4 - 20, 115)),
            itemCount: count,
            itemBuilder: (context, index) {
              final info = controller.categoryList[index];
              return GestureDetector(
                onTap: () => controller.toNext(info),
                child: AiCategoryItemTile(info: info),
              );
            },
          ),
        );
      });
    });
  }
}

class AiCategoryItemTile extends StatelessWidget {
  final AiCategoryInfo info;

  const AiCategoryItemTile({
    required this.info,
    super.key,
  });

  Widget get tag {
    return Center(
      child: Container(
        width: 40.0,
        height: 20.0,
        padding: EdgeInsets.symmetric(horizontal: 4),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: info.status==1 ? MColor.pass :info.status==3? MColor.xFFDF8D14:MColor.failed,
          borderRadius: BorderRadius.circular(5),
        ),
        child: FittedBox(
          child: Text(
            info.status==1 ? S.of(Get.context!).approve :info.status==3? S.of(Get.context!).ai_wait:S.of(Get.context!).ai_default_config_fail,

            style: MFont.medium12,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = 80.0;
    return Stack(
      fit: StackFit.passthrough,
      children: [
        Container(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.0),
              border: Border.all(color: MColor.xFFE5E5E5)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                info.categoryType.iconPath,
                width: size,
                height: size,
              ),
              Padding(
                padding: const EdgeInsets.only(top: 2),
                child: Text(
                  info.categoryType.name,
                  style: MFont.medium18.apply(color: MColor.xFF000000),
                ),
              ),
            ],
          ),
        ),
        if (info.hasTag)
          Positioned(
            top: 5,
            right: 5,
            child: tag,
          ),
      ],
    );
  }
}

///产品列表
class AiProductSelectBar extends GetView<AiCategoryController> {
  const AiProductSelectBar({this.loader, super.key});

  final AiDataFeature? loader;

  @override
  Widget build(BuildContext context) {
    print('');
    if (controller.modelList.isEmpty) return const SizedBox.shrink();
    return Obx(()=>DropdownButtonHideUnderline(
      child: DropdownButton2<AiCategoryNameModel>(
        isExpanded: true,
        value: controller.currentModel.value,
        onChanged: (item){
          controller.changeModel(model: item!, loader: loader);
        },
        buttonStyleData: ButtonStyleData(
            padding: EdgeInsets.only(bottom: 8)
        ),
        iconStyleData: IconStyleData(icon: Container()),
        dropdownStyleData: DropdownStyleData(
          decoration: BoxDecoration(
            color: Colors.white
          ),
          padding: EdgeInsets.zero,
          scrollPadding: EdgeInsets.zero
        ),
        selectedItemBuilder: (BuildContext context)=>List.generate(controller.modelList.length,(index)=>Container(
          color: Colors.white,
          height: 40,
          margin: const EdgeInsets.only(top: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                controller.modelList[index].name,
                style: TextStyle(
                  fontSize: 14.0,
                  height: 1.0,
                  color: MColor.skin,),
              ),
              SizedBox(
                width: 4,
              ),
              Transform.rotate(
                angle: pi / 2,
                child: const Icon(
                  Icons.play_arrow_sharp,
                  size: 12,
                  color: MColor.skin,
                ),
              ),
            ],
          ),
        )),
        items: List.generate(controller.modelList.length, (index)=>DropdownMenuItem<AiCategoryNameModel>(
            value: controller.modelList[index],
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  controller.modelList[index].name,
                  style: TextStyle(
                    fontSize: 14.0,
                    height: 1.0,
                    color: controller.modelList[index].model.id==controller.currentModel.value.model.id?MColor.skin:MColor.black,),
                ),
              ],
            )
        )),
      ),
    ));
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Obx(() {
        if (controller.expanded.isTrue) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 151,
                child: ListView.separated(
                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                  itemCount: controller.modelList.length,
                  itemBuilder: (context, index) {
                    final item = controller.modelList[index];
                    return Obx(() {
                      return GestureDetector(
                        onTap: () => controller.changeModel(model: item, loader: loader),
                        child: _CategoryImageTextTile(
                          selected: item.model.id == controller.currentModel.value.model.id,
                          model: item,
                        ),
                      );
                    });
                  },
                  separatorBuilder: (context, index) =>
                      const Padding(padding: EdgeInsets.symmetric(horizontal: 8)),
                ),
              ),
              GestureDetector(
                onTap: controller.expanded.toggle,
                child: Container(
                  width: 38,
                  height: 17,
                  decoration:
                      BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(5)),
                  child: Transform.rotate(
                    angle: -pi / 2,
                    child: const Icon(
                      Icons.play_arrow_sharp,
                      size: 12,
                    ),
                  ),
                ),
              ),
            ],
          );
        } else {
          return GestureDetector(
            onTap: controller.expanded.toggle,
            child: Container(
              color: Colors.white,
              height: 38,
              margin: const EdgeInsets.only(top: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    controller.currentModel.value.name,
                    style: MFont.medium14,
                  ),
                  SizedBox(
                    width: 4,
                  ),
                  Transform.rotate(
                    angle: pi / 2,
                    child: const Icon(
                      Icons.play_arrow_sharp,
                      size: 12,
                    ),
                  ),
                ],
              ),
            ),
          );
        }
      }),
    );
  }
}

///产品项
class _CategoryImageTextTile extends StatelessWidget {
  const _CategoryImageTextTile({
    required this.selected,
    required this.model,
    super.key,
  });

  final AiCategoryNameModel model;
  final bool selected;

  @override
  Widget build(BuildContext context) {
    final borderRadius = BorderRadius.circular(10);
    final size = 80.0;
    return Text(
      model.name,
      textAlign: TextAlign.center,
      maxLines: 2,
      style: TextStyle(
          fontSize: 14.0,
          height: 1.0,
          color: selected ? MColor.skin : MColor.xFF333333,
          fontWeight: selected ? MFont.medium14.fontWeight : MFont.regular14.fontWeight),
    );
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: borderRadius,
            border: Border.all(
              color: selected ? MColor.skin : Colors.white,
            ),
          ),
          child: ClipRRect(
            borderRadius: borderRadius,
            // child: Image.asset(
            //   Assets.aiModelDefaultImage,
            //   width: 80,
            //   height: 80,
            //   fit: BoxFit.cover,
            // ),
            child: CachedNetworkImage(
              width: size,
              height: size,
              imageUrl: model.model.pic!,
              fit: BoxFit.cover,
            ),
          ),
        ),
        SizedBox(height: 10),
        SizedBox(
          width: size,
          height: 30,
          child: Text(
            model.name,
            textAlign: TextAlign.center,
            maxLines: 2,
            style: TextStyle(
                fontSize: 14.0,
                height: 1.0,
                color: selected ? MColor.skin : MColor.xFF333333,
                fontWeight: selected ? MFont.medium14.fontWeight : MFont.regular14.fontWeight),
          ),
        ),
      ],
    );
  }
}
