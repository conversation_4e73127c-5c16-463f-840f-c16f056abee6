import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:getwidget/getwidget.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_default_config_controller.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_classify_info.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_template_info.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../product/ai_product_list_view.dart';

class AiDefaultConfigView extends StatefulWidget {
  const AiDefaultConfigView({super.key});

  @override
  State<AiDefaultConfigView> createState() => _AiDefaultConfigViewState();
}

class _AiDefaultConfigViewState extends State<AiDefaultConfigView> {
  late AiDefaultConfigController controller = Get.find<AiDefaultConfigController>();

  @override
  Widget build(BuildContext context) {
    return DefaultTextStyle(
      style: MFont.regular14.copyWith(color: MColor.black),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              AiCategoryController.to.categoryData.value.message ??
                  S.of(context).ai_default_config_des,
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 20),
            GFButton(
              color: MColor.aiMain.withOpacity(0.8),
              text: S.current.ai_default_config_select_template,
              onPressed: () => toTemplateSelectedPage(context),
            ),
          ],
        ),
      ),
    );
  }

  Future toTemplateSelectedPage(BuildContext context) async {
    await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AiDefaultTemplateView(),
        ));
    if (mounted) {
      setState(() {});
    }
  }
}


class AiFullConfigView extends StatefulWidget {
  const AiFullConfigView({super.key});

  @override
  State<AiFullConfigView> createState() => _AiFullConfigViewState();
}

class _AiFullConfigViewState extends State<AiFullConfigView> {
  late AiCategoryController controller = Get.find<
      AiCategoryController>();

  @override
  Widget build(BuildContext context) {
    return DefaultTextStyle(
      style: MFont.regular14.copyWith(color: MColor.black),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
                  S
                      .of(context)
                      .ai_product_full_tip,
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 20),
            GFButton(
              color: MColor.aiMain.withOpacity(0.8),
              text: S.current.ai_product_full,
              onPressed: () =>Get.to(ProductListView(id: AiCategoryController.to.orderId))?.then((value){
                controller.loadOrdersData();
            }),
            ),
          ],
        ),
      ),
    );
  }
}

///分类页面
class AiDefaultClassifyView extends StatefulWidget {
  const AiDefaultClassifyView({required this.onConfirm, super.key});

  final VoidCallback onConfirm;

  @override
  State<AiDefaultClassifyView> createState() => _AiDefaultClassifyViewState();
}

class _AiDefaultClassifyViewState extends State<AiDefaultClassifyView> {
  late AiDefaultConfigController controller = Get.find<AiDefaultConfigController>();

  @override
  void initState() {
    super.initState();
    if (!controller.classifyInfo.value.isEnd || controller.classifyInfo.value.data == null) {
      Future.microtask(controller.loadClassifyData);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.classifyInfo.value.isLoading) {
        return Center(child: CupertinoActivityIndicator());
      }
      return ConstrainedBox(
        constraints: BoxConstraints.tight(Size(Get.width, Get.height * 0.5)),
        child: Container(
          decoration: BoxDecoration(color: Colors.white),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Row(
                  children: [
                    SizedBox(width: 10),
                    Expanded(child: classifyContainer(0)),
                    Expanded(child: classifyContainer(1)),
                    Expanded(child: classifyContainer(2)),
                    Expanded(child: classifyContainer(3)),
                  ],
                ),
              ),
              Divider(height: 10, color: MColor.xFFE5E5E5),
              Row(
                children: [
                  Expanded(
                    child: AiSubmitButton.rest(
                      onPressed: () {
                        setState(() {
                          controller.clearClassSelected();
                        });
                      },
                    ),
                  ),
                  Expanded(
                    child: AiSubmitButton.confirm(
                      onPressed: () {
                        controller.confirmClassify();
                        widget.onConfirm.call();
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget classifyContainer(int index) {
    List<AiClassifyInfo> list = [];
    AiClassifyInfo? current = controller.classifyPathOnSelected.elementAtOrNull(index);
    if (index == 0) {
      list = controller.classifyInfo.value.data!;
    } else {
      final preview = controller.classifyPathOnSelected.elementAtOrNull(index - 1);
      list = preview?.child ?? [];
    }

    return AiClassifyListView(
      list: list,
      current: current,
      selected: (value) => selectedChange(value, index),
    );
  }

  void selectedChange(AiClassifyInfo info, int index) {
    setState(() {
      controller.pushClassifyPath(info, index);
    });
  }
}

class AiClassifyListView extends StatefulWidget {
  final List<AiClassifyInfo> list;
  final AiClassifyInfo? current;
  final ValueChanged<AiClassifyInfo> selected;

  const AiClassifyListView({required this.list, required this.selected, this.current, super.key});

  @override
  State<AiClassifyListView> createState() => _AiClassifyListViewState();
}

class _AiClassifyListViewState extends State<AiClassifyListView> {
  List<AiClassifyInfo> get list => widget.list;
  late AiClassifyInfo? tempInfo = widget.current;
  late AiDefaultConfigController controller = Get.find<AiDefaultConfigController>();

  @override
  void didUpdateWidget(covariant AiClassifyListView oldWidget) {
    tempInfo = widget.current;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: list.length,
      padding: EdgeInsets.zero,
      separatorBuilder: (context, index) {
        return Divider(height: 1, color: MColor.xFFE5E5E5);
      },
      itemBuilder: (context, index) {
        final info = list[index];
        return ColoredBox(
          color: MColor.white,
          child: GestureDetector(
            onTap: () {
              tempInfo = info;
              widget.selected.call(tempInfo!);
            },
            child: GFListTile(
              padding: EdgeInsets.symmetric(horizontal: 0, vertical: 8),
              margin: EdgeInsets.zero,
              title: Text(
                info.name,
                maxLines: 2,
                style: MFont.medium14
                    .copyWith(color: tempInfo?.id == info.id ? MColor.aiMain : MColor.black),
              ),
            ),
          ),
        );
      },
    );
  }
}

///模版页面
class AiDefaultTemplateView extends StatefulWidget {
  const AiDefaultTemplateView({super.key});

  @override
  State<AiDefaultTemplateView> createState() => _AiDefaultTemplateViewState();
}

class _AiDefaultTemplateViewState extends State<AiDefaultTemplateView> {
  late AiDefaultConfigController controller = Get.find<AiDefaultConfigController>();

  AiTemplateInfo? get templateOnSelected => controller.templateOnRequest;

  @override
  void initState() {
    super.initState();
    Future.microtask(controller.loadTemplateFirstListData);
  }

  @override
  void dispose() {
    controller.clearTemplatePageData();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final border = OutlineInputBorder(
      borderSide: BorderSide(color: MColor.xFFE5E5E5),
      borderRadius: BorderRadius.circular(20),
    );
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).ai_default_config_template_selection),
        centerTitle: true,
      ),
      body: KeyboardDismissOnTap(
        dismissOnCapturedTaps: true,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
              child: TextField(
                cursorColor: MColor.aiMain,
                style: MFont.regular14.copyWith(color: MColor.black),
                onChanged: controller.changeKeyword,
                decoration: InputDecoration(
                  fillColor: MColor.white,
                  filled: true,
                  contentPadding: EdgeInsets.zero,
                  constraints: BoxConstraints.tightFor(height: 37),
                  prefixIconConstraints: BoxConstraints.tightFor(width: 40),
                  prefixIcon: Image.asset(
                    'assets/images/ai_template_search_icon.png',
                    width: 15,
                    height: 15,
                  ),
                  hintStyle: MFont.regular14.copyWith(color: MColor.xFFC4C4C4),
                  hintText: S.of(context).ai_default_config_search_template,
                  border: border,
                  enabledBorder: border,
                  focusedBorder: border,
                ),
              ),
            ),
            AiClassifyFilter(),
            Expanded(child: Obx(() {
              if (controller.templateData.value.isLoading) {
                return Center(child: CupertinoActivityIndicator());
              }
              if (controller.templateData.value.data!.list.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.hourglass_empty,
                        size: 40,
                        color: context.isDarkMode ? DarkColor.xFF999999 : MColor.xFF999999,
                      ),
                      const SizedBox(height: 10),
                      Text(S.of(Get.context!).no_data,
                          style: MFont.regular15.apply(
                              color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)),
                    ],
                  ),
                );
              }
              return SmartRefresher(
                controller: controller.refreshController,
                enablePullDown: false,
                enablePullUp: true,
                onLoading: controller.loadMoreTemplateList,
                child: ListView.separated(
                  padding: EdgeInsets.only(bottom: 10),
                  keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                  itemCount: controller.templateData.value.data!.list.length,
                  separatorBuilder: (context, index) {
                    return Divider(height: 1, color: MColor.xFFE5E5E5);
                  },
                  itemBuilder: (context, index) {
                    final item = controller.templateData.value.data!.list[index];
                    return ColoredBox(
                      color: MColor.white,
                      child: GFCheckboxListTile(
                        padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                        margin: EdgeInsets.zero,
                        title: Row(
                          children: [
                            Expanded(
                              child: Text(
                                item.name,
                                style: MFont.medium18.copyWith(color: MColor.black),
                              ),
                            ),
                            GestureDetector(
                              onTap: () => showPreview(item.id),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 10),
                                child: Text(
                                  S.of(context).ai_default_config_preview,
                                  style: MFont.medium14.copyWith(color: MColor.aiMain),
                                ),
                              ),
                            ),
                          ],
                        ),
                        value: templateOnSelected?.id == item.id,
                        activeBgColor: MColor.aiMain,
                        size: 20,
                        type: GFCheckboxType.circle,
                        activeIcon: Icon(
                          Icons.check,
                          size: 15,
                          color: Colors.white,
                        ),
                        onChanged: (value) {
                          setState(() {
                            controller.confirmTemplate(item);
                          });
                        },
                      ),
                    );
                  },
                ),
              );
            })),
            Padding(
              padding: const EdgeInsets.only(bottom: 10),
              child: Row(
                children: [
                  Expanded(
                    child: AiSubmitButton(
                      name: S.of(context).ai_default_config_current_use_button,
                      margin: EdgeInsets.only(left: 20, right: 10),
                      onPressed: () {
                        if (!canNext()) {
                          return;
                        }
                        controller.useToCurrentModel().then(
                          (value) {
                            if (value.isGood) {
                              Navigator.popUntil(
                                context,
                                (route) => route.settings.name == AiRoutes.CATEGORY,
                              );
                            }
                          },
                        );
                      },
                    ),
                  ),
                  Expanded(
                    child: AiSubmitButton(
                      name: S.of(context).ai_default_config_more_use_button,
                      margin: EdgeInsets.only(right: 20, left: 10),
                      onPressed: () {
                        if (canNext()) {
                          toMoreModelPage();
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool canNext() {
    if (templateOnSelected == null) {
      EasyLoading.showError(
          '${S.current.ai_please_select}${S.current.ai_default_config_select_template}',
          duration: Duration(
            milliseconds: 500,
          ));
      return false;
    }
    return true;
  }

  void showPreview(int id) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return AiDefaultPreviewTemplateDetail(
          templateId: id,
        );
      },
    );
  }

  void toMoreModelPage() {
    Navigator.push(context, MaterialPageRoute(
      builder: (context) {
        return AiModelListPage();
      },
    ));
  }
}

///预览模版详情
class AiDefaultPreviewTemplateDetail extends StatefulWidget {
  final int templateId;

  const AiDefaultPreviewTemplateDetail({required this.templateId, super.key});

  @override
  State<AiDefaultPreviewTemplateDetail> createState() => _AiDefaultPreviewTemplateDetailState();
}

class _AiDefaultPreviewTemplateDetailState extends State<AiDefaultPreviewTemplateDetail>
    with TickerProviderStateMixin {
  late AiDefaultConfigController controller = Get.find<AiDefaultConfigController>();

  @override
  void initState() {
    super.initState();
    Future.microtask(() => controller.loadTemplateDetail(widget.templateId));
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final templateList = controller.templateDetailList.value.data ?? [];
      final tabController = TabController(length: templateList.length, vsync: this);
      return Container(
        color: MColor.white,
        height: MediaQuery.of(context).size.height * 0.6,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 37,
              decoration: BoxDecoration(
                color: MColor.white,
                borderRadius: BorderRadius.circular(5.0),
              ),
              child: TabBar.secondary(
                controller: tabController,
                isScrollable: true,
                tabAlignment: TabAlignment.start,
                padding: EdgeInsets.only(left: 15),
                labelPadding: EdgeInsets.only(right: 15),
                indicatorColor: MColor.aiMain,
                labelColor: MColor.aiMain,
                unselectedLabelColor: MColor.black,
                labelStyle: MFont.regular14,
                unselectedLabelStyle: MFont.regular14,
                splashFactory: NoSplash.splashFactory,
                indicatorPadding: EdgeInsets.zero,
                indicatorSize: TabBarIndicatorSize.label,
                dividerHeight: 0,
                tabs: [
                  for (AiSummaryDetailData o in templateList) Tab(text: o.details?.name ?? ''),
                ],
              ),
            ),
            SizedBox(height: 10),
            Expanded(
                child: TabBarView(controller: tabController, children: [
              for (AiSummaryDetailData o in templateList) buildTemplateView(o),
            ])),
            SizedBox(height: 10),
          ],
        ),
      );
    });
  }

  Widget buildTemplateView(AiSummaryDetailData detail) {
    final forms = detail.details?.config?.forms ?? [];
    Widget content;
    if (detail.type == 1) {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (int i = 0; i < forms.length; i++)
            Container(
                height: 38,
                color: MColor.white,
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(left: 10),
                child: Text(
                  '· ${forms[i].name ?? ''}',
                  style: MFont.medium14.copyWith(color: MColor.black),
                )),
        ],
      );
    } else {
      content = Table(
        border: TableBorder.all(color: MColor.xFFE5E5E5),
        columnWidths: const {
          0: FixedColumnWidth(70),
          1: FlexColumnWidth(),
        },
        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
        children: [
          TableRow(children: [
            _boldCell(S.current.ai_numerical),
            _boldCell(S.current.mine_recommend),
          ]),
          for (int i = 0; i < forms.length; i++)
            TableRow(children: [
              _boldCell((i + 1).toString()),
              _normalCell(forms[i].name ?? ''),
            ]),
        ],
      );
    }
    return SingleChildScrollView(child: content);
  }

  Widget _boldCell(String title) {
    return Container(
      height: 38,
      decoration: BoxDecoration(color: MColor.xFFFFF3F0),
      alignment: Alignment.center,
      child: Text(title,
          style: MFont.medium14.copyWith(
            color: MColor.black,
          )),
    );
  }

  Widget _normalCell(String title) {
    return Container(
      padding: EdgeInsets.only(left: 5),
      height: 38,
      color: MColor.white,
      alignment: Alignment.centerLeft,
      child: Text(title,
          style: MFont.regular14.copyWith(
            color: MColor.black,
          )),
    );
  }
}

///
class AiClassifyFilter extends StatefulWidget {
  const AiClassifyFilter({super.key});

  @override
  _AiClassifyFilterState createState() => _AiClassifyFilterState();
}

class _AiClassifyFilterState extends State<AiClassifyFilter> {
  late AiDefaultConfigController controller = Get.find<AiDefaultConfigController>();

  final LayerLink layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool show = false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: (didPop) {
        hideOverlay();
      },
      child: GestureDetector(
        onTap: _toggleOverlay,
        child: CompositedTransformTarget(
          link: layerLink,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Container(
              margin: const EdgeInsets.only(left: 20, bottom: 10),
              height: 36,
              padding: EdgeInsets.symmetric(horizontal: 10),
              decoration: BoxDecoration(
                color: MColor.xFFE5E5E5.withOpacity(0.5),
                borderRadius: BorderRadius.circular(5),
              ),
              // child: ,
              child: IntrinsicWidth(
                child: Center(
                  child: Obx(() {
                    return Row(
                      children: [
                        Text(
                          controller.classifyOnRequest.value.data?.name ??
                              S.of(context).ai_default_config_category_all,
                          style: MFont.medium14.copyWith(
                              color: controller.classifyOnRequest.value.data?.name != null
                                  ? MColor.aiMain
                                  : MColor.black),
                        ),
                        Icon(
                          Icons.keyboard_arrow_down_sharp,
                          color: controller.classifyOnRequest.value.data?.name != null
                              ? MColor.aiMain
                              : MColor.black,
                          size: 20,
                        )
                      ],
                    );
                  }),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _createOverlayEntry() {
    _overlayEntry = OverlayEntry(
        builder: (BuildContext context) => CompositedTransformFollower(
            targetAnchor: Alignment.topLeft,
            offset: Offset(0, 46),
            link: layerLink,
            child: Material(
              color: Colors.black.withOpacity(0.5),
              child: Align(
                alignment: Alignment.topLeft,
                child: AiDefaultClassifyView(
                  onConfirm: () {
                    setState(() {
                      hideOverlay();
                    });
                  },
                ),
              ),
            )));
  }

  void _toggleOverlay() {
    if (!show) {
      _showOverlay();
    } else {
      _hideOverlay();
    }
    show = !show;
  }

  void _showOverlay() {
    _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
  }

  void hideOverlay() {
    if (show) {
      _overlayEntry?.remove();
      _overlayEntry = null;
      show = false;
    }
  }
}

///
class AiModelListPage extends StatefulWidget {
  const AiModelListPage({super.key});

  @override
  State<AiModelListPage> createState() => _AiModelListPageState();
}

class _AiModelListPageState extends State<AiModelListPage> {
  late AiDefaultConfigController controller = Get.find<AiDefaultConfigController>();
  late List<AiCategoryNameModel> selectedModels = [AiCategoryController.to.currentModel.value];
  bool allSelected = false;

  @override
  void initState() {
    super.initState();
    Future.microtask(controller.loadModelList);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).ai_default_config_product_list),
        centerTitle: true,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
            child: Text(
              S.of(context).ai_default_config_use_warning,
              style: MFont.medium14,
            ),
          ),
          Expanded(
              child: ListView.separated(
            itemCount: controller.orderModelList.length + 1,
            separatorBuilder: (context, index) {
              return Divider(color: MColor.xFFE5E5E5, indent: 1, height: 0);
            },
            itemBuilder: (context, index) {
              if (index == 0) {
                return allTile();
              }
              final item = controller.orderModelList[index - 1];
              return GFCheckboxListTile(
                titleText: item.name,
                padding: EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                margin: EdgeInsets.only(left: 15, right: 10),
                avatar: avatar(item.model.id!),
                activeBgColor: MColor.aiMain,
                size: 20,
                type: GFCheckboxType.circle,
                activeIcon: Icon(
                  Icons.check,
                  size: 15,
                  color: Colors.white,
                ),
                value: selectedModels.contains(item),
                onChanged: (value) => tapModel(item),
              );
            },
          )),
          AiSubmitButton.use(
            onPressed: toLoadProgressPage,
          ),
        ],
      ),
    );
  }

  Widget allTile() {
    return GFCheckboxListTile(
      titleText: S.of(context).general_all,
      padding: EdgeInsets.fromLTRB(20, 8, 0, 8),
      margin: EdgeInsets.only(left: 15, right: 10),
      activeBgColor: MColor.aiMain,
      size: 20,
      type: GFCheckboxType.circle,
      activeIcon: Icon(
        Icons.check,
        size: 15,
        color: Colors.white,
      ),
      value: allSelected,
      onChanged: (value) => toggleAllSelected(),
    );
  }

// 31583 31870 31900 31910
  Widget avatar(int id) {
    return Obx(() {
      if (controller.modelListEntity.value.data == null) {
        return CupertinoActivityIndicator();
      }
      final model = controller.modelListEntity.value.data?.firstWhereOrNull((e) => e.modelId == id);
      Color? color;
      String? text;

      if (model != null) {
        text = model.fromDefault
            ? S.current.ai_default_config_tag_default
            : S.current.ai_default_config_tag_manual;
        color = model.fromDefault ? Colors.amberAccent.shade100 : MColor.aiMain;
      }
      return Container(
          width: 20,
          height: 20,
          decoration: text != null
              ? BoxDecoration(color: color, borderRadius: BorderRadius.circular(4))
              : null,
          child: text != null
              ? Center(
                  child: Text(
                    text,
                    style: MFont.medium12.copyWith(color: MColor.white),
                  ),
                )
              : null);
    });
  }

  void tapModel(AiCategoryNameModel model) {
    setState(() {
      if (selectedModels.contains(model)) {
        selectedModels.remove(model);
      } else {
        selectedModels.add(model);
      }
      allSelected = selectedModels.length == controller.orderModelList.length;
    });
  }

  void toggleAllSelected() {
    setState(() {
      allSelected = !allSelected;
      selectedModels.clear();
      if (allSelected) {
        selectedModels.addAll(controller.orderModelList);
      }
    });
  }

  void toLoadProgressPage() {
    if (selectedModels.isEmpty) {
      EasyLoading.showError('${S.current.ai_please_select}${S.current.ai_add_product_model_title}',
          duration: Duration(milliseconds: 500));
      return;
    }
    Navigator.push(context, MaterialPageRoute(
      builder: (context) {
        return AiUseTemplateProgressView(selectedModels: selectedModels);
      },
    ));
  }
}

class AiUseTemplateProgressView extends StatefulWidget {
  final List<AiCategoryNameModel> selectedModels;

  const AiUseTemplateProgressView({required this.selectedModels, super.key});

  @override
  State<AiUseTemplateProgressView> createState() => _AiUseTemplateProgressViewState();
}

class _AiUseTemplateProgressViewState extends State<AiUseTemplateProgressView> {
  late final loadList = widget.selectedModels.map((e) => AiBaseEntity(data: e).obs).toList();
  late AiDefaultConfigController controller = Get.find<AiDefaultConfigController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).ai_default_config_load_progress),
        centerTitle: true,
      ),
      body: Column(
        children: [
          SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.only(left: 10),
            child: buildProgressBar(),
          ),
          SizedBox(height: 10),
          Expanded(
              child: ListView(
            children: [for (var o in loadList) _AiUseTemplateToModelTile(loadInfo: o)],
          )),
          AiSubmitButton(
              onPressed: () {
                Navigator.popUntil(
                  context,
                  (route) => route.settings.name == AiRoutes.CATEGORY,
                );
              },
              name: S.of(context).done)
        ],
      ),
    );
  }

  Widget buildProgressBar() {
    return Obx(() {
      int good = 0;
      int bad = 0;
      for (var o in loadList) {
        if (o.value.isGood) {
          good += 1;
        } else if (o.value.isEnd && o.value.isBad) {
          bad += 1;
        }
      }
      return Row(
        children: [
          Text(S.current.ai_default_config_template_progress('$good/${loadList.length}')),
          if (bad != 0 && (bad + good) == loadList.length)
            GestureDetector(
              onTap: useTemplateOnFail,
              child: Text(
                S.current.ai_default_config_template_fail_count(bad.toString()),
                style: MFont.medium14.copyWith(color: MColor.failed),
              ),
            )
        ],
      );
    });
  }

  void useTemplateOnFail() {
    for (final model in loadList) {
      if (model.value.isGood) continue;
      model.value.code = null;
      model.refresh();
      controller.useTemplate(model.value.data!).then(
        (value) {
          model.value.code = value.code;
          if (mounted) {
            model.refresh();
          }
        },
      );
    }
  }
}

class _AiUseTemplateToModelTile extends StatefulWidget {
  const _AiUseTemplateToModelTile({required this.loadInfo, super.key});

  final Rx<AiBaseEntity<AiCategoryNameModel>> loadInfo;

  @override
  State<_AiUseTemplateToModelTile> createState() => _AiUseTemplateToModelTileState();
}

class _AiUseTemplateToModelTileState extends State<_AiUseTemplateToModelTile> {
  AiCategoryNameModel get model => widget.loadInfo.value.data!;
  late AiDefaultConfigController controller = Get.find<AiDefaultConfigController>();

  @override
  void initState() {
    super.initState();
    Future.microtask(useTemplate);
  }

  void useTemplate() {
    controller.useTemplate(model).then(
      (value) {
        widget.loadInfo.value.code = value.code;
        if (mounted) {
          widget.loadInfo.refresh();
        }
      },
    );
  }

  void reuseIfNeed() {
    if (widget.loadInfo.value.isBad) {
      setState(() {
        widget.loadInfo.value.code = null;
      });
      useTemplate();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      decoration: BoxDecoration(
          color: MColor.white, border: Border(bottom: BorderSide(color: MColor.xFFE5E5E5))),
      child: Row(
        children: [
          Expanded(child: Text(model.name, style: MFont.medium14)),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(S.of(context).ai_default_config_load, style: MFont.medium14),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Text(controller.templateOnRequest!.name, style: MFont.medium14),
          ),
          SizedBox(
            width: 50,
            child: Obx(() {
              Widget content = Text(
                widget.loadInfo.value.isGood
                    ? S.of(context).ai_default_config_success
                    : S.of(context).ai_default_config_fail,
                style: MFont.medium14
                    .copyWith(color: widget.loadInfo.value.isGood ? MColor.pass : MColor.failed),
              );
              if (widget.loadInfo.value.isBad) {
                content = Row(mainAxisSize: MainAxisSize.min, children: [
                  content,
                  SizedBox(width: 4),
                  Icon(
                    Icons.refresh,
                    size: 16,
                  )
                ]);
              }
              content = GestureDetector(
                onTap: reuseIfNeed,
                child: content,
              );
              return Visibility(
                visible: widget.loadInfo.value.isEnd,
                replacement: CupertinoActivityIndicator(),
                child: content,
              );
            }),
          )
        ],
      ),
    );
  }
}
