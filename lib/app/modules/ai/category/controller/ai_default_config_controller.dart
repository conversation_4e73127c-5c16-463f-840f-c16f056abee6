import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_classify_info.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_default_model_simple.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_template_info.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class AiDefaultConfigController extends GetxController {
  static const int _initPage = 1;

  final classifyOnRequest = AiBaseEntity<AiClassifyInfo>().obs;
  AiTemplateInfo? templateOnRequest;

  late int _page = _initPage;
  final int _rows = 20;
  String? keyword;

  final templateData = AiBaseEntity<AiTemplateInfoData>().obs;
  final classifyInfo = AiBaseEntity<List<AiClassifyInfo>>().obs;
  final templateDetailList = AiBaseEntity<List<AiSummaryDetailData>>().obs;
  final modelListEntity = AiBaseEntity<List<AiDefaultModelSimple>>().obs;
  late final orderModelList = AiCategoryController.to.modelList;

  late RefreshController refreshController = RefreshController();

  late List classifyPathOnSelected = <AiClassifyInfo>[];

  void loadClassifyData() {
    AiProvider().getClassify().then(
      (value) {
        if (value.isGood) {
          classifyInfo.value = value;
        }
      },
    );
  }

  void loadTemplateFirstListData({bool hasLoading = true}) {
    _page = _initPage;
    refreshController.loadComplete();
    templateOnRequest = null;
    AiProvider()
        .getTemplate(
            page: _page.toString(),
            rows: _rows.toString(),
            classifyId: classifyOnRequest.value.data?.id,
            keyword: keyword,
            hasLoading: hasLoading)
        .then(
      (value) {
        templateData.value = value;
        if (value.isGood && (value.data?.list.isNotEmpty ?? false)) {
          _page = _initPage + 1; //减少概率防止异步可能多加，所以不用_page++;
        }
      },
    );
  }

  void loadMoreTemplateList() {
    if (templateData.value.data!.list.length >= templateData.value.data!.total) {
      refreshController.loadNoData();
      return;
    }
    AiProvider()
        .getTemplate(
            page: _page.toString(),
            rows: _rows.toString(),
            classifyId: classifyOnRequest.value.data?.id,
            keyword: keyword,
            hasLoading: false)
        .then(
      (value) {
        refreshController.loadComplete();
        if (value.isGood && (value.data?.list.isNotEmpty ?? false)) {
          _page++;
          templateData.value.data!.list.addAll(value.data?.list ?? []);
          templateData.refresh();
        }
      },
    );
  }

  void loadModelList() {
    AiProvider().getModelListInTemplate(AiCategoryController.to.orderId).then(
      (value) {
        if (value.isGood) {
          modelListEntity.value = value;
        }
      },
    );
  }

  void changeKeyword(String? value) {
    keyword = value;
    templateOnRequest = null;
    loadTemplateFirstListData();
  }

  void confirmTemplate(AiTemplateInfo template) {
    templateOnRequest = template;
  }

  void loadTemplateDetail(int id) {
    AiProvider().getTemplateDetail(id).then(
      (value) {
        if (value.isGood) {
          templateDetailList.value = value;
        }
      },
    );
  }

  void clearTemplatePageData() {
    _page = _initPage;
    keyword = '';
    refreshController.loadComplete();
    templateData.value = AiBaseEntity<AiTemplateInfoData>();
  }

  void confirmClassify() {
    classifyOnRequest.value.data = classifyPathOnSelected.lastOrNull;
    classifyOnRequest.refresh();
    templateOnRequest = null;
    loadTemplateFirstListData();
  }

  void clearClassSelected() {
    classifyPathOnSelected.clear();
    classifyOnRequest.value.data = null;
    classifyOnRequest.refresh();
    loadTemplateFirstListData(hasLoading: false);
  }

  void pushClassifyPath(AiClassifyInfo info, int index) {
    final a = classifyPathOnSelected.elementAtOrNull(index);
    classifyPathOnSelected.removeRange(index, classifyPathOnSelected.length);
    if (a != info) {
      classifyPathOnSelected.add(info);
    }
  }

  Future<AiBaseEntity> useTemplate(AiCategoryNameModel item) {
    if (item.model.id == AiCategoryController.to.modelId) {
      return useToCurrentModel(hasLoading: false);
    }
    return AiProvider().useTemplate(
        orderId: item.model.orderId.toString(),
        productId: item.model.orderProductId.toString(),
        modelId: item.model.id.toString(),
        productName: item.productName,
        ruleId: templateOnRequest!.id.toString(),
        orderNum: AiCategoryController.to.orderNum.toString(),
        hasLoading: false);
  }

  Future<AiBaseEntity> useToCurrentModel({bool hasLoading = true}) {
    assert(templateOnRequest != null);
    return AiCategoryController.to.useTemplate(templateOnRequest!.id,hasLoading: hasLoading);
  }
}
