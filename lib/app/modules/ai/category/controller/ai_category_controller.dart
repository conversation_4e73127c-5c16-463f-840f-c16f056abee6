import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_category_info.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../data/public_model.dart';
import '../../../order/order_provider.dart';
import '../../entiy/ai_envent_refresh.dart';

class AiCategoryController extends GetxController {
  static AiCategoryController get to => Get.find();

  int get modelId => currentModel.value.model.id ?? 0;
  int get productId => currentModel.value.model.orderProductId ?? 0;
  int  orderId = 0;

  final currentModel = AiCategoryNameModel('', ProductModel(), '').obs;
  late OrderDetailEntity order;

  int get orderNum => currentModel.value.model.amount ?? 0;

  final expanded = true.obs;
  final categoryData = AiBaseEntity<List<AiCategoryInfo>>(data: []).obs;
  List<AiCategoryInfo> get categoryList => categoryData.value.data ?? [];
  final modelList = <AiCategoryNameModel>[].obs;
  // https://www.baidu.com/img/flexible/logo/pc/<EMAIL>

  AiCategoryType nextType({required int fromType}) {
    final category = categoryList.firstWhereOrNull((element) => element.type == fromType);
    assert(category != null);
    int index = categoryList.indexOf(category!);
    assert(index > -1);
    final nextCategory = categoryList.elementAtOrNull(index + 1);

    final result = nextCategory?.categoryType;
    assert(result != null);
    return result!;
  }

  @override
  void onInit() {
    Map map = Get.arguments;
    final o = map['data'];
    if (o is OrderDetailEntity) {
      order = o;
    }
    for (OrderProduct p in order.orderProducts ?? []) {
      for (ProductModel m in p.productModel ?? []) {
        modelList.add(AiCategoryNameModel('${p.productName}-${m.mark}', m, p.productName ?? ''));
      }
    }
    try {
      currentModel.value = modelList.first;
    } catch (e) {
      // print(e);
    }
    orderId = int.parse(map['orderId']);
    if(currentModel.value!=null){
      getCategoryResult();
      loadOrdersData();
      loadQulity();
    }else{
    }

    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
  }

  var qulityNum = 0.obs;
  void loadQulity(){
    AiProvider()
        .getAiCategoryDetail(
        orderId: orderId,
        productId: productId,
        modelId: modelId,
        type: AiCategoryType.quality.id,
        orderNum: orderNum)
        .then((value) {
      if (value.code ==200 &&value.data != null ) {
        qulityNum.value =value.data!.details!.config!.forms!.length;
      }else{
        // showToast(value.message!);
      }
    });
  }

  OrderProvider orderProvider = OrderProvider();
  final errorInfo = false.obs;
  Future<void> loadOrdersData() async{
    errorInfo.value = false;
    BaseModel<OrderDetailEntity> result =  await orderProvider.takeOrderDetail(orderId);
    if(result.isSuccess && result.data != null){


        if(currentModel.value.model.mark!.isEmpty || currentModel.value.model.amount==0 || currentModel.value.model.unit!.isEmpty){
          errorInfo.value = true;
      }else{
          errorInfo.value = false;
        }

    // : controller.modelList[index].model.id==controller.currentModel.value.model.id

      // for(OrderProduct item in result.data!.orderProducts!){
      //   if(item.productName!.isEmpty ){
      //     errorInfo.value = true;
      //     break;
      //   }
      //   bool fail = false;
      //   if(item.productModel!.isEmpty){
      //     errorInfo.value = true;
      //     break;
      //   }else{
      //     for(var product in item!.productModel!){
      //       if(product.mark!.isEmpty || product.amount==0 || product.unit!.isEmpty){
      //         fail = true;
      //         errorInfo.value = true;
      //         break;
      //       }
      //     }
      //   }
      //
      //   if(fail){
      //     break;
      //   }
      // }
      update();
    }else{
      showToast(result.message!);
    }
  }


  Future<void> reload() async{
    BaseModel<OrderDetailEntity> result =  await orderProvider.takeOrderDetail(orderId);
    if(result.isSuccess && result.data != null){
      modelList.clear();
      for(var p in result.data!.orderProducts!){
        for (ProductModel m in p.productModel ?? []) {
          modelList.add(AiCategoryNameModel('${p.productName}-${m.mark}', m, p.productName ?? ''));
        }
      }
      try {
        currentModel.value = modelList.first;
      } catch (e) {
        // print(e);
      }
      update();
    }else{
    }
  }

  Future<void> changeModel({required AiCategoryNameModel model, AiDataFeature? loader}) async {
    if (model == currentModel.value) return;
    currentModel.value = model;
    orderId =    currentModel.value.model.orderId!;
    await loader?.uploadData();
    getCategoryResult(hasMask: false);
    loader?.loadData();
  }

  void getCategoryResult({bool hasMask = true}) {
    AiProvider()
        .getAiCategoryResult(
            orderId: orderId, productId: productId, modelId: modelId, hasMask: hasMask)
        .then(
      (value) {
        categoryData.value = value;
      },
    );
  }

  Future<AiBaseEntity> useTemplate(int id, {bool hasLoading = true}) {
    return AiProvider()
        .useTemplate(
            orderId: orderId.toString(),
            productId: productId.toString(),
            modelId: modelId.toString(),
            productName: currentModel.value.productName,
            ruleId: id.toString(),
            orderNum: orderNum.toString(),
            hasLoading: hasLoading)
        .then(
      (value) {
        if (value.isGood) {
          getCategoryResult();
        }
        return value;
      },
    );
  }

  Future<void> toNext(AiCategoryInfo info) async {
    await Get.toNamed(info.categoryType.location, parameters: {
      'orderId': orderId.toString(),
      'productId': productId.toString(),
      'modelId': modelId.toString(),
      'type': info.type.toString(),
    })?.then((value){
      if(value!=null){
        getCategoryResult();
        if(value!= null && value!=-1 &&value['page']!=null){
          EasyLoading.show();
          Future.delayed(Duration(milliseconds: 400), () {
            EasyLoading.dismiss();
            Get.toNamed(value['page'],parameters: value['param']);
          });
        }
      }
      // loadQulity();
    });

    getCategoryResult(hasMask: false);
  }

  Future<void> toNext2(AiCategoryInfo info) async {
    await Get.toNamed(info.categoryType.location, parameters: {
      'orderId': orderId.toString(),
      'productId': productId.toString(),
      'modelId': modelId.toString(),
      'type': info.type.toString(),
    })?.then((value){
      getCategoryResult();
      if(value!=null){
        if(value.p){

        }
      }
      // loadQulity();
    });
    getCategoryResult(hasMask: false);
  }

}

class AiCategoryNameModel {
  String name;
  ProductModel model;
  String productName;
  AiCategoryNameModel(this.name, this.model, this.productName);
}
