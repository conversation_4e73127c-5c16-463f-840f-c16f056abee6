import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/modules/auth/account_service.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/storage_util.dart';

typedef DataParseFun<T> = T Function(dynamic json);

class AiBaseEntity<T> {
  int? code;
  String? message;
  T? data;

  bool get isEnd => code != null;
  bool get isLoading => !isEnd;
  bool get isGood => code == 200 || code == 20000;
  bool get isBad => !isGood;
  bool get templateEmpty => isEnd && code == 2222;

  AiBaseEntity({this.code, this.message, this.data});

  AiBaseEntity.fromJson(Response response, {DataParseFun<T>? parseFun}) {
    if (response.statusCode != 200 && response.statusCode != 20000) {
      code = response.statusCode;
      message = response.statusMessage;
      return;
    }
    try {
      Map<String, dynamic> commonData = response.data!;
      code = commonData['code'];
      message = commonData['msg'];
      final temp = commonData['data'];
      if (temp != null && parseFun != null) {
        data = parseFun(temp);
      }

      if (code == 10005) {
        int? userId = GlobalConst.tempModel?.uid;
        if (userId != null) {
          AccountService.instance.removeSavedAccount(userId);
        }
        GlobalConst.tempModel = null;
        StorageUtil.remove(Constant.kUser);
        if (Get.currentRoute == Routes.AUTH_LOGIN) {
          return;
        }
        Get.offAllNamed(Routes.AUTH_LOGIN);
        return;
      }
    } catch (e) {
      assert(() {
        print('数据解析出错 ********************** \n\n');
        print('$e\n\n');
        print('数据解析出错 ********************** ');

        return true;
      }());
      // code ??= -888;
      // message = S.of(Get.context!).data_parsing_exception;
    }
  }
}
