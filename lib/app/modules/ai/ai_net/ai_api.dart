abstract class AiApi {
  static const categoryResult = '/api/rule/lists'; //8大项结果列表
  static const categoryDetail = '/api/rule/detail'; //8大项配置详情
  static const categoryUpload = '/api/rule/storeItem'; //8大项配置项保存
  static const sampleAql = '/api/rule/getSampleAql'; //计算抽样aql样本数量和ac数量
  static const imageConfig = '/api/file/getOssConfig'; //前端OSS直接上传获取配置信息
  static const itemSearch = '/api/rule/itemSearch'; //推荐

  static const classify = '/v2/category'; //分类接口 非Ai host
  static const templateList = '/api/rule/index'; //模板列表
  static const templateDetail = '/api/rule/templateDetail'; //模板详情
  static const templateStore = '/api/rule/orderSelectTemplate'; //模板应用保存
  static const productListInTemplate =
      '/api/rule/getOrderRuleLists'; //订单所有模板列表-状态|后台配置
  static const reportRuleLists = '/api/rule/reportLists'; //报告的概要列表
}
