class AiUploadAssetsConfig {
  AiUploadAssetsConfig({
    required this.accessid,
    required this.host,
    required this.policy,
    required this.signature,
    required this.expire,
    required this.callback,
    required this.cdnUrl,
    this.dir,
  });

  AiUploadAssetsConfig.fromJson(dynamic json) {
    accessid = json['accessid'];
    host = json['host'];
    policy = json['policy'];
    signature = json['signature'];
    expire = json['expire'];
    callback = json['callback'];
    dir = json['dir'];
    cdnUrl = json['cdn_url'];
  }
  late String accessid;
  late String host;
  late String policy;
  late String signature;
  late int expire;
  late String callback;
  String? dir;
  late String cdnUrl;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['accessid'] = accessid;
    map['host'] = host;
    map['policy'] = policy;
    map['signature'] = signature;
    map['expire'] = expire;
    map['callback'] = callback;
    map['dir'] = dir;
    map['cdn_url'] = cdnUrl;
    return map;
  }
}
