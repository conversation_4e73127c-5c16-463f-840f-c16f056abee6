import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_api.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_uplaod_assets_config.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_category_info.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_classify_info.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_default_model_simple.dart';
import 'package:inspector/app/modules/ai/category/entity/ai_template_info.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/photo_selected/photo_picker/AiAssetPickerBuilderDelegate.dart';
import 'package:inspector/app/modules/ai/photo_selected/upload/image_service.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../category/entity/ai_category_simple.dart';
import '../category/entity/ai_recmend_data.dart';

enum AiHttpMethod {
  get,
  post,
  patch,
  put,
  delete;

  String get name => toString().split('.').last.toUpperCase();
}

class AiServer {
  static String get domain {
    if (Server.env) {
      return 'https://inspctor-ai-api.aizhiru.com';
    } else {
      return 'https://inspctor-ai-api.aizhiru.com';
      return 'http://inspctor-ai-api.test.aizhiru.com';
    }
  }
}

class AiProvider {
  factory AiProvider() => _instance ??= AiProvider._();
  static AiProvider? _instance;
  late final Dio _dio = Dio();

  Dio? _uploadDio;
  AiUploadAssetsConfig? _uploadConfig;

  AiProvider._() {
    _config();
    _dio.interceptors.add(AppCommonHeaderInterceptor());
    _addLoggerForDio(_dio);
  }

  void _addLoggerForDio(Dio dio) {
    if (!Server.env) {
      dio.interceptors.add(InterceptorsWrapper(
        onRequest: (options, handler) {
          return handler.next(options);
        },
        onResponse: (response, handler) {
          log('------------------------- \n 请求地址: ${response.requestOptions.uri} \n 请求参数：${response.requestOptions.data.toString()} \n 响应结果：${response.data?.toString()} \n-------------------------');
          return handler.next(response);
        },
        onError: (e, handler) {
          log(e.toString());
          return handler.next(e);
        },
      ));
      /*dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (message) {
          if (message is String) {
            dynamic j;
            try {
              j = json.decode(message);
              if (j != null) {
                Logger(
                  printer: PrettyPrinter(methodCount: 0),
                ).i(j);
              } else {}
            } catch (e) {
              Logger(
                printer: PrettyPrinter(methodCount: 0),
              ).d(message);
            }
          }
        },
      ));*/
    }
  }

  void _config() {
    _dio.options.baseUrl = AiServer.domain;
    _dio.options.contentType = ContentType.json;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.responseType = ResponseType.json;
  }

  Future<AiBaseEntity<T>> request<T>({
    required String path,
    required AiHttpMethod method,
    data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    DataParseFun<T>? parseFun,
    String? baseUrl,
    bool hasLoading = true,
  }) async {
    if (hasLoading) {
      EasyLoading.show();
    }
    if (baseUrl?.isNotEmpty ?? false) {
      _dio.options.baseUrl = baseUrl!;
    } else {
      _dio.options.baseUrl = AiServer.domain;
    }

    //简单粗暴方式处理校验证书方法
    (_dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
        (client) {
      client.badCertificateCallback =
          (X509Certificate cert, String host, int port) {
        return true;
      };
    };

    final response = await _dio
        .request(path,
            options: Options(method: method.name),
            data: data,
            queryParameters: queryParameters,
            cancelToken: cancelToken,
            onSendProgress: onSendProgress,
            onReceiveProgress: onReceiveProgress)
        .catchError((e) {
      print('request === catchError $e');
    });
    if (hasLoading) {
      EasyLoading.dismiss();
    }
    return AiBaseEntity.fromJson(response, parseFun: parseFun);
  }

  dynamic parsePath(Map<String, dynamic>? source, String? parseWay) {
    if ((parseWay == null && parseWay!.isEmpty) || source == null)
      return source;
    if (!parseWay.contains('.')) return source[parseWay];
    final prefix = parseWay.split('.').first;
    final data = source[prefix];
    return parsePath(data, parseWay.replaceFirst('$prefix.', ''));
  }
}

extension AiProviderApiServer on AiProvider {
  Future<AiBaseEntity<List<AiCategoryInfo>>> getAiCategoryResult({
    required int orderId,
    required int productId,
    required int modelId,
    bool hasMask = true,
  }) async {
    final data = await request<List<AiCategoryInfo>>(
        path: AiApi.categoryResult,
        hasLoading: hasMask,
        method: AiHttpMethod.post,
        parseFun: (json) {
          final data = parsePath(json, 'lists') as List;
          List<AiCategoryInfo> result =
              data.map((e) => AiCategoryInfo.fromJson(e)).toList();
          return result;
        },
        data: {
          'order_id': orderId,
          'product_id': productId,
          'model_id': modelId,
        });
    return data;
  }

  Future<AiBaseEntity<AiSummaryDetailData>> getAiCategoryDetail({
    required int orderId,
    required int productId,
    required int modelId,
    required int type,
    required int orderNum,
  }) async {
    final data = await request<AiSummaryDetailData>(
      path: AiApi.categoryDetail,
      method: AiHttpMethod.post,
      parseFun: (json) {
        return AiSummaryDetailData.fromJson(json);
      },
      data: {
        'order_id': orderId,
        'product_id': productId,
        'model_id': modelId,
        'type': type,
        'order_num': orderNum
      },
    );

    return data;
  }

  Future<AiBaseEntity<List<AiCategoryInfo>>> getReportRuleLists({
    required int orderId,
    required int productId,
  }) async {
    final data = await request<List<AiCategoryInfo>>(
        path: AiApi.reportRuleLists,
        hasLoading: false,
        method: AiHttpMethod.post,
        parseFun: (json) {
          final data = parsePath(json, 'lists') as List;
          List<AiCategoryInfo> result =
              data.map((e) => AiCategoryInfo.fromJson(e)).toList();
          return result;
        },
        data: {
          'order_id': orderId,
          'product_id': productId,
        });
    return data;
  }

  Future<AiBaseEntity> uploadConfigNoSee(
      {required id, required AiCategoryDetails details}) async {
    final result = await request<AiSummaryDetailData>(
      path: AiApi.categoryUpload,
      method: AiHttpMethod.post,
      data: {
        'rule_order_item_id': id,
        'details': jsonEncode(details.toJson2()),
      },
    );
    return result;
  }

  Future<AiBaseEntity> uploadConfig(
      {required id, required AiCategoryDetails details}) async {
    final result = await request<AiSummaryDetailData>(
      path: AiApi.categoryUpload,
      method: AiHttpMethod.post,
      data: {
        'rule_order_item_id': id,
        'details': jsonEncode(details.toJson()),
      },
    );
    return result;
  }

  Future<AiBaseEntity<List<AiRecmendData>>> itemSearch(
      String keyword, String category_id, String type) async {
    final result = await request<List<AiRecmendData>>(
      path: AiApi.itemSearch,
      method: AiHttpMethod.post,
      parseFun: (json) {
        final data = parsePath(json, 'list') as List;
        List<AiRecmendData> result =
            data.map((e) => AiRecmendData.fromJson(e)).toList();
        return result;
      },
      data: {
        'keyword': keyword,
        'category_id': category_id,
        'type': type,
      },
    );
    return result;
  }

  Future<AiBaseEntity<String>> getSampleNum(
      {required String level, String? orderNum}) async {
    final result = await request<String>(
      path: AiApi.sampleAql,
      method: AiHttpMethod.post,
      parseFun: (json) {
        return json['sample_num'].toString();
      },
      data: {
        'grade': level,
        'num': orderNum,
        'aql': 0,
      },
    );
    return result;
  }

  Future<AiBaseEntity<int>> getAqlNum(
      {required String level,
      required String orderNum,
      required String aql}) async {
    final result = await request<int>(
      path: AiApi.sampleAql,
      method: AiHttpMethod.post,
      parseFun: (json) {
        return (json['aql_ac_num'] as int?) ?? 0;
      },
      data: {
        'grade': level,
        'num': orderNum,
        'aql': aql,
      },
    );
    return result;
  }

  Future<AiBaseEntity<AiCategorySimple>> getAqlNum2(
      {required String level,
      required String orderNum,
      required String aql}) async {
    final result = await request<AiCategorySimple>(
      path: AiApi.sampleAql,
      method: AiHttpMethod.post,
      parseFun: (json) {
        return AiCategorySimple.fromJson(json);
        // return (json['sample_num'] as int?) ?? 0;
      },
      data: {
        'grade': level,
        'num': orderNum,
        'aql': aql,
      },
    );
    return result;
  }

  Future<AiBaseEntity<AiUploadAssetsConfig>> getUploadConfig() async {
    final result = await request<AiUploadAssetsConfig>(
      path: AiApi.imageConfig,
      method: AiHttpMethod.post,
      parseFun: (json) {
        return AiUploadAssetsConfig.fromJson(json);
      },
    );
    return result;
  }
}

extension AiUpload on AiProvider {
  Future<List<String?>> uploadAssetList(List<AssetEntity> assets) async {
    List<String?> result = [];
    // EasyLoading.show(status: '上传图片···');
    for (var o in assets) {
      final url = await uploadAsset(o);
      result.add(url);
    }

    // EasyLoading.dismiss();
    return result;
  }

  Future<String?> uploadAsset(
    AssetEntity asset, {
    ProgressCallback? onSendProgress,
  }) async {
    final int now = DateTime.now().millisecondsSinceEpoch;
    final allow = 15; //图多时，保险延迟几秒
    if (_uploadConfig == null ||
        (_uploadConfig!.expire - allow) * 1000 <= now) {
      _uploadConfig = (await AiProvider().getUploadConfig()).data;
    }
    if (_uploadConfig == null) return null;
    final config = _uploadConfig!;
    final fileName = '${config.dir}$now${asset.title}';

    _uploadDio ??= Dio(BaseOptions(
        baseUrl: config.host,
        sendTimeout: Duration(seconds: 30),
        headers: {
          HttpHeaders.contentTypeHeader: 'multipart/form-data',
        }));
    // {Status: Ok}
    final imageUrl = '${config.cdnUrl}/$fileName';
    try {
      final response = await _uploadDio!.post(
        '',
        data: FormData.fromMap({
          'policy': config.policy,
          'OSSAccessKeyId': config.accessid,
          'success_action_status': '200',
          'signature': config.signature,
          'callback': config.callback,
          'key': fileName,
          'file': await multipartFileFromAssetEntity(asset),
        }),
        onSendProgress: onSendProgress,
      );
      print('StatusStatusStatus===' + response.data!['Status'].toString());
      final end = response.data?['Status'] == 'Ok';
      print('upload image ==== $imageUrl' +
          ' && createtime=' +
          asset.createDateSecond.toString());
      if (end) return imageUrl;
      return null;
    } catch (e) {
      print('catch====>' + e.toString());
      return null;
    }
  }

  Future<String?> uploadFile(
    String fileName2,
    File file, {
    ProgressCallback? onSendProgress,
  }) async {
    final int now = DateTime.now().millisecondsSinceEpoch;
    final allow = 15; //图多时，保险延迟几秒
    _uploadConfig = (await AiProvider().getUploadConfig()).data;
    final config = _uploadConfig!;
    final fileName = '${config.dir}$now${fileName2}';
    _uploadDio ??= Dio(BaseOptions(
        baseUrl: config.host,
        sendTimeout: Duration(seconds: 30),
        headers: {
          HttpHeaders.contentTypeHeader: 'multipart/form-data',
        }));
    // {Status: Ok}
    final imageUrl = '${config.cdnUrl}/$fileName';
    try {
      final response = await _uploadDio!.post(
        '',
        data: FormData.fromMap({
          'policy': config.policy,
          'OSSAccessKeyId': config.accessid,
          'success_action_status': '200',
          'signature': config.signature,
          'callback': config.callback,
          'key': fileName,
          'file': file,
        }),
        onSendProgress: onSendProgress,
      );
      final end = response.data?['Status'] == 'Ok';
      print('upload image ==== $imageUrl');
      if (end) return imageUrl;
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<MultipartFile> multipartFileFromAssetEntity(AssetEntity entity) async {
    MultipartFile mf;
    // Using the file path.
    if (entity is AiAssetEntity) {
      // Using the bytes.
      final startBytes = entity.fileData?.data;
      if (startBytes == null) {
        throw StateError('Unable to obtain bytes of the entity ${entity.id}.');
      }
      final bytes =
          await ImageService.compressWithList(startBytes, entity.title ?? '');
      mf = MultipartFile.fromBytes(bytes);
    } else {
      final file = await entity.file;
      if (file == null) {
        throw StateError('Unable to obtain file of the entity ${entity.id}.');
      }
      final compressFile = await ImageService.compressAndGetFile(file);
      mf = await MultipartFile.fromFile(compressFile.path);
    }
    return mf;
  }
}

extension AiDefaultConfig on AiProvider {
  Future<AiBaseEntity<List<AiClassifyInfo>>> getClassify() async {
    // final data = await rootBundle.loadString('assets/data.json');
    // final jsonResult = jsonDecode(data.toString());
    // return AiBaseEntity.fromJson(
    //   Response(data: jsonResult,statusCode: 200,requestOptions: RequestOptions()),
    //   parseFun: (json) {
    //     return (json as List).map((e) => AiClassifyInfo.fromJson(e)).toList();
    //   },
    // );
    final result = await request<List<AiClassifyInfo>>(
      path: AiApi.classify,
      baseUrl: Server.domain,
      method: AiHttpMethod.get,
      parseFun: (json) {
        return (json as List).map((e) => AiClassifyInfo.fromJson(e)).toList();
      },
    );
    return result;
  }

  Future<AiBaseEntity<AiTemplateInfoData>> getTemplate(
      {required String page,
      required String rows,
      String? keyword,
      int? classifyId,
      bool hasLoading = true}) async {
    final result = await request<AiTemplateInfoData>(
      path: AiApi.templateList,
      method: AiHttpMethod.post,
      hasLoading: hasLoading,
      data: {
        'page': page,
        'rows': rows,
        'keyword': keyword ?? '',
        'category_id': classifyId?.toString() ?? '',
      },
      parseFun: (json) {
        return AiTemplateInfoData.fromJson(json);
      },
    );
    return result;
  }

  Future<AiBaseEntity<List<AiSummaryDetailData>>> getTemplateDetail(
      int templateId) async {
    final result = await request<List<AiSummaryDetailData>>(
      path: AiApi.templateDetail,
      method: AiHttpMethod.post,
      data: {'id': templateId.toString()},
      parseFun: (json) {
        final data = parsePath(json, 'lists') as List;
        List<AiSummaryDetailData> result =
            data.map((e) => AiSummaryDetailData.fromJson(e)).toList();
        return result;
      },
    );
    return result;
  }

  Future<AiBaseEntity<String>> useTemplate(
      {required String orderId,
      required String productId,
      required String modelId,
      required String productName,
      required String ruleId, //模板id
      required String orderNum,
      bool hasLoading = true}) async {
    final result = await request<String>(
      path: AiApi.templateStore,
      method: AiHttpMethod.post,
      hasLoading: hasLoading,
      data: {
        'order_id': orderId,
        'product_id': productId,
        'model_id': modelId,
        'product_name': productName,
        'rule_id': ruleId,
        'order_num': orderNum,
      },
    );
    return result;
  }

  Future<AiBaseEntity<List<AiDefaultModelSimple>>> getModelListInTemplate(
      int orderId) async {
    final result = await request<List<AiDefaultModelSimple>>(
      path: AiApi.productListInTemplate,
      method: AiHttpMethod.post,
      parseFun: (json) {
        final data = json['lists'] as List;
        final result =
            data.map((e) => AiDefaultModelSimple.fromJson(e)).toList();
        return result;
      },
      data: {'order_id': orderId},
    );
    return result;
  }
}
