import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_image_selected_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_summary_result.dart';
import 'package:inspector/app/modules/ai/widgets/ai_tips_button.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';

import '../ai_net/ai_base_entity.dart';
import '../ai_net/ai_server.dart';
import '../ai_pages.dart';
import '../category/controller/ai_category_controller.dart';
import '../category/entity/ai_category_info.dart';
import '../entiy/ai_detail_checout_single.dart';
import '../entiy/ai_detail_defect_info.dart';

class AiPackageView<T extends AiPackageController> extends GetView<T> {
  const AiPackageView({super.key});

  @override
  Widget build(BuildContext context) {
    var mContext;
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(controller.typeInfo.name),
        centerTitle: true,
          leading: Builder(builder: (BuildContext context) {
            return IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: (){
                Get.back();
              },
            );
          }),
          actions: [
            Obx(() {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if(AiCategoryController.to.qulityNum.value==0)
                    InkWell(
                        onTap: (){
                          Get.toNamed(  AiRoutes.QUALITY_QUICK,
                            arguments: {
                            },)?.then((value){
                            AiCategoryController.to.loadQulity();
                          });
                        },
                        child:Icon(Icons.remove_red_eye_outlined,color: MColor.skin,)
                    )else
                    InkWell(
                        onTap: (){
                          Get.toNamed(  AiRoutes.QUALITY_QUICK,
                            arguments: {
                            },)?.then((value){
                            AiCategoryController.to.loadQulity();
                          });
                        },
                        child:Badge(
                          label: Text(AiCategoryController.to.qulityNum.value>0?AiCategoryController.to.qulityNum.value.toString():''),
                          child:Icon(Icons.remove_red_eye_outlined,color: MColor.skin,),
                        )
                    )
                ],
              );
            }),
            SizedBox(width: 5,),
            IconButton(onPressed: (){
              Scaffold.of(mContext).openDrawer();
            }, icon: Icon(Icons.format_align_center_outlined,color: MColor.skin)),
          ]
      ),
      drawer:  AiSideItem(categoryList: AiCategoryController.to.categoryList,),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: StatefulBuilder(builder: (context, setState) {
          mContext = context;
          return KeyboardDismissOnTap(
            child: Column(
              children: [
                AiProductSelectBar(loader: controller),
                Expanded(
                  child: Obx(() {
                    return AiEmptyView(
                      baseResult: controller.baseResult,
                      child: ListView(
                        children: [
                          if(!controller.isResult)
                          Container(
                            padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
                            // color: MColor.white,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(bottom: 15),
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        S.of(context).ai_simple_project,
                                        style: MFont.medium18,
                                      ),
                                      Spacer(),
                                      InkWell(
                                        onTap: () {
                                          controller.goManage(setState);
                                          }  ,
                                        child:  Container(
                                          height: 27.0,
                                          width: 60,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            color: MColor.skin,
                                            borderRadius: BorderRadius.circular(5),
                                          ),
                                          child: Text(
                                            S.of(context).public_manage,
                                            style: MFont.medium14.copyWith(color: MColor.white),
                                          ),
                                        ),
                                      )

                                    ],
                                  ),
                                ),
                                for(int i=0;i<controller.inputList!.length;i++)...{
                                  _row(controller.inputList![i],i,setState)
                                }
                              ],
                            ),
                          ),
                          if(  controller.isResult)
                          AiSummaryResultView(
                            isSame: controller.result?.isSame,
                            note: controller.result?.remark,
                            noteChange: (value) => controller.result?.remark = value,
                            resultChange: (value) => controller.result?.changeResult(value),
                          ),

                          SizedBox(height: 10),
                        ],
                      ),
                    );
                  }),
                ),

              if(!controller.isResult)...{
                AiSubmitButton(
                    onPressed: (){
                      controller.goResult(setState);
                    },
                    name: S.current.check_submit,
                    isSave:false
                ),
              }else...{
                Row(
                  children: [
                    Container(
                      margin: EdgeInsets.only(left: 20,right: 20),
                      child: Row(
                        children: [
                          Container(
                            width: Get.width/2 - 20 ,
                            child:    AiSubmitButton(
                                onPressed: (){
                                  controller.goBackAndSave();
                                },
                                name: S.current.date_save,
                                isSave:true
                            ),
                          )
                          ,
                          Container(
                            width: Get.width/2 - 20 ,
                            child:  AiSubmitButton(
                                onPressed: (){
                                  logger.e('AiSubmitButton2222');
                                  controller.toNext();
                                },
                                name: S.current.ai_next_item,
                                isSave:false
                            ),
                          )
                        ],
                      )
                      ,
                    )
                  ],
                )
              }

              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _row(AiItemForms item,index,setState){
    return InkWell(
        onTap: (){
          controller.goAddEdit(item,index,setState);
        },
        child: Container(
          padding: EdgeInsets.fromLTRB(17, 8, 13, 8),
          margin: EdgeInsets.only(bottom: 2),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(5)
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(child: Text(item.name??'',
                softWrap: true,
                textAlign: TextAlign.left,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(color: MColor.black,fontSize: 16),))
              ,
              Row(
                children: [
                  _tag(item.status??0),
                  SizedBox(width: 14,),
                  Icon(Icons.keyboard_arrow_right_outlined, )
                ],
              )
            ],
          ),
        )
    ) ;
  }

  Widget _tag(int status){
    Color color;
    String text = '';
    if(status==1){//失败
      color= MColor.xFF92D050;
      text = S.current.approve;
    }
    else if(status==2){//失败
      color= MColor.xFFFF1111;
      text = S.current.ai_default_config_fail;
    }else if(status==3){// 待定
      color= MColor.xFFDF8D14;
      text = S.current.ai_wait;
    }else{
      return Container();
    }

    return Container(
      padding: EdgeInsets.fromLTRB(7, 2, 7, 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Text(text,style: TextStyle(color: MColor.black,fontSize: 12),),
    );
  }
}



class AiPackageChecking extends StatefulWidget {
  final List<AiItemForms> itemNames;
  final List<AiDetailDefectInfo> defects;
  final ValueChanged<AiItemForms?>? onAdded;
  final ValueChanged<int>? onClickItem;
  final String tag;
  final bool showResult ;
  const AiPackageChecking({
    required this.tag,
    required this.itemNames,
    required this.defects,
    this.showResult=true,
    this.onAdded,
    this.onClickItem,
    super.key,
  });

  @override
  State<AiPackageChecking> createState() => _AiPackageCheckingState();
}

class _AiPackageCheckingState extends State<AiPackageChecking> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      // color: MColor.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: 15),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.tag,
                  style: MFont.medium18,
                ),
                Spacer(),
                if(widget.showResult)
                GestureDetector(
                onTap: () => widget.onAdded?.call(null),
                  child: Container(
                    height: 27.0,
                    width: 60,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: MColor.skin,
                        borderRadius: BorderRadius.circular(5),
                    ),
                    child: Text(
                      S.of(context).public_manage,
                      style: MFont.medium14.copyWith(color: MColor.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
          for(int i=0;i<widget.itemNames.length;i++)...{
            _row(widget.itemNames[i],i)
          }
        ],
      ),
    );
  }


  calCount( List<AiCheckOutInfoSingle> countItem){
    if(countItem==null || countItem.isEmpty){
      return '';
    }
    int totalNum1=0;
    int totalNum2=0;
    int totalNum3=0;
    for(var parent in countItem){
      for(var item in parent.defectsList!){
        if(item.level=='aql_1'){// 关键
          totalNum1=totalNum1+(item.totalNum==null?0: int.parse(item.totalNum!));
        }else if(item.level=='aql_2'){// 关键
          totalNum2=totalNum2+(item.totalNum==null?0: int.parse(item.totalNum!));
        }else if(item.level=='aql_3'){// 关键
          totalNum3=totalNum3+(item.totalNum==null?0: int.parse(item.totalNum!));
        }
      }
    }
    return '$totalNum1/$totalNum2/$totalNum3';
  }



  Widget _row(AiItemForms item,index){
    return InkWell(
        onTap: (){
          widget.onClickItem!(index);
        },
        child: Container(
          padding: EdgeInsets.fromLTRB(17, 8, 13, 8),
          margin: EdgeInsets.only(bottom: 2),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(5)
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(child: Text(item.name??'',
                softWrap: true,
                textAlign: TextAlign.left,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(color: MColor.black,fontSize: 16),))
              ,
              Row(
                children: [
                  if(item.data!=null && item.data!.defectCheckOut!=null && item.data!.defectCheckOut!.values!.isNotEmpty)
                  Text( calCount(item.data!.defectCheckOut!.values![0].recordsList!),style: TextStyle(
                    color:  MColor.xFF808080,
                    fontSize: 14
                  ),),
                  SizedBox(width: 10,),
                  if(widget.showResult)
                    _tag(item.status??0),
                  SizedBox(width: 14,),
                  Icon(Icons.keyboard_arrow_right_outlined, )
                ],
              )
            ],
          ),
        )
    ) ;
  }

  Widget _tag(int status){
    Color color= MColor.xFF92D050;
    String text = '';
    if(status==1){
      color= MColor.xFF92D050;
      text = S.current.approve;
    }
    else if(status==2){//失败
      color= MColor.xFFFF1111;
      text = S.current.ai_default_config_fail;
    }else if(status==3){// 待定
      color= MColor.xFFDF8D14;
      text = S.current.ai_wait;
    }else{
      return Container();
    }

    return Container(
      padding: EdgeInsets.fromLTRB(7, 2, 7, 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Text(text,style: TextStyle(color: MColor.black,fontSize: 12),),
    );
  }


}


class AiSideItem extends StatefulWidget {
  final List<AiCategoryInfo> categoryList;

  List<bool> clickBool =[];

  final ValueChanged<AiSideItem?>? onRemove;
  int clickIndex = 0;
   AiSideItem({
    required this.categoryList,
    super.key, this.onRemove,
  });



  @override
  State<AiSideItem> createState() => _AiSideItemState();
}

class _AiSideItemState extends State<AiSideItem> {


  List<AiItemForms>? childList=[];
  void loadData(type){
    AiProvider()
        .getAiCategoryDetail(
        orderId: AiCategoryController.to.orderId,
        productId: AiCategoryController.to.productId,
        modelId: AiCategoryController.to.modelId,
        type: type,
        orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      if (value.data != null) {
        setState(() {
          childList = value.data!.details?.config?.forms;
        });

      }
    });
  }

  Widget  tag(info) {
    return Center(
      child: Container(
        width: 40.0,
        height: 20.0,
        padding: EdgeInsets.symmetric(horizontal: 4),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: info.status==1 ? MColor.pass :info.status==3? MColor.xFFDF8D14:MColor.failed,
          borderRadius: BorderRadius.circular(5),
        ),
        child: FittedBox(
          child: Text(
            info.status==1 ? S.of(Get.context!).approve :info.status==3? S.of(Get.context!).ai_wait:S.of(Get.context!).ai_default_config_fail,
            style: MFont.medium12,
          ),
        ),
      ),
    );
  }

  Widget _child(name){
    return Container(
      alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(
              width: 1,//宽度
              color: MColor.xFFE5E5E5, //边框颜色
            ),
          ),
            color: Colors.white,
        ),
      child: Container(
          padding: EdgeInsets.only(left: 36,right: 16,top: 10,bottom: 10),
        child: Text(
          name,
          style: MFont.medium14,
        )
      )
    );
  }

  Widget _rowParent(index,AiCategoryInfo info){
    return InkWell(
      onTap: (){
        setState(() {
          if( widget.clickBool[index]){
            widget.clickBool[index] =  false;
          }else{
            for(int i=0;i<widget.clickBool.length;i++){
              widget.clickBool[i] = false;
            }
            widget.clickBool[index] =true;
            loadData(info.type);
          }


        });

      },
      child: Container(
        margin: EdgeInsets.only(top: 10,left: 10,right: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5)
        ),

        child: Column(
          children: [
            Container(
                padding: EdgeInsets.only(left: 16,right: 16,top: 10,bottom: 10),
              child:  Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [

                  Text('${index+1}.${info.categoryType.name}',style: MFont.medium16,),
                  Row(
                    children: [
                      tag(info),
                      Icon(Icons.arrow_forward_ios,color: MColor.xFF808080,size: 16,)
                    ],
                  )
                ],
              )
            )
           ,
            if(widget.clickBool[index])
            for(int m=0;m<childList!.length;m++)...{
              _child('${index+1}.${m+1}${childList![m].name!}')
            }

          ],
        ),
      )
    ) ;
  }
  
  @override
  Widget build(BuildContext context) {

    if(widget.clickBool.isEmpty){
      for(int i=0;i<widget.categoryList.length;i++){
        widget.clickBool.add(false);
      }
    }

    return Drawer(
        backgroundColor:MColor.backgroundColor,
      child: ListView(
        children: [
          Container(
            margin: EdgeInsets.only(left: 20,top: 20),
            child: Text(S.current.home_navi,style: MFont.medium18,)
          )
          ,
          for(int i=0;i<widget.categoryList.length;i++)...{
            _rowParent(i,widget.categoryList[i])
          }
        ],
      )
    ) ;
  }

}

class AiCheckingItem extends StatefulWidget {
  final AiItemForms form;
  final ValueChanged<AiItemForms?>? onRemove;

  const AiCheckingItem({
    required this.form,
    this.onRemove,
    super.key,
  });

  @override
  State<AiCheckingItem> createState() => _AiCheckingItemState();
}

class _AiCheckingItemState extends State<AiCheckingItem> {
  late TextEditingController _textEditingController = TextEditingController(text: widget.form.name);

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topRight,
      clipBehavior: Clip.none,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          margin: EdgeInsets.only(bottom: 10),
          color: MColor.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Flexible(
                    child: Text(
                        widget.form.name ??
                            '${S.of(context).ai_please_input}${S.of(context).ai_test_item}',
                        style: MFont.medium18),
                  ),
                  GestureDetector(
                      onTap: () async {
                        _textEditingController.text = widget.form.name ?? '';
                        final old = _textEditingController.text;
                        await showCustomDialog(
                          '',
                          textController: _textEditingController,
                          cancel: true,
                          onCancel: () {
                            _textEditingController.text = old;
                          },
                        );
                        widget.form.name = _textEditingController.text;
                        widget.onRemove?.call(null);
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(left: 20, right: 10),
                        child: Image.asset('assets/images/ai_input_form_name_icon.png'),
                      )),
                  if (!widget.form.fromAdd)
                    AiTipsButton(
                      richText: widget.form.tips?.values,
                      images: widget.form.orderRequire?.values,
                      padding: const EdgeInsets.only(left: 10),
                    ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(top: 20),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    AiImageSelectedButton(manager: widget.form, big: true),
                    Expanded(
                      child: AiFormAssetsListView(
                        manager: widget.form,
                        empty: SizedBox(height: 40),
                      ),
                    ),

                  ],
                ),
              ),
              Obx(() {
                if (widget.form.editTimeTitleObs.isEmpty) return SizedBox.shrink();
                return Container(
                  alignment: Alignment.centerRight,
                  padding: EdgeInsets.only(top: 10),
                  child: Text(
                    widget.form.editTimeTitleObs.value,
                    style: MFont.medium12.copyWith(color: MColor.xFF808080),
                  ),
                );
              }),
            ],
          ),
        ),
        Positioned(
          top: -20,
          child: IconButton(
            onPressed: () {
              String? name = widget.form.name;
              name = name != null ? '"$name"' : '';
              showCustomDialog(
                S.of(context).ai_confirm_delete(name),
                cancel: true,
                onConfirm: () {
                  widget.onRemove?.call(widget.form);
                },
              );
            },
            icon: Image.asset(Assets.aiDeleteIcon),
          ),
        ),
      ],
    );
  }
}
