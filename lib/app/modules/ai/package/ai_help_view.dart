import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/generated/l10n.dart';

import '../category/entity/ai_recmend_data.dart';

class AiHelpView<T extends AiPackageController> extends GetView<T> {
  const AiHelpView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(Get.arguments ),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: StatefulBuilder(builder: (context, setState) {
          return KeyboardDismissOnTap(
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(10),
                    child:Text(
                        Get.arguments==S.current.ai_action_standard_instructions ?(controller.clickItem?.tipsIndustry?.values?.firstOrNull??''):(controller.clickItem?.tipsGuide?.values?.firstOrNull??'')
                    )),
              ],
            ),
          );
        }),
      ),
    );
  }






}
