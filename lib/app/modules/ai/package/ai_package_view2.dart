import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_image_selected_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_summary_result.dart';
import 'package:inspector/app/modules/ai/widgets/ai_tips_button.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';

class AiPackageView2<T extends AiPackageController> extends GetView<T> {
  const AiPackageView2({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(controller.typeInfo.name),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: StatefulBuilder(builder: (context, setState) {
          return KeyboardDismissOnTap(
            child: Column(
              children: [
                AiProductSelectBar(loader: controller),
                Expanded(
                  child: Obx(() {
                    return AiEmptyView(
                      baseResult: controller.baseResult,
                      child: ListView(
                        children: [
                          AiPackageChecking(
                            itemNames: (controller.inputList ?? [])
                                .where(
                                  (e) => !e.fromAdd,
                                )
                                .toList(),
                            onAdded: (value) {
                              setState(() {
                                controller.addInputItem(value);
                              });
                            },
                          ),
                          SizedBox(height: 10),
                          for (AiItemForms form in controller.inputList ?? [])
                            if (form.joinWrite)
                              AiCheckingItem(
                                form: form,
                                onRemove: (value) {
                                  setState(() {
                                    if (value != null) {
                                      controller.removeInputItem(value);
                                    }
                                  });
                                },
                              ),
                          AiSummaryResultView(
                            isSame: controller.result?.isSame,
                            note: controller.result?.remark,
                            noteChange: (value) => controller.result?.remark = value,
                            resultChange: (value) => controller.result?.changeResult(value),
                          )
                        ],
                      ),
                    );
                  }),
                ),
                AiSubmitButton.next(
                  onPressed: controller.toNext,
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
}

class AiPackageChecking extends StatefulWidget {
  final List<AiItemForms> itemNames;
  final ValueChanged<AiItemForms?>? onAdded;

  const AiPackageChecking({
    required this.itemNames,
    this.onAdded,
    super.key,
  });

  @override
  State<AiPackageChecking> createState() => _AiPackageCheckingState();
}

class _AiPackageCheckingState extends State<AiPackageChecking> {
  @override
  Widget build(BuildContext context) {
    final side = BorderSide(color: MColor.xFFE5E5E5);
    final titleList = [
      S.of(context).ai_numerical,
      S.of(context).ai_test_item,
      S.of(context).ai_add_all,
    ];
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      color: MColor.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: 15),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  S.of(context).ai_recommended_test_items,
                  style: MFont.medium18,
                ),
                Spacer(),
                GestureDetector(
                  onTap: () => widget.onAdded?.call(null),
                  child: Container(
                    height: 27.0,
                    width: 80,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(color: MColor.aiMain)),
                    child: Text(
                      S.of(context).ai_add_plus,
                      style: MFont.medium14.copyWith(color: MColor.aiMain),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Table(
            border: TableBorder(
                bottom: side, right: side, top: side, horizontalInside: side, verticalInside: side),
            columnWidths: const {
              0: FixedColumnWidth(70),
              1: FlexColumnWidth(),
              2: FixedColumnWidth(80)
            },
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            children: [
              TableRow(children: [
                for (int i = 0; i < titleList.length; i++)
                  _firstRowCell(child: Text(titleList[i]), showLine: i == 0),
              ]),
              for (int i = 0; i < widget.itemNames.length; i++)
                TableRow(children: [
                  _firstColumnCell(index: i),
                  _secondColumnCell(name: widget.itemNames[i].name ?? ''),
                  _thirdColumnCell(index: i)
                ]),
            ],
          ),
        ],
      ),
    );
  }

  Widget _firstRowCell({required Text child, bool showLine = false}) {
    final border = showLine ? Border(left: BorderSide(color: MColor.xFFE5E5E5, width: 1)) : null;
    return Container(
      height: 38,
      decoration: BoxDecoration(color: MColor.xFFFFF3F0, border: border),
      alignment: Alignment.center,
      child: child,
    );
  }

  Widget _firstColumnCell({required int index}) {
    final showLeftTag = widget.itemNames[index].joinWrite;

    return TableCell(
      child: Padding(
        padding: const EdgeInsets.only(left: 0),
        child: Container(
          height: 38,
          decoration: BoxDecoration(
            color: MColor.xFFFFF3F0,
            border: Border(
                left: BorderSide(
                    color: showLeftTag ? MColor.aiMain : MColor.xFFE5E5E5,
                    width: showLeftTag ? 6.0 : 1)),
          ),
          alignment: Alignment.center,
          child: Text((index + 1).toString()),
        ),
      ),
    );
  }

  Widget _secondColumnCell({required String name}) {
    return Container(
      height: 38,
      alignment: Alignment.center,
      child: Text(name),
    );
  }

  Widget _thirdColumnCell({required int index}) {
    return GestureDetector(
      onTap: () {
        widget.onAdded?.call(widget.itemNames[index]);
      },
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: 38,
        alignment: Alignment.center,
        child: const Icon(
          Icons.add_box,
          color: MColor.aiMain,
        ),
      ),
    );
  }
}

class AiCheckingItem extends StatefulWidget {
  final AiItemForms form;
  final ValueChanged<AiItemForms?>? onRemove;

  const AiCheckingItem({
    required this.form,
    this.onRemove,
    super.key,
  });

  @override
  State<AiCheckingItem> createState() => _AiCheckingItemState();
}

class _AiCheckingItemState extends State<AiCheckingItem> {
  late TextEditingController _textEditingController = TextEditingController(text: widget.form.name);

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topRight,
      clipBehavior: Clip.none,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          margin: EdgeInsets.only(bottom: 10),
          color: MColor.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Flexible(
                    child: Text(
                        widget.form.name ??
                            '${S.of(context).ai_please_input}${S.of(context).ai_test_item}',
                        style: MFont.medium18),
                  ),
                  GestureDetector(
                      onTap: () async {
                        _textEditingController.text = widget.form.name ?? '';
                        final old = _textEditingController.text;
                        await showCustomDialog(
                          '',
                          textController: _textEditingController,
                          cancel: true,
                          onCancel: () {
                            _textEditingController.text = old;
                          },
                        );
                        widget.form.name = _textEditingController.text;
                        widget.onRemove?.call(null);
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(left: 20, right: 10),
                        child: Image.asset('assets/images/ai_input_form_name_icon.png'),
                      )),
                  if (!widget.form.fromAdd)
                    AiTipsButton(
                      richText: widget.form.tips?.values,
                      images: widget.form.orderRequire?.values,
                      padding: const EdgeInsets.only(left: 10),
                    ),
/*
                  Expanded(
                      child: Container(
                    height: 27,
                    alignment: Alignment.center,
                    child: TextField(
                      maxLines: 1,
                      scribbleEnabled: false,
                      cursorColor: MColor.aiMain,
                      style: MFont.medium18,
                      onChanged: (value) {
                        widget.form.name = value;
                      },
                      decoration: InputDecoration(
                          isDense: true,
                          contentPadding: EdgeInsets.zero,
                          border: InputBorder.none,
                          hintText:
                              '${S.of(context).ai_please_input}${S.of(context).ai_test_item}'),
                    ),
                  )),
*/
                ],
              ),
              Padding(
                padding: EdgeInsets.only(top: 20),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    AiImageSelectedButton(manager: widget.form, big: true),
                    Expanded(
                      child: AiFormAssetsListView(
                        manager: widget.form,
                        empty: SizedBox(height: 40),
                      ),
                    ),
                  ],
                ),
              ),
              Obx(() {
                if (widget.form.editTimeTitleObs.isEmpty) return SizedBox.shrink();
                return Container(
                  alignment: Alignment.centerRight,
                  padding: EdgeInsets.only(top: 10),
                  child: Text(
                    widget.form.editTimeTitleObs.value,
                    style: MFont.medium12.copyWith(color: MColor.xFF808080),
                  ),
                );
              }),
            ],
          ),
        ),
        Positioned(
          top: -20,
          child: IconButton(
            onPressed: () {
              String? name = widget.form.name;
              name = name != null ? '"$name"' : '';
              showCustomDialog(
                S.of(context).ai_confirm_delete(name),
                cancel: true,
                onConfirm: () {
                  widget.onRemove?.call(widget.form);
                },
              );
            },
            icon: Image.asset(Assets.aiDeleteIcon),
          ),
        ),
      ],
    );
  }
}
