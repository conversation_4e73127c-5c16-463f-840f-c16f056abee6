import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/app/modules/ai/widgets/ai_summary_result.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';


class AiResultView<T extends AiPackageController> extends GetView<T> {
  const AiResultView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(controller.typeInfo.name),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: StatefulBuilder(builder: (context, setState) {
          return KeyboardDismissOnTap(
            child: Column(
              children: [
                Expanded(
                    child: AiEmptyView(
                      baseResult: controller.baseResult,
                      child: Column(
                        children: [
                          AiProductSelectBar(loader: controller),
                          AiSummaryResultView(
                            isSame: controller.result?.isSame,
                            note: controller.result?.remark,
                            noteChange: (value) => controller.result?.remark = value,
                            resultChange: (value) => controller.result?.changeResult(value),
                          )
                        ],
                      ),
                    )),
                Row(
                  children: [
                    Container(
                      margin: EdgeInsets.only(left: 20,right: 20),
                      child: Row(
                        children: [
                          Container(
                              width: Get.width/2 - 20 ,
                              child:    AiSubmitButton(
                                  onPressed: (){
                                    controller.goBackAndSave();
                                  },
                                  name: S.current.date_save,
                                  isSave:true
                              ),
                          )
                          ,
                          Container(
                              width: Get.width/2 - 20 ,
                              child:     AiSubmitButton(
                                  onPressed: (){
                                    controller.toNext();
                                  },
                                  name: S.current.ai_next_item,
                                  isSave:false
                              ),
                          )
                        ],
                      )
                      ,
                    )
                  ],
                )

              ],
            ),
          );
        }),
      ),
    );
  }
}
