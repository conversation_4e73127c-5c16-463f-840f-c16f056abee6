import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';

import '../photo_selected/picker_method.dart';
import '../widgets/ai_judge_result.dart';

class AiInputView extends GetView<AiPackageController> {
  const AiInputView({super.key});

  @override
  Widget build(BuildContext context) {
    Map map = Get.arguments;
    bool isNew = map['isNew'];

    return StatefulBuilder(builder: (context, setState) {
      return Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          title: Text(S.current.ai_simple_project_input),
          centerTitle: true,
          actions: [
            if (!isNew)
              InkWell(
                onTap: () {
                  showCustomDialog(
                    S.of(context).delete_account_confirm,
                    cancel: true,
                    onConfirm: () {
                      controller.clickItem?.records = [];
                      controller.clickItem?.status = null;
                      Get.back(result: -2);
                    },
                  );
                },
                child: Container(
                    margin: EdgeInsets.only(right: 12),
                    child: Text(
                      S.current.ai_delete,
                      style: TextStyle(color: MColor.skin),
                    )),
              )
          ],
        ),
        body: DefaultTextStyle(
          style: MFont.medium14.copyWith(color: MColor.black),
          child: Column(
            children: [
              Expanded(
                  child: AiEmptyView(
                baseResult: controller.baseResult,
                child: Column(
                  children: [
                    _record(),
                    Expanded(
                        child: SingleChildScrollView(
                            child: Column(
                      children: _items(setState, context),
                    ))),
                    _add(setState),
                    _judge(),
                    SizedBox(
                      height: 30,
                    )
                  ],
                ),
              )),
              Container(
                margin: EdgeInsets.only(left: 20, right: 20),
                child: Container(
                    child: Container(
                        child: AiSubmitButton(
                            onPressed: () {
                              controller.toNext();
                              // Get.back(result: -1);
                            },
                            name: S.current.apply_submit,
                            isSave: false))),
              )
            ],
          ),
        ),
      );
    });
  }

  List<Widget> _items(setstate, context) {
    List<Widget> aa = [];
    for (int i = 0; i < (controller.clickItem?.records ?? []).length; i++) {
      aa.add(_item(controller.clickItem!.records![i], setstate, context, i));
    }
    return aa;
  }

  Widget _add(setState) {
    return InkWell(
        onTap: () {
          controller.clickItem?.records?.add(AiRecordsInfo('', []));
          setState(() {});
        },
        child: Container(
          margin: EdgeInsets.only(top: 15),
          padding: EdgeInsets.fromLTRB(12, 7, 12, 7),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: MColor.skin, width: 1)),
          child: Text(
            S.current.ai_simple_add,
            style: TextStyle(color: MColor.skin),
          ),
        ));
  }

  Widget _record() {
    return Container(
        margin: EdgeInsets.only(left: 20, right: 20, top: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.current.ai_simple_project_record,
              style: MFont.medium18,
            ),
          ],
        ));
  }

  Widget _item(AiRecordsInfo item, setState, context, index) {
    return Container(
      margin: EdgeInsets.only(top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              Container(
                  margin: EdgeInsets.only(top: 24),
                  padding: EdgeInsets.fromLTRB(24, 8, 12, 8),
                  color: Colors.white,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 5,
                      ),
                      Text(
                        S.current.ai_simple_record,
                        style: MFont.medium16,
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        children: [
                          InkWell(
                            child: Container(
                              width: 62,
                              height: 62,
                              margin: EdgeInsets.only(right: 10),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(color: MColor.xFFC4C4C4)),
                              child: Image.asset(
                                Assets.productPic,
                              ),
                            ),
                            onTap: () async {
                              if (item.text == null || item.text!.isEmpty) {
                                showToast(S.current.remark_toast);
                                return;
                              }
                              final assets = item.assetsIn;
                              final inCount =
                                  item.imageInfoList.length - assets.length;
                              controller.clickImgIndex = index;
                              final list = await PickMethod(
                                maxAssetsCount: max(100 - max(0, inCount), 0),
                              ).forCamera(context);
                              if (list != null && list.isNotEmpty) {
                                item.changeAssetsOnPicker(list);
                                // controller.subscription =    eventBus.on<AiEnventImgage>().listen((event) {
                                //        print('~~~~~~~~~~~~~~~~~~~~~~~indexindexindexindex==='+index.toString());
                                //        item.images?.add(event.url);
                                //      });
                              }
                            },
                          ),
                          // AiImageSelectedButton(manager: item),
                          Expanded(
                            child: AiFormAssetsListView(
                              manager: item,
                              onImageChanged: (res) {
                                logger.e(
                                    '===============================>>>>>>>>>>>>>>>');
                                List<String> aa = [];
                                for (final info in res.imageInfoList.value) {
                                  aa.add(info.url ?? '');
                                }
                                item.images = aa;

                                logger.e(controller.clickItem?.records);
                                logger.e(
                                    '===============================>>>>>>>>>>>>>>>');
                              },
                            ),
                          ),
                        ],
                      )
                    ],
                  )),
              Positioned(
                right: 20,
                child: IconButton(
                  icon: Icon(Icons.highlight_off),
                  color: MColor.xFFC4C4C4,
                  onPressed: () {
                    controller.clickItem?.records?.remove(item);
                    setState(() {});
                  },
                ),
              ),
            ],
          ),
          SizedBox(
            height: 3,
          ),
          Container(
              padding: EdgeInsets.fromLTRB(24, 8, 12, 8),
              color: Colors.white,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 5,
                  ),
                  Text(
                    S.current.ai_simple_dsec,
                    style: MFont.medium16,
                  ),
                  SizedBox(
                    width: 25,
                  ),
                  Expanded(
                      child: TextField(
                    minLines: 3,
                    maxLines: 18,
                    controller: TextEditingController(text: item.text),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 2, horizontal: 5),
                      fillColor: MColor.white,
                      hintText: S.of(Get.context!).ai_simple_add_desc,
                      hintStyle:
                          MFont.regular13.apply(color: DarkColor.xFF999999),
                      enabledBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: MColor.xFFE5E5E5, width: 1),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: MColor.skin, width: 1),
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                    onChanged: (text) {
                      item.text = text;
                    },
                  ))
                ],
              ))
        ],
      ),
    );
  }

  Widget _judge() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 15,
        ),
        Container(
            padding: EdgeInsets.only(left: 20),
            child: Text(
              S.current.ai_judgment,
              style: MFont.medium18,
              textAlign: TextAlign.start,
            )),
        SizedBox(
          height: 15,
        ),
        AiJudgeView(
          isSame: controller.clickItem?.status,
          noteChange: (value) => controller.result?.remark = value,
          resultChange: (value) => {controller.clickItem?.status = value},
        ),
        // Container(
        //   color: Colors.white,
        //   padding: EdgeInsets.fromLTRB(21, 23, 21, 23),
        //   child: Row(
        //     children:  [
        //       Icon(Icons.check_circle , color: MColor.aiMain,
        //         size: 20,),
        //       SizedBox(width: 11,),
        //       Text(S.current.approve)
        //     ],
        //   ),
        // )
      ],
    );
  }
}
