import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_image_selected_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_summary_result.dart';
import 'package:inspector/app/modules/ai/widgets/ai_tips_button.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/modules/mine/wallet/bindings/wallet_binding.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';

import '../ai_pages.dart';
import '../photo_selected/ai_records_assets_list_view.dart';
import '../widgets/ai_require_button.dart';

class AiAddEditView<T extends AiPackageController> extends GetView<T> {
  const AiAddEditView({super.key});

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      return
        Scaffold(
          backgroundColor: MColor.backgroundColor,
          appBar: AppBar(
          title: Text(controller.clickItem?.name??''),
      centerTitle: true,
      ),    body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
      child:  Column(
            children: [
              Expanded(
                  child: AiEmptyView(
                    baseResult: controller.baseResult,
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _instruction(),
                          _help(),
                          _yaoqiu(),
                          _record(setState),
                          if(controller.clickItem!=null &&controller.clickItem!.records!.isNotEmpty )...{
                            _recordRow(),
                            if( controller.clickItem?.status==1 || controller.clickItem?.status==2|| controller.clickItem?.status==3)
                              _judge()
                          }

                        ],
                      )
                    ) ,
                  )),
              Container(
                margin: EdgeInsets.only(left: 20,right: 20),
                child: Row(
                  children: [
                    Container(
                        width: Get.width/2 - 20 ,
                        child:  AiSubmitButton(
                            onPressed: (){
                              controller.goPrevious(setState);
                            },
                            name: S.current.ai_simple_before,
                            isSave:true
                        )
                    )
                    ,
                    Container(
                        width: Get.width/2 - 20 ,
                        child:  AiSubmitButton(
                            onPressed:(){
                              controller.goNext(setState);
                            },
                            name: S.current.ai_next_item,
                            isSave:true
                        )
                    )
                  ],
                )
                ,
              )
            ],
          )));
    });}

  Widget _instruction(){
    return ConstrainedBox(
        constraints: BoxConstraints(
        minHeight: 80,
        maxHeight: 120,
    ),
    child:Container(
      padding: EdgeInsets.fromLTRB(20, 8, 20, 8),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(S.current.general_desc),
          SizedBox(width: 60,),
          Expanded(child:
          SingleChildScrollView(
              child:  Text(controller.clickItem?.tips!.values!.firstOrNull??'')
          ))
        ],
      )
      ,
    ));
  }

  Widget _help(){
    return Container(
      padding: EdgeInsets.fromLTRB(20, 8, 20, 8),
      color: Colors.white,
      child: Row(
        children: [
          Text(S.current.ai_simple_help),
          SizedBox(width: 60,),
          InkWell(
            onTap: (){
              Get.toNamed(arguments: S.current.ai_action_standard_instructions, AiRoutes.AI_HELP,);
            },
            child:  Container(
              padding: EdgeInsets.fromLTRB(26, 8, 26, 8),
              decoration: BoxDecoration(
                  border: Border.all(color: MColor.skin,width: 1),
                  borderRadius: BorderRadius.circular(5)
              ),
              child: Text(S.current.ai_action_standard_instructions,style: TextStyle(
                  color: MColor.skin
              ),),
            )
          )
         ,
          SizedBox(width: 8,),
          InkWell(
              onTap: (){
                Get.toNamed(arguments: S.current.ai_action_guidance_instructions, AiRoutes.AI_HELP,);
              },
            child: Container(
              padding: EdgeInsets.fromLTRB(26, 8, 26, 8),
              decoration: BoxDecoration(
                  border: Border.all(color: MColor.skin,width: 1),
                  borderRadius: BorderRadius.circular(5)
              ),
              child: Text(S.current.ai_action_guidance_instructions,style: TextStyle(
                  color: MColor.skin
              )),
            )
          )
          ,
        ],
      )
      ,
    );
  }

  Widget _yaoqiu(){
    return Container(
      padding: EdgeInsets.only(left: 20,top: 5),
      margin: EdgeInsets.only(top: 3),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(S.current.ai_simple_require,),
          Padding(
            padding: EdgeInsets.only(top: 20),
            child:
            AiRecordsAssetsListView(manager: controller.clickItem?.orderRequire?.values??[],
              showAdd:false,
              canEdit: false,
            ),

          ),
        ],
      )
      ,
    ) ;
  }


  Widget _record(setstate){
    return Container(
      margin: EdgeInsets.only(left: 20,right: 20,top: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(S.current.ai_simple_project_record,style: MFont.medium18,),
          InkWell(
            onTap: (){
              controller.goAdd(setstate);
            },
            child: Container(
              padding: EdgeInsets.fromLTRB(10, 1, 10, 3),
              decoration: BoxDecoration(
                  color: MColor.skin,
                  borderRadius: BorderRadius.circular(5)
              ),
              child: Text(controller.clickItem!=null &&controller.clickItem!.records!.isNotEmpty?S.current.edit:S.current.ai_add_plus,style: TextStyle(
                  color: Colors.white,
                  fontSize: 14
              ),),
            )
            ,
          )

        ],
      )
    ) ;
  }

  Widget _recordRow(){
    return InkWell(
      onTap: (){

      },
      child: Container(
        width: Get.width,

        margin: EdgeInsets.fromLTRB(0,10,0,0),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5)
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children:  [
            Container(
              margin: EdgeInsets.only(left: 20),
              child: Text(S.current.ai_simple_record,)
            )
            ,
            SizedBox(height: 7,),

            for(final item in controller.clickItem?.records??[] )...{
              _recordItems(item)
            }
          ],
        ),
      ),
    ) ;
  }


  Widget _recordItems(AiRecordsInfo item){
    return InkWell(
      onTap: (){
      },
      child: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5)
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children:  [
            Container(
                margin: EdgeInsets.only(left: 20,right: 20),
                child:  AiFormAssetsListView(manager: item,
                  readOnly: true,
                )
            )

            // Container(
            //   margin: EdgeInsets.only(left: 20,right: 20),
            //   child:  AiRecordsAssetsListView(manager: item.images??[],
            //     showAdd:false,
            //     canEdit: false,
            //   )
            // )
           ,
            Container(height: 5,color: MColor.backgroundColor,margin: EdgeInsets.only(top: 5,bottom: 5),),
            ConstrainedBox(constraints: BoxConstraints(
              minHeight: 80,
              maxHeight: 180
            ),
              child: Container(
                  margin: EdgeInsets.only(left: 20,right: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(S.current.ai_simple_dsec),
                    SizedBox(width: 25,),
                    Expanded(child:     Text(item.text??'',),)
                  ],
                )
              )
            )

          ],
        ),
      ),
    ) ;
  }


  Widget _judge(){
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 15,),
        Container(
          padding: EdgeInsets.only(left:20 ),
          child:   Text(S.current.ai_judgment,style: MFont.medium18,textAlign: TextAlign.start,)
        )
      ,
        SizedBox(height: 15,),
        Container(
          color: Colors.white,
          padding: EdgeInsets.fromLTRB(21, 23, 21, 23),
          child: Row(
            children:  [
              Icon(Icons.check_circle , color: MColor.aiMain,
                size: 20,),
              SizedBox(width: 11,),
              Text( controller.clickItem?.status==1?S.current.approve:controller.clickItem?.status==2?S.current.ai_default_config_fail:S.current.ai_wait)
            ],
          ),
        )
      ],
    );
  }


}
