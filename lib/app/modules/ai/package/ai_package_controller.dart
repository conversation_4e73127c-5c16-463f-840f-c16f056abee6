import 'dart:async';
import 'dart:developer';

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';
import '../ai_pages.dart';
import '../category/entity/ai_recmend_data.dart';
import '../entiy/ai_envent_imgage.dart';

class AiPackageController extends GetxController implements AiDataFeature {
  late int type;

  int get modelId => 1;
  int get productId => 1;
  int get orderId => 1;

  late AiCategoryType typeInfo = AiCategoryType.from(type);

  final aiDetail = AiSummaryDetailData(
          id: 1,
          ruleOrderId: 1,
          orderId: 1,
          productId: 1,
          modelId: 1,
          type: 2,
          status: 1)
      .obs;

  List<AiItemForms>? get inputList =>
      aiDetail.value.details?.config?.forms ?? [];
  List<AiItemEvent>? get eventList =>
      aiDetail.value.details?.config?.event ?? [];
  AiSummaryResult? get result => aiDetail.value.details?.config?.result;
  late var baseResult = AiBaseEntity();

  // List<AiRecmendData>?  recmendList =[];

  final aiRecmend = AiSummaryDetailData(
          id: 1,
          ruleOrderId: 1,
          orderId: 1,
          productId: 1,
          modelId: 1,
          type: 2,
          status: 1)
      .obs;
  final String tag = 'package';

  @override
  void onInit() {
    // Get.lazyPut<AiPackageController>(() => AiPackageController(), tag: 'package');
    type = int.parse(Get.parameters['type']!);
    loadData();
    search('', null);
    super.onInit();
    print('监听图片事件');
    subscription = eventBus.on<AiEnventImgage>().listen((event) {
      print("收到图片事件: ${event.url}");
      clickItem!.records?[clickImgIndex].images?.add(event.url);
    });
  }

  void aiImageChange(String url){
    clickItem!.records?[clickImgIndex].images?.add(url);
  }

  @override
  void onClose() {
    super.onClose();
    print('销毁图片事件');
    if (subscription != null) {
      subscription?.cancel();
    }
  }

  final recmendData = AiBaseEntity<List<AiRecmendData>>(data: []).obs;
  List<AiRecmendData> get recmendList => recmendData.value.data ?? [];

  String searchKeywords = '';
  void search(keyword, setState) {
    AiProvider().itemSearch(searchKeywords, '1', type.toString()).then((value) {
      if (value.data != null) {
        recmendData.value = value;
      }
      if (setState != null) {
        setState(() {});
      }
    });
  }

  void clickRecItem(item) {
    item.isSelected = !item.isSelected;
    update();
  }

  void addRec() {
    List<AiItemForms> aa = [];
    for (var item in recmendList) {
      if (item.isSelected) {
        final now = DateTime.now();
        AiItemForms child =
            AiItemForms(key: 'form${now.day}${now.hour}${now.minute}');
        child.tips = AiModelInfo.empty();
        child.name = item.itemName;
        child.tips!.values = [item.remark ?? ''];
        aa.add(child);
      }
    }
    inputList?.addAll(aa);
    Get.back(result: -1);
  }

  void addInputItem([AiItemForms? form]) {
    if (form != null) {
      form.joinWrite = true;
    } else {
      final now = DateTime.now();
      final add = AiItemForms(key: 'form${now.day}${now.hour}${now.minute}');
      add.manual = 1;
      add.joinWrite = true;
      aiDetail.value.details?.config?.forms ??= [];
      inputList!.add(add);
    }
  }

  void removeInputItem(AiItemForms form) {
    // if (form.fromAdd) {
    inputList?.remove(form);
    update();
    // } else {
    //   form.resetEditStatus();
    // }
  }

  int clickImgIndex = 0;
  StreamSubscription? subscription;
  void toNext() {
    final nextType = AiCategoryController.to.nextType(fromType: type);
    Map<String, String> p = {}
      ..addAll(Get.parameters as Map<String, String>)
      ..['type'] = nextType.id.toString();
    print('=========================');
    log('${aiDetail.value.details.toString()}');
    print('=========================');
    uploadData().then((value) {
      if (subscription != null) {
        subscription?.cancel();
      }

      Get.back(result: {'param': p, 'page': nextType.location});
    });
  }

  void goBack() {
    Get.back(result: -1);
  }

  void goBackAndSave() {
    uploadData().then((value) {
      Get.back(result: -1);
    });
  }

  AiItemForms? clickItem;
  void goEdit(item, setState) {
    clickItem = item;
    log('goEditgoEditgoEdit');
    log('${clickItem}');
    log('goEditgoEditgoEdit');
    Get.toNamed(
      AiRoutes.AI_EDIT,
      arguments: {type: 'package'},
    )?.then((value) {
      setState(() {});
      if (value == -1) {
        Get.back(result: -1);
      }
    });
  }

  void goManage(setState) {
    Get.toNamed(
      AiRoutes.AI_MANAGE,
      arguments: {'type': 'package'},
      parameters: Get.parameters as Map<String, String>,
    )?.then((value) {
      if (value == -1) {
        setState(() {});
        // Get.back(result: -1);
      }
    });
  }

  void goRecmend() {
    Get.toNamed(
      AiRoutes.AT_RECMEND,
      arguments: {'type': type},
    )?.then((value) {
      if (value != null && value['data'] != null) {
        inputList?.addAll(value['data']);
        Get.back(result: -1);
      }
    });
  }

  int clickIndex = 0;
  void goAddEdit(item, index, setState) {
    clickIndex = index;
    clickItem = item;
    clickItem?.records ??= [];
    if (clickItem?.tips == null) {
      clickItem?.tips = AiModelInfo.empty();
    }

    Get.toNamed(
      AiRoutes.AI_ADD_EDIT,
    )?.then((value) {
      setState(() {});
      // loadData();
    });
  }

  Future<void> goPrevious(setState) async{
    AiBaseEntity entity = await uploadData();
    if(entity.code == 200){
      if (clickIndex == 0) {
        showToast(S.current.ai_no_more);
        return;
      }
      clickIndex--;
      clickItem = inputList?[clickIndex];
      setState(() {});
    }
  }

  Future<void> goNext(setState) async{
    AiBaseEntity entity = await uploadData();
    if(entity.code == 200){
      if (clickIndex == inputList!.length - 1) {
        showToast(S.current.ai_no_more);
        return;
      }
      clickIndex++;
      clickItem = inputList?[clickIndex];
      logger.e('@@@@@@@====aiDetail11====@@@@@@@@@@@');
      log('${inputList?.toString()}');
      logger.e('@@@@@@@====aiDetail22====@@@@@@@@@@@');
      setState(() {});
    }
  }

  void goAdd(setState) {
    bool isNew = clickItem == null || clickItem!.records!.isEmpty;
    if (isNew) {
      clickItem!.records?.add(AiRecordsInfo('', []));
    }
    Get.toNamed(
      AiRoutes.AI_INPUT,
      arguments: {'isNew': isNew},
      parameters: Get.parameters as Map<String, String>,
    )?.then((value) async{
      // List<AiRecordsInfo> aa = [];
      // for(AiRecordsInfo item in  clickItem!.records??[]){
      //   if(item.images!.isEmpty && item.text!.isEmpty){
      //   }else{
      //     aa.add(item);
      //   }
      // }

      if(value == -2){
        await uploadData();
      }

      List<String> uniqueNumbers = [];
      if (clickItem!.records != null && clickItem!.records!.isNotEmpty) {
        clickItem!.records?.first.images?.forEach((path) {
          if (!uniqueNumbers.contains(path)) {
            uniqueNumbers.add(path);
          }
        });
        clickItem!.records?.first.images = uniqueNumbers;
      }
      // clickItem!.records = aa;

      setState(() {});
    });
  }

  bool isResult = false;
  void goResult(setState) {
    final nextType = AiCategoryController.to.nextType(fromType: type);
    Map<String, String> p = {}
      ..addAll(Get.parameters as Map<String, String>)
      ..['type'] = nextType.id.toString();
    uploadData().then((value) {
      if (result?.status == 0) {
        bool isSucc = true;
        for (AiItemForms item in inputList!) {
          if (item.status == 3 || item.status == 2) {
            isSucc = false;
            break;
          }
        }
        result?.status = isSucc ? 1 : 2;
      }
      logger.e('7777777777777777777');
      log('${aiDetail.value.details?.toJson()}');
      logger.e('7777777777777777777');
      isResult = true;
      setState(() {});
    });
  }

  @override
  void loadData() {
    AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: type,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      baseResult = value;
      if (value.data != null) {
        aiDetail.value = value.data!;
      }
    });
  }

  @override
  Future<AiBaseEntity> uploadData() {
    if (aiDetail.value.details == null) {
      return Future.value(AiBaseEntity(code: 200));
    }
    inputList
        ?.removeWhere((element) => element.fromAdd && element.name == null);
    aiDetail.value.details?.config?.uploadImageIfNeed();
    // if (clickItem!.records != null && clickItem!.records!.isNotEmpty) {
    //   aiDetail.value.details!.config!.forms![clickIndex] = clickItem!;
    // }
    // aiDetail.value.details?.config?.forms = inputList;
    debugPrint('>>>>>$inputList');
    inputList?.removeWhere(
        (element) => element.fromAdd && element.imageList.isEmpty);
    return AiProvider()
        .uploadConfig(id: aiDetail.value.id, details: aiDetail.value.details!);
  }
}
