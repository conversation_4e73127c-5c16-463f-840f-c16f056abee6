import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';


class AiEditView extends GetView<AiPackageController>  {
  const AiEditView({super.key});

  @override
  Widget build(BuildContext context) {
     AiItemForms? items ;
    bool isNew=true;
    if( controller.clickItem!= null){
      isNew = false;
       items = controller.clickItem as AiItemForms;
    }else{
      items = AiItemForms(key:'form${controller.inputList!.length}' );
      items.name='';
      items.tips =  AiModelInfo.text() ;
      // items.manual = 1;
      // items.joinWrite = true;
      items.tips!.values=[];
    }

    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(S.current.ai_simple_project_edit),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: StatefulBuilder(builder: (context, setState) {
          return KeyboardDismissOnTap(
            child: Column(
              children: [
                Expanded(
                    child: AiEmptyView(
                      baseResult: controller.baseResult,
                      child: Column(
                        children: [
                          SizedBox(height: 5,),
                          Container(
                            color:Colors.white,
                              child: TextField(
                                textAlignVertical: TextAlignVertical.center,
                                maxLines: 5,
                                minLines: 1,
                                controller: TextEditingController(text:items?.name) ,
                                decoration: InputDecoration(
                                    contentPadding: EdgeInsets.zero,
                                    border: InputBorder.none,
                                    hintText:S.current.apply_enter,
                                    isDense: true,
                                    prefixIcon: Container(
                                      margin: EdgeInsets.only(left:20,right: 28,top: 13),
                                      child: Text(
                                        S.of(context).ai_simple_project,
                                        style: MFont.medium16,
                                      ),
                                    )),
                                onChanged: (e){
                                  items!.name=e;
                                },
                              )
                          )
                         ,
                        Container(
                          height: 2,
                          color: MColor.backgroundColor,
                        ),
                          Container(
                            alignment: Alignment.centerLeft,
                              color:Colors.white,
                            child: TextField(
                                controller: TextEditingController(text:items!=null && items.tips!.values!.length!>0  ?items.tips!.values?.firstOrNull:'') ,
                                textAlignVertical: TextAlignVertical.center,
                                decoration: InputDecoration(
                                    isCollapsed: true,
                                    border: InputBorder.none,
                                    hintText:S.current.apply_enter,
                                    prefixIcon: Container(
                                      margin: EdgeInsets.only(left:20,right: 60,top: 13),
                                      child: Text(
                                        textAlign: TextAlign.center,
                                        S.of(context).general_desc,
                                        style:       MFont.medium16,
                                      ),
                                    )),
                              onChanged: (e){
                                  if(items!.tips!.values!.isNotEmpty){
                                    items.tips!.values?[0]=e;
                                  }else{
                                    items.tips!.values?.add(e);
                                  }
                              },
                            )
                          )
                          ,
                          if(isNew)...{
                            InkWell(
                                onTap: (){
                                  controller.goRecmend();
                                },
                                child:  Container(
                                    margin: EdgeInsets.only(top: 40),
                                    width: 110,
                                    height: 40,
                                    padding: EdgeInsets.fromLTRB(12, 6, 12, 6),
                                    decoration: BoxDecoration(
                                        color: MColor.white,
                                        borderRadius: BorderRadius.circular(20)
                                    ),
                                    child: Row(
                                      children: [
                                        Image.asset('assets/images/ai.png',width: 19,),
                                        SizedBox(width: 8,),
                                        Text(S.current.ai_simple_project_recmend,style: TextStyle(
                                            color:  MColor.skin
                                        ),)
                                      ],
                                    )
                                )
                            )
                          }

                         ,

                        ],
                      ),
                    )),
                AiSubmitButton(
                    onPressed: (){
                      if(isNew){
                        if(items!.name!.isEmpty){
                          showToast(S.current.ai_input_tip);
                          return ;
                        }
                        controller.inputList?.insert(0,items!);
                      }
                      Get.back();
                    },
                    name: S.current.date_save,
                    isSave:true
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
}
