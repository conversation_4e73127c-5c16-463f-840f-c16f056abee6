import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/package/ai_package_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_controller.dart';
import 'package:inspector/app/modules/ai/widgets/ai_defect_record_tile.dart';
import 'package:inspector/app/modules/ai/widgets/ai_input_devect_tile.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';

import '../../../../generated/l10n.dart';
import '../ai_pages.dart';
import '../category/controller/ai_category_controller.dart';

class AiQualityView<T extends AiQualityController> extends GetView<T> {
  const AiQualityView({super.key});

  @override
  Widget build(BuildContext context) {
   var  mContext;
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(AiCategoryType.quality.name),
        centerTitle: true,
          leading: Builder(builder: (BuildContext context) {
            return IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: (){
                Get.back();
              },
            );
          }),
          actions: [

            Obx(() {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if(AiCategoryController.to.qulityNum.value==0)
                    InkWell(
                        onTap: (){
                          controller.reset();
                          Get.toNamed(  AiRoutes.QUALITY_QUICK,
                            arguments: {

                            },)?.then((value){
                            AiCategoryController.to.loadQulity();
                          });
                        },
                        child:Icon(Icons.remove_red_eye_outlined,color: MColor.skin,)
                    )else
                    InkWell(
                        onTap: (){
                          Get.toNamed(  AiRoutes.QUALITY_QUICK,
                            arguments: {
                            },)?.then((value){
                            AiCategoryController.to.loadQulity();
                          });
                        },
                        child:Badge(
                          label: Text(AiCategoryController.to.qulityNum.value>0?AiCategoryController.to.qulityNum.value.toString():''),
                          child:Icon(Icons.remove_red_eye_outlined,color: MColor.skin,),
                        )
                    )
                ],
              );
            })

            ,
            SizedBox(width: 5,),
            IconButton(onPressed: (){
              Scaffold.of(mContext).openDrawer();
            }, icon: Icon(Icons.format_align_center_outlined,color: MColor.skin)),

          ]
      ),
      drawer:  AiSideItem(categoryList: AiCategoryController.to.categoryList,),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: StatefulBuilder(builder: (context, setState) {
          mContext = context;
          return Column(
            children: [
              AiProductSelectBar(loader: controller),
              Expanded(
                child: Obx(() {
                  return AiEmptyView(
                    baseResult: controller.baseResult,
                    child: ListView(
                      children: [
                        AiPackageChecking(
                          tag: S.current.ai_defect_record,
                          defects:[],
                          itemNames: (controller.inputList ?? []),
                          onClickItem: (index){
                            print('2222233333333333333');
                            controller.goDetail(index,setState,true);
                          },
                          onAdded: (value) async {
                            await Get.toNamed(
                              AiRoutes.QUALITY_MANAGE,
                              arguments: {
                                'type':'quality'
                              },
                              parameters: Get.parameters as Map<String, String>,
                            )?.then((value){
                              setState((){});
                            });
                          },
                        ),

                        SizedBox(height: 10),
                      ],
                    ),
                  );
                }),
              ),

              AiSubmitButton.submit(
                onPressed: controller.toNext,
              ),
            ],
          );
        }),
      ),
    );
  }
}
