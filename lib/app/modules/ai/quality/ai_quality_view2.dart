import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/package/ai_package_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_controller.dart';
import 'package:inspector/app/modules/ai/widgets/ai_defect_record_tile.dart';
import 'package:inspector/app/modules/ai/widgets/ai_input_devect_tile.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';

class AiQualityView extends GetView<AiQualityController> {
  const AiQualityView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(AiCategoryType.quality.name),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: StatefulBuilder(builder: (context, setState) {
          return Column(
            children: [
              AiProductSelectBar(loader: controller),
              // Expanded(
              //   child: Obx(() {
              //     return AiEmptyView(
              //       baseResult: controller.baseResult,
              //       child: ListView(
              //         children: [
              //           AiPackageChecking(
              //             tag: 'quality',
              //             defects:controller.defects??[],
              //             itemNames: (controller.inputList ?? []).where((e) => !e.fromAdd).toList(),
              //             onAdded: (value) async {
              //               var defect =
              //                   value != null ? controller.fromForm(value) : AiDetailDefectInfo();
              //               await AiInputDefectTile.inputDefectView(
              //                 context,
              //                 defect,
              //                 addLabel: (value) {
              //                   setState(() {
              //                     controller.addLabel(value);
              //                     controller.checkoutForms();
              //                   });
              //                 },
              //                 changeLabel: (oldValue, newValue) {
              //                   setState(() {
              //                     controller.changeLabel(oldValue, newValue);
              //                     controller.checkoutForms();
              //                   });
              //                 },
              //                 labels: controller.formLabels.toList(),
              //               );
              //               controller.addDefect(defect);
              //               setState(() {});
              //             },
              //           ),
              //           SizedBox(height: 10),
              //
              //           ///记录的数据
              //           for (int y = 0; y < (controller.defects?.length ?? 0); y++)
              //             AiDefectRecordTile(
              //               defect: controller.defects![y],
              //               index: y + 1,
              //               addLabel: (value) {
              //                 setState(() {
              //                   controller.addLabel(value);
              //                   controller.checkoutForms();
              //                 });
              //               },
              //               changeLabel: (oldValue, newValue) {
              //                 setState(() {
              //                   controller.changeLabel(oldValue, newValue);
              //                   controller.checkoutForms();
              //                 });
              //               },
              //               labels: controller.formLabels.toList(),
              //               deleteCall: (value) {
              //                 setState(() {
              //                   controller.removeDefect(value);
              //                 });
              //               },
              //             ),
              //         ],
              //       ),
              //     );
              //   }),
              // ),
              AiSubmitButton.next(
                onPressed: controller.toNext,
              ),
            ],
          );
        }),
      ),
    );
  }
}
