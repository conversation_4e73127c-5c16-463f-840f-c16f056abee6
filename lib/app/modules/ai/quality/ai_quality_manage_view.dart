import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

import '../ai_pages.dart';
import '../quality/ai_quality_controller.dart';

class AiQualityManageView<T extends AiQualityController> extends GetView<T> {
  const AiQualityManageView({super.key});

  @override
  Widget build(BuildContext context) {
    Map map = Get.arguments;
    // var controller ;
    // String tag = map['type'];
    // if(tag=='package'){
    //   controller = Get.find<AiPackageController>(tag: 'package');
    // }else if(tag=='quality'){
    //   controller = Get.find<AiQualityController>(tag: 'quality');
    // }

    Widget _row(AiItemForms item ,setState){
      return Container(
        padding: EdgeInsets.fromLTRB(17, 8, 13, 8),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5)
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(child: Text(item.name??'',
                  softWrap: true,
                  textAlign: TextAlign.left,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(color: MColor.black,fontSize: 16),)),
                Row(
                  children: [
                    InkWell(
                        onTap: (){
                          print('InkWellInkWell');
                          controller.goEdit(item,setState);
                        },
                        child: Image.asset('assets/images/edit.png',width: 22,height: 22,)
                    )
                    ,
                    SizedBox(width: 24,),

                    InkWell(
                        onTap: (){
                          showCustomDialog(
                            S.of(context).ai_confirm_delete(S.of(context).ai_simple_project),
                            cancel: true,
                            onConfirm: () async{
                              controller.removeInputItem(item);
                              await controller.uploadData();
                              setState(() {

                              });
                            },
                          );
                        },
                        child: Image.asset('assets/images/del.png',width: 22,height: 22,)
                    )
                  ],
                )
              ],
            ),
            SizedBox(height: 5,),
            Text(item.tips!.values!.firstOrNull??'',style:  MFont.regular14.apply(color: MColor.xFF808080),
              softWrap: true,
              textAlign: TextAlign.left,
              overflow: TextOverflow.ellipsis,
            ),

          ],
        ),
      );
    }

    var state;
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
          title: Text(S.current.ai_simple_project_manage),
          centerTitle: true,
          actions: [
            InkWell(
              onTap: (){
                controller.goEdit(null,state);
              },
              child: Container(
                  margin: EdgeInsets.only(right: 20),
                  width: 26,
                  height: 26,
                  child: Image.asset('assets/images/add_plus.png',width: 18,height: 18,)
              )
            ),
          ]
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: StatefulBuilder(builder: (context, setState) {
          state = setState;
          return KeyboardDismissOnTap(
            child: Column(
              children: [
                SizedBox(height: 10,),
                Expanded(
                  child: Obx(() {
                    return AiEmptyView(
                      baseResult: controller.baseResult,
                      child: ListView.separated(
                        itemBuilder: (BuildContext context, int index) {
                          return _row(controller.inputList![index],setState);
                        },
                        separatorBuilder: (BuildContext context, int index) {
                          return Container(
                            height: 1,
                            color:  MColor.xFFE5E5E5,
                          );
                        },
                        itemCount: controller.inputList?.length ?? 0,

                      ),
                    );
                  }),
                ),
                /*AiSubmitButton.save(
                  onPressed: controller.goBack,
                ),*/
              ],
            ),
          );
        }),
      ),
    );

  }




}




