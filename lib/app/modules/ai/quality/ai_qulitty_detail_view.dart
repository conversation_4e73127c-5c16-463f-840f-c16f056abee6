import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/widgets/ai_defect_record_tile_defect.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

import '../entiy/ai_summary_detail_data.dart';
import 'ai_quality_controller.dart';

class AiQualityDetailView<T extends AiQualityController> extends GetView<T> {
  const AiQualityDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      Widget _instruction() {
        Future.delayed(Duration(milliseconds: 1000), () {
          controller.refreshData(setState);
        });
        if (controller.clickItem?.tips == null) {
          log('88888888888888888888888888888');
          log(Get.arguments.toString());
          Map aa = Get.arguments;
          controller.clickItem = AiItemForms.fromJson(aa['clickItem']);
          controller.goDetail(aa['index'], setState, false);
          logger.e(controller.clickItem?.toJson());
          controller.clickItem!.tips = AiModelInfo.empty();
        }

        return ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: 80,
              maxHeight: 120,
            ),
            child: Container(
              padding: EdgeInsets.fromLTRB(20, 8, 20, 8),
              color: Colors.white,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(S.current.general_desc),
                  SizedBox(
                    width: 60,
                  ),
                  Expanded(
                      child: SingleChildScrollView(
                          child: Text(
                              controller.clickItem?.tips!.values!.firstOrNull ??
                                  '')))
                ],
              ),
            ));
      }

      Widget _recordsWidget(item, index) {
        return InkWell(
          onTap: () {
            controller.changeItem(index, item, setState);
          },
          child: Container(
              margin: EdgeInsets.only(right: 10),
              child: Column(
                children: [
                  Text(S.current.ai_simple_record + (index + 1).toString(),
                      style: TextStyle(
                        color: MColor.black,
                        fontSize: 16,
                      )),
                  SizedBox(
                    height: 5,
                  ),
                  if (controller.showIndex == index)
                    Container(
                      height: 2,
                      color: MColor.skin,
                      width: 30,
                    ),
                ],
              )),
        );
      }

      Widget _recordRows() {
        return Container(
          margin: EdgeInsets.only(top: 15),
          padding: EdgeInsets.only(left: 20, right: 14, top: 8, bottom: 8),
          decoration: BoxDecoration(color: Colors.white),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                  width: Get.width - 110,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        for (int i = 0;
                            i < controller.showRecordsList!.length;
                            i++)
                          _recordsWidget(controller.showRecordsList?[i], i)
                      ],
                    ),
                  )),
              InkWell(
                  onTap: () {
                    setState(() {
                      controller.showMore = true;
                    });
                  },
                  child: Container(
                      width: 50,
                      margin: EdgeInsets.only(left: 10, right: 0),
                      child: Image.asset(
                        'assets/images/more.png',
                        width: 20,
                        height: 20,
                      )))
            ],
          ),
        );
      }

      Widget _moreItem(item, index) {
        return InkWell(
            onTap: () {
              controller.changeItem(index, item, setState);
            },
            child: Container(
              alignment: Alignment.center,
              width: Get.width / 3,
              height: 40,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    S.current.ai_simple_record + (index + 1).toString(),
                    style: MFont.regular16,
                  ),
                  SizedBox(
                    height: 5,
                  ),
                  if (controller.showIndex == index)
                    Container(
                      height: 2,
                      color: MColor.skin,
                      width: 30,
                    ),
                ],
              ),
            ));
      }

      Widget _more() {
        return Container(
          width: Get.width,
          color: Colors.white,
          child: Column(
            children: [
              Wrap(
                direction: Axis.horizontal,
                children: [
                  for (int i = 0;
                      i < controller.showRecordsList!.length;
                      i++) ...{_moreItem(controller.showRecordsList?[i], i)}
                ],
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    controller.showMore = false;
                  });
                },
                icon: Icon(
                  Icons.arrow_drop_up_sharp,
                  color: MColor.skin,
                  size: 50,
                ),
                padding: EdgeInsets.all(0),
              )
            ],
          ),
        );
      }

      Widget _record() {
        return Container(
            margin: EdgeInsets.only(left: 20, right: 20, top: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.ai_defect_record,
                  style: MFont.medium18,
                ),
                Row(
                  children: [
                    InkWell(
                      onTap: () {
                        controller.goInput(null, setState);
                      },
                      child: Container(
                        padding: EdgeInsets.fromLTRB(10, 1, 10, 3),
                        decoration: BoxDecoration(
                            color: MColor.skin,
                            borderRadius: BorderRadius.circular(5)),
                        child: Text(
                          S.current.ai_add_plus,
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    if (controller.newAiCheckOutInfo?.defectsList != null &&
                        controller.newAiCheckOutInfo!.defectsList!.isNotEmpty)
                      InkWell(
                        onTap: () {
                          controller.goInput(
                              controller
                                  .newAiCheckOutInfo!.defectsList?.firstOrNull,
                              setState);
                        },
                        child: Container(
                          padding: EdgeInsets.fromLTRB(10, 1, 10, 3),
                          decoration: BoxDecoration(
                              color: MColor.skin,
                              borderRadius: BorderRadius.circular(5)),
                          child: Text(
                            S.current.edit,
                            style: TextStyle(color: Colors.white, fontSize: 14),
                          ),
                        ),
                      )
                  ],
                ),
              ],
            ));
      }

      Widget _builTable(
          {required String title,
          required String value1,
          required String value2,
          required String value3,
          required bool isFirst}) {
        return Container(
          height: 60,
          decoration: BoxDecoration(
              color: Colors.white,
              border: isFirst
                  ? Border.all(width: 1, color: MColor.xFFE5E5E5)
                  : Border(
                      left: BorderSide(
                        width: 1, //宽度
                        color: MColor.xFFE5E5E5, //边框颜色
                      ),
                      bottom: BorderSide(
                        width: 1, //宽度
                        color: MColor.xFFE5E5E5, //边框颜色
                      ),
                      right: BorderSide(
                        width: 1, //宽度
                        color: MColor.xFFE5E5E5, //边框颜色
                      ),
                    )),
          child: Row(
            children: [
              Container(
                alignment: Alignment.center,
                width: 115,
                height: 60,
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(
                      width: 1, //宽度
                      color: MColor.xFFE5E5E5, //边框颜色
                    ),
                  ),
                ),
                child: Text(title),
              ),
              Container(
                alignment: Alignment.center,
                width: ((Get.width - 115 - 3) / 3),
                height: 60,
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(
                      width: 1, //宽度
                      color: MColor.xFFE5E5E5, //边框颜色
                    ),
                  ),
                ),
                child: Text(value1),
              ),
              Container(
                alignment: Alignment.center,
                width: ((Get.width - 115 - 6) / 3),
                height: 60,
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(
                      width: 1, //宽度
                      color: MColor.xFFE5E5E5, //边框颜色
                    ),
                  ),
                ),
                child: Text(value2),
              ),
              Container(
                alignment: Alignment.center,
                width: ((Get.width - 115 - 6) / 3),
                height: 60,
                decoration: BoxDecoration(),
                child: Text(value3),
              ),
            ],
          ),
        );
      }

      return Scaffold(
          backgroundColor: MColor.backgroundColor,
          appBar: AppBar(
            title: Text(controller.clickItem?.name ?? ''),
            centerTitle: true,
          ),
          body: DefaultTextStyle(
              style: MFont.medium14.copyWith(color: MColor.black),
              child: Column(
                children: [
                  Expanded(
                      child: AiEmptyView(
                    baseResult: controller.baseResult,
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _instruction(),
                          if (controller.mapTotal != null) ...{
                            SizedBox(
                              height: 10,
                            ),
                            _builTable(
                                title: S.current.ai_defect_level,
                                value1: S.current.ai_critical,
                                value2: S.current.ai_important,
                                value3: S.current.ai_minor,
                                isFirst: true),
                            _builTable(
                                title: S.current.ai_found_quantity,
                                value1: controller.mapTotal['totalNum1'] ?? '',
                                value2: controller.mapTotal['totalNum2'] ?? '',
                                value3: controller.mapTotal['totalNum3'] ?? '',
                                isFirst: false),
                            _builTable(
                                title: S.current.ai_pick_out,
                                value1: controller.mapTotal['tiaoChu1'] ?? '',
                                value2: controller.mapTotal['tiaoChu2'] ?? '',
                                value3: controller.mapTotal['tiaoChu3'] ?? '',
                                isFirst: false),
                            _builTable(
                                title: S.current.ai_rework,
                                value1: controller.mapTotal['fanGong1'] ?? '',
                                value2: controller.mapTotal['fanGong2'] ?? '',
                                value3: controller.mapTotal['fanGong3'] ?? '',
                                isFirst: false),
                            _builTable(
                                title: S.current.ai_replace,
                                value1: controller.mapTotal['tiHuan1'] ?? '',
                                value2: controller.mapTotal['tiHuan2'] ?? '',
                                value3: controller.mapTotal['tiHuan3'] ?? '',
                                isFirst: false),
                          },
                          _record(),
                          Stack(
                            children: [
                              Column(
                                children: [
                                  if (controller.newAiCheckOutInfo != null &&
                                      (controller.newAiCheckOutInfo!
                                          .defectsList!.isNotEmpty)) ...{
                                    _recordRows(),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    AiDefectRecordTileDefect(
                                        defect: controller
                                            .newAiCheckOutInfo!.defectsList!,
                                        index: 1,
                                        readOnly: true,
                                        addLabel: (value) {
                                          setState(() {});
                                        },
                                        changeLabel: (oldValue, newValue) {
                                          setState(() {});
                                        },
                                        labels: [],
                                        deleteCall: (value) {
                                          setState(() {});
                                        }),
                                  },
                                ],
                              ),
                              controller.showMore
                                  ? Positioned(
                                      top: 0,
                                      child: _more(),
                                    )
                                  : Container(),
                            ],
                          ),
                        ],
                      ),
                    ),
                  )),
                  Container(
                    margin: EdgeInsets.only(left: 20, right: 20),
                    child: Row(
                      children: [
                        Container(
                            width: Get.width / 2 - 20,
                            child: AiSubmitButton(
                                onPressed: () {
                                  controller.goPrevious(setState);
                                },
                                name: S.current.ai_simple_before,
                                isSave: true)),
                        Container(
                            width: Get.width / 2 - 20,
                            child: AiSubmitButton(
                                onPressed: () {
                                  controller.goNext(setState);
                                },
                                name: S.current.ai_next_item,
                                isSave: true))
                      ],
                    ),
                  )
                ],
              )));
    });
  }
}
