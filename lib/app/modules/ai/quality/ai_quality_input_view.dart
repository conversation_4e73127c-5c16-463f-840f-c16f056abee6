import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_controller.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';

import '../checkout/type/chekout_level_type.dart';
import '../entiy/ai_detail_defect_checout_info.dart';
import '../photo_selected/picker_method.dart';
import '../widgets/ai_input_devect_tile.dart';

class AiQualityInputView<T extends AiQualityController> extends GetView<T> {
  const AiQualityInputView({super.key});

  @override
  Widget build(BuildContext context) {
    Map map = Get.arguments;
    bool isNew = map['isNew'];

    Widget _builFirst({required String title, required StateSetter setState}) {
      return Container(
        height: 60,
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                width: 1, //宽度
                color: MColor.xFFE5E5E5, //边框颜色
              ),
              bottom: BorderSide(
                width: 1, //宽度
                color: MColor.xFFE5E5E5, //边框颜色
              ),
            )),
        child: Row(
          children: [
            Container(
              alignment: Alignment.center,
              width: 115,
              height: 60,
              decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                ),
              ),
              child: Text(title),
            ),
            Container(
                alignment: Alignment.center,
                width: Get.width - 115 - 3,
                height: 60,
                decoration: BoxDecoration(),
                child: Wrap(
                  runSpacing: 10,
                  children: [
                    for (int i = 0;
                        i < AiCheckoutDefectLevelType.values.length;
                        i++)
                      Padding(
                        padding: EdgeInsets.only(
                            right:
                                (AiCheckoutDefectLevelType.values.length - 1) !=
                                        i
                                    ? 30
                                    : 0),
                        child: AiCheckButton(
                          title: AiCheckoutDefectLevelType.values[i].name,
                          selected: controller.editDefectItem?.level ==
                              AiCheckoutDefectLevelType.values[i].value,
                          onPressed: () {
                            setState(() {
                              controller.editDefectItem?.level =
                                  AiCheckoutDefectLevelType.values[i].value;
                            });
                            controller.editDefectItem?.refreshEditTime();
                          },
                        ),
                      ),
                  ],
                )),
          ],
        ),
      );
    }

    Widget _buildSecond(
        {required String title,
        required String values,
        required StateSetter setState}) {
      return Container(
        height: 60,
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(
                width: 1, //宽度
                color: MColor.xFFE5E5E5, //边框颜色
              ),
            )),
        child: Row(
          children: [
            Container(
              alignment: Alignment.center,
              width: 115,
              height: 60,
              decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                ),
              ),
              child: Text(title),
            ),
            Container(
                width: Get.width - 118,
                alignment: Alignment.center,
                child: AiAddSubButton(
                  value: int.parse(values ?? '0'),
                  onChange: (value) {
                    values = value.toString();
                    controller.editDefectItem!.totalNum = values;
                    controller.calcTotal();
                  },
                ))
          ],
        ),
      );
    }

    Widget _builThree(
        {required String title,
        required String values,
        required int type,
        required StateSetter setState}) {
      return Container(
        height: 60,
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(
                width: 1, //宽度
                color: MColor.xFFE5E5E5, //边框颜色
              ),
            )),
        child: Row(
          children: [
            Container(
              alignment: Alignment.center,
              width: 115,
              height: 60,
              decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                ),
              ),
              child: Text(title),
            ),
            Container(
                alignment: Alignment.center,
                width: Get.width - 115 - 3,
                height: 60,
                decoration: BoxDecoration(),
                child: TextField(
                  controller: TextEditingController(text: values),
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: S.current.ai_quantity),
                  onChanged: (e) {
                    values = e.toString();
                    if (type == 0) {
                      controller.editDefectItem?.tiaoChu = e.toString();
                    } else if (type == 1) {
                      controller.editDefectItem?.tiHuan = e.toString();
                    } else if (type == 2) {
                      controller.editDefectItem?.fanGong = e.toString();
                    }
                    controller.calcTotal();
                  },
                )),
          ],
        ),
      );
    }

    Widget _add(setState) {
      return InkWell(
          onTap: () {
            setState(() {
              controller.editDefectItem?.records ??= [];
              controller.editDefectItem?.records?.add(
                  AiCheckOutDefectInfo.empty(
                      DateTime.now().millisecondsSinceEpoch.toString()));
            });
          },
          child: Container(
            margin: EdgeInsets.only(top: 15),
            padding: EdgeInsets.fromLTRB(12, 7, 12, 7),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: MColor.skin, width: 1)),
            child: Text(
              S.current.ai_simple_add,
              style: TextStyle(color: MColor.skin),
            ),
          ));
    }

    Widget _record() {
      return Container(
          margin: EdgeInsets.only(left: 20, right: 20, top: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                S.current.ai_defect_record,
                style: MFont.medium18,
              ),
            ],
          ));
    }

    Widget _item(AiCheckOutDefectInfo item, setState, context, index) {
      return Container(
        margin: EdgeInsets.only(top: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                Container(
                    margin: EdgeInsets.only(top: 24),
                    padding: EdgeInsets.fromLTRB(24, 8, 12, 8),
                    color: Colors.white,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 5,
                        ),
                        Text(
                          S.current.ai_simple_record,
                          style: MFont.medium16,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        Row(
                          children: [
                            InkWell(
                              child: Container(
                                width: 62,
                                height: 62,
                                margin: EdgeInsets.only(right: 10),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5),
                                    border:
                                        Border.all(color: MColor.xFFC4C4C4)),
                                child: Image.asset(
                                  Assets.productPic,
                                ),
                              ),
                              onTap: () async {
                                controller.clickImgIndex = index;
                                final assets = item.assetsIn;
                                final inCount =
                                    item.imageInfoList.length - assets.length;
                                final list = await PickMethod(
                                  maxAssetsCount: max(100 - max(0, inCount), 0),
                                ).forCamera(context);

                                item.changeAssetsOnPicker(list);
                                // eventBus.on<AiEnventImgage>().listen((event) {
                                //   item.images?.add(event.url);
                                // });
                              },
                            ),
                            Expanded(
                              child: AiFormAssetsListView(
                                manager: item,
                                onImageChanged: (res) {
                                  List<String> aa = [];
                                  for (final info in res.imageInfoList.value) {
                                    aa.add(info.url ?? '');
                                  }
                                  item.images = aa;
                                },
                              ),
                            ),
                          ],
                        )
                      ],
                    )),
                Positioned(
                  right: 20,
                  child: IconButton(
                    icon: Icon(Icons.highlight_off),
                    color: MColor.xFFC4C4C4,
                    onPressed: () {
                      controller.editDefectItem?.records?.remove(item);
                      setState(() {});
                    },
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 3,
            ),
            Container(
                padding: EdgeInsets.fromLTRB(24, 8, 12, 8),
                color: Colors.white,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 5,
                    ),
                    Text(
                      S.current.ai_simple_dsec,
                      style: MFont.medium16,
                    ),
                    SizedBox(
                      width: 25,
                    ),
                    Expanded(
                        child: TextField(
                      minLines: 3,
                      maxLines: 18,
                      controller: TextEditingController(text: item.label),
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 5),
                        fillColor: MColor.white,
                        hintText: S.of(Get.context!).ai_simple_add_desc,
                        hintStyle:
                            MFont.regular13.apply(color: DarkColor.xFF999999),
                        enabledBorder: OutlineInputBorder(
                          borderSide:
                              BorderSide(color: MColor.xFFE5E5E5, width: 1),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: MColor.skin, width: 1),
                          borderRadius: BorderRadius.circular(5),
                        ),
                      ),
                      onChanged: (text) {
                        item.label = text;
                      },
                    ))
                  ],
                ))
          ],
        ),
      );
    }

    return StatefulBuilder(builder: (context, setState) {
      return Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          leading: IconButton(onPressed: (){
            save(isNew);
          }, icon: Icon(Icons.arrow_back)),
          title: Text(controller.clickItem!.name! + S.current.ai_simple_record),
          centerTitle: true,
          actions: [
            if (!isNew)
              InkWell(
                onTap: () {
                  showCustomDialog(
                    S.of(context).delete_account_confirm,
                    cancel: true,
                    onConfirm: () {
                      controller.showRecordsList
                          ?.removeAt(controller.showIndex);
                      controller.showIndex = 0;
                      if (controller.showRecordsList!.isNotEmpty) {
                        controller.newAiCheckOutInfo =
                            controller.showRecordsList?[0];
                      } else {
                        controller.newAiCheckOutInfo = null;
                      }
                      Get.back(result: -2);
                    },
                  );
                },
                child: Container(
                    margin: EdgeInsets.only(right: 12),
                    child: Text(
                      S.current.ai_delete,
                      style: TextStyle(color: MColor.skin),
                    )),
              )
          ],
        ),
        body: DefaultTextStyle(
          style: MFont.medium14.copyWith(color: MColor.black),
          child: Column(
            children: [
              Expanded(
                  child: AiEmptyView(
                baseResult: controller.baseResult,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _record(),
                    Expanded(
                        child: SingleChildScrollView(
                            child: Column(
                      children: [
                        for (int i = 0;
                            i <
                                (controller.editDefectItem?.records ?? [])
                                    .length;
                            i++)
                          _item(controller.editDefectItem!.records![i],
                              setState, context, i),
                        _add(setState),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: 20,
                            ),
                            Text(
                              S.current.ai_handling_method,
                              style: MFont.medium18,
                              textAlign: TextAlign.left,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        _builFirst(
                            title: S.current.ai_defect_level,
                            setState: setState),
                        _buildSecond(
                            title: S.current.ai_found_quantity,
                            values: controller.editDefectItem!.totalNum!,
                            setState: setState),
                        _builThree(
                            title: S.current.ai_pick_out,
                            values: controller.editDefectItem!.tiaoChu!,
                            type: 0,
                            setState: setState),
                        _builThree(
                            title: S.current.ai_replace,
                            values: controller.editDefectItem!.tiHuan!,
                            type: 1,
                            setState: setState),
                        _builThree(
                            title: S.current.ai_rework,
                            values: controller.editDefectItem!.fanGong!,
                            type: 2,
                            setState: setState),
                      ],
                    ))),
                    // _judge(),
                    SizedBox(
                      height: 30,
                    )
                  ],
                ),
              )),
              Container(
                  child: AiSubmitButton(
                      onPressed: () {
                        save(isNew);
                      },
                      name: S.current.ai_submit,
                      isSave: false))
              // Container(
              //   margin: EdgeInsets.only(left: 20,right: 20),
              //   child: Row(
              //     children: [
              //       Container(
              //           width: Get.width/2 - 20 ,
              //           child:  AiSubmitButton(
              //               onPressed: (){
              //                 if(controller.calcTotal()){
              //                   controller.goPrevious(setState);
              //                 }
              //
              //               },
              //               name: S.current.ai_simple_before,
              //               isSave:true
              //           )
              //       )
              //       ,
              //       Container(
              //           width: Get.width/2 - 20 ,
              //           child:  AiSubmitButton(
              //               onPressed:(){
              //                 if(controller.calcTotal()){
              //                   controller.addOneDefect(isNew);
              //                 }
              //               },
              //               name: S.current.ai_next_item,
              //               isSave:true
              //           )
              //       )
              //     ],
              //   )
              //   ,
              // )
            ],
          ),
        ),
      );
    });
  }

  save(bool isNew){
    if ((controller.editDefectItem?.level ?? '').isEmpty) {
      showToast(S.current.publish_inspection_time_tip +
          S.current.ai_defect_level);
      return;
    }

    // if(controller.calcTotal()){
    //   if(isQuick){
    //     controller.addOneDefectQuick(isNew);
    //   }else{
    //   }
    //
    // }

    if (controller.calcTotal()) {
      if (controller.isQuick) {
        controller.addOneDefectQuick(true);
      } else {
        if (isNew) {
          controller.addOneDefect(isNew);
        }
        controller.uploadData().then((value) {
          Get.back();
        });
      }
    }
  }
}
