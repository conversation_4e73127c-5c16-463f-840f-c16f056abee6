
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../../../generated/l10n.dart';
import '../../../config/design.dart';
import '../../../tools/tools.dart';
import '../category/entity/ai_recmend_data.dart';
import '../entiy/ai_detail_checout.dart';
import '../entiy/ai_detail_checout_record.dart';
import '../entiy/ai_detail_checout_single.dart';
import '../entiy/ai_detail_defect_checout_info.dart';
import '../../../tools/tools.dart';
import 'package:event_bus/event_bus.dart';


class AiQualityDetailController extends GetxController implements AiDataFeature {
  static AiQualityDetailController get to => Get.find();

  final aiDetail = AiSummaryDetailData(
      id: 0, ruleOrderId: 0, orderId: 0, productId: 0, modelId: 0, type: 5, status: 0)
      .obs;

  List<AiItemForms>? get inputList => (aiDetail.value.details?.config?.forms ?? []);
  List<AiItemEvent>? get eventList => aiDetail.value.details?.config?.event ?? [];


  AiSummaryResult? get result => aiDetail.value.details?.config?.result;
  late var baseResult = AiBaseEntity();


   int type=AiCategoryType.quality.id;
  EventBus eventBus = EventBus();

  @override
  void onInit() {
    type = Get.parameters['type']==null?0: int.parse(Get.parameters['type']!);
    if(Get.arguments['clickItem']!=null){
      clickItem = Get.arguments['clickItem'] as AiItemForms;
    }
    // loadData();
    // search('',null);
    super.onInit();
  }

  AiItemForms? clickItem;// 首页点击的那一项
  List<AiCheckOutInfo>? itemList;// 详情页大list,包含两个list

  late AiDefectCheckOutModel defectCheckOut;
  late AiCheckOutInfo showDefectItem;// 正在显示的那一大项

  List<AiCheckOutInfoSingle>? showRecordsList;// 正在显示的检测列表全部


  AiCheckOutInfoSingle? newAiCheckOutInfo;// 正在显示的检测单个对象

  AiCheckOutDefectInfo? editDefectItem;// 编辑的缺陷对象
  AiCheckOutRecordInfo? editRecordItem;// 编辑的检测记录对象

  bool showMore = false;
  int showIndex = 0;
  void changeItem(index,AiCheckOutInfoSingle item,setState){
    newAiCheckOutInfo = item;
    showIndex = index;
    setState(() {
    });
  }

  int clickIndex = 0;
  void goDetail(index,setState,needGo){
    clickIndex = index;
    clickItem = inputList?[index];
    clickItem?.data ??= AiFormData.empty();
    if(clickItem?.data!.defectCheckOut==null){
      clickItem?.data!.defectCheckOut = AiDefectCheckOutModel.empty();
    }else{
    }

    itemList =  clickItem?.data!.defectCheckOut!.values??[];
    bool isNew = false;
    if(itemList!.isNotEmpty){
      showDefectItem = itemList![0];
    }else{
      isNew = true;
      showDefectItem = AiCheckOutInfo.empty();
      itemList?.add(showDefectItem);
    }

    showRecordsList=  showDefectItem.recordsList;
    if(showRecordsList!.isNotEmpty){
      newAiCheckOutInfo = showRecordsList?[0];
      if(newAiCheckOutInfo!.defectsList !=null &&newAiCheckOutInfo!.defectsList!.isNotEmpty){
        editDefectItem = newAiCheckOutInfo!.defectsList?.firstOrNull;
      }else{
      }
    }else{
      newAiCheckOutInfo=AiCheckOutInfoSingle.empty();
    }
    calCount2();
    if(needGo){
      Get.toNamed(
          AiRoutes.QUALITY_DETAIL,
          arguments: {
            'isNew':isNew
          }
      )?.then((value){
        if(isNew){
        }
        setState((){});
      });
    }else{
      setState((){});
    }

  }


  void goPrevious(setState) {
    if(clickIndex==0){
      showToast(S.current.ai_no_more);
      return;
    }
    clickIndex--;
    goDetail(clickIndex,setState,false);
  }

  void goNext(setState) {
    if(clickIndex==inputList!.length-1){
      showToast(S.current.ai_no_more);
      return;
    }
    clickIndex++;
    goDetail(clickIndex,setState,false);
  }


  void goInput(item,setState){
    bool isNew = true;
    if(item!=null){
      isNew = false;
      editDefectItem = item;
    }else{
      editDefectItem = AiCheckOutDefectInfo.empty('');
    }
    Get.toNamed(
      AiRoutes.QUALITY_ADD,
      arguments: {
        'isNew':isNew
      },
    )?.then((value){
      calCount2();
      setState((){});
      if(value==-1){
      }
    });
  }


  void goInputQuick(item,setState){
    bool isNew = true;
    Get.toNamed(
      AiRoutes.QUALITY_ADD,
      parameters: Get.parameters as Map<String, String>,
      arguments: {
        'isNew':isNew,
        'isQuick':true,
      },
    )?.then((value){
      setState((){});
      if(value==10){
        editDefectItem = AiCheckOutDefectInfo.empty('');
        loadData();
        setState((){

        });
      }
    });
  }

  void addOneRecords(bool isNew) {
    List<AiCheckOutRecordInfo>? records=[];
    for(var item in newAiCheckOutInfo!.recordsList!){
      if(item.images!.isEmpty && item.label!.isEmpty ){
      }else{
        records.add(item);
      }
    }
    newAiCheckOutInfo?.recordsList = records;
    if(isNew){
      if(records.isNotEmpty){
        showRecordsList?.add(newAiCheckOutInfo!);
      }
    }

  }

  void addOneDefect(bool isNew) {
    if(calcTotal()){
      List<AiCheckOutDefectInfo>? records=[];
      for(var item in editDefectItem!.records!){
        if(item.images!.isEmpty && item.label!.isEmpty ){
        }else{
          records.add(item);
        }
      }
      editDefectItem?.records = records;
      if(isNew){
        AiCheckOutInfoSingle single = AiCheckOutInfoSingle.empty();
        single.defectsList?.add(editDefectItem!);
        showRecordsList?.add(single);

      }else{

      }
      newAiCheckOutInfo = showRecordsList?.first;
      // showRecordsList?.add(newAiCheckOutInfo!);
      Get.back();
    }
  }

  void addOneDefectQuick(bool isNew) {
    if(calcTotal()){
      List<AiCheckOutDefectInfo>? records=[];
      for(var item in editDefectItem!.records!){
        if(item.images!.isEmpty && item.label!.isEmpty ){
        }else{
          records.add(item);
        }
      }
      editDefectItem?.records = records;
      if(isNew){
        newAiCheckOutInfo?.defectsList?.add(editDefectItem!);
      }
      showRecordsList?.add(newAiCheckOutInfo!);
      uploadData().then((value) {
        Get.back(result: 10);
      });
    }
  }

  void goEdit(item,setState){
    clickItem = item;
    Get.toNamed(
      AiRoutes.QUALITY_EDIT,
      arguments: {
      },
    )?.then((value){
      setState(() {
      });
      if(value==-1){
        Get.back(result: -1);
      }
    });
  }

  void goRecmend(){
    print('typetypetypetype==='+type.toString());
    Get.toNamed(
      AiRoutes.AT_RECMEND,
      arguments: {
        'type':type
      },
    )?.then((value){
      if(value!=null && value['data']!=null){
        inputList?.addAll(value['data']);
        Get.back(result: -1);
      }
    });
  }

  void clickRecItem(item){
    item.isSelected = !item.isSelected;
    update();
  }
  String searchKeywords = '';
  final recmendData = AiBaseEntity<List<AiRecmendData>>(data: []).obs;
  List<AiRecmendData> get recmendList => recmendData.value.data ?? [];
  void search(keyword,setState){
    AiProvider()
        .itemSearch(
        searchKeywords,
        '1',
        type.toString()
    )
        .then((value) {
      if(value.data!=null){
        recmendData.value =value;
      }
      if(setState!=null){
        setState((){});
      }
    });
  }


  void addRec(){
    List<AiItemForms> aa= [];
    for(var item in recmendList){
      if(item.isSelected){
        final now = DateTime.now();
        AiItemForms child = AiItemForms(key:'form${now.day}${now.hour}${now.minute}' );
        child.tips=AiModelInfo.empty();
        child.name = item.itemName;
        child.tips!.values =[item.remark??''] ;
        aa.add(child);
      }
    }
    inputList?.addAll( aa);
    Get.back(result:{
      'data':aa,
      result:-1
    });
  }

  calCount( List<AiCheckOutInfoSingle> countItem){
    int totalNum1=0;
    int totalNum2=0;
    int totalNum3=0;
    for(var parent in countItem){
      for(var item in parent.defectsList!){
        if(item.level=='aql_1'){// 关键
          totalNum1=totalNum1+(item.totalNum==null?0: int.parse(item.totalNum!));
        }else if(item.level=='aql_2'){// 关键
          totalNum2=totalNum2+(item.totalNum==null?0: int.parse(item.totalNum!));
        }else if(item.level=='aql_3'){// 关键
          totalNum3=totalNum3+(item.totalNum==null?0: int.parse(item.totalNum!));
        }
      }
    }
    return '$totalNum1/$totalNum2/$totalNum3';
  }

  Map mapTotal = {};
  void calCount2(){
    int totalNum1=0;
    int totalNum2=0;
    int totalNum3=0;

    int tiaochu1=0;
    int tiaochu2=0;
    int tiaochu3=0;

    int tihuan1=0;
    int tihuan2=0;
    int tihuan3=0;

    int fangong1=0;
    int fangong2=0;
    int fangong3=0;

    for(var parent in showRecordsList!){
      for(var item in parent.defectsList!){
      print('item.level='+item!.level!+'    ~~~'+item.tiaoChu.toString());
      if(item.level=='aql_1'){// 关键
        totalNum1=totalNum1+(item.totalNum==null?0: int.parse(item.totalNum!));
        tiaochu1=tiaochu1+(item.tiaoChu==null?0: int.parse(item.tiaoChu!));
        tihuan1=tihuan1+(item.tiHuan==null?0: int.parse(item.tiHuan!));
        fangong1=fangong1+(item.fanGong==null?0: int.parse(item.fanGong!));
      }else if(item.level=='aql_2'){// 关键
        totalNum2=totalNum2+(item.totalNum==null?0: int.parse(item.totalNum!));
        tiaochu2=tiaochu2+(item.tiaoChu==null?0: int.parse(item.tiaoChu!));
        tihuan2=tihuan2+(item.tiHuan==null?0: int.parse(item.tiHuan!));
        fangong2=fangong2+(item.fanGong==null?0: int.parse(item.fanGong!));
      }else if(item.level=='aql_3'){// 关键
        totalNum3=totalNum3+(item.totalNum==null?0: int.parse(item.totalNum!));
        tiaochu3=tiaochu3+(item.tiaoChu==null?0: int.parse(item.tiaoChu!));
        tihuan3=tihuan3+(item.tiHuan==null?0: int.parse(item.tiHuan!));
        fangong3=fangong3+(item.fanGong==null?0: int.parse(item.fanGong!));
      }
      }
    }

    mapTotal['totalNum1'] =  totalNum1.toString();
    mapTotal['totalNum2'] =  totalNum2.toString();
    mapTotal['totalNum3'] =  totalNum3.toString();
    mapTotal['fanGong1'] =  fangong1.toString();
    mapTotal['fanGong2'] =  fangong2.toString();
    mapTotal['fanGong3'] =  fangong3.toString();

    mapTotal['tiHuan1'] =  tihuan1.toString();
    mapTotal['tiHuan2'] =  tihuan2.toString();
    mapTotal['tiHuan3'] =  tihuan3.toString();

    mapTotal['tiaoChu1'] =  tiaochu1.toString();
    mapTotal['tiaoChu2'] =  tiaochu2.toString();
    mapTotal['tiaoChu3'] =  tiaochu3.toString();

  }


  bool calcTotal(){
    int tiaochu = editDefectItem!.tiaoChu!.isEmpty?0:int.parse(editDefectItem!.tiaoChu!);
    int tihuan = editDefectItem!.tiHuan!.isEmpty?0:int.parse(editDefectItem!.tiHuan!);
    int fangong = editDefectItem!.fanGong!.isEmpty?0: int.parse(editDefectItem!.fanGong!);
    int totalNum =int.parse(editDefectItem!.totalNum!);
    if(tiaochu>totalNum){
      showToast(S.current.ai_wrong_tip);
      return false;
    }
    if(tihuan>totalNum){
      showToast(S.current.ai_wrong_tip);
      return false;
    }
    if(fangong>totalNum){
      showToast(S.current.ai_wrong_tip);
      return false;
    }
    if(tiaochu+tihuan+fangong>totalNum){
      showToast(S.current.ai_wrong_tip);
      return false;
    }
    editDefectItem?.refreshEditTime();
    return true;
  }

  void goBack(){
    uploadData().then((value){
      Get.back();
    });

  }

  void addInputItem(AiItemForms form) {
    aiDetail.value.details?.config?.addInputItem(form);
  }

  void removeInputItem(AiItemForms form) {
    aiDetail.value.details?.config?.removeInputItem(form);
    aiDetail.refresh();
  }

  void toNext() {
    // final nextType = AiCategoryController.to.nextType(fromType: type);
    // Map<String, String> p = {}
    //   ..addAll(Get.parameters as Map<String, String>)
    //   ..['type'] = nextType.id.toString();

    uploadData().then((value) {
      Get.offAndToNamed(AiRoutes.QUALITY_RESULT,
          parameters: Get.parameters as Map<String, String>);
    });
  }


  final simpleReadOnly = true.obs;
  final level = ''.obs;
  final levelNum = ''.obs;
  void showSheet(){
    Get.bottomSheet(
        Container(
            color: Colors.white,
            height: 300,
            child:
            ListView.separated(
              itemBuilder: (context, index) =>InkWell(
                  onTap: (){
                    if(AICheckoutSampleLevelType.values[index].name=='自定义'){
                      level.value = 'E';
                      simpleReadOnly.value = false;
                      update();
                    }else{
                      simpleReadOnly.value = true;
                      level.value = AICheckoutSampleLevelType.values[index].name;
                      changeSampleLevel(AICheckoutSampleLevelType.values[index].name);
                    }

                    Get.back();
                  },
                  child: Container(
                      padding: EdgeInsets.only(top: 10,bottom: 10),
                      child: Text(AICheckoutSampleLevelType.values[index].name,textAlign: TextAlign.center,)
                  )
              )   ,
              itemCount: AICheckoutSampleLevelType.values.length,
              separatorBuilder: (BuildContext context, int index) => Divider(
                color:  MColor.xFFE5E5E5,
                height: 1,
              ),)
        )
    );
  }

  void changeSampleLevel(String value) {
    AiProvider()
        .getSampleNum(
        level: value, orderNum: AiCategoryController.to.orderNum.toString())
        .then((value) {

      levelNum.value = value.data.toString();
      refresh();
    });
  }

  final numCount = ''.obs;
  final levelCount = ''.obs;




  @override
  void loadData() {
    AiProvider()
        .getAiCategoryDetail(
        orderId: AiCategoryController.to.orderId,
        productId: AiCategoryController.to.productId,
        modelId: AiCategoryController.to.modelId,
        type: AiCategoryType.quality.id,
        orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      baseResult = value;
      if (value.data != null) {
        aiDetail.value = value.data!;
      }
    });
  }

  @override
  Future<AiBaseEntity> uploadData() {
    if (aiDetail.value.details == null) {
      return Future.value(AiBaseEntity(code: 200));
    }
    aiDetail.value.details?.config?.uploadImageIfNeed();
    return AiProvider().uploadConfig(id: aiDetail.value.id, details: aiDetail.value.details!);
  }
}
