import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/package/ai_package_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_controller.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';
import '../ai_pages.dart';
import '../entiy/ai_summary_detail_data.dart';
import 'ai_quality_quick_controller.dart';

class AiQualityQuickView<T extends AiQualityController> extends GetView<T> {
  const AiQualityQuickView({super.key});

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(S.current.ai_defect_quick),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: StatefulBuilder(builder: (context, setState) {
          return Column(
            children: [
              Expanded(
                child: Obx(() {
                  return AiEmptyView(
                    baseResult: controller.baseResult,
                    child: ListView(
                      children: [
                        AiPackageChecking(
                          tag: S.current.ai_defect_record_list,
                          showResult: false,
                          defects:[],
                          itemNames: (controller.inputList ?? []),
                          onClickItem: (index){
                            // controller.reset();
                            controller.goDetail(index,setState,true);
                          },
                          onAdded: (value) async {
                            await Get.toNamed(
                              AiRoutes.QUALITY_MANAGE,
                              arguments: {
                                'type':'quality'
                              },
                              parameters: Get.parameters as Map<String, String>,
                            )?.then((value){
                              setState((){});
                            });
                          },
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 20,right: 20),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                S.current.ai_defect_self,
                                style: MFont.medium18,
                              ),
                              Spacer(),
                              InkWell(
                                onTap: (){
                                  setState((){
                                    // controller.reset();
                                  });

                                },
                                child: Container(
                                  padding: EdgeInsets.only(left: 10,right: 10,top: 4,bottom: 4),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      color: MColor.skin
                                  ),
                                  child: Text(S.current.ai_add_plus,style: TextStyle(
                                      color: Colors.white
                                  ),),
                                )
                              )

                            ],
                          ),
                        ),
                        SizedBox(height: 10,),
                        _question(),
                        _instruction(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            InkWell(
                                onTap: (){
                                  controller.goRecmend();
                                },
                                child:  Container(
                                    margin: EdgeInsets.only(top: 20),
                                    width: 110,
                                    height: 40,
                                    padding: EdgeInsets.fromLTRB(12, 6, 12, 6),
                                    decoration: BoxDecoration(
                                        color: MColor.white,
                                        borderRadius: BorderRadius.circular(20)
                                    ),
                                    child: Row(
                                      children: [
                                        Image.asset('assets/images/ai.png',width: 19,),
                                        SizedBox(width: 8,),
                                        Text(S.current.ai_simple_project_recmend,style: TextStyle(
                                            color:  MColor.skin
                                        ),)
                                      ],
                                    )
                                )
                            )
                          ],
                        )

                      ],
                    ),
                  );
                }),
              ),
              AiSubmitButton(
                onPressed: (){
                  if(controller.clickItem!.name!.isEmpty){
                    showToast(S.current.apply_enter+S.current.ai_defect_names);
                    return;
                  }
                  // controller.reset();
                  controller.goInputQuick(controller.editDefectItem, setState);
                },
                name: S.current.publish_next,
                isSave: true,
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _question(){
    return Container(
      padding: EdgeInsets.fromLTRB(20, 12, 20, 12),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(S.current.ai_defect_question),
          SizedBox(width: 30,),

          Expanded(child: TextField(
            textAlign: TextAlign.start,
            controller: TextEditingController(text:controller.clickItem?.name??''),
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: S.current.apply_enter,
              isDense:true,
              contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
            ),
            onChanged: (value){
              controller.clickItem?.name=value;
            },
          ))
          ,
        ],
      )
      ,
    );
  }

  String getTips(){
    if(controller.clickItem != null){
      if(controller.clickItem?.tips != null){
        if(controller.clickItem?.tips?.values != null){
          if(controller.clickItem!.tips!.values!.isNotEmpty){
            return controller.clickItem!.tips!.values!.single;
          }
        }
      }
    }
    return '';
  }

  Widget _instruction(){
    return Container(
      margin: EdgeInsets.only(top: 3),
      padding: EdgeInsets.fromLTRB(20, 8, 20, 8),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(S.current.general_desc),
          SizedBox(width: 60,),
          Expanded(child: TextField(
            textAlign: TextAlign.start,
            minLines: 3,
            maxLines: 8,
            //controller: TextEditingController(text:controller.clickItem?.tips!.values!.isNotEmpty?controller.clickItem!.tips!.values![0]:''),
            controller: TextEditingController(text:getTips()),
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: S.current.apply_enter,
              contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
            ),
            onChanged: (value){
              controller.clickItem?.tips?.values = [];
              controller.clickItem?.tips?.values?.add(value);
            },
          ),

          )
          ,
        ],
      )
      ,
    );
  }
}
