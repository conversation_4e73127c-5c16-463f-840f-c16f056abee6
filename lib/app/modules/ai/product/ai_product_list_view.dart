import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/data/public_model.dart';

import '../../../routes/app_pages.dart';
import '../category/controller/ai_category_controller.dart';

class ProductListView extends StatefulWidget{

  final int id;

  const ProductListView({required this.id, super.key});

  @override
  State<StatefulWidget> createState() => ProductListViewState();

}

class ProductListViewState extends State<ProductListView>{

  RefreshController refreshController = RefreshController();

  OrderProvider orderProvider = OrderProvider();

  List<OrderProduct> productList = [];

  TextEditingController editingController = TextEditingController();

  @override
  initState(){
    super.initState();
    loadData();
  }

  Future<void> loadData() async{
    BaseModel<OrderDetailEntity> result =  await orderProvider.takeOrderDetail(widget.id);
    if(result.isSuccess && result.data != null){
      setState(() {
        productList = result.data?.orderProducts??[];
      });


      // if(productList!=null && productList.isNotEmpty){
      //   for(OrderProduct item in productList){
      //     for(ProductModel model in item.productModel??[]){
      //       if(model.id ==AiCategoryController.to.modelId && AiCategoryController.to.orderId==model.orderId){
      //         AiCategoryController.to.currentModel.value = AiCategoryNameModel(item.productName!,model,item.productName!);
      //           break;
      //     }
      //   }
      // }
      // }
    }else{
      showToast('${result.message}');
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).ai_default_config_product_list),
        centerTitle: true,
        actions: [
          IconButton(onPressed: showAddDialog, icon: Icon(Icons.add_box_outlined,color: MColor.skin,))
        ],
      ),
      body: Column(
        children: [
          SizedBox(height: 16,),
          Expanded(child: ListView.separated(
            itemCount: productList.length,
            padding: const EdgeInsets.only(bottom: 12),
            itemBuilder: (ctx, index) {
              OrderProduct product = productList[index];
              return Material(
                color: Colors.white,
                child: ListTile(
                  onTap: (){
                    Get.toNamed(Routes.ADD_PRODUCT_INFO, arguments: {'orderId': widget.id, 'product': product})!.then((value) {
                      loadData();
                    });

                  },
                  title: Text('${product.productName}'),
                  trailing: Icon(Icons.arrow_forward_ios_rounded),
                  subtitle: modelWidget(product.productModel),
                ),
              );
            },
            separatorBuilder: (BuildContext context, int index) => Container(height: 0.5,color: Colors.grey,),
          ))
        ],
      ),
    );
  }

  Widget modelWidget(List<ProductModel>? modelList){

    if(modelList == null || modelList.isEmpty){
      return const Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Text('型号 缺失',style: TextStyle(color: Colors.redAccent),),
          Spacer(),
          Text('数量/单位 缺失',style: TextStyle(color: Colors.redAccent),),
        ],
      );
    }
    return ListView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: modelList.length,
        itemBuilder: (ctx, index){
          ProductModel? model = modelList[index];
          String name = '${model.mark}';
          String amount = '${model.amount}';
          String unit2 = '${model.unit}';
          String unit = '${model.amount}${model.unit}';
          Color modelTextColor = Colors.grey;
          Color modelNumColor = Colors.blue;
          if(name.isEmpty){
            name = '型号 缺失';
            modelTextColor = Colors.redAccent;
          }

          if( unit2.isEmpty ||amount.isEmpty  ){
            unit = '数量/单位 缺失';
            modelNumColor = Colors.redAccent;
          }
          return Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              Text(name,style: TextStyle(color: modelTextColor),),
              Spacer(),
              Text(unit,style: TextStyle(color: modelNumColor),),
            ],
          );
        }
    );
  }

  showAddDialog(){
    showCustomDialog(
      S.current.ai_add_product_product_name,
      textController: editingController,
      textPlaceHolder: S.current.ai_add_product_input_product_name,
      cancel: true,
      onConfirm: () {
        if(editingController.text==''){
          showToast(S.current.ai_wrong_name);
          return ;
        }
        orderProvider.addProduct(orderId: widget.id, productName: editingController.text).then((value) {
          if (value.isSuccess) {
            editingController.text = '';
          }else{
            showToast('${value.message}');
          }
          loadData();
        },
      );
    },
    );
  }

  @override
  void dispose() {
    refreshController.dispose();
    editingController.dispose();
    super.dispose();
  }
}