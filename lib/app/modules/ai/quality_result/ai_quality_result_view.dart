import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/checkout_result/ai_checkout_result_view.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/extension/table_border_extension.dart';
import 'package:inspector/app/modules/ai/quality_result/ai_quality_result_controller.dart';
import 'package:inspector/app/modules/ai/widgets/ai_summary_result.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';

class AiQualityResultView extends GetView<AiQualityResultController> {
  const AiQualityResultView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(AiCategoryType.quality.name),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: Column(
          children: [
            AiProductSelectBar(loader: controller),
            Expanded(child: Obx(() {
              return AiEmptyView(
                baseResult: controller.baseResult.value,
                child: ListView(
                  children: [
                    _AiQualityDefectResultView(),
                    SizedBox(height: 10),
                    _AiQualityJudgeResultView(),
                    Obx(() {
                      return AiSummaryResultView(
                        isSame: controller.result?.isSame,
                        note: controller.result?.remark,
                        noteChange: (value) => controller.result?.remark = value,
                        resultChange: (value) => controller.result?.changeResult(value),
                      );
                    })
                  ],
                ),
              );
            })),
            AiSubmitButton.submit(
              onPressed: controller.toNext,
            ),
          ],
        ),
      ),
    );
  }
}

class _AiQualityDefectResultView extends GetView<AiQualityResultController> {
  const _AiQualityDefectResultView({super.key});

  @override
  Widget build(BuildContext context) {

    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      color: MColor.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: Text(
              S.of(context).ai_defect_list,
              style: MFont.medium18,
            ),
          ),
          Obx(() {
            return Table(
              border: TableBorder.all(color: MColor.xFFE5E5E5),
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              columnWidths: const {
                0: FlexColumnWidth(),
                1: FixedColumnWidth(57),
                2: FixedColumnWidth(57),
                3: FixedColumnWidth(57),
              },
              children: [
                TableRow(decoration: BoxDecoration(color: MColor.xFFFFF3F0), children: [
                  for (var o in [
                    S.of(context).ai_defect_description,
                    S.of(context).ai_critical,
                    S.of(context).ai_important,
                    S.of(context).ai_minor,
                  ])
                    aiResultCell(
                      name: o,
                      height: 38,
                    ),
                ]),
                for (int i=0;i<(controller.inputList??[]).length;i++)
                  if(controller.inputList?[i].data!=null
                  )
                    // if(controller.inputList?[i].data!=null && controller.inputList?[i].data!.defectCheckOut!=null&&controller.inputList?[i].data!.defectCheckOut!.values!=null
                    //     && controller.inputList![i].data!.defectCheckOut!.values!.isNotEmpty
                    //     && controller.inputList![i].data!.defectCheckOut!.values!.first.recordsList!.isNotEmpty
                    // )
                  TableRow(children: [
                    aiResultCell(name: controller.inputList![i].name??''),
                    aiResultCell(name: controller.countList[i]['aql_1'] ?? '0'),
                    aiResultCell(name: controller.countList[i]['aql_2'] ?? '0'),
                    aiResultCell(name: controller.countList[i]['aql_3'] ?? '0'),
                  ]
                  ),

              ],
            );
          }),
        ],
      ),
    );
  }


}

class _AiQualityJudgeResultView extends GetView<AiQualityResultController> {
  const _AiQualityJudgeResultView({super.key});

  @override
  Widget build(BuildContext context) {
    final sampleLevelObs = controller.table?.forms?.first.sampleLevelObs;
    final sampleCountObs = controller.table?.forms?.first.sampleNumObs;

    final level = sampleLevelObs?.value ?? '';
    print('level======'+level.toString());
    if (level.isEmpty || level == AICheckoutSampleLevelType.custom.value) {
      // return SizedBox.shrink();
    }
    final color = MColor.xFFE5E5E5;

    final noBottomBorder = TableBorderExt.without(color: color, bottom: true);
    final noHBorder = TableBorderExt.without(color: color, right: true, left: true);
    final titleHeight = 74.0;
    final borderAll = TableBorder.all(color: color);
    final rowDecoration = BoxDecoration(color: MColor.xFFFFF3F0);

    Map<int, FlexColumnWidth> titleWidths = {
      0: FlexColumnWidth(1),
      1: FlexColumnWidth(1),
      2: FlexColumnWidth(1),
      3: FlexColumnWidth(1),
      4: FlexColumnWidth(1),
      5: FlexColumnWidth(1),
    };
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      margin: EdgeInsets.only(bottom: 10),
      color: MColor.white,
      child: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 15),
              child: Text(
                S.of(context).ai_judgment,
                style: MFont.medium18,
              ),
            ),
            //标题
            Table(
              border: noBottomBorder,
              columnWidths: {
                0: titleWidths[0]!,
                1: FlexColumnWidth(titleWidths[1]!.value +
                    titleWidths[2]!.value +
                    titleWidths[3]!.value +
                    titleWidths[4]!.value),
                2: titleWidths[5]!,
              },
              children: [
                TableRow(decoration: rowDecoration, children: [
                  aiResultCell(name: ''),
                  Table(
                    children: [
                      TableRow(decoration: rowDecoration, children: [
                        aiResultCell(name: S.of(context).ai_standard+'ANSl/ASQZ1.4(MIL-STD-105E)', height: titleHeight / 2)
                      ]),
                      TableRow(decoration: rowDecoration, children: [
                        Table(
                          border: TableBorderExt.only(color: color, top: true),
                          children: [
                            TableRow(children: [
                              aiResultCell(
                                  name: S.of(context).ai_sampling_level, height: titleHeight / 2),
                              aiResultCell(
                                  name: S.of(context).ai_sample_count, height: titleHeight / 2),
                              aiResultCell(name: 'AQL', height: titleHeight / 2),
                              aiResultCell(
                                  name: S.of(context).ai_maximum_allowable_value,
                                  height: titleHeight / 2),
                            ])
                          ],
                        )
                      ])
                    ],
                  ),
                  aiResultCell(name: S.of(context).ai_result, height: titleHeight),
                ]),
              ],
            ),
            //值
            Table(
              border: borderAll,
              columnWidths: {
                0: titleWidths[0]!,
                1: FlexColumnWidth(titleWidths[1]!.value + titleWidths[2]!.value),
                2: FlexColumnWidth(
                    titleWidths[3]!.value + titleWidths[4]!.value + titleWidths[5]!.value),
              },
              children: [
                TableRow(children: [
                  //值标题
                  Table(border: noHBorder, columnWidths: {
                    0: titleWidths[0]!,
                  }, children: [
                    for (int i = 0; i < AiCheckoutDefectLevelType.values.length; i++)
                      TableRow(children: [
                        aiResultCell(
                            name: AiCheckoutDefectLevelType.values[i].name,
                            pass: controller.isPassLeve(i)),
                      ]),
                  ]),
                  //值 样本等级/样本数
                  Table(border: noHBorder, columnWidths: {
                    0: titleWidths[1]!,
                    1: titleWidths[2]!,
                  }, children: [
                    TableRow(children: [
                      Obx(() {
                        return buildCell<String>(
                            onSelected: (value) {
                              controller.table?.forms?.first.changeSampleLevel(
                                value,
                                callback: () {
                                  for (int i = 0;
                                      i < AiCheckoutDefectLevelType.values.length;
                                      i++) {
                                    controller.table
                                        ?.getAqlAcNum(i, controller.table?.aqlList?[i] ?? '', sampleLevelObs?.value ?? '')
                                        .then((value) {
                                    bool isSucc=      controller.isPassLeve(i);
                                    controller.result?.status = isSucc?1:2;
                                    print('isSucc==='+isSucc.toString());
                                      controller.aiDetail.refresh();
                                    });
                                  }
                                },
                              );
                            },
                            name: AICheckoutSampleLevelType.getName(sampleLevelObs?.value ?? ''),
                            labels: (List<AICheckoutSampleLevelType>.from(
                                    AICheckoutSampleLevelType.values)
                                  ..removeLast())
                                .map((e) => e.name)
                                .toList(),
                            values: (List<AICheckoutSampleLevelType>.from(
                                    AICheckoutSampleLevelType.values)
                                  ..removeLast())
                                .map((e) => e.value)
                                .toList(),
                            height: 43 * 3);
                      }),
                      Obx(() {
                        return aiResultCell(
                            name: sampleCountObs?.value ?? '', pass: true, height: 43 * 3);
                      })
                    ]),
                  ]),
                  //值 aql/最大值/结果
                  Table(border: noHBorder, columnWidths: {
                    0: titleWidths[3]!,
                    1: titleWidths[4]!,
                    2: titleWidths[5]!,
                  }, children: [
                    for (int i = 0; i < AiCheckoutDefectLevelType.values.length; i++)
                      TableRow(children: [
                        buildAqlCell(
                            onSelected: (value) {
                              controller.table
                                  ?.getAqlAcNum(i, value, sampleLevelObs?.value ?? '')
                                  .then(
                                (value) {
                                  // bool isSucc=      controller.isPassLeve(i);
                                  // controller.result?.status = isSucc?1:2;
                                  // print('isSucc==='+isSucc.toString());
                                  controller.aiDetail.refresh();
                                },
                              );
                            },
                            name: controller.table?.aqlList?[i] ?? '',
                            pass: controller.isPassLeve(i)),

                        aiResultCell(
                            name: controller.table?.aqlAcNumList?[i] ?? '',
                            pass: controller.isPassLeve(i)),
                        aiResultCell(
                            // name: controller.getTotalMap[AiCheckoutDefectLevelType.values[i].value]
                            //         ?.toString() ??
                            //     '0',
                            name: controller.resultList[i]['aql']
                                ?.toString() ??
                                '0',

                            pass: controller.isPassLeve(i)),
                      ]),
                  ]),
                ])
              ],
            )
          ],
        );
      }),
    );
  }


  Widget buildAqlCell({
    required String name,
    required ValueChanged<String> onSelected,
    required bool pass,
  }) {
    final aqlList = [
      "0",
      "0.65",
      "1.0",
      "1.5",
      "2.5",
      "4.0",
      "0.010",
      "0.015",
      "0.025",
      "0.04",
      "0.065",
      "0.1",
      "0.15",
      "0.25",
      "0.4",
      "6.5",
      "10",
      "15",
      "25",
      "40",
      "65",
      "100",
      "150",
      "250",
      "400",
      "650",
      "1000",
    ];
    return buildCell<String>(
      name: name,
      onSelected: onSelected,
      pass: pass,
      labels: aqlList,
      values: aqlList,
    );
  }

  Widget buildCell<T>({
    required String name,
    required ValueChanged<T> onSelected,
    required List<String> labels,
    required List<T> values,
    double? height,
    bool pass = true,
  }) {
    return SizedBox(
      height: height,
      child: PopupMenuButton<T>(
        enabled: false,
        constraints: BoxConstraints(maxWidth: 100, maxHeight: 200),
        onSelected: onSelected,
        color: MColor.xFFFAFAFA,
        itemBuilder: (context) {
          return <PopupMenuEntry<T>>[
            for (var i = 0; i < values.length; ++i) ...[
              PopupMenuItem<T>(value: values[i], child: Text(labels[i])),
              PopupMenuDivider(height: 1),
            ]
          ]..removeLast();
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            aiResultCell(
              name: name,
              pass: pass,
            ),
            Icon(
              Icons.arrow_drop_down_outlined,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
