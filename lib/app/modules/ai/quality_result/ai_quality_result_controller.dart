import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/defect_mixin.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/detail_mixin.dart';
import 'package:inspector/app/tools/tools.dart';

import '../entiy/ai_detail_checout_single.dart';

class AiQualityResultController extends GetxController
    with ConfigMixin, DefectMixin
    implements AiDataFeature {
  final aiDetail = AiSummaryDetailData(
          id: 0, ruleOrderId: 0, orderId: 0, productId: 0, modelId: 0, type: 5, status: 0)
      .obs;

  List<AiItemForms>? get inputList => aiDetail.value.details?.config?.forms;
  List<AiItemForms> get standardList => aiDetail.value.details?.config?.table?.forms ?? [];
  AiCategoryConfig? get table => aiDetail.value.details?.config?.table;

  @override
  List<AiDetailDefectInfo>? get defects => aiDetail.value.details?.config?.defects;
  AiSummaryResult? get result => aiDetail.value.details?.config?.result;
  late final baseResult = AiBaseEntity().obs;

  @override
  void onInit() {
    loadData();

    super.onInit();
  }

  // bool isPassLeve(int index) {
  //   return (int.tryParse(table?.aqlAcNumList?[index] ?? '0') ?? 0) >=
  //       (getTotalMap[AiCheckoutDefectLevelType.values[index].value] ?? 0);
  // }

  bool isPassLeve(int index) {
    int aql1 = 0;
    int aql2 = 0;
    int aql3 = 0;
    for( Map map in countList){
      aql1 = aql1+int.parse(map['aql_1']);
      aql2 = aql2+int.parse(map['aql_2']);
      aql3 = aql3+ int.parse(map['aql_3']);
    }
    int diffNum = 0;
    if(index==0){
      diffNum = aql1;
    }else  if(index==1){
      diffNum = aql2;
    }else  if(index==2){
      diffNum = aql3;
    }
    bool isSucc = (int.tryParse(table?.aqlAcNumList?[index] ?? '0') ?? 0) >=
        diffNum;

    return isSucc;
  }


  void toNext() {
    logger.e('-------2222-------------');
    logger.e(Get.parameters);
    logger.e('------2222--------------');
    // {"orderId":"29718","productId":"32266","modelId":"32161","type":"5"}

    uploadData().then((value) {
      Get.offAndToNamed(
        AiCategoryController.to.nextType(fromType: AiCategoryType.quality.id).location,
        parameters: Get.parameters as Map<String, String>,
      );
    });
  }



  Map calCount( List<AiCheckOutInfoSingle> countItem){
    Map aa = {'aql_1':'0',
      'aql_2':'0',
      'aql_3':'0',};
    if(countItem==null || countItem.isEmpty){
      return aa;
    }

    int totalNum1=0;
    int totalNum2=0;
    int totalNum3=0;
    for(var parent in countItem){
      for(var item in parent.defectsList!){
        if(item.level=='aql_1'){// 关键
          totalNum1=totalNum1+(item.totalNum==null?0: int.parse(item.totalNum!));
          aa['aql_1'] = totalNum1.toString();
        }else if(item.level=='aql_2'){// 关键
          totalNum2=totalNum2+(item.totalNum==null?0: int.parse(item.totalNum!));
          aa['aql_2'] = totalNum2.toString();
        }else if(item.level=='aql_3'){// 关键
          totalNum3=totalNum3+(item.totalNum==null?0: int.parse(item.totalNum!));
          aa['aql_3'] = totalNum3.toString();
        }
      }
    }
    return aa;
  }

  List<Map> countList= [];
  List<Map> resultList = [];
   getResult(){
     int aql_1 = 0;
     num aql_2 = 0;
     num aql_3 = 0;
    for(var item in countList){
      // controller.countList[i]['aql_1']
      print('@@@@@@@@@@@@@@');
      print(item['aql_1']);
      print('@@@@@@@@@@@@@@');
      aql_1 += int.tryParse(item['aql_1']?.toString() ?? '0') ?? 0;
      aql_2 += num.tryParse(item['aql_2']?.toString() ?? '0') ?? 0;
      aql_3 += num.tryParse(item['aql_3']?.toString() ?? '0') ?? 0;
    }
    resultList= [
      { 'aql':aql_1,},{ 'aql':aql_2,},{ 'aql':aql_3,},
    ];

  }

  @override
  void loadData() {
    AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: AiCategoryType.quality.id,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      baseResult.value = value;
      if (value.data != null) {
        aiDetail.value = value.data!;

        for(var item in inputList??[]){
          if(item.data==null || item.data!.defectCheckOut==null){
            countList.add({
              'aql_1':'0',
              'aql_2':'0',
              'aql_3':'0',
            });
          }else{
            countList.add(calCount(item.data!.defectCheckOut!.values![0].recordsList!)) ;
          }
        }
        getResult();

      }
    });
  }

  @override
  Future<AiBaseEntity> uploadData() {
    if (aiDetail.value.details == null) {
      return Future.value(AiBaseEntity(code: 200));
    }
    aiDetail.value.details?.config?.uploadImageIfNeed();
    return AiProvider().uploadConfig(id: aiDetail.value.id, details: aiDetail.value.details!);
  }
}
