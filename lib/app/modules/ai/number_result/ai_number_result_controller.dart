import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';

class AiNumberResultController extends GetxController implements AiDataFeature {
  final aiDetail = AiSummaryDetailData(
          id: 0,
          ruleOrderId: 0,
          orderId: 0,
          productId: 0,
          modelId: 0,
          type: 0,
          status: 0)
      .obs;
  late var baseResult = AiBaseEntity();

  List<AiItemForms>? get inputList =>
      aiDetail.value.details?.config?.forms ?? [];
  List<AiItemEvent>? get eventList =>
      aiDetail.value.details?.config?.event ?? [];
  AiSummaryResult? get result => aiDetail.value.details?.config?.result;

  @override
  void onInit() {
    loadData();
    super.onInit();
  }

  void toNext() {
    uploadData().then(
      (value) {
        Get.offAndToNamed(
          AiCategoryController.to
              .nextType(fromType: AiCategoryType.number.id)
              .location,
          parameters: (Get.parameters as Map<String, String>)
            ..['type'] = AiCategoryType.pack.id.toString(),
        );
      },
    );
  }

  int getUnTotal() {
    int packagedNums = AiCategoryController.to.orderNum -
        getValueNum('packagedNum') * getValueNum('packagedUnit') -
        getValueNum('unpackagedNum');

    return packagedNums < 0 ? 0 : packagedNums;
  }

  int getValueNum(key) {
    for (AiItemForms item in inputList ?? []) {
      if (item.key == key) {
        if (item.values == null || item.values!.isEmpty) {
          return 0;
        } else {
          return int.parse(item.values![0].isEmpty ? '0' : item.values![0]);
        }
      }
    }
    return 0;
  }

  void save() {
    uploadData().then((value) {
      Get.back(result: -1);
    });
  }

  @override
  void loadData() {
    AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: AiCategoryType.number.id,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      baseResult = value;
      if (value.data != null) {
        aiDetail.value = value.data!;
        double rate =
            getValueNum('packagedTotal') / AiCategoryController.to.orderNum;
        rate = (rate * 100);

        for (AiItemEvent item in eventList!) {
          for (AiDeatilEventCondion condition in item.condion!) {
            /*if (condition.key == 'packageRate') {
              condition.result?.values = [rate.toStringAsFixed(2)];
              eventList![0].condion?[0].result?.values = [
                rate.toStringAsFixed(2)
              ];
              setContion(rate, condition, 0);
            }
            if (condition.key == 'unproducedNum') {
              condition.result?.values = [
                getUnTotal() < 0 ? '0' : getUnTotal().toString()
              ];
              eventList![0].condion?[1].result?.values = [
                getUnTotal() < 0 ? '0' : getUnTotal().toString()
              ];
              setContion(getUnTotal(), condition, 1);
            }*/
          }
        }
        if (result?.status == 0) {
          bool isSucc = true;
          for (AiItemEvent item in eventList!) {
            for (AiDeatilEventCondion condition in item.condion!) {
              if (condition.result?.status == 2) {
                isSucc = false;
                break;
              }
            }
          }
          result?.status = isSucc ? 1 : 2;
        }
      }
    });
  }

  void setContion(value, condition, index) {
    if (condition.compareType == '>=') {
      if (value >= double.parse(condition.compareValue!)) {
        condition.result?.status = 1;
        eventList![0].condion?[index].result?.status = 1;
      } else {
        condition.result?.status = 2;
        eventList![0].condion?[index].result?.status = 2;
      }
    } else if (condition.compareType == '<=') {
      if (value <= double.parse(condition.compareValue!)) {
        eventList![0].condion?[index].result?.status = 1;
      } else {
        eventList![0].condion?[index].result?.status = 2;
      }
    } else if (condition.compareType == '>') {
      if (value > double.parse(condition.compareValue!)) {
        eventList![0].condion?[index].result?.status = 1;
      } else {
        eventList![0].condion?[index].result?.status = 2;
      }
    } else if (condition.compareType == '<') {
      if (value < double.parse(condition.compareValue!)) {
        eventList![0].condion?[index].result?.status = 1;
      } else {
        eventList![0].condion?[index].result?.status = 2;
      }
    }
  }

  @override
  Future<AiBaseEntity> uploadData() {
    if (aiDetail.value.details == null) {
      return Future.value(AiBaseEntity(code: 200));
    }
    aiDetail.value.details?.config?.uploadImageIfNeed();
    return AiProvider()
        .uploadConfig(id: aiDetail.value.id, details: aiDetail.value.details!);
  }
}
