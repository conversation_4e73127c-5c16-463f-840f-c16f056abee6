import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/detail_mixin.dart';
import 'package:inspector/app/modules/ai/number_result/ai_number_result_controller.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/widgets/ai_summary_result.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';

class AiNumberResultView extends GetView<AiNumberResultController> {
  const AiNumberResultView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(AiCategoryType.number.name),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: KeyboardDismissOnTap(
          child: Column(
            children: [
              AiProductSelectBar(loader: controller),
              Expanded(
                child: Obx(() {
                  return AiEmptyView(
                    baseResult: controller.baseResult,
                    child: ListView(
                      children: [
                        _AiNumberDataTable(),
                        SizedBox(height: 10),
                        _AiNumberJudgeResultView(),
                        SizedBox(height: 10),
                        AiSummaryResultView(
                          isSame: controller.result?.isSame,
                          note: controller.result?.remark ?? '',
                          noteChange: (value) => controller.result?.remark = value,
                          resultChange: (value) => controller.result?.changeResult(value),
                        ),
                      ],
                    ),
                  );
                }),
              ),

              Container(
                  margin: EdgeInsets.only(left: 20,right: 20),
                  child:  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                          width: Get.width/2 - 20,
                          height: 40,
                          child:
                          AiSubmitButton.save(
                            onPressed: controller.save,
                          ),
                      )
                      ,
                      SizedBox(
                          width: Get.width/2 - 20,
                          height: 40,
                          child:  AiSubmitButton.next(
                            onPressed: controller.toNext,
                          ),
                      )
                    ],
                  )
              )

            ],
          ),
        ),
      ),
    );
  }
}

class _AiNumberDataTable extends GetView<AiNumberResultController> {
  const _AiNumberDataTable({super.key});


  @override
  Widget build(BuildContext context) {

    return Container(
      color: MColor.white,
      padding: EdgeInsets.all(20),
      child: Obx(() {
        return Table(
            border: TableBorder.all(color: MColor.xFFE5E5E5),
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            children: [
              for (AiItemForms info in controller.inputList ?? [])
                if(info.key!='backupNumSample' && info.key!='sampleLevel'  && info.key!='sampleNum'  && info.key!='packagedUnit' && info.key!='unpackagedTotal'   )
                TableRow(children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 10),
                    child: Container(
                        alignment: Alignment.centerLeft,
                        height: 38,
                        child: Text(
                          info.name ?? '',
                        )),
                  ),
                  if(info.key=='unproducedTotal' )
                  Padding(
                    padding: const EdgeInsets.only(left: 10),
                    child: Text(
                      //controller.getUnTotal().toString(),
                      '${controller.getValueNum('unproducedTotal')}',
                      style: MFont.regular14,
                    ),
                  )
                  else
                    Padding(
                      padding: const EdgeInsets.only(left: 10),
                      child: Text(
                        info.values?.firstOrNull ?? '',
                        style: MFont.regular14,
                      ),
                    )
                  ,
                ])
            ]);
      }),
    );
  }
}

class _AiNumberJudgeResultView extends GetView<AiNumberResultController> {
  const _AiNumberJudgeResultView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      color: MColor.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: Text(
              S.of(context).ai_judgment,
              style: MFont.medium18,
            ),
          ),
          Obx(() {
            return Table(
              border: TableBorder.all(color: MColor.xFFE5E5E5),
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: [
                TableRow(
                    decoration: BoxDecoration(color: MColor.xFFFFF3F0),
                    children: [
                      S.of(context).ai_judgment_item,
                      S.of(context).ai_standard,
                      S.of(context).ai_result,
                      S.of(context).ai_conclusion
                    ]
                        .map(
                          (e) => _cell(child: Text(e)),
                        )
                        .toList()),
                for (AiItemEvent event in controller.eventList ?? [])
                  for (AiDeatilEventCondion con in event.condion ?? [])

                    _buildTableRow(
                        con.titles(
                            controller.inputList
                                    ?.where(
                                      (e) => e.key == con.key,
                                    )
                                    .firstOrNull
                                    ?.values
                                    ?.firstOrNull ??
                                (con.key == ConfigMixin.orderNumKey
                                    ? AiCategoryController.to.orderNum.toString()
                                    : '0'),
                            ConfigMixin.keyName(con.key ?? '')),
                        con.result?.isPassInLast ?? false),
              ],
            );
          })
        ],
      ),
    );
  }

  TableRow _buildTableRow(List<String?> list, bool status) {
    return TableRow(
        children: list.map(
      (e) {
        final isLast = e == list.last;
        var style = isLast ? MFont.medium14 : MFont.regular14;
        final color = status ? (isLast ? MColor.pass : null) : MColor.failed;
        style = style.copyWith(color: color);
        return _cell(child: Text(e ?? '', textAlign: TextAlign.center, style: style));
      },
    ).toList());
  }

  Widget _cell({required Text child}) {
    return Container(
      height: 43,
      alignment: Alignment.center,
      child: child,
    );
  }
}
