import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';

import '../category/entity/ai_recmend_data.dart';

class AiRecmendController extends GetxController {
  static AiRecmendController get to => Get.find();

  int type=0;
  @override
  void onInit() {
    Map map = Get.arguments;
    type = map['type'];
    search('');
    super.onInit();
  }

  void clickRecItem(item){
    item.isSelected = !item.isSelected;
    recmendData.refresh();
    // update();
  }
  void goBack(){
    Get.back();
  }

  void addRec(){
    List<AiItemForms> aa= [];
    for(var item in recmendList){
      if(item.isSelected){
        final now = DateTime.now();
        AiItemForms child = AiItemForms(key:'form${now.day}${now.hour}${now.minute}' );
        child.tips=AiModelInfo.empty();
        child.name = item.itemName;
        child.tips!.values =[item.remark??''] ;
        aa.add(child);
      }
    }
    // inputList?.addAll(aa);
    Get.back(result:{
      'data':aa
    });
  }

  String searchKeywords = '';
  final recmendData = AiBaseEntity<List<AiRecmendData>>(data: []).obs;
  List<AiRecmendData> get recmendList => recmendData.value.data ?? [];
  void search(keyword){
    AiProvider()
        .itemSearch(
        searchKeywords,
        '1',
        type.toString()
    )
        .then((value) {
      if(value.data!=null){
        recmendData.value =value;
      }
      update();
      print(' recmendData.value==='+( recmendData.value.data??[]).length.toString());
    });
  }



  @override
  void loadData() {
  }



}
