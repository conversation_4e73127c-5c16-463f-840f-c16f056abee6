import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';

import '../category/entity/ai_recmend_data.dart';
import 'ai_recmend_controller.dart';

class ATRecmendView<T extends AiRecmendController> extends GetView<T> {
  const ATRecmendView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          title: Text(S.current.ai_simple_project_recmend),
          centerTitle: true,
        ),
        body: Obx(() {
          return Column(
            children: [
              Expanded(
                  child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    _searchView(),
                    for (AiRecmendData item
                        in controller.recmendList ?? []) ...{_row(item)},
                  ],
                ),
              )),
              AiSubmitButton.add(
                onPressed: () {
                  controller.addRec();
                },
              )
            ],
          );
        }));
  }

  Widget _row(AiRecmendData item) {
    return InkWell(
        onTap: () {
          // controller.goAddEdit();
          controller.clickRecItem(item);
        },
        child: Container(
          padding: EdgeInsets.fromLTRB(12, 8, 12, 8),
          margin: EdgeInsets.fromLTRB(20, 10, 20, 0),
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(5)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      if (item.isSelected)
                        Icon(
                          Icons.check_circle,
                          color: MColor.skin,
                        ),
                      Text(
                        item.itemName,
                        style: MFont.medium16,
                      ),
                    ],
                  ),
                  Text(
                    item.hot.toString() + S.current.ai_simple_add_citations,
                    style: TextStyle(color: MColor.xFF838383, fontSize: 12),
                  ),
                ],
              ),
              SizedBox(
                height: 7,
              ),
              Text(item.remark,
                  style: TextStyle(color: MColor.xFF838383, fontSize: 12))
            ],
          ),
        ));
  }

  Widget _searchView() {
    return Builder(builder: (context) {
      return Container(
          color: Colors.white,
          padding: EdgeInsets.fromLTRB(20, 8, 18, 8),
          child: Row(
            children: [
              Expanded(
                  child: TextField(
                decoration: InputDecoration(
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                  prefixIcon: Icon(
                    Icons.search,
                    size: 24,
                    color: MColor.xFFC4C4C4,
                  ),
                  fillColor: MColor.white,
                  filled: true,
                  isDense: true,
                  hintText: S.of(Get.context!).apply_enter,
                  hintStyle: MFont.regular13.apply(
                      color: context.isDarkMode
                          ? DarkColor.xFF999999
                          : MColor.xFF999999),
                  labelStyle: MFont.regular13.apply(
                      color: context.isDarkMode
                          ? DarkColor.xFF333333
                          : MColor.xFF333333),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: MColor.xFFE5E5E5, width: 1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: MColor.skin, width: 1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  constraints: const BoxConstraints(maxHeight: 40),
                ),
                onChanged: (text) {
                  // controller.searchWords();
                  controller.searchKeywords = text;
                },
              )),
              SizedBox(
                width: 16,
              ),
              InkWell(
                  onTap: () {
                    controller.search(controller.searchKeywords, );
                  },
                  child: Text(
                    S.current.search,
                    style: TextStyle(color: MColor.skin, fontSize: 16),
                  ))
            ],
          ));
    });
  }
}
