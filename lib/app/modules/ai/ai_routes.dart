part of 'ai_pages.dart';

abstract class AiRoutes {
  static const CATEGORY = _Path.CATEGORY;
  static const NUMBER = _Path.CATEGORY + _Path.NUMBER;
  static const NUMBER_RESULT = NUMBER + _Path.NUMBER_RESULT;
  static const PACKAGE = _Path.CATEGORY + _Path.PACKAGE;
  static const MARK = _Path.CATEGORY + _Path.MARK;
  static const STYLE = _Path.CATEGORY + _Path.STYLE;
  static const CHECKOUT = _Path.CATEGORY + _Path.CHECKOUT;
  static const CHECKOUT_RESULT = CHECKOUT + _Path.CHECKOUT_RESULT;
  static const MEASURE = _Path.CATEGORY + _Path.MEASURE;
  static const MEASURE_RESULT = MEASURE + _Path.MEASURE_RESULT;


  static const QUALITY = _Path.CATEGORY + _Path.QUALITY;
  static const QUALITY_DETAIL = _Path.CATEGORY + _Path.QUALITY_DETAIL;
  static const QUALITY_ADD = _Path.CATEGORY + _Path.QUALITY_ADD;
  static const QUALITY_QUICK = _Path.CATEGORY + _Path.QUALITY_QUICK;
  static const QUALITY_QUICK_ADD = _Path.CATEGORY + _Path.QUALITY_QUICK_ADD;

  static const QUALITY_RESULT = QUALITY + _Path.QUALITY_RESULT;
  static const ATTACHMENT = _Path.CATEGORY + _Path.ATTACHMENT;
  static const QUALITY_EDIT = _Path.CATEGORY + _Path.QUALITY_EDIT;
  static const QUALITY_MANAGE = _Path.CATEGORY + _Path.QUALITY_MANAGE;
  static const QUALITY_REC = _Path.CATEGORY + _Path.QUALITY_REC;

  static const AI_EDIT = _Path.CATEGORY + _Path.EDIT;
  static const AI_MANAGE = _Path.CATEGORY + _Path.MANAGE;
  static const AI_RECMEND = _Path.CATEGORY + _Path.RECMEND;
  static const AI_ADD_EDIT = _Path.CATEGORY + _Path.ADD_EDIT;
  static const AI_INPUT = _Path.CATEGORY + _Path.INPUT;
  static const AI_HELP = _Path.CATEGORY + _Path.HELP;
  static const AI_RESULT = _Path.CATEGORY + _Path.RESULT;

  static const CHECKOUT_REC= _Path.CATEGORY + _Path.CHECKOUT_REC;
  static const CHECKOUT_MANAGE = _Path.CATEGORY + _Path.CHECKOUT_MANAGE;
  static const CHECKOUT_EDIT = _Path.CATEGORY + _Path.CHECKOUT_EDIT;
  static const CHECKOUT_DETAIL = _Path.CATEGORY + _Path.CHECKOUT_DETAIL;
  static const CHECKOUT_ADD_RECORD = _Path.CATEGORY + _Path.CHECKOUT_ADD_RECORD;
  static const CHECKOUT_ADD_DEFECT = _Path.CATEGORY + _Path.CHECKOUT_ADD_DEFECT;
  static const CHECKOUT_LEVEL = _Path.CATEGORY + _Path.CHECKOUT_LEVEL;
  static const MEASURE_DETAIL = _Path.CATEGORY + _Path.MEASURE_DETAIL;
  static const MEASURE_REQUIRE = _Path.CATEGORY + _Path.MEASURE_REQUIRE;
  static const MEASURE_RECORD = _Path.CATEGORY + _Path.MEASURE_RECORD;
  static const MEASURE_Standard = _Path.CATEGORY + _Path.MEASURE_Standard;
  static const MEASURE_REC = _Path.CATEGORY + _Path.MEASURE_REC;
  static const MEASURE_EDIT = _Path.CATEGORY + _Path.MEASURE_EDIT;
  static const MEASURE_MANAGE = _Path.CATEGORY + _Path.MEASURE_MANAGE;

  static const ATTACHMENT_SECOND = _Path.CATEGORY + _Path.ATTACHMENT_SECOND;
  static const ATTACHMENT_SIGN = _Path.CATEGORY + _Path.ATTACHMENT_SIGN;

  static const AT_RECMEND = _Path.CATEGORY + _Path.AT_RECMEND;

}

abstract class _Path {
  static const CATEGORY = '/category';
  static const NUMBER = '/number';
  static const NUMBER_RESULT = '/number_result';
  static const PACKAGE = '/package';
  static const MARK = '/mark';
  static const STYLE = '/style';
  static const CHECKOUT = '/checkout';
  static const CHECKOUT_RESULT = '/checkout_result';
  static const MEASURE = '/measure';
  static const MEASURE_RESULT = '/measure_result';
  static const MEASURE_DETAIL = '/measure_detail';
  static const MEASURE_RECORD = '/measure_record';
  static const MEASURE_Standard = '/measure_standard';

  static const QUALITY = '/quality';
  static const QUALITY_DETAIL = '/quality_detail';
  static const QUALITY_RESULT = '/quality_result';
  static const QUALITY_REC = '/quality_rec';
  static const QUALITY_ADD = '/quality_add';
  static const QUALITY_QUICK = '/quality_quick';
  static const QUALITY_QUICK_ADD = '/quality_quick_add';
  static const ATTACHMENT = '/attachment';
  static const EDIT = '/edit';
  static const MANAGE = '/manage';
  static const RECMEND = '/recmend';
  static const ADD_EDIT = '/add_edit';
  static const INPUT = '/input';
  static const HELP = '/help';
  static const RESULT = '/result';
  static const QUALITY_MANAGE = '/quality_manage';
  static const QUALITY_EDIT = '/quality_edit';

  static const CHECKOUT_MANAGE = '/checkout_manage';
  static const CHECKOUT_EDIT = '/checkout_edit';
  static const CHECKOUT_REC = '/checkout_rec';
  static const CHECKOUT_DETAIL = '/checkout_detail';
  static const CHECKOUT_ADD_RECORD = '/checkout_add_record';
  static const CHECKOUT_ADD_DEFECT = '/checkout_add_defect';
  static const CHECKOUT_LEVEL = '/checkout_level';
  static const MEASURE_REQUIRE = '/measure_require';
  static const MEASURE_REC = '/measure_rec';
  static const MEASURE_EDIT = '/measure_edit';
  static const MEASURE_MANAGE = '/measure_manage';

  static const ATTACHMENT_SECOND = '/attachment_second';
  static const ATTACHMENT_SIGN = '/attachment_sign';
  static const AT_RECMEND = '/at_recmend';
}
