import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/entiy/image_info_entity.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/app/modules/ai/photo_selected/selected_assets_list_view.dart';
import 'package:inspector/app/tools/tools.dart';

class AiFormAssetsListView extends StatefulWidget {

  const AiFormAssetsListView(
      {required this.manager, this.empty, this.readOnly=false,super.key, this.padding = const EdgeInsets.all(0),  this.onImageChanged });

  final ImageAssetsListManager manager;
  final Widget? empty;
  final EdgeInsetsGeometry padding;
  final bool  readOnly ;
  final Function(ImageAssetsListManager)? onImageChanged;
  @override
  State<AiFormAssetsListView> createState() => _AiFormAssetsListViewState();
}

class _AiFormAssetsListViewState extends State<AiFormAssetsListView> {

  @override
  void initState() {
    super.initState();
    print('初始化时 onImageChanged: ${widget.onImageChanged != null}');
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        if (widget.manager.imageInfoList.isEmpty) return widget.empty ?? SizedBox.shrink();
        return SelectedAssetsListView(
          infoList:widget.manager.imageInfoList,
          padding: widget.padding,
          readOnly:widget.readOnly,
          isDisplayingDetail: ValueNotifier(true),
          onResult: (result) {
            logger.i('----------------11111111111-----------');
            widget.manager.changeAssetsOnPicker(result);
            logger.i('---------------------------');
            if(widget.onImageChanged!=null){
              logger.i('-----------22222222222----------------');
              widget.onImageChanged!(widget.manager);
            }
          },
          onRemoveAsset: (index) {
            widget.manager.removeImageAtIndex(index);
            logger.i('onImageChanged 是否为 null：${widget.onImageChanged == null}');

            if(widget.onImageChanged!=null){

              widget.onImageChanged!(widget.manager);
            }
            logger.i('~~~~~~~~~~~~~~~~~~~~~~~~~');
            logger.i(widget.manager.imageInfoList.length);
          },
        );
      },
    );
  }
}
