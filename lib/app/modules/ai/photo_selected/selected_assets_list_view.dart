// Copyright 2019 The FlutterCandies author. All rights reserved.
// Use of this source code is governed by an Apache license that can be found
// in the LICENSE file.

import 'package:flutter/material.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/photo_selected/action_picture/ai_image_cell.dart';
import 'package:inspector/app/modules/ai/photo_selected/action_picture/ai_image_info.dart';
import 'package:inspector/app/modules/ai/photo_selected/image_browse_page.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart'
    show AssetEntity, AssetPicker, AssetPickerViewer;
import 'package:inspector/generated/l10n.dart';

class SelectedAssetsListView extends StatefulWidget {
  const SelectedAssetsListView({
    required this.infoList,
    required this.isDisplayingDetail,
    required this.onResult,
    required this.onRemoveAsset,
    this.readOnly=false,
    super.key,
    this.padding = const EdgeInsets.all(0),
  });

  final List<AiImageInfo>? infoList;
  final ValueNotifier<bool> isDisplayingDetail;
  final void Function(List<AssetEntity>? result) onResult;
  final void Function(int index) onRemoveAsset;
  final EdgeInsetsGeometry padding;
  final bool readOnly;

  @override
  State<SelectedAssetsListView> createState() => _SelectedAssetsListViewState();
}

class _SelectedAssetsListViewState extends State<SelectedAssetsListView> {
  List<AiImageInfo> get imageInfoList => widget.infoList ?? [];

  Widget _selectedAssetWidget(BuildContext context, int index) {
    final info = imageInfoList[index];
    return ValueListenableBuilder<bool>(
      valueListenable: widget.isDisplayingDetail,
      builder: (context, value, child) => AiImageCell(
        info,
        onTap: (value) {
          Navigator.push(context, MaterialPageRoute(
            builder: (context) {
              return ImageBrowsePage(title: S.of(context).ai_inspection_image, selected: index, images: imageInfoList);
            },
          ));
        },
      ),
    );
  }

  Widget _selectedAssetDeleteButton(BuildContext context, int index) {
    return GestureDetector(
      onTap: () => widget.onRemoveAsset(index),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.0),
          color: Theme.of(context).canvasColor.withOpacity(0.5),
        ),
        child: const Icon(Icons.close, size: 18.0),
      ),
    );
  }

  Widget selectedAssetsListView(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const BouncingScrollPhysics(),
      scrollDirection: Axis.horizontal,
      itemCount: imageInfoList.length,
      itemBuilder: (BuildContext context, int index) {
        return  Padding(
          padding: EdgeInsets.only(right: 8),
          child: SizedBox(
            height: 68,
            width: 68,
            child: Stack(
              children: <Widget>[

                Positioned.fill(child: _selectedAssetWidget(context, index)),
                if(!widget.readOnly)
                ValueListenableBuilder<bool>(
                  valueListenable: widget.isDisplayingDetail,
                  builder: (_, bool value, __) => AnimatedPositioned(
                    duration: kThemeAnimationDuration,
                    top: value ? 4.0 : -30.0,
                    right: value ? 4.0 : -30.0,
                    child:  _selectedAssetDeleteButton(context, index),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  void didUpdateWidget(covariant SelectedAssetsListView oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return Container(
      color: MColor.white,
      width: width,
      alignment: Alignment.centerLeft,
      padding: widget.padding,
      child: ValueListenableBuilder<bool>(
        valueListenable: widget.isDisplayingDetail,
        builder: (_, bool value, __) {
          return AnimatedContainer(
            duration: kThemeChangeDuration,
            curve: Curves.easeInOut,
            child: SizedBox(
              height: 68,
              child: Column(
                children: [
                  Expanded(child: selectedAssetsListView(context)),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
