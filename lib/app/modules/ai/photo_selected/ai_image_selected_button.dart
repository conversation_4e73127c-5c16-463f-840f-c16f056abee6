import 'dart:math';

import 'package:flutter/material.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/entiy/image_info_entity.dart';
import 'package:inspector/app/modules/ai/photo_selected/picker_method.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

typedef PickImageAction = void Function(List<AssetEntity>?);

class AiImageSelectedButton extends StatefulWidget {
  const AiImageSelectedButton({
    required this.manager,
    this.big = false,
    super.key,
  });
  final ImageAssetsListManager manager;
  final bool big;

  @override
  State<AiImageSelectedButton> createState() => _AiImageSelectedButtonState();
}

class _AiImageSelectedButtonState extends State<AiImageSelectedButton> {
  @override
  Widget build(BuildContext context) {
    final cover = Icon(Icons.camera_alt,  color:  MColor.xFFC4C4C4,);
    return GestureDetector(
      onTap: () async {
        final assets = widget.manager.assetsIn;
        final inCount = widget.manager.imageInfoList.length - assets.length;

        final list = await PickMethod(
          maxAssetsCount: max(100 - max(0, inCount), 0),
        ).forCamera(context);
        widget.manager.changeAssetsOnPicker(list);
      },
      child: widget.big
          ? Container(
              width: 68, //68与图片大小一致
              height: 68,
              decoration: BoxDecoration(
                border: Border.all(color: MColor.xFFE5E5E5),
                borderRadius: BorderRadius.circular(8),
              ),
              margin: EdgeInsets.only(right: 8),
              child: cover,
            )
          : cover,
    );
  }
}
