import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/photo_selected/utils/crop_editor_helper.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:inspector/generated/l10n.dart';

class AiAssetPickerBuilderDelegate extends DefaultAssetPickerBuilderDelegate {
  AiAssetPickerBuilderDelegate({
    required super.provider,
    required super.initialPermission,
    super.gridCount = 4,
    super.themeColor,
    super.textDelegate,
    super.locale,
    super.pathNameBuilder,
  }) : super(specialPickerType: SpecialPickerType.noPreview);

  @override
  Future<void> selectAsset(
    BuildContext context,
    AssetEntity asset,
    int index,
    bool selected,
  ) async {
    AiAssetEntity? editImage = await Navigator.push(context, PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) {
        return FadeTransition(opacity: animation, child: ImageBrowseEditPage(asset));
      },
    ));
    if (editImage != null) {
      Navigator.maybeOf(context)?.maybePop([editImage]);
    }
  }
}

class ImageBrowseEditPage extends StatefulWidget {
  final AssetEntity asset;
  const ImageBrowseEditPage(this.asset, {super.key});

  @override
  State createState() => _ImageBrowseEditPageState();
}

class _ImageBrowseEditPageState extends State<ImageBrowseEditPage> {
  @override
  void initState() {
    super.initState();
  }

  final GlobalKey<ExtendedImageEditorState> editorKey = GlobalKey<ExtendedImageEditorState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          FutureBuilder(
            future: widget.asset.file,
            builder: (context, snapshot) {
              if (!snapshot.hasData) return CupertinoActivityIndicator();
              return ExtendedImage.file(
                snapshot.data!,
                fit: BoxFit.contain,
                mode: ExtendedImageMode.editor,
                extendedImageEditorKey: editorKey,
                cacheRawData: true,
                initEditorConfigHandler: (state) {
                  return EditorConfig(
                    maxScale: 8.0,
                    cornerColor: MColor.aiMain,
                    cornerSize: Size(20, 3),
                    lineColor: MColor.aiMain,
                    editorMaskColorHandler: (context, pointerDown) {
                      if (pointerDown) {
                        return MColor.black.withOpacity(0.02);
                      }
                      return MColor.black.withOpacity(0.8);
                    },
                    cropRectPadding: EdgeInsets.symmetric(horizontal: 10),
                    hitTestSize: 20.0,
                    cropAspectRatio: CropAspectRatios.ratio4_3,
                  );
                },
              );
            },
          ),
          Positioned(
              left: 0,
              top: 0,
              child: SafeArea(
                  child: CloseButton(
                onPressed: () => Navigator.pop(context),
                color: MColor.white,
              ))),
          Positioned(
              right: 20,
              bottom: 0,
              child: SafeArea(minimum: EdgeInsets.only(bottom: 20), child: buildConfirmButton())),
        ],
      ),
    );
  }

  Widget buildConfirmButton() {
    return MaterialButton(
      minWidth: 20,
      height: 32,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      color: MColor.aiMain,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(3),
      ),
      onPressed: cropImage,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      child: Text(
        S.of(context).ai_photo_confirm,
        style: TextStyle(
          color: MColor.white,
          fontSize: 17,
          fontWeight: FontWeight.normal,
        ),
      ),
    );
  }

  bool _cropping = false;
  Future<void> cropImage() async {
    if (_cropping) {
      return;
    }
    _cropping = true;
    EditImageInfo? fileData;
    try {
      fileData = await cropImageDataWithNativeLibrary(state: editorKey.currentState!);
    } catch (e) {
      await EasyLoading.showError('save faild');
    } finally {
      _cropping = false;
    }
    if (mounted) {
      Navigator.maybeOf(context)?.pop(AiAssetEntity(
          id: widget.asset.id,
          typeInt: widget.asset.typeInt,
          width: widget.asset.width,
          height: widget.asset.height,
          title: widget.asset.title,
          duration: widget.asset.duration,
          createDateSecond: widget.asset.createDateSecond,
          isFavorite: widget.asset.isFavorite,
          latitude: widget.asset.latitude,
          longitude: widget.asset.longitude,
          mimeType: widget.asset.mimeType,
          modifiedDateSecond: widget.asset.modifiedDateSecond,
          orientation: widget.asset.orientation,
          relativePath: widget.asset.relativePath,
          subtype: widget.asset.subtype,
          fileData: fileData));
    }
  }
}

class AiAssetEntity extends AssetEntity {
  const AiAssetEntity({
    required super.id,
    required super.typeInt,
    required super.width,
    required super.height,
    super.duration = 0,
    super.orientation = 0,
    super.isFavorite = false,
    super.title,
    super.createDateSecond,
    super.modifiedDateSecond,
    super.relativePath,
    super.latitude,
    super.longitude,
    super.mimeType,
    super.subtype = 0,
    this.fileData,
  });
  final EditImageInfo? fileData;
}
