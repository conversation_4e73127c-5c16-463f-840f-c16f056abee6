// Copyright 2019 The FlutterCandies author. All rights reserved.
// Use of this source code is governed by an Apache license that can be found
// in the LICENSE file.

import 'dart:async';
import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/photo_selected/image_edit.dart';
import 'package:inspector/app/modules/ai/photo_selected/photo_picker/AiAssetPickerBuilderDelegate.dart';
import 'package:inspector/app/modules/ai/photo_selected/utils/crop_editor_helper.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';
import 'package:inspector/app/tools/storage_util.dart';
import 'package:path/path.dart' as path;
import 'package:inspector/generated/l10n.dart';

class AiCameraPickerTextDelegate extends CameraPickerTextDelegate {
  @override
  String get confirm => '确定';
}

class AiAssetPickerTextDelegate extends AssetPickerTextDelegate {
  @override
  String get confirm => '确定';
}

class PickMethod {
  PickMethod({this.maxAssetsCount = 9, this.assets});

  int maxAssetsCount;
  List<AssetEntity>? assets;

  Future<List<AssetEntity>?>? forCamera(BuildContext context) async {
    List<AssetEntity> assets = [];
    Widget bottomSheet = Material(
      borderRadius: BorderRadius.only(topLeft: Radius.circular(8),topRight: Radius.circular(8)),
      child: SizedBox(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ListTile(title: Center(child: Text(S.of(context).camera),),onTap: () async{
              final ImagePicker picker = ImagePicker();
              final XFile? image = await picker.pickImage(source: ImageSource.camera);
              if(image != null && context.mounted){
                AssetEntity? entity = await Navigator.of(context).push<AssetEntity?>(MaterialPageRoute(builder: (_)=>ImageEditPage(path: image.path,)));
                if(entity != null){
                  assets.insert(0,entity);
                  Get.back();
                }
              }
            },),
            ListTile(title: Center(child: Text(S.of(context).photo_album),),onTap: () async{
              final ImagePicker picker = ImagePicker();
              final XFile? image = await picker.pickImage(source: ImageSource.gallery);
              if(image != null && context.mounted){
                AssetEntity? entity = await Navigator.of(context).push<AssetEntity?>(MaterialPageRoute(builder: (_)=>ImageEditPage(path: image.path,)));
                if(entity != null){
                  assets.insert(0,entity);
                  Get.back();
                }
              }
            },),
            ListTile(title: Center(child: Text(S.of(context).ai_cancel_action,style: TextStyle(color: MColor.xFFE95332),),),onTap: (){Navigator.of(context).pop();},),
          ],
        ),
      ),
    );
    await Get.bottomSheet(bottomSheet);
    return assets;
  }

  Future<List<AssetEntity>?>? forCameraOld(BuildContext context) async {
    final Widget picker = CameraPicker(
      pickerConfig: CameraPickerConfig(textDelegate: AiCameraPickerTextDelegate(),theme: Theme.of(context)),
      createPickerState: () => AiCameraPickerState(
        maxAssetsCount: maxAssetsCount,
        assets: assets,
        selectedTake: (value) {
          // Singleton.textDelegate.confirm
          // Get.showSnackbar(GetSnackBar(
          //   messageText: Center(
          //       child: Text(
          //     '已添加',
          //     textAlign: TextAlign.center,
          //   )),
          //   duration: Duration(seconds: 1),
          //   snackPosition: SnackPosition.TOP,
          // ));
          assets ??= [];
          assets!.insert(0, value);
          EasyLoading.showSuccess(S.of(context).ai_addition_successful,
              duration: Duration(milliseconds: 500));
        },
        selectedPhotos: (value) {
          assets ??= [];
          assets!.insert(0, value.first);
          Navigator.pop(context);
        },
      ),
    );
    await Navigator.of(
      context,
      rootNavigator: true,
    ).push<AssetEntity>(
      CameraPickerPageRoute<AssetEntity>(builder: (_) => picker),
    );
    return assets;
  }

  static Future<List<AssetEntity>?>? forPhoto(BuildContext context,
      {List<AssetEntity>? assets, int maxAssetsCount = 9}) async {
    AssetPickerTextDelegate textDelegate;
    int? index = StorageUtil.getInt(Constant.kLanguage);
    if (index == 2) {
      textDelegate = EnglishAssetPickerTextDelegate();
    } else if (index == 3) {
      textDelegate = JapaneseAssetPickerTextDelegate();
    } else {
      textDelegate = AiAssetPickerTextDelegate();
    }
    final PermissionState ps = await AssetPicker.permissionCheck(
      requestOption: const PermissionRequestOption(
        androidPermission: AndroidPermission(
          type: RequestType.image,
          mediaLocation: false,
        ),
      ),
    );

    final DefaultAssetPickerProvider provider = DefaultAssetPickerProvider(
      // selectedAssets: assets,
      maxAssets: 1,
    );
    final localizations = Localizations.maybeLocaleOf(context);
    final AiAssetPickerBuilderDelegate builder = AiAssetPickerBuilderDelegate(
      provider: provider,
      initialPermission: ps,
      locale: localizations,
      pathNameBuilder: (path) {
        final name = path.name.toLowerCase();
        if (localizations?.languageCode.toLowerCase() != 'zh') return name;
        if (name == 'recent') {
          return '最近';
        } else if (name == 'camera') {
          return '相机';
        } else if (name == 'screenshots') {
          return '截图';
        } else if (name == 'pictures') {
          return '图片';
        } else if (name == 'weixin') {
          return '微信';
        } else if (name == 'browser') {
          return '浏览器';
        }
        return name;
      },
    );
    final List<AssetEntity>? result = await AssetPicker.pickAssetsWithDelegate(
      context,
      delegate: builder,
    );
    return result;

/*
    return AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        maxAssets: maxAssetsCount,
        selectedAssets: assets,
        textDelegate: textDelegate,
        specialItemPosition: SpecialItemPosition.none,
        requestType: RequestType.image,
        pathNameBuilder: (path) {
          final name = path.name.toLowerCase();
          if (name == 'recent') {
            return '最近';
          } else if (name == 'camera') {
            return '相机';
          } else if (name == 'screenshots') {
            return '截图';
          } else if (name == 'pictures') {
            return '图片';
          } else if (name == 'weixin') {
            return '微信';
          } else if (name == 'browser') {
            return '浏览器';
          }
          return name;
        },
      ),
    );
*/
  }
}

class AiCameraPickerState extends CameraPickerState {
  AiCameraPickerState({
    this.maxAssetsCount = 9,
    this.assets,
    this.selectedPhotos,
    this.selectedTake,
  });
  final int maxAssetsCount;
  List<AssetEntity>? assets;
  final ValueChanged<List<AssetEntity>>? selectedPhotos;
  final ValueChanged<AssetEntity>? selectedTake;

  /// This displayed at the top of the screen.
  /// 该区域显示在屏幕下方。
  @override
  Widget buildCaptureActions({
    required BuildContext context,
    required BoxConstraints constraints,
    CameraController? controller,
  }) {
    const fallbackSize = 150.0;
    final previewSize = controller?.value.previewSize;
    final orientation = controller?.value.deviceOrientation ?? MediaQuery.orientationOf(context);
    final isPortrait = orientation.toString().contains('portrait');
    double effectiveSize;
    if (controller == null || pickerConfig.enableScaledPreview) {
      effectiveSize = lastCaptureActionsEffectiveHeight ?? fallbackSize;
    } else if (previewSize != null) {
      Size constraintSize = Size(constraints.maxWidth, constraints.maxHeight);
      if (isPortrait && constraintSize.aspectRatio > 1 ||
          !isPortrait && constraintSize.aspectRatio < 1) {
        constraintSize = constraintSize.flipped;
      }
      if (isPortrait) {
        effectiveSize = constraintSize.height - constraintSize.width * previewSize.aspectRatio;
      } else {
        effectiveSize = constraintSize.width - constraintSize.height * previewSize.aspectRatio;
      }
    } else if (lastCaptureActionsEffectiveHeight != null) {
      effectiveSize = lastCaptureActionsEffectiveHeight!;
    } else {
      // Fallback to a reasonable height.
      effectiveSize = fallbackSize;
    }
    if (effectiveSize <= 0) {
      print(
        'Unexpected layout size calculation: $effectiveSize, '
        'portrait: $isPortrait, '
        'orientation: $orientation',
      );
      effectiveSize = fallbackSize;
    } else if (effectiveSize < fallbackSize) {
      effectiveSize = fallbackSize;
    }
    lastCaptureActionsEffectiveHeight = effectiveSize;
    return Container(
      width: isPortrait ? null : effectiveSize,
      height: isPortrait ? effectiveSize : null,
      padding: EdgeInsets.only(bottom: MediaQuery.paddingOf(context).bottom),
      child: Flex(
        direction: isPortrait ? Axis.horizontal : Axis.vertical,
        verticalDirection: orientation == DeviceOrientation.landscapeLeft
            ? VerticalDirection.up
            : VerticalDirection.down,
        children: <Widget>[
          Expanded(child: Center(child: buildPhotoButton())),
          Expanded(
            child: Center(
              child: buildCaptureButton(context, constraints),
            ),
          ),
          if (controller != null && !controller.value.isRecordingVideo && cameras.length > 1)
            Expanded(
              child: RotatedBox(
                quarterTurns: !enableScaledPreview ? cameraQuarterTurns : 0,
                child: buildCameraSwitch(context),
              ),
            )
          else
            const Spacer(),
        ],
      ),
    );
  }

  /// The picture will only taken when [CameraValue.isInitialized],
  /// and the camera is not taking pictures.
  /// 仅当初始化成功且相机未在拍照时拍照。
  @override
  Future<void> takePicture() async {
    if (!controller.value.isInitialized) {
      handleErrorWithHandler(
        StateError('Camera has not initialized.'),
        StackTrace.current,
        pickerConfig.onError,
      );
    }
    if (isControllerBusy) {
      return;
    }
    setState(() {
      isControllerBusy = true;
    });
    final ExposureMode previousExposureMode = controller.value.exposureMode;
    try {
      await Future.wait(<Future<void>>[
        wrapControllerMethod<void>(
          'setFocusMode',
          () => controller.setFocusMode(FocusMode.locked),
        ).catchError((e, s) {
          handleErrorWithHandler(e, s, pickerConfig.onError);
        }),
        if (previousExposureMode != ExposureMode.locked)
          wrapControllerMethod<void>(
            'setExposureMode',
            () => controller.setExposureMode(ExposureMode.locked),
          ).catchError((e, s) {
            handleErrorWithHandler(e, s, pickerConfig.onError);
          }),
      ]);
      final XFile file = await controller.takePicture();
      await controller.pausePreview();
      final bool? isCapturedFileHandled = pickerConfig.onXFileCaptured?.call(
        file,
        CameraPickerViewType.image,
      );
      if (isCapturedFileHandled ?? false) {
        return;
      }
      final AssetEntity? entity = await pushToViewer(
        file: file,
        viewType: CameraPickerViewType.image,
      );
      if (entity != null) {
        selectedTake?.call(entity);
        controller.resumePreview();
        return;
      }
      wrapControllerMethod<void>(
        'setFocusMode',
        () => controller.setFocusMode(FocusMode.auto),
      );
      if (previousExposureMode != ExposureMode.locked) {
        wrapControllerMethod<void>(
          'setExposureMode',
          () => controller.setExposureMode(previousExposureMode),
        );
      }
      await controller.resumePreview();
    } catch (e, s) {
      handleErrorWithHandler(e, s, pickerConfig.onError);
    } finally {
      safeSetState(() {
        isControllerBusy = false;
        isShootingButtonAnimate = false;
      });
    }
  }

  Widget buildPhotoButton() {
    return GestureDetector(
      onTap: () async {
        final list =
            await PickMethod.forPhoto(context, assets: assets, maxAssetsCount: maxAssetsCount);
        if (list != null) {
          assets = list;
          selectedPhotos?.call(list);
        }
      },
      child: Text(S.of(context).photo_album),
    );
  }

  @override
  Future<AssetEntity?> pushToViewer(
      {required XFile file, required CameraPickerViewType viewType}) async {
    if (viewType == CameraPickerViewType.image) {
      await precacheImage(FileImage(File(file.path)), context);
    }
    return CameraPickerViewer.pushToViewer(
      context,
      pickerConfig: widget.pickerConfig,
      viewType: viewType,
      previewXFile: file,
      createViewerState: () => AiCameraPickerViewerState(),
    );
  }
}

class AiCameraPickerViewerState extends CameraPickerViewerState {
  @override
  Future<void> createAssetEntityAndPop() async {
    if (isSavingEntity) {
      return;
    }
    setState(() {
      isSavingEntity = true;
    });

    // Handle the explicitly entity saving method.
    if (widget.pickerConfig.onEntitySaving != null) {
      try {
        await widget.pickerConfig.onEntitySaving!(
          context,
          widget.viewType,
          File(widget.previewXFile.path),
        );
      } catch (e, s) {
        handleErrorWithHandler(e, s, onError);
      } finally {
        isSavingEntity = false;
        if (mounted && !context.debugDoingBuild && context.owner?.debugBuilding != true) {
          // ignore: invalid_use_of_protected_member
          setState(() {});
        }
      }
      return;
    }

    AssetEntity? entity;
    try {
      final PermissionState ps = await PhotoManager.requestPermissionExtend();
      if (ps == PermissionState.authorized || ps == PermissionState.limited) {
        final filePath = previewFile.path;
        switch (widget.viewType) {
          case CameraPickerViewType.image:
            await cropImage();
            entity = await PhotoManager.editor.saveImage(
              fileData!.data!,
              filename: path.basename(filePath),
              title: path.basename(filePath),
            );
            // entity = await PhotoManager.editor.saveImageWithPath(
            //   filePath,
            //   title: path.basename(filePath),
            // );
            break;
          case CameraPickerViewType.video:
            entity = await PhotoManager.editor.saveVideo(
              previewFile,
              title: path.basename(filePath),
            );
            break;
        }
        if (widget.pickerConfig.shouldDeletePreviewFile && previewFile.existsSync()) {
          previewFile.delete().catchError((e, s) {
            handleErrorWithHandler(e, s, onError);
            return previewFile;
          });
        }
        return;
      }
      handleErrorWithHandler(
        StateError(
          'Permission is not fully granted to save the captured file.',
        ),
        StackTrace.current,
        onError,
      );
    } catch (e, s) {
      // realDebugPrint('Saving entity failed: $e');
      handleErrorWithHandler(e, s, onError);
    } finally {
      isSavingEntity = false;
      if (mounted && !context.debugDoingBuild && context.owner?.debugBuilding != true) {
        // ignore: invalid_use_of_protected_member
        setState(() {});
      }
      if (mounted) {
        Navigator.of(context).pop(entity);
      }
    }
  }

  final GlobalKey<ExtendedImageEditorState> editorKey = GlobalKey<ExtendedImageEditorState>();
  EditImageInfo? fileData;
  @override
  Widget buildPreview(BuildContext context) {
    final Widget builder;
    // builder = Image.file(previewFile);
    builder = ExtendedImage.file(
      previewFile,
      fit: BoxFit.contain,
      mode: ExtendedImageMode.editor,
      extendedImageEditorKey: editorKey,
      cacheRawData: true,
      initEditorConfigHandler: (state) {
        return EditorConfig(
          maxScale: 8.0,
          cornerColor: MColor.aiMain,
          cornerSize: Size(20, 3),
          lineColor: MColor.aiMain,
          editorMaskColorHandler: (context, pointerDown) {
            if (pointerDown) {
              return MColor.black.withOpacity(0.02);
            }
            return MColor.black.withOpacity(0.8);
          },
          cropRectPadding: EdgeInsets.symmetric(horizontal: 10),
          hitTestSize: 20.0,
          cropAspectRatio: CropAspectRatios.ratio4_3,
        );
      },
    );
    return builder;
  }

  bool _cropping = false;
  Future<void> cropImage() async {
    if (_cropping) {
      return;
    }
    _cropping = true;
    try {
      fileData = await cropImageDataWithNativeLibrary(state: editorKey.currentState!);
    } catch (e) {
      EasyLoading.showError('save faild');
    } finally {
      _cropping = false;
    }
  }

  /// 预览区的确认按钮
  @override
  Widget buildConfirmButton(BuildContext context) {
    return MaterialButton(
      minWidth: 20,
      height: 32,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      color: MColor.aiMain,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(3),
      ),
      onPressed: createAssetEntityAndPop,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      child: Text(
        widget.pickerConfig.textDelegate?.confirm ?? '',
        style: TextStyle(
          color: theme.textTheme.bodyLarge?.color,
          fontSize: 17,
          fontWeight: FontWeight.normal,
        ),
      ),
    );
  }
}

void handleErrorWithHandler(
  Object e,
  StackTrace s,
  CameraErrorHandler? handler,
) {
  if (handler != null) {
    handler(e, s);
    return;
  }
  Error.throwWithStackTrace(e, s);
}

extension AiSafeSetStateExtension on State {
  /// [setState] after the [fn] is done while the [State] is still [mounted]
  /// and [State.context] is safe to mark needs build.
  FutureOr<void> safeSetState(FutureOr<dynamic> Function() fn) async {
    await fn();
    if (mounted && !context.debugDoingBuild && context.owner?.debugBuilding != true) {
      // ignore: invalid_use_of_protected_member
      setState(() {});
    }
  }
}
