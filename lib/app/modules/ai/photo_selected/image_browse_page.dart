import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_swiper_null_safety/flutter_swiper_null_safety.dart';
import 'package:inspector/app/config/design.dart';
import 'action_picture/ai_image_info.dart';
import 'package:inspector/generated/l10n.dart';

class ImageBrowsePage extends StatefulWidget {
  final String title;
  final int selected;
  final List<AiImageInfo> images;
  final bool canDelete;

  const ImageBrowsePage(
      {this.title = '图片',
      this.selected = 0,
      this.images = const [],
      this.canDelete = true,
      super.key});

  @override
  State createState() => _ImageBrowsePageState();

  static Future pushBrowse(BuildContext context, String url) {
    return Navigator.push(context, MaterialPageRoute(
      builder: (context) {
        return ImageBrowsePage(
          title: S.of(context).ai_inspection_image,
          selected: 0,
          images: [AiImageInfo(url: url)],
          canDelete: false,
        );
      },
    ));
  }
}

class _ImageBrowsePageState extends State<ImageBrowsePage> {
  late int selected = widget.selected;
  late List<AiImageInfo> images = widget.images;

  ///item 视图
  Widget itemView(BuildContext context, int index) {
    final info = images[index];
    final config = GestureConfig(
      minScale: 0.5,
      animationMinScale: 0.4,
      maxScale: 2.0,
      animationMaxScale: 3.5,
      speed: 1.0,
      inertialSpeed: 100.0,
      initialScale: 1.0,
      inPageView: true,
      initialAlignment: InitialAlignment.center,
    );
    final fit = BoxFit.contain;
    if (info.hasUrl) {
      return ExtendedImage.network(
        info.url!,
        fit: fit,
        mode: ExtendedImageMode.gesture,
        initGestureConfigHandler: (ExtendedImageState state) {
          return config;
        },
      );
    } else {
      return FutureBuilder(
        future: info.assetEntity!.file,
        builder: (context, snapshot) {
          if (!snapshot.hasData) return Center(child: CupertinoActivityIndicator());
          return ExtendedImage.file(
            snapshot.data!,
            fit: fit,
            mode: ExtendedImageMode.gesture,
            initGestureConfigHandler: (ExtendedImageState state) {
              return config;
            },
          );
        },
      );
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        centerTitle: true,
        actions: widget.canDelete
            ? [
                TextButton(
                    onPressed: deleteImage,
                    child: Text(
                      S.of(context).ai_delete,
                      style: TextStyle(color: MColor.failed),
                    ))
              ]
            : null,
      ),
      body: GestureDetector(
        child: Container(
          color: Colors.black,
          child: Swiper(
            itemBuilder: itemView,
            loop: false,
            index: selected,
            itemCount: images.length,
            pagination: SwiperPagination(
                alignment: Alignment.bottomCenter,
                builder: MyFractionPaginationBuilder(
                  activeColor: Colors.white,
                  fontSize: 12,
                  activeFontSize: 12,
                )),
            onIndexChanged: (value) => selected = value,
            onTap: (index) {
              Navigator.pop(context);
            },
            // pagination: SwiperPag
          ),
        ),
        onTap: () => Navigator.pop(context),
      ),
    );
  }

  void deleteImage() {
    images.removeAt(selected);
    if (images.isEmpty) {
      Navigator.pop(context);
    } else {
      setState(() {});
    }
  }
}

class MyFractionPaginationBuilder extends SwiperPlugin {
  ///color ,if set null , will be Theme.of(context).scaffoldBackgroundColor
  final Color? color;

  ///color when active,if set null , will be Theme.of(context).primaryColor
  final Color? activeColor;

  ////font size
  final double fontSize;

  ///font size when active
  final double activeFontSize;

  final Key? key;

  const MyFractionPaginationBuilder(
      {this.color, this.fontSize = 20.0, this.key, this.activeColor, this.activeFontSize = 35.0});

  @override
  Widget build(BuildContext context, SwiperPluginConfig config) {
    if (config.itemCount <= 1) return SizedBox.shrink();
    ThemeData themeData = Theme.of(context);
    Color activeColor = this.activeColor ?? themeData.primaryColor;
    Color color = this.color ?? themeData.scaffoldBackgroundColor;

    if (Axis.vertical == config.scrollDirection) {
      return Column(
        key: key,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            '${config.activeIndex + 1}',
            style: TextStyle(color: activeColor, fontSize: activeFontSize),
          ),
          Text(
            '/',
            style: TextStyle(color: color, fontSize: fontSize),
          ),
          Text(
            '${config.itemCount}',
            style: TextStyle(color: color, fontSize: fontSize),
          )
        ],
      );
    } else {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 8),
        height: 18,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.3),
          borderRadius: BorderRadius.circular(9),
        ),
        child: Row(
          key: key,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              '${config.activeIndex + 1}',
              style: TextStyle(color: activeColor, fontSize: activeFontSize),
            ),
            Text(
              ' / ${config.itemCount}',
              style: TextStyle(color: color, fontSize: fontSize),
            )
          ],
        ),
      );
    }
  }
}
