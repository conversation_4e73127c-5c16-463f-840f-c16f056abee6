import 'dart:io';
import 'dart:typed_data' as typed_data;

import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:inspector/app/modules/ai/photo_selected/upload/asset_cache_service.dart';
import 'package:inspector/app/modules/ai/photo_selected/upload/file_ext.dart';

/// 图片处理工具类
class ImageService {
  /// 图片压缩
  static Future<File> compressAndGetFile(File file,
      {String? targetPath, bool needLogInfo = true}) async {
    try {
      var fileName = file.absolute.path.split('/').last;

      // Directory tempDir = await getTemporaryDirectory();
      // Directory assetDir = Directory('${tempDir.path}/asset');
      // if (!assetDir.existsSync()) {
      //   assetDir.createSync();
      //   debugPrint('assetDir 文件保存路径为 ${assetDir.path}');
      // }

      Directory? assetDir = await AssetCacheService().getDir();
      var tmpPath = '${assetDir.path}/$fileName';
      targetPath ??= tmpPath;

      final filePath = file.absolute.path;
      var format = CompressFormat.jpeg;
      if (filePath.toLowerCase().endsWith(".png")) {
        format = CompressFormat.png;
      } else if (filePath.toLowerCase().endsWith(".webp")) {
        format = CompressFormat.webp;
      } else if (filePath.toLowerCase().endsWith(".heic")) {
        format = CompressFormat.heic;
      }
      final compressQuality = file.lengthSync().compressQuality;
      var result = await FlutterImageCompress.compressAndGetFile(
        filePath,
        targetPath,
        quality: compressQuality,
        format: format,
      );
      final path = result?.path;
      if (result == null || path == null || path.isEmpty) {
        debugPrint("压缩文件路径获取失败");
        return file;
      }

      /* if (needLogInfo) {
        final length = await result.length();
        final infos = [
          "图片名称: $fileName",
          "压缩前: ${file.lengthSync().fileSizeDesc}",
          "压缩质量: $compressQuality",
          "压缩后: ${length.fileSizeDesc}",
          "原路径: ${file.absolute.path}",
          "压缩路径: $targetPath",
        ];
        debugPrint("图片压缩: ${infos.join("\n")}");
      }*/

      return File(path);
    } catch (e) {
      debugPrint("compressAndGetFile:${e.toString()}");
    }
    return file;
  }

  /// 图片压缩
  static Future<typed_data.Uint8List> compressWithList(
      typed_data.Uint8List image, String title) async {
    try {
      var format = CompressFormat.jpeg;
      if (title.toLowerCase().endsWith(".png")) {
        format = CompressFormat.png;
      } else if (title.toLowerCase().endsWith(".webp")) {
        format = CompressFormat.webp;
      } else if (title.toLowerCase().endsWith(".heic")) {
        format = CompressFormat.heic;
      }
      final compressQuality = image.length.compressQuality;
      // final compressQuality = 60;
      var result = await FlutterImageCompress.compressWithList(
        image,
        quality: compressQuality,
        format: format,
      );
/*
      final infos = [
        "图片名称: $title",
        "压缩质量: $compressQuality",
        "压缩前: ${image.length.fileSizeDesc}",
        "压缩后: ${result.length.fileSizeDesc}",
      ];
      debugPrint("图片压缩: ${infos.join("\n")}");
*/
      return result;
    } catch (e) {
      return image;
    }
  }

  /// 图像裁剪
/*
  static Future<File?> toCropImage(
    File file, {
    int? maxWidth,
    int? maxHeight,
    bool showLoading = false,
    ImageCompressFormat compressFormat = ImageCompressFormat.jpg,
    int compressQuality = 90,
    CropAspectRatio? aspectRatio,
  }) async {
    final sourcePath = file.path;

    var croppedFile = await ImageCropper().cropImage(
      sourcePath: sourcePath,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      compressFormat: compressFormat,
      compressQuality: compressQuality,
      aspectRatio: aspectRatio,
    );

    if (croppedFile == null) {
      return null;
    }
    return File(croppedFile.path);
  }
*/
}

/// 图片文件扩展方法
extension ImageFileExt on File {
  /// 图片压缩
  Future<File> toCompressImage() async {
    var compressFile = await ImageService.compressAndGetFile(this);
    return compressFile;
  }

  /// 图片裁剪
/*
  Future<File?> toCropImage({
    int? maxWidth,
    int? maxHeight,
    bool showLoading = true,
  }) async {
    var compressFile = await ImageService().toCropImage(this,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      showLoading: showLoading,
    );
    return compressFile;
  }
*/
}
