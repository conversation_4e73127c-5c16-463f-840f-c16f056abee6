import 'dart:io';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/photo_selected/utils/crop_editor_helper.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';
import 'package:uuid/uuid.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class ImageEditPage extends StatefulWidget{

  final String path;

  const ImageEditPage({required this.path, super.key});

  @override
  State<StatefulWidget> createState() => ImageEditPageState();

}

class ImageEditPageState extends State<ImageEditPage>{

  GlobalKey<ExtendedImageEditorState> editorKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.black,
      appBar: AppBar(
        title: Text(S.of(context).edit),
        centerTitle: true,
      ),
      body: ExtendedImage.file(
        File(widget.path),
        fit: BoxFit.contain,
        mode: ExtendedImageMode.editor,
        extendedImageEditorKey: editorKey,
        cacheRawData: true,
        initEditorConfigHandler: (state) {
          return EditorConfig(
            maxScale: 8.0,
            cornerColor: MColor.aiMain,
            cornerSize: Size(20, 3),
            lineColor: MColor.aiMain,
            editorMaskColorHandler: (context, pointerDown) {
              if (pointerDown) {
                return MColor.black.withAlpha(100);
              }
              return MColor.black.withAlpha(100);
            },
            cropRectPadding: EdgeInsets.symmetric(horizontal: 10),
            hitTestSize: 20.0,
            cropAspectRatio: CropAspectRatios.ratio4_3,
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
          child: Text(S.of(context).confirm),
          onPressed: () async{
            try {
              await EasyLoading.show(maskType: EasyLoadingMaskType.black);
              EditImageInfo? fileData = await cropImageDataWithNativeLibrary(state: editorKey.currentState!);
              if(fileData.data != null){
                Uuid uuid = Uuid();
                String fileName = '${uuid.v4()}.jpg';
                final AssetEntity entity = await PhotoManager.editor.saveImage(fileData.data!, filename: fileName,);
                await EasyLoading.dismiss();
                if(context.mounted){
                  Navigator.of(context).pop(entity);
                }
              }
            } catch (e) {
              await EasyLoading.showError('save faild');
            }
          }),
    );
  }

}