import 'package:flutter/cupertino.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../entiy/ai_envent_imgage.dart';

class AiImageUploadAction with ChangeNotifier {
  final AssetEntity asset;
  String? _uploadUrl;
  String? get uploadUrl => _uploadUrl;
  double _progress = 0;
  double get progress => _progress;

  AiImageUploadStateType state = AiImageUploadStateType.wait;

  AiImageUploadAction(this.asset);

  Future<String?> startUpload() async {
    final url = await AiProvider().uploadAsset(
      asset,
      onSendProgress: (count, total) {
        _progress = count / total;
        if (count < total) {
          state = AiImageUploadStateType.loading;
        } else if (count >= total) {
          state = AiImageUploadStateType.success;
        }
        notifyListeners();
      },
    ).then(
      (value) {
        _uploadUrl = value;
        state = _uploadUrl == null ? AiImageUploadStateType.fail : AiImageUploadStateType.success;
        /*if(state == AiImageUploadStateType.success){
          PhotoManager.plugin.deleteWithId(asset.id);
        }*/
        // print('========= startUpload end ${asset.id}');
        // print('========= $value');
        notifyListeners();
        return value;
      },
    );
    return url;
  }
}

enum AiImageUploadStateType {
  wait,
  loading,
  success,
  fail;
}
