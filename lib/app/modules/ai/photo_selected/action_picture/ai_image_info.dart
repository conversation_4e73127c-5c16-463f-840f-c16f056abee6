import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:inspector/app/modules/ai/checkout/ai_checkout_controller.dart';
import 'package:inspector/app/modules/ai/measure/ai_measure_controller.dart';
import 'package:inspector/app/modules/ai/number/ai_number_controller.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/app/modules/ai/photo_selected/action_picture/ai_image_upload_action.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_controller.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_quick_controller.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../entiy/ai_envent_imgage.dart';

class AiImageInfo {
  AiImageInfo({this.url, this.assetEntity})
      : assert(url != null || assetEntity != null) {
    updateUploadAction();
  }

  String? url;
  AssetEntity? assetEntity;

  bool get hasUrl => url?.isNotEmpty ?? false;
  bool get hasAssets => assetEntity != null;
  bool get onlyUrl => hasUrl && !hasAssets;
  bool get onlyAssets => hasAssets && !hasUrl;

  late AiImageUploadAction? uploadAction;
  void updateUploadAction() {
    if (onlyAssets) {
      uploadAction = AiImageUploadAction(assetEntity!);
    } else {
      uploadAction = null;
      return;
    }
    uploadAction?.startUpload();
    uploadAction?.addListener(() {
      if (uploadAction!.uploadUrl != null && uploadAction!.uploadUrl!.isNotEmpty) {
        print('发送图片事件');
        if(Get.isRegistered<AiPackageController>()){
          AiPackageController controller = Get.find<AiPackageController>();
          controller.aiImageChange(uploadAction!.uploadUrl!);
        }
        if(Get.isRegistered<AiMeasureController>()){
          AiMeasureController controller = Get.find<AiMeasureController>();
          controller.aiImageChange(uploadAction!.uploadUrl!);
        }
        if(Get.isRegistered<AiCheckoutController>()){
          /*AiCheckoutController controller = Get.find<AiCheckoutController>();
          controller.aiImageChange(uploadAction!.uploadUrl!);*/
          eventBus.fire(AiEnventImgage(uploadAction!.uploadUrl!));
        }
        if(Get.isRegistered<AiNumberController>()){
          AiNumberController controller = Get.find<AiNumberController>();
          controller.aiImageChange(uploadAction!.uploadUrl!);
        }
        if(Get.isRegistered<AiQualityController>()){
          AiQualityController controller = Get.find<AiQualityController>();
          controller.aiImageChange(uploadAction!.uploadUrl!);
        }
        if(Get.isRegistered<AiQualityQuickController>()){
          AiQualityQuickController controller = Get.find<AiQualityQuickController>();
          controller.aiImageChange(uploadAction!.uploadUrl!);
        }
        //eventBus.fire(AiEnventImgage(uploadAction!.uploadUrl!));
      }
    });
  }

  static List<AiImageInfo> fromList(
      {List<String>? urlList, List<AssetEntity>? assets}) {
    List<AiImageInfo> result = [];
    if (urlList?.isNotEmpty ?? false) {
      result.addAll(urlList!.map((e) => AiImageInfo(url: e)));
    }
    if (assets?.isNotEmpty ?? false) {
      result.addAll(assets!.map((e) => AiImageInfo(assetEntity: e)));
    }
    return result;
  }
}
