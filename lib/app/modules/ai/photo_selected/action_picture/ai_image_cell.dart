import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/photo_selected/action_picture/ai_image_info.dart';
import 'package:inspector/app/modules/ai/photo_selected/action_picture/ai_image_upload_action.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class AiImageCell extends StatefulWidget {
  final AiImageInfo info;
  final ValueChanged<AiImageInfo>? onTap;

  const AiImageCell(this.info, {this.onTap, super.key});

  @override
  State<AiImageCell> createState() => _AiImageCellState();
}

class _AiImageCellState extends State<AiImageCell> {
  AiImageInfo get info => widget.info;
  AiImageUploadAction? get uploadAction => info.uploadAction;

  @override
  void didUpdateWidget(covariant AiImageCell oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  Widget buildOverlay() {
    if (uploadAction != null) {
      return ListenableBuilder(
        listenable: uploadAction!,
        builder: (context, child) {
          switch (uploadAction!.state) {
            case AiImageUploadStateType.wait:
              return SizedBox.shrink();
            case AiImageUploadStateType.success:
              info.url = uploadAction!.uploadUrl;
              return SizedBox.shrink();
            case AiImageUploadStateType.loading:
              return GFProgressBar(
                backgroundColor: MColor.xFF565656,
                progressBarColor: MColor.white,
                type: GFProgressType.circular,
                radius: 20,
                percentage: uploadAction!.progress,
              );
            case AiImageUploadStateType.fail:
              return GestureDetector(
                onTap:(){
                  uploadAction!.startUpload();
                } ,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.refresh),
                    Text(
                      '重新上传',
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              );
          }
        },
      );
    }
    return SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    ImageProvider? content;
    final borderRadius = BorderRadius.circular(8);
    if (info.hasAssets) {
      content = AssetEntityImage(
        info.assetEntity!,
      ).image;
      print('hasAssetshasAssetshasAssets');
    } else if (info.hasUrl) {
      print('info.url==='+info.url!);
      content = CachedNetworkImageProvider(info.url!);
    }

    return GestureDetector(
      onTap: uploadAction?.state == AiImageUploadStateType.fail
          ? null
          : () {
              widget.onTap?.call(info);
            },
      child: GFImageOverlay(
        boxFit: BoxFit.cover,
        borderRadius: borderRadius,
        image: content,
        child: buildOverlay(),
      ),
    );
  }
}

class AiImageUploadController {}
