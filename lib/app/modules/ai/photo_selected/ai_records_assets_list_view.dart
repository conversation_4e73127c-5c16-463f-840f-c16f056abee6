import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/entiy/image_info_entity.dart';
import 'package:inspector/app/modules/ai/photo_selected/picker_method.dart';
import 'package:inspector/app/modules/ai/photo_selected/selected_assets_list_view.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../../../generated/assets.dart';
import '../ai_net/ai_server.dart';
import 'action_picture/ai_image_info.dart';
import 'image_browse_page.dart';

class AiRecordsAssetsListView extends StatefulWidget {
  const AiRecordsAssetsListView(
      {required this.manager,   this.showAdd=false,this.canEdit= false});

  final List<String> manager;
  final bool showAdd;
  final bool canEdit;
  @override
  State<AiRecordsAssetsListView> createState() => _AiFormAssetsListViewState();
}

class _AiFormAssetsListViewState extends State<AiRecordsAssetsListView> {
  @override
  Widget build(BuildContext context) {
        return SingleChildScrollView(
          child: Row(
            children: [
              if(widget.showAdd)
                _add(),
              for(var i=0;i<  widget.manager.length;i++)...{
                _img(widget.manager[i],i)
              }
            ],
          ),
    );
  }

  void changeAssetsOnPicker(List<AssetEntity>? assets) {
    EasyLoading.show();
    if (assets != null) {
      List<AssetEntity> tempAssets = assets;
      for (final a in tempAssets) {
          // imageInfoList.add(AiImageInfo(assetEntity: a));
          startUpload(a,tempAssets.length);
      }
    }
  }
  int tempIndex = 0;
  Future<String?> startUpload(asset,count) async {
    final url = await AiProvider().uploadAsset(
      asset,
      onSendProgress: (count, total) {
        // _progress = count / total;
        // if (count < total) {
        //   state = AiImageUploadStateType.loading;
        // } else if (count >= total) {
        //   state = AiImageUploadStateType.success;
        // }
      },
    ).then(
          (value) {
            tempIndex++;

            widget.manager.add(value!);
            setState(() {
            });
            if(count==tempIndex){
              EasyLoading.dismiss();
            }
        // _uploadUrl = value;
        return value;
      },
    );
    return url;
  }


  Widget _add(){
    return InkWell(
      onTap: () async {

        // final assets = controller.packagedNum?.assetsIn;
        // final inCount = controller.packagedNum!.imageInfoList.length - assets!.length;
        final list = await PickMethod(
          maxAssetsCount: max(100 - max(0, 20), 0),
        ).forCamera(context);
        print('listlist=='+list.toString());
        changeAssetsOnPicker(list);

        // Navigator.push(context, MaterialPageRoute(
        //   builder: (context) {
        //     return ImageBrowsePage(title: S.of(context).ai_inspection_image, selected: index, images: imageInfoList);
        //   },
        // ));
      },
      child: Container(
        width: 62,
        height: 62,
        margin: EdgeInsets.only(right: 10),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            border: Border.all(color: MColor.xFFC4C4C4)
        ),
        child: Image.asset(
          Assets.productPic,
        ),
      )
    ) ;
  }

  Widget _img(url,index){
    return InkWell(
      onTap: () async {
        // final assets = controller.packagedNum?.assetsIn;
        // final inCount = controller.packagedNum!.imageInfoList.length - assets!.length;
        final list = await PickMethod(
          maxAssetsCount: max(100 - max(0, 20), 0),
        ).forCamera(context);
        // controller.packagedNum?.changeAssetsOnPicker(list);

        // Navigator.push(context, MaterialPageRoute(
        //   builder: (context) {
        //     return ImageBrowsePage(title: S.of(context).ai_inspection_image, selected: index, images: imageInfoList);
        //   },
        // ));
      },
      child: Stack(
        children: [
          Container(
            width: 62,
              height: 62,
              margin: EdgeInsets.only(right: 10),
              child:  ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child:  Image.network(url,width: 62,height: 62,fit: BoxFit.cover,),

              )
          ),
          widget.canEdit?
          Positioned(
               top: -15,
              right: -10,
              child: IconButton(
            onPressed: (){
              setState(() {
                widget.manager.removeAt(index);
              });

            },
            icon: Icon(Icons.highlight_off, size: 18.0,color: Colors.white,),
          )):Container()
        ],
      )
    ) ;
  }

}
