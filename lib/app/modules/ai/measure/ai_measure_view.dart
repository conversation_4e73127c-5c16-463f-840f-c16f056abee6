import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_measure_data.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/entiy/type/ai_measure_type.dart';
import 'package:inspector/app/modules/ai/measure/ai_measure_controller.dart';
import 'package:inspector/app/modules/ai/number/ai_number_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_image_selected_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_input_devect_tile.dart';
import 'package:inspector/app/modules/ai/widgets/ai_tips_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_widget_style.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

import '../ai_pages.dart';
import '../category/controller/ai_category_controller.dart';
import '../package/ai_package_view.dart';

class AiMeasureView extends GetView<AiMeasureController> {
  const AiMeasureView({super.key});

  @override
  Widget build(BuildContext context) {
    var mContext;
    return KeyboardDismissOnTap(
      child: Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          title: Text(AiCategoryType.measure.name),
          centerTitle: true,
            leading: Builder(builder: (BuildContext context) {
              return IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: (){
                  Get.back();
                },
              );
            }),
            actions: [
              Obx(() {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if(AiCategoryController.to.qulityNum.value==0)
                      InkWell(
                          onTap: (){
                            Get.toNamed(  AiRoutes.QUALITY_QUICK,
                              arguments: {
                              },)?.then((value){
                              AiCategoryController.to.loadQulity();
                            });
                          },
                          child:Icon(Icons.remove_red_eye_outlined,color: MColor.skin,)
                      )else
                      InkWell(
                          onTap: (){
                            Get.toNamed(  AiRoutes.QUALITY_QUICK,
                              arguments: {
                              },)?.then((value){
                              AiCategoryController.to.loadQulity();
                            });
                          },

                          child:Badge(
                            label: Text(AiCategoryController.to.qulityNum.value>0?AiCategoryController.to.qulityNum.value.toString():''),
                            child:Icon(Icons.remove_red_eye_outlined,color: MColor.skin,),
                          )
                      )
                  ],
                );
              })

              ,
              SizedBox(width: 5,),
              IconButton(onPressed: (){
                Scaffold.of(mContext).openDrawer();
              }, icon: Icon(Icons.format_align_center_outlined,color: MColor.skin)),
            ]
        ),
        drawer:  AiSideItem(categoryList: AiCategoryController.to.categoryList,),
        body: DefaultTextStyle(
          style: MFont.medium14.copyWith(color: MColor.black),
          child: StatefulBuilder(builder: (context, setState) {
            mContext = context;
            return Obx(() {
              return Column(
                children: [
                  AiProductSelectBar(loader: controller),
                  Expanded(
                    child: AiEmptyView(
                      baseResult: controller.baseResult,
                      child: ListView(
                        children: [
                          Padding(
                            padding: EdgeInsets.only(left:20,right:20,bottom: 15),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                 S.current.ai_measurement_project,
                                  style: MFont.medium18,
                                ),
                                Spacer(),
                                  GestureDetector(
                                    onTap: () {
                                       Get.toNamed(
                                        AiRoutes.MEASURE_MANAGE,
                                        arguments: {
                                        },
                                        parameters: Get.parameters as Map<String, String>,
                                      )?.then((value){
                                        setState((){});
                                      });
                                    },
                                    child: Container(
                                      height: 27.0,
                                      width: 60,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        color: MColor.skin,
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      child: Text(
                                        S.of(context).public_manage,
                                        style: MFont.medium14.copyWith(color: MColor.white),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          for(int i=0;i<(controller.inputList??[]).length;i++)...{
                            _AiMeasureItem(form: controller.inputList![i],
                              onAdded: (value) async {
                                controller.goDetail(value,i, setState);
                              },
                            ),
                          }
                          // for (AiItemForms form in controller.inputList ?? [])

                        ],
                      ),
                    ),
                  ),

                  AiSubmitButton.submit(
                    onPressed: controller.submit,
                  ),
                ],
              );
            });
          }),
        ),
      ),
    );
  }
}

class _AiMeasureItem extends StatefulWidget {
  const _AiMeasureItem({required this.form, required this.onAdded,super.key});

  final AiItemForms form;
  final ValueChanged<AiItemForms?>? onAdded;
  @override
  State<_AiMeasureItem> createState() => _AiMeasureItemState();
}

class _AiMeasureItemState extends State<_AiMeasureItem> {
  bool _expanded = false;

  Color get borderColor => _expanded ? MColor.aiMain : MColor.xFFE5E5E5;

  IconData get iconData =>
      _expanded ? Icons.keyboard_arrow_down_rounded : Icons.keyboard_arrow_right_rounded;

  @override
  Widget build(BuildContext context) {
    final titleWidth = 80.0;
    return _row(widget.form,0);
  }

  Widget _row(AiItemForms item,index){
    return InkWell(
        onTap: (){
          widget.onAdded!(item);
          // widget.onClickItem!(index);
        },
        child: Container(
          padding: EdgeInsets.fromLTRB(17, 8, 13, 8),
          margin: EdgeInsets.only(bottom: 2),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(5)
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(child: Text(item.name??'',
                softWrap: true,
                textAlign: TextAlign.left,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(color: MColor.black,fontSize: 16),))
              ,
              Row(
                children: [
                  SizedBox(width: 10,),
                    _tag(item.status??0),
                  SizedBox(width: 14,),
                  Icon(Icons.keyboard_arrow_right_outlined, )
                ],
              )
            ],
          ),
        )
    ) ;
  }

  Widget _tag(int status){
    Color color= MColor.xFF92D050;
    String text ='';
    if(status==1){
      text = S.current.approve;
      color= MColor.xFF92D050;
    }
    else if(status==2){//失败
      color= MColor.xFFFF1111;
      text = S.current.ai_default_config_fail;
    }else if(status==3){// 待定
      color= MColor.xFFDF8D14;
      text = S.current.ai_wait;
    }else{
      return Container();
    }

    return Container(
      padding: EdgeInsets.fromLTRB(7, 2, 7, 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Text(text,style: TextStyle(color: MColor.black,fontSize: 12),),
    );
  }

  Widget _buildRowText({required String title, required String value}) {
    return Container(
      color: MColor.white,
      padding: EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(title),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

///测量记录
class _AiMeasureInfoRecord extends StatefulWidget {
  final AiMeasureInfo info;
  final AiItemForms form;
  final int index;
  final VoidCallback deleteCall; //TODO:AI 下策

  const _AiMeasureInfoRecord(
      {required this.info,
      required this.form,
      required this.index,
      required this.deleteCall,
      super.key});

  @override
  _AiMeasureInfoRecordState createState() => _AiMeasureInfoRecordState();
}

class _AiMeasureInfoRecordState extends State<_AiMeasureInfoRecord> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: MColor.white,
      margin: EdgeInsets.only(top: 10),
      padding: EdgeInsets.fromLTRB(20, 15, 20, 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '测量记录-${widget.index}',
                style: MFont.medium18,
              ),
              Spacer(),
              PopupMenuButton<String>(
                child: Icon(Icons.more_horiz),
                color: MColor.xFFFAFAFA,
                itemBuilder: (context) {
                  return [
                    PopupMenuItem(
                      child: Text(S.of(context).ai_edit),
                      onTap: () async {
                        await showCupertinoModalPopup(
                          context: context,
                          builder: (context) {
                            return _AiMeasureInfoInputView(info: widget.info);
                          },
                        );
                        setState(() {});
                      },
                    ),
                    PopupMenuItem(
                      child: Text(S.of(context).ai_delete),
                      onTap: () {
                        showCustomDialog(
                          S.of(context).ai_confirm_delete('"测量记录-${widget.index}"'),
                          cancel: true,
                          onConfirm: () {
                            widget.form.removeMeasureInfo(widget.info);
                            widget.deleteCall();
                          },
                        );
                      },
                    )
                  ];
                },
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 20),
            child: Text(
              S.of(context).ai_measurement_record,
            ),
          ),
          AiFormAssetsListView(
            manager: widget.info,
            padding: EdgeInsets.only(top: 10),
          ),
          _buildRowText(
              title: '${S.of(context).ai_measurement_record}：',
              value: widget.info.measureValueName),
          _buildRowText(
              title: '${S.of(context).ai_product_number}：', value: widget.info.productNumber ?? ''),
          Obx(() {
            if (widget.info.editTimeTitleObs.isEmpty) return SizedBox.shrink();
            return Container(
              alignment: Alignment.centerRight,
              color: MColor.white,
              child: Text(
                widget.info.editTimeTitleObs.value,
                style: MFont.medium12.copyWith(color: MColor.xFF808080),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildRowText({required String title, required String value}) {
    return Padding(
      padding: EdgeInsets.only(
        top: 20,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(title),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

///弹出输入项
class _AiMeasureInfoInputView extends StatefulWidget {
  final AiMeasureInfo? info;
  final String? type;
  const _AiMeasureInfoInputView({this.type, this.info}) : assert(info != null || type != null);

  @override
  State<_AiMeasureInfoInputView> createState() => _AiMeasureInfoInputViewState();
}

class _AiMeasureInfoInputViewState extends State<_AiMeasureInfoInputView> {
  late final AiMeasureInfo _info = widget.info ?? AiMeasureInfo(type: widget.type);

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissOnTap(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          title: Text(S.of(context).ai_measurement_record),
          centerTitle: true,
        ),
        body: Container(
          width: Get.width,
          padding: EdgeInsets.fromLTRB(20, 20, 20, 0),
          decoration: BoxDecoration(
              color: MColor.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(10),
              )),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  SizedBox(
                    width: 80,
                    child: Text(S.of(context).ai_defect_photo),
                  ),
                  AiImageSelectedButton(manager: _info),
                ],
              ),
              AiFormAssetsListView(
                manager: _info,
                padding: EdgeInsets.only(top: 20),
              ),
              SizedBox(height: 15),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(width: 80, child: Text('*${S.of(context).ai_measured_value}')),
                  _textField(
                    name: _info.isWeight
                        ? S.of(context).ai_measured_value
                        : S.of(context).ai_dimensions_length,
                    value: _info.l,
                    onChanged: (value) {
                      _info.l = value;
                      _info.refreshEditTime();
                    },
                  ),
                  if (_info.isDimensions || _info.isLengthWidth)
                    _textField(
                      name: S.of(context).ai_dimensions_width,
                      value: _info.w,
                      onChanged: (value) {
                        _info.w = value;
                        _info.refreshEditTime();
                      },
                    ),
                  if (_info.isDimensions)
                    _textField(
                      name: S.of(context).ai_dimensions_height,
                      value: _info.h,
                      onChanged: (value) {
                        _info.h = value;
                        _info.refreshEditTime();
                      },
                    ),
                ],
              ),
              SizedBox(height: 15),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(width: 80, child: Text(S.of(context).ai_product_number)),
                  _textField(
                    name: S.of(context).ai_number,
                    value: _info.productNumber,
                    onChanged: (value) => _info.productNumber = value,
                  ),
                  Spacer(
                    flex: 2,
                  ),
                ],
              ),
              SizedBox(height: 10),
              if (widget.info == null) bottomButton(),
            ],
          ),
        ),
      ),
    );
  }

  Expanded _textField({
    required String name,
    required ValueChanged<String> onChanged,
    String? value,
  }) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(right: 10),
        child: TextField(
          decoration: InputDecoration(
            hintText: name,
            enabledBorder: AiWidgetStyle.enabledBorder,
            focusedBorder: AiWidgetStyle.focusedBorder,
            border: AiWidgetStyle.focusedBorder,
            contentPadding: EdgeInsets.symmetric(horizontal: 12),
            constraints: BoxConstraints.tight(const Size.fromHeight(40)),
          ),
          controller: TextEditingController(text: value),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget bottomButton() {
    return Container(
      padding: EdgeInsets.only(top: 50.0, bottom: 30),
      child: AiModalBottomButton(
        confirm: () {
          bool writeAll = true;
          if (_info.isWeight) {
            writeAll = _info.l?.isNotEmpty ?? false;
          } else if (_info.isLengthWidth) {
            writeAll = writeAll && (_info.l?.isNotEmpty ?? false) && (_info.w?.isNotEmpty ?? false);
          } else if (_info.isDimensions) {
            writeAll = writeAll &&
                (_info.l?.isNotEmpty ?? false) &&
                (_info.w?.isNotEmpty ?? false) &&
                (_info.h?.isNotEmpty ?? false);
          }
          if (!writeAll) {
            EasyLoading.showError(
                '${S.of(context).ai_please_fill_in}${S.of(context).ai_measured_value}');
            return;
          }
          Get.back(result: _info);
        },
      ),
    );
  }
}

///新建测量项
class AiMeasureNewItem extends StatefulWidget {
  const AiMeasureNewItem({this.fromForm, super.key});
  final AiItemForms? fromForm;
  @override
  State<AiMeasureNewItem> createState() => _AiMeasureNewItemState();
}

class _AiMeasureNewItemState extends State<AiMeasureNewItem> {
  late AiItemForms form = widget.fromForm ??
      AiItemForms(key: AiItemForms.generatedKey, measureStandardData: AiMeasureData());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(S.of(context).ai_new_measurement_item),
        centerTitle: true,
      ),
      body: KeyboardDismissOnTap(
        child: Container(
          width: Get.width,
          padding: EdgeInsets.fromLTRB(20, 20, 20, 0),
          decoration: BoxDecoration(
              color: MColor.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(10),
              )),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AiInputCell(
                title: S.of(context).ai_measurement_project,
                titleWidth: 120,
                hint: S.of(context).ai_measurement_project,
                initial: form.name,
                numberOnly: false,
                padding: EdgeInsets.only(bottom: 10),
                onChanged: (value) {
                  form.name = value;
                  form.refreshEditTime();
                },
              ),
              AiInputCell(
                title: S.of(context).ai_measurement_unit,
                titleWidth: 120,
                hint: S.of(context).ai_measurement_unit,
                initial: form.measureStandardData?.measureUnit,
                numberOnly: false,
                padding: EdgeInsets.only(bottom: 10),
                onChanged: (value) {
                  form.measureStandardData!.measureUnit = value;
                  form.refreshEditTime();
                },
              ),
              AiInputCell(
                title: S.of(context).ai_measurement_method,
                titleWidth: 120,
                hint: S.of(context).ai_measurement_method,
                initial: form.measureStandardData?.measureType,
                menus: AiMeasureType.values.map((e) => e.name).toList(),
                menusValues: AiMeasureType.values.map((e) => e.value).toList(),
                onChanged: (value) {
                  form.measureStandardData!.measureType = value;
                  form.refreshEditTime();
                },
                padding: EdgeInsets.only(bottom: 10),
              ),
              if (widget.fromForm == null) bottomButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget bottomButton() {
    return Container(
      padding: EdgeInsets.only(top: 50.0, bottom: 30),
      child: AiModalBottomButton(
        confirm: () {
          if (form.name?.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_fill_in}${S.of(context).ai_measurement_project}');
            return;
          }
          if (form.measureStandardData?.measureUnit.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_fill_in}${S.of(context).ai_measurement_unit}');
            return;
          }
          if (form.measureStandardData?.measureType.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_select}${S.of(context).ai_measurement_method}');
            return;
          }
          Get.back(result: form);
        },
      ),
    );
  }
}
