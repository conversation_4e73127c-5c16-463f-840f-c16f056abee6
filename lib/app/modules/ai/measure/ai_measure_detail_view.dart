import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/entiy/ai_measure_data.dart';
import 'package:inspector/app/modules/ai/measure/ai_measure_controller.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';

import '../ai_pages.dart';
import '../widgets/ai_judge_result.dart';

class AiMeasureDetailView extends GetView<AiMeasureController> {
  const AiMeasureDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissOnTap(
      child: Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          title: Text(controller.clickItem!.name!),
          centerTitle: true,
        ),
        body: DefaultTextStyle(
          style: MFont.medium14.copyWith(color: MColor.black),
          child: StatefulBuilder(builder: (context, setState) {
            return Column(
              children: [
                Expanded(
                  child: AiEmptyView(
                    baseResult: controller.baseResult,
                    child: Column(
                      children: [
                        Expanded(
                            child: AiEmptyView(
                                baseResult: controller.baseResult,
                                child: SingleChildScrollView(
                                    child: Column(
                                  children: [
                                    _instruction(S.current.general_desc, 1),

                                    // _instruction(S.current.ai_product_number,2),
                                    Container(
                                      alignment: Alignment.topLeft,
                                      margin: EdgeInsets.only(
                                          left: 20, top: 10, bottom: 10),
                                      child: Text(
                                        S.current.ai_measure_require,
                                        style: MFont.medium16,
                                      ),
                                    ),
                                    _requireItem(setState),

                                    Padding(
                                      padding: EdgeInsets.only(
                                          left: 20,
                                          right: 20,
                                          bottom: 15,
                                          top: 15),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            S.current.ai_measurement_standard,
                                            style: MFont.medium18,
                                          ),
                                          Spacer(),
                                          GestureDetector(
                                            onTap: () {
                                              Get.toNamed(
                                                      AiRoutes.MEASURE_Standard)
                                                  ?.then((value) {
                                                setState(() {});
                                              });
                                            },
                                            child: Container(
                                              height: 27.0,
                                              width: 60,
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                color: MColor.skin,
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                              ),
                                              child: Text(
                                                S.of(context).edit,
                                                style: MFont.medium14.copyWith(
                                                    color: MColor.white),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    _builTable(
                                        title: S.current.ai_measurement_item,
                                        value1: S.current.order_goods_unit,
                                        value2: S.current
                                            .ai_measurement_value_standard,
                                        value3:
                                            '${S.current.ai_measurement_error}(±)',
                                        isFirst: true,
                                        item: null),
                                    for (var item in controller.clickItem
                                            ?.measureStandardData?.values ??
                                        []) ...{
                                      _builTable(
                                          title: item.name ?? '/',
                                          value1: item.unitType ?? '/',
                                          value2:
                                              controller.getMeasureData(item),
                                          value3: item.error ?? '/',
                                          isFirst: false,
                                          item: item),
                                    },

                                    Padding(
                                      padding: EdgeInsets.only(
                                          left: 20,
                                          right: 20,
                                          bottom: 15,
                                          top: 15),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            S.current.ai_measurement_record,
                                            style: MFont.medium18,
                                          ),
                                          Spacer(),
                                          Row(
                                            children: [
                                              GestureDetector(
                                                onTap: () {
                                                  setState(() {
                                                    controller.addOneRecord();
                                                  });
                                                },
                                                child: Container(
                                                  height: 27.0,
                                                  width: 60,
                                                  alignment: Alignment.center,
                                                  decoration: BoxDecoration(
                                                    color: MColor.skin,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            5),
                                                  ),
                                                  child: Text(
                                                    S.of(context).ai_add_plus,
                                                    style: MFont.medium14
                                                        .copyWith(
                                                            color:
                                                                MColor.white),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                width: 5,
                                              ),
                                              if (controller.clickItem!
                                                  .measureData!.isNotEmpty)
                                                GestureDetector(
                                                  onTap: () {
                                                    controller.operateRecords =
                                                        controller.clickRecords;
                                                    Get.toNamed(AiRoutes
                                                            .MEASURE_RECORD)
                                                        ?.then((value) {
                                                      controller
                                                              .operateRecords =
                                                          controller
                                                              .clickRecords;
                                                      if ((controller
                                                                  .operateRecords
                                                                  ?.values ??
                                                              [])
                                                          .isEmpty) {
                                                        controller.clickItem!
                                                            .measureData
                                                            ?.removeAt(
                                                                controller
                                                                    .showIndex);
                                                        if ((controller
                                                                    .clickItem!
                                                                    .measureData ??
                                                                [])
                                                            .isNotEmpty) {
                                                          controller.showIndex =
                                                              0;
                                                          controller
                                                                  .clickRecords =
                                                              controller
                                                                  .clickItem!
                                                                  .measureData
                                                                  ?.firstOrNull;
                                                        }
                                                      }
                                                      setState(() {});
                                                    });
                                                  },
                                                  child: Container(
                                                    height: 27.0,
                                                    width: 60,
                                                    alignment: Alignment.center,
                                                    decoration: BoxDecoration(
                                                      color: MColor.skin,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              5),
                                                    ),
                                                    child: Text(
                                                      S.of(context).edit,
                                                      style: MFont.medium14
                                                          .copyWith(
                                                              color:
                                                                  MColor.white),
                                                    ),
                                                  ),
                                                )
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),

                                    if (controller.clickItem!.measureData!
                                        .isNotEmpty) ...{
                                      Stack(
                                        children: [
                                          Column(
                                            children: [
                                              _recordRows(setState),
                                              _builTable2(
                                                  title: S.current
                                                      .ai_measurement_item,
                                                  value1: S.current
                                                      .ai_measured_value,
                                                  value2: [],
                                                  value3: S.current
                                                      .ai_measurement_camera,
                                                  value4: S.current
                                                      .ai_product_number,
                                                  isFirst: true,
                                                  item: null),
                                              for (AiMeasureInfo item
                                                  in controller.clickRecords
                                                          ?.values ??
                                                      []) ...{
                                                _builTable2(
                                                    title: item.name ?? '',
                                                    value1:
                                                        item.measureValue ?? '',
                                                    value2: item.images ?? [],
                                                    value3: '',
                                                    value4:
                                                        item.productNumber ??
                                                            '',
                                                    isFirst: false,
                                                    item: item),
                                              },
                                            ],
                                          ),
                                          controller.showMore
                                              ? Positioned(
                                                  top: 0,
                                                  child: _more(setState),
                                                )
                                              : Container(),
                                        ],
                                      ),
                                    },
                                    _judge(),
                                  ],
                                )))),
                        Container(
                          margin: EdgeInsets.only(left: 20, right: 20),
                          child: AiSubmitButton(
                              onPressed: () {
                                controller.submit2();
                              },
                              name: S.current.apply_submit,
                              isSave: false),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget _judge() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 15,
        ),
        Container(
            padding: EdgeInsets.only(left: 20),
            child: Text(
              S.current.ai_judgment,
              style: MFont.medium18,
              textAlign: TextAlign.start,
            )),
        SizedBox(
          height: 15,
        ),
        AiJudgeView(
          isSame: controller.clickItem?.status,
          noteChange: (value) => controller.result?.remark = value,
          resultChange: (value) => {
            controller.clickItem?.status = value,
          },
        ),
      ],
    );
  }

  Widget _requireItem(setState) {
    return Container(
      padding: EdgeInsets.fromLTRB(20, 18, 20, 18),
      margin: EdgeInsets.only(top: 5),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(S.current.ai_measure_need_num),
          InkWell(
              onTap: () {
                Get.toNamed(AiRoutes.MEASURE_REQUIRE, arguments: {
                  'num': controller.clickItem?.required.toString()
                })?.then((value) {
                  Map map = value;
                  setState(() {
                    if (map['num'] != null) {
                      controller.clickItem?.required = int.parse(map['num']);
                    } else {
                      controller.clickItem?.required = 0;
                    }
                  });
                });
              },
              child: Row(
                children: [
                  Text(controller.clickItem?.sampleNum?.values?.firstOrNull ??
                      '/'),
                  SizedBox(
                    width: 10,
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 14,
                  ),
                ],
              ))
        ],
      ),
    );
  }

  Widget _instruction(desc, type) {
    return ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: 80,
          maxHeight: 120,
        ),
        child: Container(
          margin: EdgeInsets.only(top: 5),
          padding: EdgeInsets.fromLTRB(20, 8, 20, 8),
          color: Colors.white,
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(desc),
                  SizedBox(
                    width: 60,
                  ),
                  Expanded(
                      child: SingleChildScrollView(
                          child: Text(
                              controller.clickItem?.tips!.values!.firstOrNull ??
                                  '')))
                ],
              ),
            ],
          ),
        ));
  }

  Widget _builTable(
      {required String title,
      required String value1,
      required String value2,
      required String value3,
      required bool isFirst,
      required dynamic item}) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
          color: Colors.white,
          border: isFirst
              ? Border.all(width: 1, color: MColor.xFFE5E5E5)
              : Border(
                  left: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                  bottom: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                  right: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                )),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            width: 115,
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1, //宽度
                  color: MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(
                isFirst ? title : controller.getMeasureName(item.type ?? '')),
          ),
          Container(
            alignment: Alignment.center,
            width: ((Get.width - 115 - 3) / 3),
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1, //宽度
                  color: MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(value1),
          ),
          Container(
            alignment: Alignment.center,
            width: ((Get.width - 115 - 6) / 3),
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1, //宽度
                  color: MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(isFirst ? value2 : controller.getMeasureData(item)),
          ),
          Container(
            alignment: Alignment.center,
            width: ((Get.width - 115 - 6) / 3),
            height: 60,
            decoration: BoxDecoration(),
            child: Text(value3),
          ),
        ],
      ),
    );
  }

  Widget _builTable2(
      {required String title,
      required String value1,
      required List<String> value2,
      required String value3,
      required String value4,
      required bool isFirst,
      required dynamic item}) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
          color: Colors.white,
          border: isFirst
              ? Border.all(width: 1, color: MColor.xFFE5E5E5)
              : Border(
                  left: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                  bottom: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                  right: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                )),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            width: 115,
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1, //宽度
                  color: MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(isFirst ? title : controller.getMeasureName(item.type)),
          ),
          Container(
            alignment: Alignment.center,
            width: ((Get.width - 115 - 3) / 3),
            height: 60,
            decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                ),
                color: (isFirst || controller.isOutRange(item))
                    ? Colors.white
                    : MColor.skin
                // color:  Colors.white
                ),
            child: Text(isFirst ? value1 : controller.getMeasureData(item)),
          ),
          Container(
            alignment: Alignment.center,
            width: ((Get.width - 115 - 3) / 3),
            height: 60,
            decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                ),
                color: Colors.white
                // color:  Colors.white
                ),
            child: Text(value4),
          ),
          if (value3.isNotEmpty)
            Container(
              alignment: Alignment.center,
              width: ((Get.width - 115 - 3) / 3),
              height: 60,
              decoration: BoxDecoration(),
              child: Text(value3),
            )
          else ...{
            if (value2.isEmpty)
              Container(
                  alignment: Alignment.center,
                  width: ((Get.width - 115 - 6) / 3),
                  height: 60,
                  decoration: BoxDecoration(),
                  child: Image.asset(
                    'assets/images/product_pic.png',
                    width: 22,
                    height: 22,
                  ))
            else
              Container(
                  alignment: Alignment.center,
                  width: ((Get.width - 115 - 6) / 3),
                  height: 60,
                  decoration: BoxDecoration(),
                  child: _img(value2))
          },
        ],
      ),
    );
  }

  Widget _img(List<String> imgs) {
    if (imgs.length > 1) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          for (int i = 0; i < 2; i++) ...{
            Stack(
              children: [
                Image.network(
                  imgs[i],
                  width: 36,
                  height: 36,
                ),
                if (i == 1)
                  Container(
                    decoration: BoxDecoration(color: Color(0xFFFFFF99)),
                    child: Text('+${imgs.length - 2}'),
                  )
              ],
            )
          }
        ],
      );
    } else {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          for (var item in imgs) ...{
            ClipRRect(
                borderRadius: BorderRadius.circular(5), // 圆角半径
                child: Image.network(
                  item,
                  width: 36,
                  height: 36,
                  fit: BoxFit.cover,
                ))
          }
        ],
      );
    }
  }

  //横向滚动
  Widget _recordRows(setState) {
    return Container(
      margin: EdgeInsets.only(top: 15),
      padding: EdgeInsets.only(left: 20, right: 14, top: 8, bottom: 8),
      decoration: BoxDecoration(color: Colors.white),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
              width: Get.width - 110,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    for (int i = 0;
                        i < controller.clickItem!.measureData!.length;
                        i++)
                      _recordsWidget(
                          controller.clickItem!.measureData?[i], i, setState)
                  ],
                ),
              )),
          InkWell(
              onTap: () {
                setState(() {
                  controller.showMore = true;
                });
              },
              child: Container(
                  width: 50,
                  margin: EdgeInsets.only(left: 10, right: 0),
                  child: Image.asset(
                    'assets/images/more.png',
                    width: 20,
                    height: 20,
                  )))
        ],
      ),
    );
  }

  Widget _recordsWidget(item, index, setState) {
    return InkWell(
      onTap: () {
        controller.changeItem(index, item, setState);
      },
      child: Container(
          margin: EdgeInsets.only(right: 10),
          child: Column(
            children: [
              Text(S.current.ai_simple_record + (index + 1).toString(),
                  style: TextStyle(
                    color: MColor.black,
                    fontSize: 16,
                  )),
              SizedBox(
                height: 5,
              ),
              if (controller.showIndex == index)
                Container(
                  height: 2,
                  color: MColor.skin,
                  width: 30,
                ),
            ],
          )),
    );
  }

  Widget _more(setState) {
    return Container(
      width: Get.width,
      color: Colors.white,
      child: Column(
        children: [
          Wrap(
            direction: Axis.horizontal,
            children: [
              for (int i = 0;
                  i < controller.clickItem!.measureData!.length;
                  i++) ...{
                _moreItem(controller.clickItem!.measureData?[i], i, setState)
              }
            ],
          ),
          IconButton(
            onPressed: () {
              setState(() {
                controller.showMore = false;
              });
            },
            icon: Icon(
              Icons.arrow_drop_up_sharp,
              color: MColor.skin,
              size: 50,
            ),
            padding: EdgeInsets.all(0),
          )
        ],
      ),
    );
  }

  Widget _moreItem(item, index, setState) {
    return InkWell(
        onTap: () {
          controller.changeItem(index, item, setState);
        },
        child: Container(
          alignment: Alignment.center,
          width: Get.width / 3,
          height: 40,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                S.current.ai_simple_record + (index + 1).toString(),
                style: MFont.regular16,
              ),
              SizedBox(
                height: 5,
              ),
              if (controller.showIndex == index)
                Container(
                  height: 2,
                  color: MColor.skin,
                  width: 30,
                ),
            ],
          ),
        ));
  }
}
