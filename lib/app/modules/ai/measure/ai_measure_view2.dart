import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_measure_data.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/entiy/type/ai_measure_type.dart';
import 'package:inspector/app/modules/ai/measure/ai_measure_controller.dart';
import 'package:inspector/app/modules/ai/number/ai_number_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_image_selected_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_input_devect_tile.dart';
import 'package:inspector/app/modules/ai/widgets/ai_tips_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_widget_style.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

class AiMeasureView extends GetView<AiMeasureController> {
  const AiMeasureView({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissOnTap(
      child: Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          title: Text(AiCategoryType.measure.name),
          centerTitle: true,
        ),
        body: DefaultTextStyle(
          style: MFont.medium14.copyWith(color: MColor.black),
          child: StatefulBuilder(builder: (context, setState) {
            return Obx(() {
              return Column(
                children: [
                  AiProductSelectBar(loader: controller),
                  Expanded(
                    child: AiEmptyView(
                      baseResult: controller.baseResult,
                      child: ListView(
                        children: [
                          for (AiItemForms form in controller.inputList ?? [])
                            _AiMeasureItem(form: form),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 30),
                            child: Divider(
                              height: 1,
                              color: MColor.xFFE5E5E5,
                            ),
                          ),
                          Center(
                            child: DottedBorder(
                              color: MColor.aiMain,
                              borderType: BorderType.RRect,
                              radius: Radius.circular(5),
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () async {
                                  final form = await showCupertinoModalPopup(
                                    context: context,
                                    builder: (context) {
                                      return AiMeasureNewItem();
                                    },
                                  );
                                  if (form != null) {
                                    setState(() => controller.addInputItem(form));
                                  }
                                },
                                child: Container(
                                  width: 166,
                                  height: 37,
                                  alignment: Alignment.center,
                                  child: Text(
                                    S.of(context).ai_add_plus,
                                    style: TextStyle(color: MColor.aiMain),
                                  ),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  AiSubmitButton.submit(
                    onPressed: controller.toNext,
                  ),
                ],
              );
            });
          }),
        ),
      ),
    );
  }
}

class _AiMeasureItem extends StatefulWidget {
  const _AiMeasureItem({required this.form, super.key});

  final AiItemForms form;

  @override
  State<_AiMeasureItem> createState() => _AiMeasureItemState();
}

class _AiMeasureItemState extends State<_AiMeasureItem> {
  bool _expanded = false;

  Color get borderColor => _expanded ? MColor.aiMain : MColor.xFFE5E5E5;

  IconData get iconData =>
      _expanded ? Icons.keyboard_arrow_down_rounded : Icons.keyboard_arrow_right_rounded;

  @override
  Widget build(BuildContext context) {
    final titleWidth = 80.0;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 38,
          margin: EdgeInsets.only(top: 5),
          padding: EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(color: MColor.white, border: Border.all(color: borderColor)),
          child: GestureDetector(
            onTap: () => setState(() => _expanded = !_expanded),
            behavior: HitTestBehavior.translucent,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.form.name ?? '',
                    maxLines: _expanded ? null : 1,
                    overflow: _expanded ? null : TextOverflow.ellipsis,
                    style: MFont.medium18,
                  ),
                ),
                if (widget.form.measureNeedNum.isNotEmpty)
                  // Container(
                  //     height: 18,
                  //     alignment: Alignment.center,
                  //     padding: EdgeInsets.symmetric(horizontal: 8),
                  //     decoration: BoxDecoration(
                  //         color: MColor.aiMain, borderRadius: BorderRadius.circular(9)),
                  //     child: Text(
                  //       '${widget.form.measureData?.values?.length ?? 0}/${widget.form.measureNeedNum}',
                  //       style: MFont.medium12.copyWith(color: MColor.white),
                  //     )),
                Icon(
                  iconData,
                  color: borderColor,
                ),
              ],
            ),
          ),
        ),
        if (_expanded) ...[
          ColoredBox(
              color: MColor.white,
              child: Padding(
                  padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
                  child: Row(
                    children: [
                      Text(S.of(context).ai_basic_information),
                      Spacer(),
                      PopupMenuButton<String>(
                        color: MColor.xFFFAFAFA,
                        itemBuilder: (context) {
                          return [
                            PopupMenuItem(
                              child: Text(S.of(context).ai_edit),
                              onTap: () async {
                                await showCupertinoModalPopup(
                                  context: context,
                                  builder: (context) {
                                    return AiMeasureNewItem(fromForm: widget.form);
                                  },
                                );
                                setState(() {});
                              },
                            ),
                            PopupMenuItem(
                              child: Text(S.of(context).ai_delete),
                              onTap: () {
                                showCustomDialog(
                                    S.of(context).ai_confirm_delete('"${widget.form.name ?? ''}"'),
                                    cancel: true,
                                    onConfirm: () =>
                                        AiMeasureController.to.removeInputItem(widget.form));
                              },
                            )
                          ];
                        },
                        child: Icon(Icons.more_horiz),
                      ),
                    ],
                  ))),
          ColoredBox(
            color: MColor.white,
            child: Padding(
              padding: EdgeInsets.fromLTRB(20, 10, 20, 0),
              child: Row(
                children: [
                  Expanded(
                      child: AiInputCell(
                    padding: EdgeInsets.zero,
                    titleWidth: titleWidth,
                    initial: widget.form.measureStandardData?.measureUnit,
                    readOnly: true,
                    onChanged: (value) => widget.form.measureStandardData?.measureUnit = value,
                    title: '${S.of(context).ai_unit_of_measurement}：',
                    hint: S.of(context).ai_unit_of_measurement,
                  )),
                  AiTipsButton(
                    richText: widget.form.tips?.values,
                    images: widget.form.orderRequire?.values,
                  ),
                ],
              ),
            ),
          ),
          if (widget.form.measureNeedNum.isNotEmpty)
            _buildRowText(
                title: S.of(context).ai_measure_need_num, value: widget.form.measureNeedNum!),
          // _buildRowText(
          //     title: S.of(context).ai_measured,
          //     value: widget.form.measureInfoLength.value.toString()),
          Container(
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: MColor.white,
              ),
              child: Row(
                children: [
                  SizedBox(width: titleWidth, child: Text(S.of(context).ai_measurement_record)),
                  GestureDetector(
                    onTap: () async {
                      final info = await showCupertinoModalPopup(
                        context: context,
                        builder: (context) {
                          return _AiMeasureInfoInputView(
                              type: widget.form.measureStandardData?.measureType ?? '');
                        },
                      );
                      if (info != null) {
                        widget.form.addMeasureInfo(info);
                      }
                      setState(() {});
                    },
                    child: Container(
                      height: 27.0,
                      width: titleWidth,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: MColor.aiMain)),
                      child: Text(
                        S.of(context).ai_add_plus,
                        style: MFont.medium14.copyWith(color: MColor.aiMain),
                      ),
                    ),
                  )
                ],
              )),
          Obx(() {
            if (widget.form.editTimeTitleObs.isEmpty) return SizedBox.shrink();
            return Container(
              alignment: Alignment.centerRight,
              padding: EdgeInsets.only(bottom: 10, right: 20),
              color: MColor.white,
              child: Text(
                widget.form.editTimeTitleObs.value,
                style: MFont.medium12.copyWith(color: MColor.xFF808080),
              ),
            );
          }),

          ///记录的数据
          // for (int i = 0; i < (widget.form.measureInfoLength.value); i++)
          //   _AiMeasureInfoRecord(
          //       info: widget.form.measureData!.values![i],
          //       index: i + 1,
          //       form: widget.form,
          //       deleteCall: () {
          //         setState(() {});
          //       }),
        ]
      ],
    );
  }

  Widget _buildRowText({required String title, required String value}) {
    return Container(
      color: MColor.white,
      padding: EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(title),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

///测量记录
class _AiMeasureInfoRecord extends StatefulWidget {
  final AiMeasureInfo info;
  final AiItemForms form;
  final int index;
  final VoidCallback deleteCall; //TODO:AI 下策

  const _AiMeasureInfoRecord(
      {required this.info,
      required this.form,
      required this.index,
      required this.deleteCall,
      super.key});

  @override
  _AiMeasureInfoRecordState createState() => _AiMeasureInfoRecordState();
}

class _AiMeasureInfoRecordState extends State<_AiMeasureInfoRecord> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: MColor.white,
      margin: EdgeInsets.only(top: 10),
      padding: EdgeInsets.fromLTRB(20, 15, 20, 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '测量记录-${widget.index}',
                style: MFont.medium18,
              ),
              Spacer(),
              PopupMenuButton<String>(
                child: Icon(Icons.more_horiz),
                color: MColor.xFFFAFAFA,
                itemBuilder: (context) {
                  return [
                    PopupMenuItem(
                      child: Text(S.of(context).ai_edit),
                      onTap: () async {
                        await showCupertinoModalPopup(
                          context: context,
                          builder: (context) {
                            return _AiMeasureInfoInputView(info: widget.info);
                          },
                        );
                        setState(() {});
                      },
                    ),
                    PopupMenuItem(
                      child: Text(S.of(context).ai_delete),
                      onTap: () {
                        showCustomDialog(
                          S.of(context).ai_confirm_delete('"测量记录-${widget.index}"'),
                          cancel: true,
                          onConfirm: () {
                            widget.form.removeMeasureInfo(widget.info);
                            widget.deleteCall();
                          },
                        );
                      },
                    )
                  ];
                },
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 20),
            child: Text(
              S.of(context).ai_measurement_record,
            ),
          ),
          AiFormAssetsListView(
            manager: widget.info,
            padding: EdgeInsets.only(top: 10),
          ),
          _buildRowText(
              title: '${S.of(context).ai_measurement_record}：',
              value: widget.info.measureValueName),
          _buildRowText(
              title: '${S.of(context).ai_product_number}：', value: widget.info.productNumber ?? ''),
          Obx(() {
            if (widget.info.editTimeTitleObs.isEmpty) return SizedBox.shrink();
            return Container(
              alignment: Alignment.centerRight,
              color: MColor.white,
              child: Text(
                widget.info.editTimeTitleObs.value,
                style: MFont.medium12.copyWith(color: MColor.xFF808080),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildRowText({required String title, required String value}) {
    return Padding(
      padding: EdgeInsets.only(
        top: 20,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(title),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

///弹出输入项
class _AiMeasureInfoInputView extends StatefulWidget {
  final AiMeasureInfo? info;
  final String? type;
  const _AiMeasureInfoInputView({this.type, this.info}) : assert(info != null || type != null);

  @override
  State<_AiMeasureInfoInputView> createState() => _AiMeasureInfoInputViewState();
}

class _AiMeasureInfoInputViewState extends State<_AiMeasureInfoInputView> {
  late final AiMeasureInfo _info = widget.info ?? AiMeasureInfo(type: widget.type);

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissOnTap(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          title: Text(S.of(context).ai_measurement_record),
          centerTitle: true,
        ),
        body: Container(
          width: Get.width,
          padding: EdgeInsets.fromLTRB(20, 20, 20, 0),
          decoration: BoxDecoration(
              color: MColor.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(10),
              )),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  SizedBox(
                    width: 80,
                    child: Text(S.of(context).ai_defect_photo),
                  ),
                  AiImageSelectedButton(manager: _info),
                ],
              ),
              AiFormAssetsListView(
                manager: _info,
                padding: EdgeInsets.only(top: 20),
              ),
              SizedBox(height: 15),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(width: 80, child: Text('*${S.of(context).ai_measured_value}')),
                  _textField(
                    name: _info.isWeight
                        ? S.of(context).ai_measured_value
                        : S.of(context).ai_dimensions_length,
                    value: _info.l,
                    onChanged: (value) {
                      _info.l = value;
                      _info.refreshEditTime();
                    },
                  ),
                  if (_info.isDimensions || _info.isLengthWidth)
                    _textField(
                      name: S.of(context).ai_dimensions_width,
                      value: _info.w,
                      onChanged: (value) {
                        _info.w = value;
                        _info.refreshEditTime();
                      },
                    ),
                  if (_info.isDimensions)
                    _textField(
                      name: S.of(context).ai_dimensions_height,
                      value: _info.h,
                      onChanged: (value) {
                        _info.h = value;
                        _info.refreshEditTime();
                      },
                    ),
                ],
              ),
              SizedBox(height: 15),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(width: 80, child: Text(S.of(context).ai_product_number)),
                  _textField(
                    name: S.of(context).ai_number,
                    value: _info.productNumber,
                    onChanged: (value) => _info.productNumber = value,
                  ),
                  Spacer(
                    flex: 2,
                  ),
                ],
              ),
              SizedBox(height: 10),
              if (widget.info == null) bottomButton(),
            ],
          ),
        ),
      ),
    );
  }

  Expanded _textField({
    required String name,
    required ValueChanged<String> onChanged,
    String? value,
  }) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(right: 10),
        child: TextField(
          decoration: InputDecoration(
            hintText: name,
            enabledBorder: AiWidgetStyle.enabledBorder,
            focusedBorder: AiWidgetStyle.focusedBorder,
            border: AiWidgetStyle.focusedBorder,
            contentPadding: EdgeInsets.symmetric(horizontal: 12),
            constraints: BoxConstraints.tight(const Size.fromHeight(40)),
          ),
          controller: TextEditingController(text: value),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget bottomButton() {
    return Container(
      padding: EdgeInsets.only(top: 50.0, bottom: 30),
      child: AiModalBottomButton(
        confirm: () {
          bool writeAll = true;
          if (_info.isWeight) {
            writeAll = _info.l?.isNotEmpty ?? false;
          } else if (_info.isLengthWidth) {
            writeAll = writeAll && (_info.l?.isNotEmpty ?? false) && (_info.w?.isNotEmpty ?? false);
          } else if (_info.isDimensions) {
            writeAll = writeAll &&
                (_info.l?.isNotEmpty ?? false) &&
                (_info.w?.isNotEmpty ?? false) &&
                (_info.h?.isNotEmpty ?? false);
          }
          if (!writeAll) {
            EasyLoading.showError(
                '${S.of(context).ai_please_fill_in}${S.of(context).ai_measured_value}');
            return;
          }
          Get.back(result: _info);
        },
      ),
    );
  }
}

///新建测量项
class AiMeasureNewItem extends StatefulWidget {
  const AiMeasureNewItem({this.fromForm, super.key});
  final AiItemForms? fromForm;
  @override
  State<AiMeasureNewItem> createState() => _AiMeasureNewItemState();
}

class _AiMeasureNewItemState extends State<AiMeasureNewItem> {
  late AiItemForms form = widget.fromForm ??
      AiItemForms(key: AiItemForms.generatedKey, measureStandardData: AiMeasureData());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(S.of(context).ai_new_measurement_item),
        centerTitle: true,
      ),
      body: KeyboardDismissOnTap(
        child: Container(
          width: Get.width,
          padding: EdgeInsets.fromLTRB(20, 20, 20, 0),
          decoration: BoxDecoration(
              color: MColor.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(10),
              )),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AiInputCell(
                title: S.of(context).ai_measurement_project,
                titleWidth: 120,
                hint: S.of(context).ai_measurement_project,
                initial: form.name,
                numberOnly: false,
                padding: EdgeInsets.only(bottom: 10),
                onChanged: (value) {
                  form.name = value;
                  form.refreshEditTime();
                },
              ),
              AiInputCell(
                title: S.of(context).ai_measurement_unit,
                titleWidth: 120,
                hint: S.of(context).ai_measurement_unit,
                initial: form.measureStandardData?.measureUnit,
                numberOnly: false,
                padding: EdgeInsets.only(bottom: 10),
                onChanged: (value) {
                  form.measureStandardData!.measureUnit = value;
                  form.refreshEditTime();
                },
              ),
              AiInputCell(
                title: S.of(context).ai_measurement_method,
                titleWidth: 120,
                hint: S.of(context).ai_measurement_method,
                initial: form.measureStandardData?.measureType,
                menus: AiMeasureType.values.map((e) => e.name).toList(),
                menusValues: AiMeasureType.values.map((e) => e.value).toList(),
                onChanged: (value) {
                  form.measureStandardData!.measureType = value;
                  form.refreshEditTime();
                },
                padding: EdgeInsets.only(bottom: 10),
              ),
              if (widget.fromForm == null) bottomButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget bottomButton() {
    return Container(
      padding: EdgeInsets.only(top: 50.0, bottom: 30),
      child: AiModalBottomButton(
        confirm: () {
          if (form.name?.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_fill_in}${S.of(context).ai_measurement_project}');
            return;
          }
          if (form.measureStandardData?.measureUnit.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_fill_in}${S.of(context).ai_measurement_unit}');
            return;
          }
          if (form.measureStandardData?.measureType.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_select}${S.of(context).ai_measurement_method}');
            return;
          }
          Get.back(result: form);
        },
      ),
    );
  }
}
