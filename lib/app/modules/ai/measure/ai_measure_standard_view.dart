import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/generated/l10n.dart';

import '../../../tools/tools.dart';
import '../entiy/ai_measure_data.dart';
import '../widgets/submit_button.dart';
import 'ai_measure_controller.dart';

class AiMeasureStandardView<T extends AiMeasureController> extends GetView<T> {
  const AiMeasureStandardView({super.key});


  @override
  Widget build(BuildContext context) {
    return  StatefulBuilder(builder: (context, setState) {
      return  Scaffold(
          backgroundColor: MColor.backgroundColor,
          appBar: AppBar(
            title: Text(S.current.ai_measurement_standard),
            centerTitle: true,
            actions: [
              InkWell(
                  onTap: (){
                    controller.clickStandard = AiMeasureInfo.empty();
                    controller.isNewStandrad = true;
                    showDilog(context,false,(isDelete) async{
                      controller.addOneStandard();
                      await controller.uploadData();
                      setState((){});
                    });
                  },
                  child: Container(
                    margin: EdgeInsets.only(right: 20),
                    padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
                    decoration: BoxDecoration(
                        color: MColor.skin,
                        borderRadius: BorderRadius.circular(5)),
                    child: Text(
                      S.current.ai_add_plus,
                      style: TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ))
            ],
          ),
          body: Column(
            children: [
              SizedBox(
                height: 10,
              ),
              _builTable(
                  title: S.current.ai_measurement_item,
                  value1: S.current.order_goods_unit,
                  value2: S.current.ai_measurement_value_standard,
                  value3: '${S.current.ai_measurement_error}(±)',
                  isFirst: true,
                  item: AiMeasureInfo()),
              // for(var item in controller.clickItem?.measureStandardData?.values??[])...{
              for (int i = 0;
                  i < controller.clickItem!.measureStandardData!.values!.length;
                  i++) ...{
                InkWell(
                  onTap: () {
                    controller.clickStandardIndex = i;
                    controller.clickStandard =
                        controller.clickItem!.measureStandardData!.values?[i];
                    controller.isNewStandrad = false;
                    showDilog(context, true, (isDelete) async{
                      if (isDelete) {
                        await Future.delayed(const Duration(milliseconds: 100), () async{
                          await showCustomDialog(S.current.ai_sure_delete, cancel: true, onConfirm: () {
                            setState(() {
                              controller.clickItem!.measureStandardData!.values
                                  ?.removeAt(controller.clickStandardIndex);
                            });
                          });
                        });
                      } else {
                        controller.clickItem!.measureStandardData!
                                .values?[controller.clickStandardIndex] =
                            controller.clickStandard!;
                      }
                      await controller.uploadData();
                      setState(() {});
                    });
                  },
                  child: _builTable(
                      title: controller.clickItem!.measureStandardData!
                              .values?[i].name ??
                          '',
                      value1: controller.clickItem!.measureStandardData!
                              .values?[i].unitType ??
                          '',
                      value2: controller.getArrayData(i, 'standard'),
                      value3: controller.getArrayData2(i),
                      isFirst: false,
                      item: controller
                          .clickItem!.measureStandardData!.values![i]),
                )
              },
            ],
          ));
    });
  }

  void showDilog(context1, bool isEdit, void Function(bool isDelete) callBack) {
    showGeneralDialog(
        context: context1,
        barrierColor: Colors.black.withOpacity(.5),
        barrierDismissible: true,
        barrierLabel: '',
        pageBuilder: (BuildContext context, Animation<double> animation,
            Animation<double> secondaryAnimation) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
            return Scaffold(
                backgroundColor: Colors.transparent,
                body: Center(
                  child: SingleChildScrollView(
                      child: Container(
                          margin: EdgeInsets.all(20),
                          padding: EdgeInsets.fromLTRB(15, 18, 15, 18),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(5)),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                S.current.edit +
                                    S.current.ai_measurement_standard,
                                style: MFont.medium18,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(S.current.ai_measurement_item),
                                  _choose2(() {
                                    print('22222' +
                                        controller.clickItem!.type.toString());
                                    setState(() {});
                                  })
                                ],
                              ),

                              // _item(S.current.ai_measurement_item,controller.clickStandard?.name??''),
                              // _item(S.current.ai_add_product_unit,''),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(S.current.ai_add_product_unit),
                                  _choose()
                                ],
                              ),
                              _item(S.current.ai_measurement_value_standard,
                                  controller.clickStandard?.l ?? '', 'l'),
                              if (controller.clickStandard?.type !=
                                  'weight') ...{
                                _item(S.current.ai_measurement_value_standard,
                                    controller.clickStandard?.w ?? '', 'w'),
                              },
                              if (controller.clickStandard?.type ==
                                  'dimensions') ...{
                                _item(S.current.ai_measurement_value_standard,
                                    controller.clickStandard?.h ?? '', 'h'),
                              },

                              _item(
                                  '${S.current.ai_measurement_error}(±)',
                                  controller.clickStandard!.error ?? '',
                                  'error'),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  InkWell(
                                      onTap: () {
                                        Navigator.pop(context);
                                      },
                                      child: Container(
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              border: Border.all(
                                                  color: MColor.skin,
                                                  width: 1)),
                                          height: 40,
                                          width: Get.width / 3 - 40,
                                          child: Text(
                                            S.current.order_cancel,
                                            style: TextStyle(
                                                color: MColor.skin,
                                                fontSize: 14),
                                          ))),
                                  if (isEdit)
                                    SizedBox(
                                      width: 10,
                                    ),
                                  if (isEdit)
                                    InkWell(
                                        onTap: () {
                                          callBack(true);
                                          Navigator.pop(context);
                                        },
                                        child: Container(
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              color: MColor.skin,
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                            height: 40,
                                            width: Get.width / 3 - 40,
                                            child: Text(
                                              S.current.address_delete,
                                              style: TextStyle(
                                                  color: MColor.white,
                                                  fontSize: 14),
                                            ))),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  InkWell(
                                      onTap: () {
                                        callBack(false);
                                        Navigator.pop(context);
                                      },
                                      child: Container(
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            color: MColor.skin,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          height: 40,
                                          width: Get.width / 3 - 40,
                                          child: Text(
                                            S.current.charge_submit,
                                            style: TextStyle(
                                                color: MColor.white,
                                                fontSize: 14),
                                          )))
                                ],
                              ),
                            ],
                          ))),
                ));
          });

          ;
        });
  }

  Widget _choose() {
    return Container(
      alignment: Alignment.centerRight,
      width: Get.width - 120,
      padding: EdgeInsets.only(left: 10, top: 13, bottom: 13),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: MColor.xFFE5E5E5, width: 1),
      ),
      child: StatefulBuilder(builder: (context, setState) {
        return PopupMenuButton<String>(
          color: MColor.xFFFAFAFA,
          initialValue: '',
          constraints: BoxConstraints(maxHeight: 240),
          onSelected: (value) {
            setState(() {
              // controller. changeUnit(value);
              // controller.unit = value;

              final startIndex = value.indexOf('(');
              final endIndex = value.indexOf(')');

              if (startIndex != -1 && endIndex != -1 && startIndex < endIndex) {
                value = value.substring(startIndex + 1, endIndex).trim();
              }
              controller.clickStandard!.unitType = value;
            });
          },
          itemBuilder: (context) {
            return controller.unitList
                .map((e) => PopupMenuItem(
                      value: e,
                      child: Text(e),
                    ))
                .toList();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              controller.clickStandard!.unitType == S.current.ai_other
                  ? Expanded(
                      child: TextField(
                      decoration: InputDecoration(
                          hintText: S.current.bind_hint,
                          border: InputBorder.none,
                          isDense: true,
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 0, vertical: 0)),
                      onChanged: (e) {
                        controller.clickStandard!.unitType = e;
                      },
                    ))
                  : Container(
                      child: Text(
                        controller.clickStandard!.unitType!.isNotEmpty
                            ? controller.clickStandard!.unitType!
                            : S.current.ai_please_select,
                        textAlign: TextAlign.end,
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: MColor.xFF808080),
                      ),
                    ),
              Container(
                margin: EdgeInsets.only(right: 10),
                child: Icon(
                  Icons.expand_more,
                  color: MColor.xFF808080,
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _choose2(void Function() callBack) {
    return Container(
      alignment: Alignment.centerRight,
      width: Get.width - 120,
      padding: EdgeInsets.only(left: 10, top: 13, bottom: 13),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: MColor.xFFE5E5E5, width: 1),
      ),
      child: StatefulBuilder(builder: (context, setState) {
        return PopupMenuButton<String>(
          color: MColor.xFFFAFAFA,
          initialValue: '',
          constraints: BoxConstraints(maxHeight: 240),
          onSelected: (value) {
            setState(() {
              if (value == S.current.ai_length_width_height) {
                controller.clickStandard!.type = 'dimensions';
              } else if (value == S.current.ai_length_width) {
                controller.clickStandard!.type = 'lengthWidth';
              } else {
                controller.clickStandard!.type = 'weight';
              }
              print('valuevaluevalue===' + value);
              callBack();
            });
          },
          itemBuilder: (context) {
            return controller.typeList
                .map((e) => PopupMenuItem(
                      value: e,
                      child: Text(e),
                    ))
                .toList();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                child: Text(
                  controller.clickStandard!.type!.isNotEmpty
                      ? controller
                          .getMeasureName(controller.clickStandard!.type!)
                      : S.current.ai_please_select,
                  textAlign: TextAlign.end,
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: MColor.xFF808080),
                ),
              ),
              Container(
                margin: EdgeInsets.only(right: 10),
                child: Icon(
                  Icons.expand_more,
                  color: MColor.xFF808080,
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _builTable(
      {required String title,
      required String value1,
      required String value2,
      required String value3,
      required bool isFirst,
      required AiMeasureInfo item}) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
          color: Colors.white,
          border: isFirst
              ? Border.all(width: 1, color: MColor.xFFE5E5E5)
              : Border(
                  left: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                  bottom: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                  right: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                )),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            width: 115,
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1, //宽度
                  color: MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(
                isFirst ? title : controller.getMeasureName(item.type ?? '')),
          ),
          Container(
            alignment: Alignment.center,
            width: ((Get.width - 115 - 3) / 3),
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1, //宽度
                  color: MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(value1),
          ),
          Container(
            alignment: Alignment.center,
            width: ((Get.width - 115 - 6) / 3),
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1, //宽度
                  color: MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(isFirst ? value2 : controller.getMeasureData(item)),
          ),
          Container(
            alignment: Alignment.center,
            width: ((Get.width - 115 - 6) / 3),
            height: 60,
            decoration: BoxDecoration(),
            child: Text(value3),
          ),
        ],
      ),
    );
  }

  Widget _item(name, value, type) {
    return Container(
        margin: EdgeInsets.only(top: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(name),
            Container(
              width: Get.width - 120,
              padding: EdgeInsets.only(left: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: MColor.xFFE5E5E5, width: 1),
              ),
              child: TextField(
                keyboardType: name != S.current.ai_measurement_item
                    ? TextInputType.number
                    : TextInputType.text,
                style: TextStyle(color: MColor.xFF808080, fontSize: 14),
                controller: TextEditingController(text: value),
                decoration: InputDecoration(
                    hintStyle: TextStyle(color: MColor.xFF808080, fontSize: 14),
                    hintText: S.current.apply_enter,
                    border: InputBorder.none),
                onChanged: (values) {
                  // value = values;

                  if (name == S.current.ai_measurement_item) {
                    controller.clickStandard?.name = values;
                  } else if (name == S.current.ai_measurement_value_standard) {
                    if (type == 'l') {
                      controller.clickStandard?.l = values;
                    } else if (type == 'w') {
                      controller.clickStandard?.w = values;
                    } else if (type == 'h') {
                      controller.clickStandard?.h = values;
                    }
                  } else if (name == '${S.current.ai_measurement_error}(±)') {
                    controller.clickStandard?.error = values;
                  }
                },
              ),
            )
          ],
        ));
  }
}
