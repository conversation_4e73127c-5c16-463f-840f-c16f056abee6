import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/generated/l10n.dart';

import '../../../../generated/assets.dart';
import '../../../tools/tools.dart';
import '../entiy/ai_measure_data.dart';
import '../photo_selected/ai_form_assets_list_view.dart';
import '../photo_selected/picker_method.dart';
import 'ai_measure_controller.dart';

class AiMeasureRecordView<T extends AiMeasureController> extends GetView<T> {
  const AiMeasureRecordView({super.key});

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      return Scaffold(
          backgroundColor: MColor.backgroundColor,
          appBar: AppBar(
            title: Text(S.current.ai_measurement_record),
            centerTitle: true,
          ),
          body: Column(
            children: [
              SizedBox(
                height: 10,
              ),
              _builTable(
                  title: S.current.ai_measurement_item,
                  value1: S.current.ai_measurement_value_standard,
                  value2: [],
                  value3: S.current.ai_measurement_camera,
                  value4: S.current.ai_product_number,
                  isFirst: true,
                  item: AiMeasureInfo()),
              // for(var item in controller.clickItem?.measureStandardData?.values??[])...{
              for (int i = 0;
                  i < controller.operateRecords!.values!.length;
                  i++) ...{
                InkWell(
                  onTap: () {
                    controller.clickStandardIndex = i;
                    controller.clickStandard =
                        controller.operateRecords!.values?[i];
                    controller.isNewStandrad = false;
                    showDilog(context, (isDelete) async{
                      if (isDelete) {
                        await Future.delayed(const Duration(milliseconds: 100), () async{
                          await showCustomDialog(S.current.ai_sure_delete, cancel: true, onConfirm: () {
                            setState(() {
                              controller.operateRecords!.values
                                  ?.removeAt(controller.clickStandardIndex);
                            });
                          });
                        });
                      } else {
                        print('33333333333333333333===>${controller.clickStandard!.images!.length}');
                        logger.e(controller.clickStandard?.toJson());
                        controller.operateRecords!.values?[controller.clickStandardIndex] = controller.clickStandard!;
                        logger.e(controller.operateRecords?.values?.firstOrNull?.toJson());
                      }
                      await controller.uploadData();
                      setState(() {});
                    });
                  },
                  child: _builTable(
                      title: controller.operateRecords!.values?[i].name ?? '',
                      value1:
                          controller.operateRecords!.values?[i].measureValue ??
                              '',
                      value2:
                          controller.operateRecords!.values?[i].images ?? [],
                      value3: '',
                      value4:
                          controller.operateRecords!.values?[i].productNumber ??
                              '',
                      isFirst: false,
                      item: controller.operateRecords!.values![i]),
                )
              },
              SizedBox(
                height: 10,
              ),
              InkWell(
                  onTap: () {
                    controller.clickStandard = AiMeasureInfo.empty();
                    showDilogStandard(context, () {
                      controller.addOneStandard();
                      setState(() {});
                    });
                    // Get.toNamed(AiRoutes.MEASURE_Standard);
                  },
                  child: Text(
                    S.current.ai_measurement_add,
                    style: TextStyle(
                      color: MColor.skin,
                    ),
                  ))
            ],
          ));
    });
  }

  Widget _choose2(void Function() callBack) {
    return Container(
      alignment: Alignment.centerRight,
      width: Get.width - 140,
      padding: EdgeInsets.only(left: 10, top: 13, bottom: 13),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: MColor.xFFE5E5E5, width: 1),
      ),
      child: StatefulBuilder(builder: (context, setState) {
        return PopupMenuButton<String>(
          color: MColor.xFFFAFAFA,
          initialValue: '',
          constraints: BoxConstraints(maxHeight: 240),
          onSelected: (value) {
            setState(() {
              if (value == S.current.ai_length_width_height) {
                controller.clickStandard!.type = 'dimensions';
              } else if (value == S.current.ai_length_width) {
                controller.clickStandard!.type = 'lengthWidth';
              } else {
                controller.clickStandard!.type = 'weight';
              }
              print('valuevaluevalue===' + value);
              callBack();
            });
          },
          itemBuilder: (context) {
            return controller.typeList
                .map((e) => PopupMenuItem(
                      value: e,
                      child: Text(e),
                    ))
                .toList();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                child: Text(
                  controller.clickStandard!.type!.isNotEmpty
                      ? controller
                          .getMeasureName(controller.clickStandard!.type!)
                      : S.current.ai_please_select,
                  textAlign: TextAlign.end,
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: MColor.xFF808080),
                ),
              ),
              Container(
                margin: EdgeInsets.only(right: 10),
                child: Icon(
                  Icons.expand_more,
                  color: MColor.xFF808080,
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  void showDilog(context1, void Function(bool isDelete) callBack) {
    showGeneralDialog(
        context: context1,
        barrierColor: Colors.black.withOpacity(.5),
        barrierDismissible: true,
        barrierLabel: '',
        pageBuilder: (BuildContext context, Animation<double> animation,
            Animation<double> secondaryAnimation) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
            return Scaffold(
                backgroundColor: Colors.transparent,
                body: Center(
                    child: SingleChildScrollView(
                        child: Center(
                  child: Container(
                      margin: EdgeInsets.all(20),
                      padding: EdgeInsets.fromLTRB(15, 18, 15, 18),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5)),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            S.current.edit + S.current.ai_measurement_record,
                            style: MFont.medium18,
                          ),
                          Container(
                            height: 1,
                            color: MColor.xFFE5E5E5,
                            margin: EdgeInsets.only(top: 10, bottom: 10),
                          ),
                          // _item(S.current.ai_measurement_item,controller.clickStandard?.name??'',true),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(S.current.ai_measurement_item),
                              _choose2(() {
                                setState(() {});
                              })
                            ],
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          _item(
                              S.current.ai_product_number,
                              controller.clickStandard?.productNumber ?? '',
                              false,
                              'l'),

                          _item(S.current.ai_measured_value,
                              controller.clickStandard?.l ?? '', false, 'l'),
                          if (controller.clickStandard?.type != 'weight') ...{
                            _item(S.current.ai_measured_value,
                                controller.clickStandard?.w ?? '', false, 'w'),
                          },
                          if (controller.clickStandard?.type ==
                              'dimensions') ...{
                            _item(S.current.ai_measured_value,
                                controller.clickStandard?.h ?? '', false, 'h'),
                          },

                          // _item(S.current.ai_measured_value,controller.clickStandard?.measureValue??'',false),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [Text(S.current.ai_measurement_camera)],
                          ),
                          Row(
                            children: [
                              InkWell(
                                child: Container(
                                  width: 62,
                                  height: 62,
                                  margin: EdgeInsets.only(right: 10),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      border:
                                          Border.all(color: MColor.xFFC4C4C4)),
                                  child: Image.asset(
                                    Assets.productPic,
                                  ),
                                ),
                                onTap: () async {
                                  final assets =
                                      controller.clickStandard?.assetsIn;
                                  final inCount = controller
                                          .clickStandard!.imageInfoList.length -
                                      assets!.length;
                                  final list = await PickMethod(maxAssetsCount: max(100 - max(0, inCount), 0),).forCamera(context);
                                  if (list != null && list.isNotEmpty) {
                                    controller.clickStandard?.changeAssetsOnPicker(list,);
                                    // controller.subscription =    eventBus.on<AiEnventImgage>().listen((event) {
                                    //       controller.clickStandard?.images?.add(event.url);
                                    //     });
                                  }
                                },
                              ),
                              Expanded(
                                child: AiFormAssetsListView(
                                  manager: controller.clickStandard!,
                                  onImageChanged: (res) {
                                    print('~~~~~~~~~~~~~~~~~~~~~~~~~~变化了');
                                    List<String> aa = [];
                                    for (final info in res.imageInfoList.value) {
                                      aa.add(info.url ?? '');
                                    }
                                    controller.clickStandard?.images = aa;
                                  },
                                ),
                              ),
                            ],
                          ),

                          SizedBox(
                            height: 20,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                  },
                                  child: Container(
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                              color: MColor.skin, width: 1)),
                                      height: 40,
                                      width: Get.width / 3 - 40,
                                      child: Text(
                                        S.current.order_cancel,
                                        style: TextStyle(
                                            color: MColor.skin, fontSize: 14),
                                      ))),
                              SizedBox(
                                width: 10,
                              ),
                              InkWell(
                                  onTap: () {
                                    callBack(true);
                                    Navigator.pop(context);
                                  },
                                  child: Container(
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        color: MColor.skin,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      height: 40,
                                      width: Get.width / 3 - 40,
                                      child: Text(
                                        S.current.address_delete,
                                        style: TextStyle(
                                            color: MColor.white, fontSize: 14),
                                      ))),
                              SizedBox(
                                width: 20,
                              ),
                              InkWell(
                                  onTap: () {
                                    if (controller.clickStandard?.productNumber == null || controller.clickStandard!.productNumber!.isEmpty) {
                                      showToast('请输入产品编号');
                                      return;
                                    }
                                    callBack(false);
                                    Navigator.pop(context);
                                  },
                                  child: Container(
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        color: MColor.skin,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      height: 40,
                                      width: Get.width / 3 - 40,
                                      child: Text(
                                        S.current.charge_submit,
                                        style: TextStyle(
                                            color: MColor.white, fontSize: 14),
                                      )))
                            ],
                          ),
                        ],
                      )),
                ))));
          });

          ;
        });
  }

  void showDilogStandard(context1, void Function() callBack) {
    showGeneralDialog(
        context: context1,
        barrierColor: Colors.black.withOpacity(.5),
        barrierDismissible: true,
        barrierLabel: '',
        pageBuilder: (BuildContext context, Animation<double> animation,
            Animation<double> secondaryAnimation) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
            return Scaffold(
                backgroundColor: Colors.transparent,
                body: Center(
                  child: SingleChildScrollView(
                      child: Container(
                          margin: EdgeInsets.all(20),
                          padding: EdgeInsets.fromLTRB(15, 18, 15, 18),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(5)),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                S.current.edit +
                                    S.current.ai_measurement_standard,
                                style: MFont.medium18,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(S.current.ai_measurement_item),
                                  _choose2(() {
                                    print('22222' +
                                        controller.clickItem!.type.toString());
                                    setState(() {});
                                  })
                                ],
                              ),

                              // _item(S.current.ai_measurement_item,controller.clickStandard?.name??''),
                              // _item(S.current.ai_add_product_unit,''),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(S.current.ai_add_product_unit),
                                  _choose()
                                ],
                              ),

                              _item(
                                  S.current.ai_measured_value,
                                  controller.clickStandard?.l ?? '',
                                  false,
                                  'l'),
                              if (controller.clickItem?.type != 'weight') ...{
                                _item(
                                    S.current.ai_measured_value,
                                    controller.clickStandard?.w ?? '',
                                    false,
                                    'w'),
                              },
                              if (controller.clickItem?.type ==
                                  'dimensions') ...{
                                _item(
                                    S.current.ai_measured_value,
                                    controller.clickStandard?.h ?? '',
                                    false,
                                    'h'),
                              },

                              _item(
                                  '${S.current.ai_measurement_error}(±)',
                                  controller.clickItem?.measureAllowNum?.values
                                          ?.firstOrNull ??
                                      '/',
                                  false,
                                  'error'),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  InkWell(
                                      onTap: () {
                                        Navigator.pop(context);
                                      },
                                      child: Container(
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              border: Border.all(
                                                  color: MColor.skin,
                                                  width: 1)),
                                          height: 40,
                                          width: Get.width / 3 - 40,
                                          child: Text(
                                            S.current.order_cancel,
                                            style: TextStyle(
                                                color: MColor.skin,
                                                fontSize: 14),
                                          ))),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  InkWell(
                                      onTap: () {
                                        callBack();
                                        Navigator.pop(context);
                                      },
                                      child: Container(
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            color: MColor.skin,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          height: 40,
                                          width: Get.width / 3 - 40,
                                          child: Text(
                                            S.current.charge_submit,
                                            style: TextStyle(
                                                color: MColor.white,
                                                fontSize: 14),
                                          )))
                                ],
                              ),
                            ],
                          ))),
                ));
          });

          ;
        });
  }

  Widget _choose() {
    return Container(
      alignment: Alignment.centerRight,
      width: Get.width - 140,
      padding: EdgeInsets.only(left: 10, top: 13, bottom: 13),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: MColor.xFFE5E5E5, width: 1),
      ),
      child: StatefulBuilder(builder: (context, setState) {
        return PopupMenuButton<String>(
          color: MColor.xFFFAFAFA,
          initialValue: '',
          constraints: BoxConstraints(maxHeight: 240),
          onSelected: (value) {
            setState(() {
              // controller. changeUnit(value);
              // controller.unit = value;
              controller.clickStandard!.unitType = value;
            });
          },
          itemBuilder: (context) {
            return controller.unitList
                .map((e) => PopupMenuItem(
                      value: e,
                      child: Text(e),
                    ))
                .toList();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                child: Text(
                  controller.clickStandard!.unitType!.isNotEmpty
                      ? controller.clickStandard!.unitType!
                      : S.current.ai_please_select,
                  textAlign: TextAlign.end,
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: MColor.xFF808080),
                ),
              ),
              Container(
                margin: EdgeInsets.only(right: 10),
                child: Icon(
                  Icons.expand_more,
                  color: MColor.xFF808080,
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _builTable(
      {required String title,
      required String value1,
      required List<String> value2,
      required String value3,
      required String value4,
      required bool isFirst,
      required AiMeasureInfo item}) {
    for (var item in value2) {
      print('value2value2==' + item.toString());
    }
    return Container(
      height: 60,
      decoration: BoxDecoration(
          color: Colors.white,
          border: isFirst
              ? Border.all(width: 1, color: MColor.xFFE5E5E5)
              : Border(
                  left: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                  bottom: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                  right: BorderSide(
                    width: 1, //宽度
                    color: MColor.xFFE5E5E5, //边框颜色
                  ),
                )),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            width: 115,
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1, //宽度
                  color: MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(
                isFirst ? title : controller.getMeasureName(item.type ?? '')),
          ),
          Container(
            alignment: Alignment.center,
            width: ((Get.width - 115 - 3) / 3),
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1, //宽度
                  color: MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(isFirst ? value1 : controller.getMeasureData(item)),
          ),
          Container(
            alignment: Alignment.center,
            width: ((Get.width - 115 - 3) / 3),
            height: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  width: 1, //宽度
                  color: MColor.xFFE5E5E5, //边框颜色
                ),
              ),
            ),
            child: Text(value4),
          ),
          if (value3.isNotEmpty)
            Container(
              alignment: Alignment.center,
              width: ((Get.width - 115 - 3) / 3),
              height: 60,
              decoration: BoxDecoration(),
              child: Text(value3),
            )
          else ...{
            if (value2.isEmpty)
              Container(
                  alignment: Alignment.center,
                  width: ((Get.width - 115 - 6) / 3),
                  height: 60,
                  decoration: BoxDecoration(),
                  child: Image.asset(
                    'assets/images/product_pic.png',
                    width: 22,
                    height: 22,
                  ))
            else
              Container(
                  alignment: Alignment.center,
                  width: ((Get.width - 115 - 6) / 3),
                  height: 60,
                  decoration: BoxDecoration(),
                  child: _img(value2))
          }
        ],
      ),
    );
  }

  Widget _img(List<String> imgs) {
    if (imgs.length > 1) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          for (int i = 0; i < 2; i++) ...{
            Stack(
              children: [
                Image.network(
                  imgs[i],
                  width: 36,
                  height: 36,
                ),
                if (i == 1)
                  Container(
                    decoration: BoxDecoration(color: Color(0xFFFFFF99)),
                    child: Text('+${imgs.length - 2}'),
                  )
              ],
            )
          }
        ],
      );
    } else {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          for (var item in imgs) ...{
            ClipRRect(
                borderRadius: BorderRadius.circular(5), // 圆角半径
                child: Image.network(
                  item,
                  width: 36,
                  height: 36,
                  fit: BoxFit.cover,
                ))
          }
        ],
      );
    }
  }

  Widget _item(name, value, readOnly, type) {
    return Container(
        margin: EdgeInsets.only(top: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(name),
            Container(
              width: Get.width - 140,
              padding: EdgeInsets.only(left: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: MColor.xFFE5E5E5, width: 1),
              ),
              child: TextField(
                readOnly: readOnly,
                keyboardType: name != S.current.ai_measurement_item
                    ? TextInputType.number
                    : TextInputType.text,
                style: TextStyle(color: MColor.xFF808080, fontSize: 14),
                controller: TextEditingController(text: value),
                decoration: InputDecoration(
                    hintStyle: TextStyle(color: MColor.xFF808080, fontSize: 14),
                    hintText: S.current.apply_enter,
                    border: InputBorder.none),
                onChanged: (values) {
                  // value = values;
                  if (name == S.current.ai_measurement_item) {
                    controller.clickStandard?.name = values;
                  } else if (name == S.current.ai_measured_value) {
                    if (type == 'l') {
                      controller.clickStandard?.l = values;
                    } else if (type == 'w') {
                      controller.clickStandard?.w = values;
                    } else if (type == 'h') {
                      controller.clickStandard?.h = values;
                    }
                  } else if (name == S.current.ai_product_number) {
                    print('00000000000000000000000000000000');
                    controller.clickStandard?.productNumber = values;
                  } else if (name == S.current.ai_measurement_value_standard) {
                    controller.clickStandard?.standard = values;
                  } else if (name == '${S.current.ai_measurement_error}(±)') {
                    controller.clickStandard?.error = values;
                  }
                },
              ),
            )
          ],
        ));
  }
}
