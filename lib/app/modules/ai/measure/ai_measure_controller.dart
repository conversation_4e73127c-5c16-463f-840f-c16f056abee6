import 'dart:async';

import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';
import '../category/entity/ai_recmend_data.dart';
import '../entiy/ai_envent_imgage.dart';
import '../entiy/ai_measure_allow_num.dart';
import '../entiy/ai_measure_data.dart';

class AiMeasureController extends GetxController implements AiDataFeature {
  static AiMeasureController get to => Get.find();
  final aiDetail = AiSummaryDetailData(
          id: 0,
          ruleOrderId: 0,
          orderId: 0,
          productId: 0,
          modelId: 0,
          type: 7,
          status: 0)
      .obs;

  List<AiItemForms>? get inputList =>
      aiDetail.value.details?.config?.forms ??= [];
  List<AiItemEvent>? get eventList =>
      aiDetail.value.details?.config?.event ?? [];
  AiSummaryResult? get result => aiDetail.value.details?.config?.result;
  late var baseResult = AiBaseEntity();
  int type = 0;
  @override
  void onInit() {
    if (Get.parameters['type'] != null) {
      type = int.parse(Get.parameters['type']!);
    }
    print('type====' + type.toString());
    loadData();
    search('', null);
    super.onInit();
    /*subscription = eventBus.on<AiEnventImgage>().listen((event) {
      operateRecords!.values?[clickImgIndex].images?.add(event.url);
    });*/
  }

  void aiImageChange(String url){
    operateRecords!.values?[clickImgIndex].images?.add(url);
  }

  @override
  void onClose() {
    super.onClose();
    if (subscription != null) {
      subscription?.cancel();
    }
  }

  StreamSubscription? subscription;
  int clickImgIndex = 0;

  getArrayData(index, type) {
    if (index < (clickItem?.measureStandardData!.values ?? []).length) {
      if (type == 'standard') {
        return clickItem?.measureStandardData!.values?[index].standard;
      }
    }
    return '';
  }

  getArrayData2(index) {
    if (index < (clickItem?.measureStandardData!.values ?? []).length) {
      return clickItem?.measureStandardData!.values?[index].error;
    }
    return '';
  }

  getArrayData3(index) {
    if (index < (clickItem?.measureStandardData!.values ?? []).length) {
      if (type == 'standard') {
        return clickItem?.measureStandardData!.values?[index];
      }
    }
    return {};
  }

  AiItemForms? clickItem;
  int clickIndex = 0;

  int clickStandardIndex = 0;
  AiMeasureInfo? clickStandard;
  AiMeasureData? clickRecords;
  AiMeasureData? operateRecords;
  void goDetail(item, index, setState) {
    clickIndex = index;
    bool isNew = false;
    clickItem = item;
    if (clickItem?.productNo == null) {
      final map = <String, dynamic>{};
      map['type'] = type;
      map['values'] = [];
      map['status'] = 0;
      map['required'] = 0;
      clickItem?.productNo =
          AiCommonInfo(values: [], type: '0', status: 0, required: 0);
    }
    if (clickItem!.measureStandardData == null) {
      clickItem!.measureStandardData = AiMeasureData.empty();
      print('1111');
    } else {}
    if (clickItem!.required == null) {
      clickItem!.required = 0;
    }
    if (clickItem!.measureData == null) {
      clickItem!.measureData = [];
    }
    if (clickItem!.measureData!.isNotEmpty) {
      clickRecords = clickItem!.measureData?[0];
      print('2222');
    } else {
      isNew = false;
      clickRecords = AiMeasureData.empty();
      print('3333');
    }

    Get.toNamed(AiRoutes.MEASURE_DETAIL, arguments: {})?.then((value) {
      inputList?[clickIndex] = clickItem!;
      if (value == -1) {
        Get.back();
      } else {
        // calData();
        setState(() {});
      }
    });
  }

  String getMeasureName(String type) {
    if (type == 'dimensions') {
      return '长宽高';
    }
    if (type == 'weight') {
      return '毛重';
    }
    if (type == 'lengthWidth') {
      return '长宽';
    }
    return '';
  }

  String getMeasureData(AiMeasureInfo info) {
    if (info.type == 'dimensions') {
      if (info.l.toString().isEmpty ||
          info.w.toString().isEmpty ||
          info.h.toString().isEmpty) {
        return '/';
      }
      return ((info.l.toString().isEmpty ? '/' : info.l.toString()) +
          'x' +
          (info.w.toString().isEmpty ? '/' : info.w.toString()) +
          'x' +
          (info.h.toString().isEmpty ? '/' : info.h.toString()));
    }
    if (info.type == 'weight') {
      return (info.l.toString().isEmpty ? '/' : info.l.toString());
    }
    if (info.type == 'lengthWidth') {
      if (info.l.toString().isEmpty || info.w.toString().isEmpty) {
        return '/';
      }
      return ((info.l.toString().isEmpty ? '/' : info.l.toString()) +
          'x' +
          (info.w.toString().isEmpty ? '/' : info.w.toString()));
    }
    return '';
  }

  calData() {
    for (AiItemForms form in inputList ?? []) {
      int isSucc = 0;
      if (form.measureData != null && form.measureData!.isNotEmpty) {
        isSucc = 1;
        for (AiMeasureData parent in form.measureData ?? []) {
          for (AiMeasureInfo child in parent.values ?? []) {
            if (!isOutRange(child)) {
              isSucc = 2;
              break;
            }
          }
          if (isSucc == 2) {
            break;
          }
        }
      }

      form.status = isSucc;
      print('aaaa=' + isSucc.toString());
    }
  }

  void clickRecItem(item) {
    item.isSelected = !item.isSelected;
    update();
  }

  void goBack() {
    Get.back();
  }

  void addRec() {
    List<AiItemForms> aa = [];
    for (var item in recmendList) {
      if (item.isSelected) {
        final now = DateTime.now();
        AiItemForms child =
            AiItemForms(key: 'form${now.day}${now.hour}${now.minute}');
        child.tips = AiModelInfo.empty();
        child.name = item.itemName;
        child.tips!.values = [item.remark ?? ''];
        aa.add(child);
      }
    }
    inputList?.addAll(aa);
    Get.back(result: -1);
  }

  String searchKeywords = '';
  final recmendData = AiBaseEntity<List<AiRecmendData>>(data: []).obs;
  List<AiRecmendData> get recmendList => recmendData.value.data ?? [];
  void search(keyword, setState) {
    AiProvider().itemSearch(searchKeywords, '1', type.toString()).then((value) {
      if (value.data != null) {
        recmendData.value = value;
      }
      if (setState != null) {
        setState(() {});
      }
    });
  }

  void goRecmend() {
    Get.toNamed(
      AiRoutes.AT_RECMEND,
      arguments: {'type': type},
    )?.then((value) {
      if (value != null && value['data'] != null) {
        inputList?.addAll(value['data']);
        Get.back(result: -1);
      }
    });
  }

  void goEdit(item, setState) {
    clickItem = item;
    Get.toNamed(
      AiRoutes.MEASURE_EDIT,
      arguments: {},
    )?.then((value) {
      setState(() {});
      if (value == -1) {
        Get.back(result: -1);
      }
    });
  }

  bool showMore = false;
  int showIndex = 0;
  AiMeasureInfo? newAiCheckOutInfo;

  void changeItem(index, AiMeasureData item, setState) {
    clickRecords = item;
    showIndex = index;
    setState(() {});
  }

  void addInputItem(AiItemForms form) {
    aiDetail.value.details?.config?.addInputItem(form);
  }

  void removeInputItem(AiItemForms form) {
    aiDetail.value.details?.config?.removeInputItem(form);
    aiDetail.refresh();
  }

  bool isOutRange(measureStandardData) {
    final standard = clickItem?.measureStandardData?.values?.firstOrNull;
    final allow = standard?.error;
    if (allow == null || allow.isEmpty || standard == null) return true;
    if (standard.isDimensions &&
        (standard.l.toString().isEmpty ||
            standard.w.toString().isEmpty ||
            standard.h.toString().isEmpty)) {
      return true;
    }
    if (standard.isLengthWidth &&
        (standard.l.toString().isEmpty || standard.w.toString().isEmpty)) {
      return true;
    }
    if (standard.isWeight && (standard.l.toString().isEmpty)) {
      return true;
    }
    bool result = true;

    result = result &&
        allowData(
            standard: standard, allow: allow, measure: measureStandardData);
    if (!result) return false;

    return result;
  }

  bool allowData(
      {required AiMeasureInfo standard,
      required String allow,
      required AiMeasureInfo measure}) {
    if (standard.isWeight) {
      return _allowRange(
          from: measure.l ?? '0', standard: standard.l ?? '0', allow: allow);
    } else if (standard.isLengthWidth) {
      final lResult = _allowRange(
          from: measure.l ?? '0', standard: standard.l ?? '0', allow: allow);
      final wResult = _allowRange(
          from: measure.w ?? '0', standard: standard.w ?? '0', allow: allow);
      return lResult && wResult;
    } else if (standard.isDimensions) {
      final lResult = _allowRange(
          from: measure.l ?? '0', standard: standard.l ?? '0', allow: allow);
      final wResult = _allowRange(
          from: measure.w ?? '0', standard: standard.w ?? '0', allow: allow);
      final hResult = _allowRange(
          from: measure.h ?? '0', standard: standard.h ?? '0', allow: allow);
      return lResult && wResult && hResult;
    }
    return false;
  }

  bool _allowRange(
      {required String from, required String standard, required String allow}) {
    // print('===========from $from standard $standard allow $allow');

    final a = num.tryParse(from) ?? 0;
    final b = num.tryParse(standard) ?? 0;
    final c = num.tryParse(allow) ?? 0;
    // print('======xxxxx a $a b $b c $c');

    final result = a <= (b + c) && a >= (b - c);

    // print('======result $result');

    return result;
  }

  void submit() {
    uploadData().then(
      (value) {
        Get.toNamed(AiRoutes.MEASURE_RESULT)?.then((value) {
          print('11111111111');
          if (value == -1) {
            Get.back();
          }
        });
      },
    );
  }

  void submit2() {
    uploadData().then(
      (value) {
        Get.back();
      },
    );
  }

  void save() {
    uploadData().then((value) {
      Get.back(result: -1);
    });
  }

  void toNext() {
    uploadData().then((value) {
      Get.offAndToNamed(
        AiCategoryController.to
            .nextType(fromType: AiCategoryType.measure.id)
            .location,
        parameters: Get.parameters as Map<String, String>,
      );
    });
  }

  void addOneStandard() {
    // AiMeasureInfo clickStandard = AiMeasureInfo.empty();
    clickItem?.measureStandardData?.values?.add(clickStandard!);
    // dealData();
  }

  void dealData() {
    // 先删除和标准不一样的项
    // List<AiMeasureData> all = [];
    // for( AiMeasureData mdata in  clickItem!.measureData!){
    //   List<AiMeasureInfo> newValues = [];
    //   for(AiMeasureInfo valueData in mdata.values!){
    //     bool has =false;
    //     for(AiMeasureInfo standard in clickItem!.measureStandardData!.values!){
    //       if(standard.name == valueData.name){
    //         print('standard.222name=='+standard.name!+"  valueData222.name==="+valueData.name!);
    //         has = true;
    //         break;
    //       }
    //     }
    //     if(!has){
    //     }else{
    //       newValues.add(valueData);
    //     }
    //   }
    //   if(newValues.length>0){
    //     AiMeasureData aiMeasureData = AiMeasureData.empty();
    //     aiMeasureData.values = newValues;
    //     all.add(aiMeasureData);
    //   }
    // }
    // clickItem!.measureData = all;

    for (AiMeasureInfo standard in clickItem!.measureStandardData!.values!) {
      for (AiMeasureData mdata in clickItem!.measureData!) {
        bool has = false;
        for (AiMeasureInfo valueData in mdata.values!) {
          if (standard.name == valueData.name) {
            has = true;
            break;
          }
        }
        if (!has) {
          AiMeasureInfo newOne = AiMeasureInfo.empty();
          newOne.name = standard.name;
          mdata.values?.add(newOne);
        }
      }
    }
  }

  void addOneRecord() {
    if (clickItem!.measureStandardData!.values == null ||
        clickItem!.measureStandardData!.values!.isEmpty) {
      showToast(S.current.ai_add_measure_tip);
      return;
    }
    AiMeasureData mdata = AiMeasureData.empty();
    for (AiMeasureInfo item in clickItem!.measureStandardData!.values!) {
      AiMeasureInfo info = AiMeasureInfo.empty();
      info.name = item.name;
      mdata.values?.add(info);
    }
    clickItem?.measureData ??= [];
    clickItem?.measureData?.add(mdata);
    clickRecords = clickItem?.measureData?.firstOrNull;
    // clickRecords?.values ?.addAll( mdata.values??[]);
    print(clickRecords?.values?.length.toString());
  }

  bool isNewStandrad = false;
  String requireNum = '';
  final unitList = [
    '毫米 (mm)',
    '厘米 (cm)',
    '米 (m)',
    '英寸(in)',
    '英尺(inch)',
    '毫克 (mg)',
    '克 (g)',
    '千克 (kg)',
    '平方厘米 (cm²)',
    '平方米 (m²)',
    '毫升 (ml)',
    '升 (L)',
  ];
  final unitListData = [
    'mm',
    'cm',
    'm',
    'in',
    'inch',
    'mg',
    'g',
    'kg',
    'cm²',
    'm²',
    'ml',
    'L',
  ];

  // final unitList = [
  //   S.current.ai_model_unit_piece,
  //   S.current.ai_model_unit_only,
  //   S.current.ai_model_unit_pair,
  //   S.current.ai_model_unit_set,
  //   S.current.ai_model_unit_dozen,
  //   S.current.ai_model_unit_roll,
  //   S.current.ai_model_unit_vehicle,
  //   S.current.ai_model_unit_head,
  //   S.current.ai_model_unit_bag,
  //   S.current.ai_model_unit_box,
  //   S.current.ai_model_unit_pack,
  //   S.current.ai_model_unit_yard,
  //   S.current.ai_model_unit_meter,
  //   S.current.ai_model_unit_kilogram,
  //   S.current.ai_model_unit_metric_ton,
  //   S.current.ai_model_unit_liter,
  //   S.current.ai_model_unit_gallon,
  //   S.current.ai_model_unit_other
  // ];

  final typeList = [
    S.current.ai_length_width_height,
    S.current.ai_length_width,
    S.current.ai_weight,
  ];

  final typeListNum = [
    'dimensions',
    'weight',
    'lengthWidth',
  ];

  @override
  void loadData() {
    AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: AiCategoryType.measure.id,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      baseResult = value;
      if (value.data != null) {
        aiDetail.value = value.data!;
        // calData();
      }
    });
  }

  @override
  Future<AiBaseEntity> uploadData() {
    if (aiDetail.value.details == null) {
      return Future.value(AiBaseEntity(code: 200));
    }
    aiDetail.value.details?.config?.uploadImageIfNeed();
    return AiProvider()
        .uploadConfig(id: aiDetail.value.id, details: aiDetail.value.details!);
  }
}
