import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/generated/l10n.dart';

import 'ai_measure_controller.dart';

class AiMeasureRequireView<T extends AiMeasureController> extends GetView<T> {
  const AiMeasureRequireView({super.key});


  @override
  Widget build(BuildContext context) {
    Map map = Get.arguments;
    String num = map['num']??'';
    return  Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          centerTitle: true,
          actions: [
            InkWell(
              onTap: (){
                Get.back(result: {
                  'num':controller.clickItem?.sampleNum?.values?.firstOrNull??'/',
                });
              },
              child:      Container(
                margin: EdgeInsets.only(right: 20),
                padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
                decoration: BoxDecoration(
                    color: MColor.skin,
                    borderRadius: BorderRadius.circular(5)
                ),
                child: Text(S.current.date_save,style: TextStyle(
                    color: Colors.white,
                    fontSize: 14
                ),) ,
              )
            )

          ],
        ),    body:Column(
      children: [
        _requireItem(S.current.ai_sampling_level,num,true),
      ],
    ) );}

  Widget _requireItem(title,values,isArrow){
    return Container(
      padding: EdgeInsets.fromLTRB(20, 18, 20, 18),
      margin: EdgeInsets.only(top: 5),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(S.current.ai_measure_need_num),
          Row(
            children: [
              Container(
                  width: 180,
                  child:  TextField(
                    textAlign: TextAlign.right,
                    controller: TextEditingController(text:values ),
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: S.current.apply_enter,
                      isDense: true,
                      contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                    ),
                    onChanged: (value){
                      controller.clickItem?.sampleNum!.values?[0] = value.toString();
                    },
                  )
              )

            ],
          )


        ],
      ),
    );
  }


}
