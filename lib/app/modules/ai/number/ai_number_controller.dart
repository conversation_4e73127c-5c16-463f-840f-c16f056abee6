import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

import '../../../config/api.dart';
import '../../../config/design.dart';
import '../../../data/order_detail_entity.dart';
import '../../../data/order_info_entity.dart';
import '../../../data/public_model.dart';
import '../../../tools/public_provider.dart';
import '../checkout/type/chekout_level_type.dart';
import '../entiy/ai_envent_imgage.dart';

class AiNumberController extends GetxController implements AiDataFeature {
  static AiNumberController get to => Get.find();

  final attachments = <String>[].obs;
  late var baseResult = AiBaseEntity();
  late final aiDetail = AiSummaryDetailData(
          id: 0,
          ruleOrderId: 0,
          orderId: 0,
          productId: 0,
          modelId: 0,
          type: 1,
          status: 0)
      .obs;
  List<AiItemForms>? get forms => aiDetail.value.details?.config?.forms;
  AiItemForms? get packagedNum =>
      forms?.firstWhereOrNull((AiItemForms e) => e.isPackagedNum);
  AiItemForms? get packageNoSample =>
      forms?.firstWhereOrNull((AiItemForms e) => e.isPackageNoSample);

  List<AiItemForms>? get bottomForms => forms
      ?.where((e) => e.isUnpackagedNumSample || e.isPackageNumSample)
      .toList();

  final orderNumShow = ''.obs;
  final unpackagedNumShow = 0.obs;
  AiItemForms? get level =>
      forms?.firstWhereOrNull((AiItemForms e) => e.key == 'sampleLevel');
  AiItemForms? get sampleNum =>
      forms?.firstWhereOrNull((AiItemForms e) => e.key == 'sampleNum');
  TextEditingController coutTextController = TextEditingController();
  TextEditingController packgedTextController = TextEditingController();
  TextEditingController unitTextController = TextEditingController();
  // text: controller.sampleNum!.values!.firstOrNull
  @override
  void onInit() {
    loadProductData();

    super.onInit();
  }

  @override
  void loadData() {
    AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: AiCategoryType.number.id,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      baseResult = value;
      if (value.data != null) {
        aiDetail.value = value.data!;

        if ((aiDetail.value.details?.config?.forms?.length ?? 0) != 0) {
          bool hasUnit = false;
          for (var item in aiDetail.value.details!.config!.forms!) {
            if (item.key == 'packagedUnit') {
              hasUnit = true;
            }
            if (item.key == 'orderNum') {
              if (productList.isNotEmpty) {
                for (OrderProduct productItem in productList) {
                  for (ProductModel model in productItem.productModel ?? []) {
                    if (model.id == AiCategoryController.to.modelId &&
                        AiCategoryController.to.orderId == model.orderId) {
                      orderNumShow.value = model.amount!.toString();
                      item.values = [model.amount!.toString()];
                      break;
                    }
                  }
                }
              }
            }
          }
          if (!hasUnit) {
            aiDetail.value.details!.config!.forms!.insert(
                2,
                AiItemForms(
                    key: 'packagedUnit',
                    name: S.current.ai_per_box,
                    values: []));
          }

          // aiDetail.value.details?.config?.forms = forms;
          if (sampleNum == null) {
            aiDetail.value.details?.config?.forms
                ?.add(AiItemForms(key: "sampleNum", name: '每箱', values: []));
          }
          if (level == null) {
            aiDetail.value.details?.config?.forms
                ?.add(AiItemForms(key: "sampleLevel"));
          } else {
            if (level?.values?.firstOrNull ==
                S.current.ai_sample_level_type_nothing) {
              simpleReadOnly.value = false;
            }
          }
          if (sampleNum?.values?.length == 0) {
            coutTextController.text = '';
          } else {
            coutTextController.text = sampleNum!.values!.firstOrNull!;
          }
        }
        // final orderNum = forms?.firstWhereOrNull((e) => e.isOrderNum);
        // orderNum?.values = [AiCategoryController.to.orderNum.toString()];
        // getAqlAcNum('',level?.values?.firstOrNull??'');
      }
    });

    subscription = eventBus.on<AiEnventImgage>().listen((event) {
      if (clickImgIndex == 0) {
        packagedNum?.image?.values?.add(event.url);
      } else {
        packageNoSample?.image?.values?.add(event.url);
      }
    });
  }

  void aiImageChange(String url){
    if (clickImgIndex == 0) {
      packagedNum?.image?.values?.add(url);
    } else {
      packageNoSample?.image?.values?.add(url);
    }
  }

  int clickImgIndex = 0;
  StreamSubscription? subscription;

  @override
  void onClose() {
    super.onClose();
    if (subscription != null) {
      print('subscription  onClose!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
      subscription?.cancel();
    }
  }

  bool checkMaxUnOrder(int num) {
    int packedNum = 1;
    int packedBox = 1;
    for (AiItemForms e in (forms ?? [])) {
      if (e.key == 'packagedNum') {
        if (e.values != null && e.values!.isNotEmpty) {
          packedNum = int.parse(e.values![0]);
        }
      } else if (e.key == 'packagedUnit') {
        if (e.values != null && e.values!.isNotEmpty) {
          packedBox = int.parse(e.values![0]);
        }
      }
    }

    int orderNumShowInt = 0;
    if (orderNumShow.value != '') {
      orderNumShowInt = int.parse(orderNumShow.value);
    }
    if (orderNumShowInt - (packedNum * packedBox) < num) {
      showToast(S.current.ai_wrong_num);
      return false;
    }
    return true;
  }

  String getValueNum(key) {
    for (AiItemForms item in forms ?? []) {
      if (item.key == key) {
        return item.values?.firstOrNull ?? '';
      }
    }
    return '';
  }

  void setValueNum(key, num) {
    for (AiItemForms item in bottomForms ?? []) {
      if (item.key == key) {
        item.values = [num];
        break;
      }
    }
  }

  int getBottomNum(key) {
    for (AiItemForms item in bottomForms ?? []) {
      if (item.key == key) {
        if (item.values == null || item.values!.isEmpty) {
          return 0;
        }
        return int.parse(item.values!.first);
      }
    }
    return 0;
  }

  void setName(key, name) {
    for (AiItemForms item in forms ?? []) {
      if (item.key == key) {
        item.name = name;
        break;
      }
    }
  }

  void setFormNum(key, num) {
    for (AiItemForms item in forms ?? []) {
      if (item.key == key) {
        item.values = [num];
        break;
      }
    }
  }

  String getUnPackNum() {
    int orderNum = 0, packagedTotal = 0;
    for (AiItemForms item in forms ?? []) {
      if (item.key == 'orderNum') {
        orderNum = int.parse(item.values?.firstOrNull ?? '0');
      }
      if (item.key == 'packagedTotal') {
        logger.e(item.values);
        if ((item.values?.firstOrNull ?? '').isNotEmpty) {
          packagedTotal = int.parse(item.values?.firstOrNull ?? '0');
        }
      }
    }
    setFormNum('unpackagedNum', (orderNum - packagedTotal).toString());
    return (orderNum - packagedTotal).toString();
  }

  String callSampleNum() {
    int packedNum = 0;
    int packedBox = 1;
    int unpackedNum = 0;
    for (AiItemForms e in (forms ?? [])) {
      if (e.key == 'packagedNum') {
        if (e.values != null && e.values!.isNotEmpty) {
          packedNum = int.parse(e.values![0]);
        }
      } else if (e.key == 'packagedUnit') {
        if (e.values != null && e.values != '' && e.values!.isNotEmpty) {
          print(e.values![0]);
          if (e.values![0] != '') {
            packedBox = int.parse(e.values![0]);
          }
        }
      } else if (e.key == 'unpackagedNum') {
        if (e.values != null && e.values!.isNotEmpty) {
          unpackedNum = int.parse(e.values![0]);
        }
      }
    }
    String result = (packedNum * packedBox + unpackedNum).toString();
    for (var item in forms!) {
      if (item.key == 'sampleNum') {
        item.values = [result ?? ''];
        coutTextController.text = result.toString();
        break;
      }
    }
    coutTextController.text = result;
    return result;
  }

  List<OrderProduct> productList = [];
  Future<void> loadProductData() async {
    BaseModel<OrderDetailEntity> result = await takeOrderDetail();
    loadData();
    if (result.isSuccess && result.data != null) {
      productList = result.data?.orderProducts ?? [];
    } else {}
  }

  Future<BaseModel<OrderDetailEntity>> takeOrderDetail() {
    return PublicProvider.request<OrderDetailEntity>(
        path: '${Api.orderDetail}/${AiCategoryController.to.orderId}/desc',
        isPost: false);
  }

  final simpleReadOnly = true.obs;
  void showSheet(setState, context) {
    Get.bottomSheet(Container(
        color: Colors.white,
        height: 300,
        child: ListView.separated(
          itemBuilder: (context, index) => InkWell(
              onTap: () {
                if (AICheckoutSampleLevelType.values[index].name == '自定义') {
                  simpleReadOnly.value = false;
                  setValueNum('packageNumSample', '');
                  setValueNum('unpackagedNumSample', '');
                  setFormNum('sampleNum', '');
                  coutTextController.text = '';
                } else {
                  simpleReadOnly.value = true;
                  // callSampleNum();
                }

                setState(() {
                  for (var item in forms!) {
                    if (item.key == 'sampleLevel') {
                      item.values = [
                        AICheckoutSampleLevelType.values[index].name
                      ];
                      break;
                    }
                  }
                  level?.values = [
                    AICheckoutSampleLevelType.values[index].name
                  ];
                  if (AICheckoutSampleLevelType.values[index].name != '自定义') {
                    changeSampleLevel(index.toString(), setState);
                  }
                });
                closeKeyboard(context);
                Get.back();
              },
              child: Container(
                  padding: EdgeInsets.only(top: 10, bottom: 10),
                  child: Text(
                    AICheckoutSampleLevelType.values[index].name,
                    textAlign: TextAlign.center,
                  ))),
          itemCount: AICheckoutSampleLevelType.values.length,
          separatorBuilder: (BuildContext context, int index) => Divider(
            color: MColor.xFFE5E5E5,
            height: 1,
          ),
        )));
  }

  final unitList = [
    S.current.ai_model_unit_piece,
    S.current.ai_model_unit_only,
    S.current.ai_model_unit_pair,
    S.current.ai_model_unit_set,
    S.current.ai_model_unit_dozen,
    S.current.ai_model_unit_roll,
    S.current.ai_model_unit_vehicle,
    S.current.ai_model_unit_head,
    S.current.ai_model_unit_bag,
    S.current.ai_model_unit_box,
    S.current.ai_model_unit_pack,
    S.current.ai_model_unit_yard,
    S.current.ai_model_unit_meter,
    S.current.ai_model_unit_kilogram,
    S.current.ai_model_unit_metric_ton,
    S.current.ai_model_unit_liter,
    S.current.ai_model_unit_gallon,
    S.current.ai_model_unit_other
  ];

  void closeKeyboard(BuildContext context) {
    FocusScopeNode currentFocus = FocusScope.of(context);

    /// 键盘是否是弹起状态
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  void showSheetunit(ValueChanged<String>? onUnitChange) {
    Get.bottomSheet(Container(
        color: Colors.white,
        height: 300,
        child: ListView.separated(
          itemBuilder: (context, index) => InkWell(
              onTap: () {
                // if(AICheckoutSampleLevelType.values[index].name=='自定义'){
                //   simpleReadOnly.value = false;
                // }else{
                //   simpleReadOnly.value = true;
                //   for(var item in forms!){
                //     if(item.key =='sampleLevel'){
                //       item.values = [AICheckoutSampleLevelType.values[index].name];
                //       break;
                //     }
                //   }
                //   changeSampleLevel(AICheckoutSampleLevelType.values[index].name);
                // }
                onUnitChange!(unitList[index]);
                Get.back();
              },
              child: Container(
                  padding: EdgeInsets.only(top: 10, bottom: 10),
                  child: Text(
                    unitList[index],
                    textAlign: TextAlign.center,
                  ))),
          itemCount: unitList.length,
          separatorBuilder: (BuildContext context, int index) => Divider(
            color: MColor.xFFE5E5E5,
            height: 1,
          ),
        )));
  }

  void changeSampleLevel(String index, setState) {
    AiProvider()
        .getSampleNum(level: index.toString(), orderNum: orderNumShow.value)
        .then((value) {
      if (value.isGood) {
        setValueNum('packageNumSample', value.data.toString());
        setValueNum('unpackagedNumSample', '0');
        for (var item in forms!) {
          if (item.key == 'sampleNum') {
            item.values = [value.data ?? ''];
            coutTextController.text = value.data.toString();

            break;
          }
        }
        setState(() {});
      } else {
        showToast(value.message ?? '');
      }

      // refresh();
    });
  }

  final numCount = ''.obs;
  final levelCount = ''.obs;

  getAqlAcNum(String aqlValue, String sampleLevel) {
    AiProvider()
        .getAqlNum2(
            level: sampleLevel,
            aql: aqlValue,
            orderNum: AiCategoryController.to.orderNum.toString())
        .then(
      (value) {
        if (value.isGood) {
          for (var item in forms!) {
            if (item.key == 'sampleLevel') {
              item.values = [value.data!.level];
            }
            if (item.key == 'sampleNum') {}
          }
          // levelCount.value = value.data!.level??'';
          // numCount.value = value.data!.sampleNum.toString()??'';
        } else {
          showToast(value.message ?? '');
        }
      },
    );
  }

  @override
  Future<AiBaseEntity> uploadData() {
    if (aiDetail.value.details == null) {
      return Future.value(AiBaseEntity(code: 200));
    }
    aiDetail.value.details?.config?.uploadImageIfNeed();
    return AiProvider()
        .uploadConfig(id: aiDetail.value.id, details: aiDetail.value.details!);
  }

  bool checkForm() {
    List<AiItemForms>? forms = aiDetail.value.details?.config?.forms;
    List<String> requiredKeys = ['packagedNum','packagedTotal','unproducedTotal'];
    for (final form in forms!) {
      for(String key in requiredKeys){
        if (form.key == key) {
          String value = getValueNum(key);
          if(value.isEmpty){
            EasyLoading.showError(S.current.ai_name_not_filled('${form.name}'));
            return false;
          }
        }
      }
    }
    return true;
  }

  void toNext() {
     if(!checkForm()){
       return;
     }
    // if (notDoneName?.isNotEmpty ?? false) {
    //   // EasyLoading.showError(S.current.ai_name_not_filled('$notDoneName'));
    //   // return ;
    // }else{
    //   int sampleNum = getValueNum('sampleNum')==''?0:int.parse(getValueNum('sampleNum'));
    // if(getBottomNum('unpackagedNumSample') + getBottomNum('packageNumSample')!= sampleNum){
    //   EasyLoading.showError(S.current.ai_wrong_sample_num_cal);
    //   return;
    // }
    uploadData().then(
      (value) {
        if (!value.isGood) return;
        Get.toNamed(
          AiRoutes.NUMBER_RESULT,
          parameters: Get.parameters as Map<String, String>,
        )?.then((value) {
          /*if (value == -1) {
            Get.back();
          }*/
        });
      },
    );
    // return ;
    // }
  }

  void unfocusAll(context) {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus) {
      currentFocus.unfocus();
    }
  }

  void done() {
    final notDoneName = aiDetail.value.details?.config?.notDoneForm;
    // if (notDoneName?.isNotEmpty ?? false) {
    //   EasyLoading.showError(S.current.ai_name_not_filled('$notDoneName'));
    //   // return ;
    // }else{
    uploadData().then(
      (value) {
        if (!value.isGood) return;
        Get.back();
      },
    );
    // return ;
    // }
  }
}
