import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/const_data/ai_const_data.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/number/ai_number_controller.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/widgets/ai_selected_number_view.dart';
import 'package:inspector/app/modules/ai/widgets/ai_widget_style.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

import '../../../../generated/assets.dart';
import '../photo_selected/picker_method.dart';

class AiNumberView extends GetView<AiNumberController> {
  const AiNumberView({super.key});

  final _titleWidth = 126.0;
  @override
  Widget build(BuildContext context1) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(AiCategoryType.number.name),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
          style: MFont.medium14.copyWith(color: MColor.black),
          child: StatefulBuilder(builder: (context, setState) {
            return Obx(() {
              return KeyboardDismissOnTap(
                child: Column(
                  children: [
                    AiProductSelectBar(loader: controller),
                    Expanded(
                        child: AiEmptyView(
                      baseResult: controller.baseResult,
                      child: ListView(
                        children: [
                          Container(
                            color: MColor.white,
                            height: 10,
                          ),
                          AiInputCell(
                            title: S.of(context).ai_order_quantity,
                            hint:
                                '${S.of(context).ai_please_input}${S.of(context).ai_order_quantity}',
                            keys: 'orderNum',
                            readOnly: true,
                            suffix: 'pcs',
                            initial: controller.orderNumShow.value ?? '',
                            titleWidth: _titleWidth,
                          ),
                          for (AiItemForms e in (controller.forms ?? []))
                            if (!e.isOrderNum &&
                                (!(e.isPackageNoSample ||
                                    e.isUnpackagedNumSample ||
                                    e.isPackageNumSample ||
                                    e.key == 'sampleLevel' ||
                                    e.key == 'sampleNum' ||
                                    e.key == 'backupNumSample' ||
                                    e.key == 'unpackagedTotal')))
                              AiInputCell(
                                keys: e.key,
                                title: e.name ?? '',
                                initial: e.key.toString() == 'unpackagedNum'
                                    ? controller.getUnPackNum()
                                    : (e.values?.firstOrNull),
                                suffix: e.isPackagedNum
                                    ? AiCategoryController
                                        .to.currentModel.value.model.unit
                                    : '',
                                intputController:
                                    controller.packgedTextController,
                                unitController: controller.unitTextController,
                                hint:
                                    '${S.of(context).ai_please_input}${e.name}',
                                titleWidth: _titleWidth,
                                remark: e.remark,
                                forms: controller.forms,
                                controller: controller,
                                onTap: (value) {},
                                onUnitChange: (value) {
                                  if (value == '其它') {
                                    Future.delayed(Duration(milliseconds: 500),
                                        () {
                                      showDilog(context, ((value11) {
                                        setState(() {
                                          AiCategoryController.to.currentModel
                                              .value.model.unit = value11;
                                          controller.setName('packagedUnit',
                                              '${'每' + value11}数量');
                                        });
                                      }));
                                    });
                                  } else {
                                    setState(() {
                                      AiCategoryController.to.currentModel.value
                                          .model.unit = value;
                                      controller.setName(
                                          'packagedUnit', '每$value数量');
                                    });
                                  }
                                },
                                onChanged: (value) {
                                  if(e.key == 'packagedTotal'){
                                    int orderNum = controller.getValueNum('orderNum') == '' ? 0 : int.parse(controller.getValueNum('orderNum'));
                                    int packagedTotal = int.parse(value);
                                    controller.setFormNum('unpackagedNum', '${orderNum-packagedTotal}');
                                    setState(() {});
                                  }

                                  /*if (e.key == 'unpackagedNum') {
                                    print(
                                        'unpackagedNumunpackagedNumunpackagedNum');
                                    int orderNum = controller
                                                .getValueNum('orderNum') ==
                                            ''
                                        ? 0
                                        : int.parse(
                                            controller.getValueNum('orderNum'));
                                    int packagedTotal = controller
                                                .getValueNum('packagedTotal') ==
                                            ''
                                        ? 0
                                        : int.parse(controller
                                            .getValueNum('packagedTotal'));
                                    int aa = orderNum - packagedTotal;
                                    controller.setFormNum('unpackagedNum',
                                        aa < 0 ? '0' : aa.toString());
                                    setState(() {});
                                  } else {
                                    if (e.key == 'packagedNum') {
                                      int unit = controller.getValueNum(
                                                  'packagedUnit') ==
                                              ''
                                          ? 0
                                          : int.parse(controller
                                              .getValueNum('packagedUnit'));
                                      int un = int.parse(
                                              controller.orderNumShow.value) -
                                          (value == '' ? 0 : int.parse(value)) *
                                              unit;
                                      controller.setFormNum('unpackagedNum',
                                          un < 0 ? '0' : un.toString());
                                      setState(() {});
                                    } else if (e.key == 'packagedUnit') {
                                      int unit = controller
                                                  .getValueNum('packagedNum') ==
                                              ''
                                          ? 0
                                          : int.parse(controller
                                              .getValueNum('packagedNum'));
                                      int un = int.parse(
                                              controller.orderNumShow.value) -
                                          (value == '' ? 0 : int.parse(value)) *
                                              unit;
                                      controller.setFormNum('unpackagedNum',
                                          un < 0 ? '0' : un.toString());
                                      setState(() {});
                                    } else if (e.key == 'packagedTotal') {
                                      print('--------------');
                                      int vv =
                                          value == '' ? 0 : int.parse(value);
                                      int packagedNum = controller
                                                  .getValueNum('packagedNum') ==
                                              ''
                                          ? 0
                                          : int.parse(controller.getValueNum(
                                              'packagedNum')); //已包装箱数
                                      int packagedUnit = controller.getValueNum(
                                                  'packagedUnit') ==
                                              ''
                                          ? 0
                                          : int.parse(controller.getValueNum(
                                              'packagedUnit')); //每箱数量
                                      if (packagedNum * packagedUnit < vv) {
                                        controller.setFormNum(
                                            'packagedTotal',
                                            (packagedNum * packagedUnit)
                                                .toString());
                                        showToast('修改值<=已包装箱数*每箱数量');
                                      } else {
                                        controller.setFormNum(
                                            'packagedTotal', value);
                                      }

                                      setState(() {});
                                    } else if (e.key == 'unproducedTotal') {
                                      int orderNum =
                                          controller.getValueNum('orderNum') ==
                                                  ''
                                              ? 0
                                              : int.parse(controller
                                                  .getValueNum('orderNum'));
                                      int vv =
                                          value == '' ? 0 : int.parse(value);
                                      if (vv > orderNum) {
                                        controller.setFormNum('unproducedTotal',
                                            orderNum.toString());
                                        showToast('未生产完成数量需要小于订单数量');
                                      } else {
                                        controller.setFormNum(
                                            'unproducedTotal', vv.toString());
                                      }

                                      setState(() {});
                                    }
                                  }*/
                                  e.values = [value];
                                },

                                // ,
                              )

                          // })
                          ,
                          Container(
                              color: Colors.white,
                              child: Row(
                                children: [
                                  InkWell(
                                      onTap: () async {
                                        final assets =
                                            controller.packagedNum?.assetsIn;
                                        final inCount = controller.packagedNum!
                                                .imageInfoList.length -
                                            assets!.length;
                                        controller.clickImgIndex = 0;
                                        final list = await PickMethod(
                                          maxAssetsCount:
                                              max(100 - max(0, inCount), 0),
                                        ).forCamera(context);
                                        controller.packagedNum
                                            ?.changeAssetsOnPicker(list);
                                      },
                                      child: Container(
                                        padding: EdgeInsets.all(15),
                                        margin: EdgeInsets.only(
                                            left: 20, bottom: 20),
                                        width: 68,
                                        height: 68,
                                        decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(5),
                                            border: Border.all(
                                                color: MColor.xFFE5E5E5,
                                                width: 1)),
                                        child: Image.asset(
                                          Assets.camera,
                                        ),
                                      )),
                                  if (controller.packagedNum != null)
                                    Expanded(
                                        child: AiFormAssetsListView(
                                      manager: controller.packagedNum!,
                                      padding:
                                          EdgeInsets.fromLTRB(10, 0, 20, 20),
                                      onImageChanged: (res) {
                                        List<String> aa = [];
                                        for (final info
                                            in res.imageInfoList.value) {
                                          aa.add(info.url ?? '');
                                        }
                                        controller.packagedNum?.image?.values =
                                            aa;
                                      },
                                    ))
                                ],
                              )),
                          SizedBox(height: 10),
                          _buildSamplingStandard(setState, context),
                          for (int i = 0;
                              i < (controller.bottomForms?.length ?? 0);
                              i++)
                            AiInputCell(
                                title: controller.bottomForms![i].name ?? '',
                                initial: controller
                                    .bottomForms![i].values?.firstOrNull,
                                hint:
                                    '${S.of(context).ai_please_input}${controller.bottomForms![i].name}',
                                titleWidth: _titleWidth,
                                padding: EdgeInsets.fromLTRB(
                                    20, i == 0 ? 10 : 0, 20, 10),
                                onChanged: (value) {
                                  print('===========================');
                                  int nowValue = 0;
                                  if (value != '') {
                                    nowValue = int.parse(value);
                                  }
                                  int sampleNum = controller
                                              .getValueNum('sampleNum') ==
                                          ''
                                      ? 0
                                      : int.parse(
                                          controller.getValueNum('sampleNum'));
                                  int packageNumSample = controller.getValueNum(
                                              'packageNumSample') ==
                                          ''
                                      ? 0
                                      : int.parse(controller
                                          .getValueNum('packageNumSample'));
                                  int unpackagedNumSample = controller
                                              .getValueNum(
                                                  'unpackagedNumSample') ==
                                          ''
                                      ? 0
                                      : int.parse(controller
                                          .getValueNum('unpackagedNumSample'));

                                  if (controller.bottomForms![i].key ==
                                      'packageNumSample') {
                                    if (unpackagedNumSample + nowValue >
                                        sampleNum) {
                                      showToast(S.current.ai_wrong_sample_num);
                                    }
                                  } else if (controller.bottomForms![i].key ==
                                      'unpackagedNumSample') {
                                    if (packageNumSample + nowValue >
                                        sampleNum) {
                                      showToast(S.current.ai_wrong_sample_num);
                                    }
                                  }
                                  controller.bottomForms![i].values = [value];
                                  setState(() {});
                                }),
                          if (controller.packageNoSample != null)
                            _buildSamplingRecord(
                                context, controller.packageNoSample!),
                          SizedBox(height: 20),
                        ],
                      ),
                    )),
                    SafeArea(
                      bottom: true,
                      child: Container(
                          margin: EdgeInsets.only(left: 20, right: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                  width: Get.width / 2 - 20,
                                  height: 40,
                                  child: AiSubmitButton.save(
                                    onPressed: () {
                                      controller.unfocusAll(context1);
                                      Future.delayed(
                                          Duration(milliseconds: 100), () {
                                        controller.done();
                                      });
                                    },
                                  )),
                              Container(
                                  width: Get.width / 2 - 20,
                                  height: 40,
                                  child: AiSubmitButton.submit(onPressed: () {
                                    controller.unfocusAll(context1);
                                    Future.delayed(Duration(milliseconds: 100), () {
                                      controller.toNext();
                                    });
                                  }))
                            ],
                          )),
                    )
                  ],
                ),
              );
            });
          })),
    );
  }

  void showDilog(context1, callBack(value)) {
    String unit = '';
    showGeneralDialog(
        context: context1,
        barrierColor: Colors.black.withOpacity(.5),
        barrierDismissible: true,
        barrierLabel: '',
        pageBuilder: (BuildContext context, Animation<double> animation,
            Animation<double> secondaryAnimation) {
          return Scaffold(
              backgroundColor: Colors.transparent,
              body: Center(
                  child: SingleChildScrollView(
                      child: Center(
                child: Container(
                    margin: EdgeInsets.all(20),
                    padding: EdgeInsets.fromLTRB(15, 18, 15, 18),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5)),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // _itemUnit(unit),
                        Container(
                            margin: EdgeInsets.only(top: 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(S.current.ai_add_product_unit),
                                Container(
                                  width: Get.width - 120,
                                  padding: EdgeInsets.only(left: 10),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5),
                                    border: Border.all(
                                        color: MColor.xFFE5E5E5, width: 1),
                                  ),
                                  child: TextField(
                                    keyboardType: TextInputType.text,
                                    style: TextStyle(
                                        color: MColor.xFF808080, fontSize: 14),
                                    controller: TextEditingController(text: ''),
                                    decoration: InputDecoration(
                                        hintStyle: TextStyle(
                                            color: MColor.xFF808080,
                                            fontSize: 14),
                                        hintText: S.current.apply_enter,
                                        border: InputBorder.none),
                                    onChanged: (values) {
                                      unit = values;
                                    },
                                  ),
                                )
                              ],
                            )),
                        SizedBox(
                          height: 20,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            InkWell(
                                onTap: () {
                                  Navigator.pop(context);
                                },
                                child: Container(
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all(
                                            color: MColor.skin, width: 1)),
                                    height: 40,
                                    width: Get.width / 2 - 80,
                                    child: Text(
                                      S.current.order_cancel,
                                      style: TextStyle(
                                          color: MColor.skin, fontSize: 14),
                                    ))),
                            SizedBox(
                              width: 20,
                            ),
                            InkWell(
                                onTap: () {
                                  callBack(unit);
                                  Navigator.pop(context);
                                },
                                child: Container(
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: MColor.skin,
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    height: 40,
                                    width: Get.width / 2 - 80,
                                    child: Text(
                                      S.current.charge_submit,
                                      style: TextStyle(
                                          color: MColor.white, fontSize: 14),
                                    )))
                          ],
                        ),
                      ],
                    )),
              ))));
        });
  }

  Widget _buildSamplingStandard(setState, context) {
    return Container(
      decoration: BoxDecoration(color: MColor.white),
      padding: EdgeInsets.only(left: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 13,
          ),
          Text(
            S.current.ai_simple_num,
            style: MFont.regular18
                .copyWith(color: MColor.xFF000000, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 8,
          ),
          Row(
            children: [
              SizedBox(
                child: Text(
                  '* ${S.current.ai_simple_level}',
                  style: MFont.medium16.copyWith(color: MColor.xFF000000),
                ),
              ),
              SizedBox(
                width: 8,
              ),
              Text(
                'ANSI/ASQZ1.4 (MIL-STD-105E)',
                style: TextStyle(color: Color(0xFF2A82E4), fontSize: 12),
              )
            ],
          ),
          SizedBox(
            height: 18,
          ),
          Container(
              margin: EdgeInsets.only(right: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.fromLTRB(17, 5, 17, 5),
                    decoration: BoxDecoration(
                        border: Border.all(color: MColor.xFFE5E5E5, width: 1),
                        borderRadius: BorderRadius.circular(5)),
                    child: Row(
                      children: [
                        Text(
                          S.current.ai_sampling_level,
                          style:
                              MFont.regular14.copyWith(color: MColor.xFF000000),
                        ),
                        Container(
                          margin: EdgeInsets.only(left: 14, right: 14),
                          color: MColor.xFFE5E5E5,
                          width: 1,
                          height: 25,
                        ),
                        if (controller.level?.values != null)
                          Container(
                              alignment: Alignment.center,
                              child: GestureDetector(
                                  onTap: () {
                                    controller.showSheet(setState, context);
                                  },
                                  child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 0),
                                      child: Text(
                                          controller
                                                  .level?.values!.firstOrNull ??
                                              '-',
                                          style: MFont.medium14.copyWith(
                                              color: MColor.xFFF2591D)))))
                        else
                          Container(
                              alignment: Alignment.center,
                              child: GestureDetector(
                                  onTap: () {
                                    controller.showSheet(setState, context);
                                  },
                                  child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 0),
                                      child: Text(
                                          S.current.publish_inspection_time_tip,
                                          style: MFont.medium14.copyWith(
                                              color: MColor.xFFF2591D))))),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 15,
                  ),
                  Container(
                    padding: EdgeInsets.fromLTRB(17, 5, 5, 5),
                    decoration: BoxDecoration(
                        border: Border.all(color: MColor.xFFE5E5E5, width: 1),
                        borderRadius: BorderRadius.circular(5)),
                    child: Row(
                      children: [
                        Text(
                          S.current.ai_simple_count,
                          style:
                              MFont.regular14.copyWith(color: MColor.xFF000000),
                        ),
                        Container(
                          margin: EdgeInsets.only(left: 14, right: 14),
                          color: MColor.xFFE5E5E5,
                          width: 1,
                          height: 25,
                        ),
                        if (controller.sampleNum != null)
                          Container(
                            width: 30,
                            height: 25,
                            alignment: Alignment.center,
                            child: Row(
                              children: [
                                Expanded(
                                    child: TextField(
                                  textAlign: TextAlign.center,
                                  controller: controller.coutTextController,
                                  keyboardType: TextInputType.number,
                                  readOnly: controller.simpleReadOnly.value,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      isDense: true,
                                      contentPadding: EdgeInsets.zero),
                                  onChanged: (value) {
                                    controller.sampleNum?.values = [value];
                                    setState(() {
                                      controller.setValueNum(
                                          'packageNumSample', value.toString());
                                      controller.setValueNum(
                                          'unpackagedNumSample', '0');
                                    });
                                  },
                                ))
                              ],
                            ),
                          )
                      ],
                    ),
                  ),
                ],
              )),
        ],
      ),
    );
  }

  Widget _buildSamplingRecord(BuildContext context, AiItemForms form) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(top: 0, bottom: 10),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(
          children: [
            InkWell(
                onTap: () async {
                  controller.clickImgIndex = 1;
                  final assets = form.assetsIn;
                  final inCount = form.imageInfoList.length - assets.length;
                  final list = await PickMethod(
                    maxAssetsCount: max(100 - max(0, inCount), 0),
                  ).forCamera(context);
                  controller.packageNoSample?.changeAssetsOnPicker(list);
                },
                child: Container(
                  padding: EdgeInsets.all(15),
                  margin: EdgeInsets.only(left: 20),
                  width: 68,
                  height: 68,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: MColor.xFFE5E5E5, width: 1)),
                  child: Image.asset(
                    Assets.camera,
                  ),
                )),
            if (form.imageInfoList.isNotEmpty)
              Expanded(
                child: AiFormAssetsListView(
                  manager: form,
                  padding: EdgeInsets.fromLTRB(20, 0, 20, 10),
                  onImageChanged: (res) {
                    List<String> aa = [];
                    for (final info in res.imageInfoList.value) {
                      aa.add(info.url ?? '');
                    }
                    form.image?.values = aa;
                  },
                ),
              )
          ],
        ),
        Container(
          decoration: BoxDecoration(color: MColor.white),
          padding: EdgeInsets.symmetric(horizontal: 20),
          height: 38,
          child: Row(
            children: [
              Text(
                S.of(context).ai_simple_no,
                style: MFont.medium14.copyWith(color: MColor.xFF000000),
              ),
              Spacer(),

              // AiImageSelectedButton(manager: form)
            ],
          ),
        ),
        Container(
          color: MColor.white,
          padding: const EdgeInsets.all(10.0),
          child: StatefulBuilder(builder: (context, setState) {
            return TextField(
              minLines: 3,
              maxLines: 10,
              controller: TextEditingController(text: form.values?.firstOrNull),
              style: MFont.medium14.copyWith(color: MColor.xFF000000),
              onTap: () async {
                List<String>? list = await showCupertinoModalPopup(
                  context: context,
                  builder: (context) {
                    return AiSelectedNumberView(
                      initial: form.numberValues,
                      max: AiCategoryController.to.orderNum,
                    );
                  },
                );
                if (list != null) {
                  setState(() {
                    list.sort(
                      (a, b) => (int.tryParse(a) ?? 0) - (int.tryParse(b) ?? 0),
                    );
                    form.changeNumberValues(list);
                    // form.values = list;
                  });
                }
              },
              readOnly: true,
              decoration: InputDecoration(
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                    color: MColor.xFFE5E5E5,
                  )),
                  focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                    color: MColor.xFFF2591D,
                  )),
                  hintText: S.of(context).ai_sampling_packaging_number_list),
            );
          }),
        ),
      ]),
    );
  }
}

class AiInputCell extends StatefulWidget {
  const AiInputCell({
    required this.title,
    required this.hint,
    this.controller,
    this.intputController,
    this.unitController,
    this.forms,
    this.initial,
    this.suffix,
    this.titleWidth,
    this.remark,
    this.keys,
    this.width,
    this.end,
    this.menus,
    this.onChanged,
    this.menusValues,
    this.readOnly = false,
    this.numberOnly = true,
    this.onUnitChange,
    this.onTap,
    this.padding = const EdgeInsets.fromLTRB(20, 0, 20, 10),
    super.key,
  });

  final TextEditingController? intputController;
  final TextEditingController? unitController;
  final AiNumberController? controller;
  final List<String>? menus;
  final String title;
  final String? keys;
  final String hint;
  final String? initial;
  final String? suffix;
  final double? titleWidth;
  final double? width;
  final EdgeInsets padding;
  final Widget? end;
  final bool readOnly;
  final bool numberOnly;
  final ValueChanged<String>? onTap;
  final ValueChanged<String>? onUnitChange;
  final ValueChanged<String>? onChanged;
  final List<AiItemForms>? forms;
  final List<String>? menusValues;
  final String? remark;

  @override
  State<AiInputCell> createState() => _AiInputCellState();
}

class _AiInputCellState extends State<AiInputCell> {
  final FocusNode _focusNode = FocusNode();
  var content = '';
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _focusNode.addListener(() {
      print('contentcontent====>' + content);
      widget.onChanged?.call(content);
    });
  }

  String? suffix2 = '';
  @override
  Widget build(BuildContext context) {
    if (suffix2!.isNotEmpty && widget.suffix != null) {
      suffix2 = widget.suffix;
    }
    final isSelected = widget.menus != null && widget.menus!.isNotEmpty;
    final decorationTheme = InputDecorationTheme(
      enabledBorder: AiWidgetStyle.enabledBorder,
      focusedBorder: AiWidgetStyle.focusedBorder,
      border: AiWidgetStyle.focusedBorder,
      contentPadding: EdgeInsets.symmetric(horizontal: 12),
      isDense: isSelected,
      constraints: BoxConstraints.tight(const Size.fromHeight(40)),
    );
    late Widget result;
    Widget input([double? width]) {
      if (widget.keys == 'packagedNum') {
        widget.intputController?.text = widget.initial ?? '';
        result = TextField(
            style: MFont.medium14.copyWith(
                color: !widget.readOnly ? MColor.black : MColor.xFF777777),
            cursorColor: MColor.xFFF2591D,
            readOnly: widget.readOnly,
            controller: widget.intputController,
            onChanged: widget.onChanged,
            keyboardType: widget.numberOnly ? TextInputType.number : null,
            inputFormatters: widget.numberOnly
                ? [
                    FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                    LengthLimitingTextInputFormatter(
                        AiConstData.numberMaxLength),
                  ]
                : null,
            decoration: InputDecoration(
              border: InputBorder.none,
              fillColor: Color(0xffF6F6F8),
              hintText: widget.hint,
              suffixIcon: (widget.suffix ?? '').isNotEmpty
                  ? Container(
                      alignment: Alignment.center,
                      width: 50,
                      child: InkWell(
                          onTap: () {
                            widget.controller?.showSheetunit((value) {
                              setState(() {
                                AiCategoryController
                                    .to.currentModel.value.model.unit = value;
                                suffix2 = value;
                              });

                              widget.onUnitChange!(value);
                            });
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                margin: EdgeInsets.only(left: 7, right: 7),
                                color: MColor.xFFE5E5E5,
                                width: 1,
                                height: 25,
                              ),
                              Row(
                                children: [
                                  Text(AiCategoryController
                                          .to.currentModel.value.model.unit ??
                                      ''),
                                  Icon(
                                    Icons.unfold_more,
                                    color: MColor.skin,
                                    size: 15,
                                  ),
                                ],
                              )
                            ],
                          )))
                  : null,
            ).applyDefaults(decorationTheme),
            onTap: () {
              widget.intputController?.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.intputController!.text.length),
              );
              // widget.onTap!(widget.keys!);
            });
      } else if (widget.keys == 'packagedUnit') {
        widget.unitController?.text = widget.initial ?? '';
        result = Row(
          children: [
            Expanded(
              child: TextField(
                  style: MFont.medium14.copyWith(
                      color:
                          !widget.readOnly ? MColor.black : MColor.xFF777777),
                  cursorColor: MColor.xFFF2591D,
                  readOnly: widget.readOnly,
                  controller: widget.unitController,
                  onChanged: widget.onChanged,
                  keyboardType: widget.numberOnly ? TextInputType.number : null,
                  inputFormatters: widget.numberOnly
                      ? [
                          FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                          LengthLimitingTextInputFormatter(
                              AiConstData.numberMaxLength),
                        ]
                      : null,
                  decoration: InputDecoration(
                    hintText: widget.hint,
                    isDense: true,
                    prefixIconConstraints: BoxConstraints(),
                    suffixIconConstraints: BoxConstraints(),
                    suffixIcon: Container(
                        margin: EdgeInsets.only(right: 5),
                        width: 30,
                        alignment: Alignment.center,
                        child: Text(
                          textAlign: TextAlign.center,
                          'pcs',
                          style:
                              MFont.medium14.copyWith(color: MColor.xFF000000),
                        )),
                  ).applyDefaults(decorationTheme),
                  onTap: () {
                    widget.unitController?.selection =
                        TextSelection.fromPosition(
                      TextPosition(offset: widget.unitController!.text.length),
                    );
                  }),
            )
          ],
        );
      } else {
        result = TextField(
          focusNode: _focusNode,
          style: MFont.medium14.copyWith(
              color: !widget.readOnly ? MColor.black : MColor.xFF777777),
          cursorColor: MColor.xFFF2591D,
          readOnly: widget.readOnly,
          controller: TextEditingController(text: widget.initial ?? ''),
          onChanged: (value) {
            content = value;
            // widget.onChanged?.call(value);
          },
          keyboardType: widget.numberOnly ? TextInputType.number : null,
          inputFormatters: widget.numberOnly
              ? [
                  FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                  LengthLimitingTextInputFormatter(AiConstData.numberMaxLength),
                ]
              : null,
          decoration: InputDecoration(
            hintText: widget.hint,
            suffixIcon: widget.keys == 'unpackagedNum' ||
                    widget.keys == 'orderNum'
                ? Container(
                    width: 30,
                    alignment: Alignment.centerRight,
                    padding: EdgeInsets.symmetric(horizontal: 10),
                    child: Text(
                      'pcs',
                      style: MFont.medium14.copyWith(color: MColor.xFF000000),
                    ),
                  )
                : null,
          ).applyDefaults(decorationTheme),
        );
      }

      if (isSelected) {
        result = DropdownMenu(
          menuHeight: 200,
          width: width,
          initialSelection: widget.initial,
          menuStyle: MenuStyle(
            backgroundColor: WidgetStatePropertyAll(MColor.xFFFAFAFA),
            padding: WidgetStatePropertyAll(EdgeInsets.zero),
          ),
          trailingIcon: Icon(
            Icons.keyboard_arrow_right,
            color: MColor.xFFE5E5E5,
          ),
          onSelected: (value) {
            if (value == null) return;
            widget.onChanged?.call(value);
          },
          inputDecorationTheme: decorationTheme.copyWith(
            // border: AiWidgetStyle.focusedBorder,
            // enabledBorder: AiWidgetStyle.focusedBorder,
            contentPadding: EdgeInsets.only(left: 12, right: 0),
          ),
          // selectedTrailingIcon:Icon(Icons.keyboard_arrow_down) ,
          dropdownMenuEntries: [
            for (var i = 0; i < widget.menus!.length; ++i)
              DropdownMenuEntry(
                  value: widget.menusValues?[i] ?? widget.menus![i],
                  label: widget.menus![i]),
          ],
        );
      }
      return result;
    }

    return ColoredBox(
      color: Colors.white,
      child: Container(
        margin: widget.padding,
        height: 38.0,
        child: Row(
          children: [
            SizedBox(
                width: widget.titleWidth,
                child: Text(
                  widget.title,
                  style: MFont.medium14.copyWith(color: MColor.xFF000000),
                )),
            if ((widget.titleWidth ?? 0) == 0) SizedBox(width: 40),
            if (!isSelected && widget.width != null)
              SizedBox(width: widget.width, child: input())
            else if (isSelected && widget.width != null)
              input(widget.width)
            else
              Expanded(child: LayoutBuilder(
                builder: (context, constraints) {
                  return input(constraints.maxWidth);
                },
              )),
            if (widget.end != null) widget.end!,
          ],
        ),
      ),
    );
  }
}
