import 'package:flutter/material.dart';

extension TableBorderExt on TableBorder {
  static TableBorder without({
    Color color = const Color(0xFF000000),
    double width = 1.0,
    BorderStyle style = BorderStyle.solid,
    BorderRadius borderRadius = BorderRadius.zero,
    bool left = false,
    bool top = false,
    bool right = false,
    bool bottom = false,
  }) {
    final BorderSide side = BorderSide(color: color, width: width, style: style);
    return TableBorder(
        top: !top ? side : BorderSide.none,
        right: !right ? side : BorderSide.none,
        bottom: !bottom ? side : BorderSide.none,
        left: !left ? side : BorderSide.none,
        horizontalInside: side,
        verticalInside: side,
        borderRadius: borderRadius);
  }

  static TableBorder only({
    Color color = const Color(0xFF000000),
    double width = 1.0,
    BorderStyle style = BorderStyle.solid,
    BorderRadius borderRadius = BorderRadius.zero,
    bool left = false,
    bool top = false,
    bool right = false,
    bool bottom = false,
  }) {
    final BorderSide side = BorderSide(color: color, width: width, style: style);
    return TableBorder(
        top: top ? side : BorderSide.none,
        right: right ? side : BorderSide.none,
        bottom: bottom ? side : BorderSide.none,
        left: left ? side : BorderSide.none,
        horizontalInside: side,
        verticalInside: side,
        borderRadius: borderRadius);
  }

}
