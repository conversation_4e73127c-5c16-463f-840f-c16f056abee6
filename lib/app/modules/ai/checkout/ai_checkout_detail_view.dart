import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/widgets/ai_defect_record_tile_record.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';

import '../ai_pages.dart';
import '../entiy/ai_detail_defect_checout_info.dart';
import '../entiy/ai_summary_detail_data.dart';
import '../widgets/ai_defect_record_tile2.dart';
import '../widgets/ai_judge_result.dart';
import 'ai_checkout_controller.dart';

class AiCheckoutDetailView<T extends AiCheckoutController> extends GetView<T> {
  const AiCheckoutDetailView({super.key});


  @override
  Widget build(BuildContext context) {

    return StatefulBuilder(builder: (context, setState) {
      Widget _instruction(){
        return ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: 80,
              maxHeight: 120,
            ),
            child:Container(
              margin: EdgeInsets.only(top: 10),
          padding: EdgeInsets.fromLTRB(20, 8, 20, 8),
          color: Colors.white,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(S.current.general_desc),
              SizedBox(width: 60,),
              // Expanded(child: Text(controller.clickItem?.tips!.values!.firstOrNull??''))
              Expanded(child:
              SingleChildScrollView(
                child:  Text(controller.clickItem?.tips!.values!.firstOrNull??'')
              )
             )
            ],
          )
        ));
      }
      Widget _recordsWidget(item,index){
        return InkWell(
          onTap: (){
            controller.changeItem(index,item,setState);
          },
          child: Container(
              margin: EdgeInsets.only(right: 10),
              child:  Column(
                children:  [
                  Text(S.current.ai_simple_record+(index+1).toString(),
                      style: TextStyle(
                        color: MColor.black,
                        fontSize: 16,

                      )),
                  SizedBox(height: 5,),
                  if(controller.showIndex==index)
                    Container(height: 2,color: MColor.skin,width: 30,),
                ],
              )
          ),
        ) ;
      }

      //横向滚动
      Widget _recordRows(){
        return Container(
          margin: EdgeInsets.only(top: 15),
          padding: EdgeInsets.only(left: 20,right: 14,top: 8,bottom: 8),
          decoration: BoxDecoration(
              color: Colors.white
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: Get.width-110,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        for(int i=0;i<controller.showRecordsList!.length;i++)
                          _recordsWidget(controller.showRecordsList?[i],i)
                      ],
                    ),
                  )
              ),
              InkWell(
                onTap: (){
                  setState((){
                    controller.showMore=true;
                  });
                },
                child: Container(
                    width: 50,
                    margin: EdgeInsets.only(left: 10,right: 0),
                    child:  Image.asset('assets/images/more.png',width: 20,height: 20,)
                )
              )

            ],
          ),
        );
      }
      Widget _moreItem(item,index){
        return InkWell(
          onTap: (){
            controller.changeItem(index, item, setState);
          },
          child: Container(
            alignment: Alignment.center,
            width: Get.width/3,
            height: 40,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(S.current.ai_simple_record+(index+1).toString(),style: MFont.regular16,),
                SizedBox(height: 5,),
                if(controller.showIndex==index)
                  Container(height: 2,color: MColor.skin,width: 30,),
              ],
            ) ,
          )
        ) ;
      }
      Widget _more(){
        return Container(
          width: Get.width,
          color: Colors.white,
          child:Column(
            children: [
              Wrap(
                direction: Axis.horizontal,
                children: [
                  for(int i=0;i<controller.showRecordsList!.length;i++)...{
                    _moreItem(controller.showRecordsList?[i],i)
                  }
                ],
              ),
              IconButton(onPressed: (){
                setState((){
                  controller.showMore=false;
                });
              }, icon: Icon(Icons.arrow_drop_up_sharp,color: MColor.skin,size: 50,),padding: EdgeInsets.all(0),)
            ],
          ) ,
        );
      }

      Widget _requireItem(title,value,isArrow){
        return Container(
          padding: EdgeInsets.fromLTRB(20, 18, 20, 18),
          margin: EdgeInsets.only(top: 5),
          color: Colors.white,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title),
              InkWell(
                onTap: (){
                  Get.toNamed(
                    AiRoutes.CHECKOUT_LEVEL,
                    arguments: {
                      'level':controller.clickItem?.sampleLevel?.values?.firstOrNull,
                      'num':controller.clickItem?.sampleNum?.values?.firstOrNull
                    }
                  )?.then((value){
                    Map map = value;
                    print(map.toString());
                    setState((){
                      if(map['level']!=null){
                        controller.clickItem?.sampleLevel ??= AiModelInfo.empty();
                        controller.clickItem?.sampleNum ??= AiModelInfo.empty();
                        if(controller.clickItem?.sampleLevel!.values==null){
                          controller.clickItem?.sampleLevel!.values=[];
                        }
                        controller.clickItem?.sampleLevel!.values?.insert(0,map['level'])  ;
                      }
                      if(map['num']!=null){
                        if(controller.clickItem?.sampleNum!.values==null){
                          controller.clickItem?.sampleNum!.values=[];
                        }
                        controller.clickItem?.sampleNum!.values?.insert(0,map['num'])  ;
                      }
                    });
                  });
                },
                child: Row(
                  children: [
                    if(title==S.current.ai_sampling_level)...{
                      Text(controller.getLevelReal(value) ??'--'),
                    }else ...{
                      Text(value ??'--'),
                    },

                    SizedBox(width: 10,),
                    isArrow?
                    Icon(Icons.arrow_forward_ios,size: 14,):SizedBox(width: 20,),
                  ],
                )
              )


            ],
          ),
        );
      }

      Widget _recordTitle(){
        return Container(
            margin: EdgeInsets.only(left: 20,right: 20,top: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(S.current.ai_inspection_record,style: MFont.medium18,),
                Row(
                  children: [
                    InkWell(
                      onTap: (){

                        // Get.toNamed(AiRoutes.MEASURE_RECORD)?.then((value){
                        //   setState((){
                        //     controller.clickItem?.measureData ?.add( controller.operateRecords! );
                        //     if(controller.clickItem?.measureData!.length==1){
                        //       controller.clickRecords = controller.operateRecords;
                        //     }
                        //   });
                        // });

                        controller.goInput(null,setState);
                      },
                      child: Container(
                        padding: EdgeInsets.fromLTRB(10, 1, 10, 3),
                        decoration: BoxDecoration(
                            color: MColor.skin,
                            borderRadius: BorderRadius.circular(5)
                        ),
                        child: Text(S.current.ai_add_plus,style: TextStyle(
                            color: Colors.white,
                            fontSize: 14
                        ),),
                      )
                      ,
                    ),
                    SizedBox(width: 10,),
                    if(controller.showRecordsList!=null &&controller.showRecordsList!.isNotEmpty)
                      InkWell(
                        onTap: (){
                          controller.goInput(controller.newAiCheckOutInfo,setState);
                        },
                        child: Container(
                          padding: EdgeInsets.fromLTRB(10, 1, 10, 3),
                          decoration: BoxDecoration(
                              color: MColor.skin,
                              borderRadius: BorderRadius.circular(5)
                          ),
                          child: Text(S.current.edit,style: TextStyle(
                              color: Colors.white,
                              fontSize: 14
                          ),),
                        )
                        ,
                      )
                  ],
                ),

              ],
            )
        ) ;
      }

      Widget _defect(){
        return Container(
          padding: EdgeInsets.fromLTRB(20, 8, 20, 8),
          color: Colors.white,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(S.current.ai_find_defect),
            ],
          ),
        );
      }
      Widget _defectItem(AiCheckOutDefectInfo item){
        return InkWell(
            onTap: (){
              controller.goEditDefect(item,setState);
            },
            child: Container(
              margin: EdgeInsets.only(top: 5),
              padding: EdgeInsets.fromLTRB(20, 8, 20, 20),
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(item.title!,style: MFont.medium16,),
                      Icon(Icons.arrow_forward_ios,size: 14,color: MColor.xFF808080,)
                    ],
                  ),
                  SizedBox(height: 5,),
                  Text(item.instructions!,style: TextStyle(
                      color: MColor.xFF808080,
                      fontSize: 14
                  ),)
                ],
              ),
            )
        ) ;
      }


      return
        Scaffold(
            backgroundColor: MColor.backgroundColor,
            appBar: AppBar(
              title: Text(controller.clickItem?.name??''),
              centerTitle: true,
            ),    body: DefaultTextStyle(
            style: MFont.medium14.copyWith(color: MColor.black),
            child:  Column(
              children: [
                Expanded(
                    child: AiEmptyView(
                      baseResult: controller.baseResult,
                      child: SingleChildScrollView(
                        child:
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _instruction(),
                                    Container(
                                      margin: EdgeInsets.only(top: 5,bottom: 5,left: 20),
                                      child:  Text(S.current.ai_check_require,style: MFont.regular18,textAlign: TextAlign.left,),
                                    ),
                                    _requireItem(S.current.ai_sampling_level,controller.clickItem?.sampleLevel?.values?.firstOrNull,true),
                                    _requireItem(S.current.ai_simple_num, controller.clickItem?.sampleNum?.values?.firstOrNull,false),
                                    _recordTitle(),

                                    Stack(
                                      children: [
                                        Column(
                                          children: [
                                            if(controller.newAiCheckOutInfo!=null &&(controller.newAiCheckOutInfo!.defectsList!.isNotEmpty || controller.newAiCheckOutInfo!.recordsList!.isNotEmpty))...{
                                              _recordRows(),
                                              SizedBox(height: 5,),
                                              AiDefectRecordTileRecord(
                                                  records: controller.newAiCheckOutInfo!.recordsList!,
                                                  index: 1,
                                                  readOnly:true,
                                                  addLabel: (value) {
                                                    setState(() {
                                                    });
                                                  },
                                                  changeLabel: (oldValue, newValue) {
                                                    setState(() {
                                                    });
                                                  },
                                                  labels: [],
                                                  deleteCall: (value) {
                                                    setState(() {
                                                    });
                                                  }),
                                              _defect(),
                                              for(var item in controller.newAiCheckOutInfo!.defectsList??[])...{
                                                _defectItem(item)
                                              },
                                              _judge()
                                            },
                                         ]

                                    ),
                                        controller.showMore?
                                        Positioned(top: 0,child: _more(),):Container(),
                                  ],
                                ),
                    ])))),
                Container(
                  margin: EdgeInsets.only(left: 20,right: 20),
                  child: Row(
                    children: [
                      Container(
                          width: Get.width/2 - 20 ,
                          child:  AiSubmitButton(
                              onPressed: (){
                                controller.goPrevious(setState);
                              },
                              name: S.current.ai_simple_before,
                              isSave:true
                          )
                      )
                      ,
                      Container(
                          width: Get.width/2 - 20 ,
                          child:  AiSubmitButton(
                              onPressed:(){
                                controller.goNext(setState);
                              },
                              name: S.current.ai_next_item,
                              isSave:true
                          )
                      )
                    ],
                  )
                  ,
                )

              ],
            )));
    });}
  Widget _judge(){
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 15,),
        Container(
            padding: EdgeInsets.only(left:20 ),
            child:   Text(S.current.ai_judgment,style: MFont.medium18,textAlign: TextAlign.start,)
        )
        ,
        SizedBox(height: 15,),
        AiJudgeView(
          isSame:  controller.clickItem?.status,
          noteChange: (value) => controller.result?.remark = value,
          resultChange: (value) => {
            controller.clickItem?.status = value
          },
        ),
      ],
    );
  }


}
