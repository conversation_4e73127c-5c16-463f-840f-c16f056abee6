import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/widgets/ai_input_devect_tile.dart';
import 'package:inspector/app/modules/ai/widgets/ai_widget_style.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';

typedef ChangeLabel = void Function(String oldValue, String newValue);
typedef AddLabel = void Function(String value);

class AiDefectAddLabelView extends StatefulWidget {
  final AiDetailDefectInfo info;
  final ChangeLabel? changeLabel;
  final AddLabel? addLabel;
  final List<String> labels;

  const AiDefectAddLabelView({
    required this.info,
    required this.labels,
    this.changeLabel,
    this.addLabel,
    super.key,
  });

  @override
  State<AiDefectAddLabelView> createState() => _AiDefectAddLabelViewState();
}

class _AiDefectAddLabelViewState extends State<AiDefectAddLabelView> {
  String? _oldValue;
  String? _newValue;
  late final List<String> _labels = widget.labels;

  void showLabelInputView([String? label]) {
    _oldValue = label;
    _newValue = label;

    final decorationTheme = InputDecorationTheme(
      enabledBorder: AiWidgetStyle.enabledBorder,
      focusedBorder: AiWidgetStyle.focusedBorder,
      border: AiWidgetStyle.focusedBorder,
      // contentPadding: EdgeInsets.symmetric(horizontal: 12,vertical: 8),
      isDense: true,
      // constraints: BoxConstraints.tight(const Size.fromHeight(40)),
    );

    showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return Center(
          child: Container(
            height: 260,
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.symmetric(horizontal: 20),
            padding: EdgeInsets.fromLTRB(15, 15, 15, 10),
            decoration: BoxDecoration(color: MColor.white, borderRadius: BorderRadius.circular(10)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).ai_add_description,
                  style: TextStyle(color: MColor.aiMain),
                ),
                SizedBox(
                  height: 10,
                ),
                TextField(
                  style: MFont.medium14,
                  cursorColor: MColor.xFFF2591D,
                  minLines: 6,
                  maxLines: 6,
                  controller: TextEditingController(text: _newValue),
                  onChanged: (value) => _newValue = value,
                  decoration: InputDecoration().applyDefaults(decorationTheme),
                ),
                Spacer(),
                LayoutBuilder(
                  builder: (context, constraints) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(
                          width: constraints.maxWidth * 0.7,
                          child: AiModalBottomButton(
                            confirmTitle: label == null
                                ? S.of(context).ai_add
                                : S.of(context).ai_confirm_action,
                            confirm: () {
                              if (label == null) {
                                widget.addLabel?.call(_newValue ?? '');
                                if (!_labels.contains(_newValue)) {
                                  _labels.add(_newValue!);
                                }
                              } else {
                                widget.changeLabel?.call(_oldValue!, _newValue!);
                                final index = _labels.indexOf(_oldValue!);
                                _labels[index] = _newValue!;
                              }
                              setState(() {
                                Navigator.pop(context);
                              });
                            },
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(S.of(context).ai_defect_description),
        centerTitle: true,
        actions: [
          TextButton(
              onPressed: showLabelInputView,
              child: Text(
                S.of(context).ai_add,
                style: TextStyle(color: MColor.aiMain),
              ))
        ],
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: ListView(
          padding: EdgeInsets.fromLTRB(20, 15, 20, 10),
          children: [
            Text(
              S.of(context).ai_change_description_note,
              style: TextStyle(color: MColor.aiMain),
            ),
            SizedBox(height: 30),
            for (var o in _labels)
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  setState(() {
                    widget.info.label = o;
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    children: [
                      Icon(
                        widget.info.label == o ? Icons.check_circle : Icons.circle_outlined,
                        color: widget.info.label == o ? MColor.aiMain : MColor.xFFA6A6A6,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Expanded(child: Text(o)),
                      GestureDetector(
                          onTap: () {
                            showLabelInputView(o);
                          },
                          child: SafeArea(
                              left: false,
                              bottom: false,
                              top: false,
                              child: Padding(
                                padding: const EdgeInsets.only(right: 5),
                                child: Image.asset(Assets.aiLabelPen),
                              )))
                    ],
                  ),
                ),
              ),
            SizedBox(height: 40),
            AiModalBottomButton(
              confirm: () {
                //TODO:AI 需要确认吗
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }
}
