import 'package:inspector/generated/l10n.dart';

enum AICheckoutSampleLevelType {
  one('0'),
  two('1'),
  three('2'),
  S1('3'),
  S2('4'),
  S3('5'),
  S4('6'),
  custom('7');

  const AICheckoutSampleLevelType(this.value);
  final String value;

  bool get isNone => AICheckoutSampleLevelType.custom == this;

  String get name {
    switch (this) {
      case AICheckoutSampleLevelType.one:
        return S.current.ai_sample_level_type_0;
      case AICheckoutSampleLevelType.two:
        return S.current.ai_sample_level_type_1;
      case AICheckoutSampleLevelType.three:
        return S.current.ai_sample_level_type_2;
      case AICheckoutSampleLevelType.S1:
        return S.current.ai_sample_level_type_s1;
      case AICheckoutSampleLevelType.S2:
        return S.current.ai_sample_level_type_s2;
      case AICheckoutSampleLevelType.S3:
        return S.current.ai_sample_level_type_s3;
      case AICheckoutSampleLevelType.S4:
        return S.current.ai_sample_level_type_s4;
      case AICheckoutSampleLevelType.custom:
        return S.current.ai_sample_level_type_nothing;
    }
  }

  static String getName(String value) =>
      AICheckoutSampleLevelType.values
          .where(
            (e) => e.value == value,
          )
          .firstOrNull
          ?.name ??
      '';
}

enum AiCheckoutDefectLevelType {
  one('aql_1'),
  two('aql_2'),
  three('aql_3');

  const AiCheckoutDefectLevelType(this.value);
  final String value;

  static bool isOne(String value) => AiCheckoutDefectLevelType.one.value == value;
  static bool isTwo(String value) => AiCheckoutDefectLevelType.two.value == value;
  static bool isThree(String value) => AiCheckoutDefectLevelType.three.value == value;

  String get name {
    switch (this) {
      case AiCheckoutDefectLevelType.one:
        return S.current.ai_critical;
      case AiCheckoutDefectLevelType.two:
        return S.current.ai_important;
      case AiCheckoutDefectLevelType.three:
        return S.current.ai_minor;
    }
  }

  static String getName(String value) =>
      AiCheckoutDefectLevelType.values
          .where(
            (e) => e.value == value,
          )
          .firstOrNull
          ?.name ??
      '';
}

enum AiCheckoutDefectActionType {
  one('action_1'),
  two('action_2'),
  three('action_3');

  const AiCheckoutDefectActionType(this.value);
  final String value;

  String get name {
    switch (this) {
      case AiCheckoutDefectActionType.one:
        return S.current.ai_pick_out;
      case AiCheckoutDefectActionType.two:
        return S.current.ai_replace;
      case AiCheckoutDefectActionType.three:
        return S.current.ai_rework;
    }
  }

  static bool isOne(String value) => AiCheckoutDefectActionType.one.value == value;
  static bool isTwo(String value) => AiCheckoutDefectActionType.two.value == value;
  static bool isThree(String value) => AiCheckoutDefectActionType.three.value == value;

  static String getName(String value) =>
      AiCheckoutDefectActionType.values
          .where(
            (e) => e.value == value,
          )
          .firstOrNull
          ?.name ??
      '';
}
