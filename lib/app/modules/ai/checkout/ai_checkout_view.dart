import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/checkout/ai_checkout_controller.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/const_data/ai_const_data.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/number/ai_number_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_image_selected_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_defect_record_tile.dart';
import 'package:inspector/app/modules/ai/widgets/ai_input_devect_tile.dart';
import 'package:inspector/app/modules/ai/widgets/ai_tips_button.dart';
import 'package:inspector/app/modules/ai/widgets/ai_widget_style.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';
import 'package:inspector/generated/l10n.dart';

import '../ai_pages.dart';
import '../category/controller/ai_category_controller.dart';
import '../package/ai_package_view.dart';

class AiCheckoutView extends GetView<AiCheckoutController> {
  const AiCheckoutView({super.key});

  @override
  Widget build(BuildContext context) {
    var  mContext;
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(AiCategoryType.checkout.name),
          leading: Builder(builder: (BuildContext context) {
            return IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: (){
                Get.back();
              },
            );
          }),
        centerTitle: true,
          actions: [
            Obx(() {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if(AiCategoryController.to.qulityNum.value==0)
                    InkWell(
                        onTap: (){
                          Get.toNamed(  AiRoutes.QUALITY_QUICK,
                            arguments: {
                            },)?.then((value){
                            AiCategoryController.to.loadQulity();
                          });
                        },
                        child:Icon(Icons.remove_red_eye_outlined,color: MColor.skin,)
                    )else
                    InkWell(
                        onTap: (){
                          Get.toNamed(  AiRoutes.QUALITY_QUICK,
                            arguments: {
                            },)?.then((value){
                            AiCategoryController.to.loadQulity();
                          });
                        },

                        child:Badge(
                          label: Text(AiCategoryController.to.qulityNum.value>0?AiCategoryController.to.qulityNum.value.toString():''),
                          child:Icon(Icons.remove_red_eye_outlined,color: MColor.skin,),
                        )
                    )
                ],
              );
            })

            ,
            SizedBox(width: 5,),
            IconButton(onPressed: (){
              Scaffold.of(mContext).openDrawer();
            }, icon: Icon(Icons.format_align_center_outlined,color: MColor.skin)),

          ]
      ),
      drawer:  AiSideItem(categoryList: AiCategoryController.to.categoryList,),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: StatefulBuilder(builder: (context, setState) {
          mContext = context;
          return KeyboardDismissOnTap(
            child: Column(
              children: [
                AiProductSelectBar(loader: controller),
                Expanded(
                  child: Obx(() {
                    return AiEmptyView(
                      baseResult: controller.baseResult,
                      child: ListView(
                        children: [
                          AiPackageChecking(
                            tag: S.current.ai_test_project,
                            defects:[],
                            itemNames: (controller.inputList ?? []),
                            onClickItem: (index){
                              controller.goDetail(index,setState,true);
                            },
                            onAdded: (value) async {
                              await Get.toNamed(
                                AiRoutes.CHECKOUT_MANAGE,
                                arguments: {
                                  'type':'quality'
                                },
                                parameters: Get.parameters as Map<String, String>,
                              )?.then((value){
                                setState((){});
                              });
                            },
                          ),

                        ],
                      ),
                    );
                  }),
                ),
                AiSubmitButton.submit(
                  onPressed: controller.toNext,
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
  Widget _row(AiItemForms item,index,setState){
    return InkWell(
        onTap: (){
          controller.goDetail(index, setState,true);
        },
        child: Container(
          padding: EdgeInsets.fromLTRB(17, 8, 13, 8),
          margin: EdgeInsets.only(bottom: 2),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(5)
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(child: Text(item.name??'',
                softWrap: true,
                textAlign: TextAlign.left,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(color: MColor.black,fontSize: 16),))
              ,
              Row(
                children: [
                  if(item.data!.defectCheckOut !=null && item.data!.defectCheckOut!.values!.length!>0)
                  Text(controller.calCount(item.data!.defectCheckOut!.values![0].recordsList!),style: TextStyle(
                      color:  MColor.xFF808080,
                      fontSize: 14
                  ),),
                  SizedBox(width: 10,),
                  _tag(item.status??0),
                  SizedBox(width: 14,),
                  Icon(Icons.keyboard_arrow_right_outlined, )
                ],
              )
            ],
          ),
        )
    ) ;
  }

  Widget _tag(int status){
    Color color= MColor.xFF92D050;
    String text ='';
    if(status==1){
      text = S.current.approve;
      color= MColor.xFF92D050;
    }
    else if(status==2){//失败
      color= MColor.xFFFF1111;
      text = S.current.ai_default_config_fail;
    }else if(status==3){// 待定
      color= MColor.xFFDF8D14;
      text = S.current.ai_wait;
    }else{
      return Container();
    }

    return Container(
      padding: EdgeInsets.fromLTRB(7, 2, 7, 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Text(text,style: TextStyle(color: MColor.black,fontSize: 12),),
    );
  }

  Widget _manage(setState){
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      // color: MColor.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: 15),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  S.current.ai_simple_project,
                  style: MFont.medium18,
                ),
                Spacer(),
                GestureDetector(
                  onTap: () {

                  },
                  child: Container(
                    height: 27.0,
                    width: 60,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: MColor.skin,
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Text(
                      S.current.public_manage,
                      style: MFont.medium14.copyWith(color: MColor.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
          for(int i=0;i<controller.inputList!.length;i++)...{
            _row(controller.inputList![i],i,setState)
          }
        ],
      ),
    );
  }

}

//测试项
class _AiCheckoutItem extends StatefulWidget {
  const _AiCheckoutItem({required this.form});

  final AiItemForms form;

  @override
  State<_AiCheckoutItem> createState() => _AiCheckoutItemState();
}

class _AiCheckoutItemState extends State<_AiCheckoutItem> {
  bool _expanded = false;

  Color get borderColor => _expanded ? MColor.aiMain : MColor.xFFE5E5E5;

  IconData get iconData =>
      _expanded ? Icons.keyboard_arrow_down_rounded : Icons.keyboard_arrow_right_rounded;
  List<AssetEntity>? assets;
  @override
  Widget build(BuildContext context) {
    final titleWidth = 80.0;
    final width = titleWidth * 2;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          constraints: BoxConstraints(minHeight: 38),
          margin: EdgeInsets.only(top: 5),
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 4),
          decoration: BoxDecoration(color: MColor.white, border: Border.all(color: borderColor)),
          child: GestureDetector(
            onTap: () => setState(() => _expanded = !_expanded),
            behavior: HitTestBehavior.translucent,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.form.name ?? '',
                    maxLines: _expanded ? null : 1,
                    overflow: _expanded ? null : TextOverflow.ellipsis,
                    style: MFont.medium18,
                  ),
                ),
                Icon(
                  iconData,
                  color: borderColor,
                ),
              ],
            ),
          ),
        ),
        if (_expanded) ...[
          ColoredBox(
              color: MColor.white,
              child: Padding(
                  padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
                  child: Row(
                    children: [
                      Text(S.of(context).ai_basic_information),
                      Spacer(),
                      PopupMenuButton<String>(
                        color: MColor.xFFFAFAFA,
                        child: Icon(Icons.more_horiz),
                        itemBuilder: (context) {
                          return [
                            PopupMenuItem(
                              child: Text(S.of(context).ai_edit),
                              onTap: () async {
                                await showCupertinoModalPopup(
                                  context: context,
                                  builder: (context) {
                                    return _AiCheckoutNewItem(fromForm: widget.form);
                                  },
                                );
                                setState(() {});
                              },
                            ),
                            PopupMenuItem(
                              child: Text(S.of(context).ai_delete),
                              onTap: () {
                                showCustomDialog(
                                  S.of(context).ai_confirm_delete('"${widget.form.name ?? ''}"'),
                                  cancel: true,
                                  onConfirm: () =>
                                      AiCheckoutController.to.removeInputItem(widget.form),
                                );
                              },
                            )
                          ];
                        },
                      ),
                    ],
                  ))),
          ColoredBox(
            color: MColor.white,
            child: Padding(
              padding: EdgeInsets.fromLTRB(20, 10, 20, 15),
              child: Row(
                children: [
                  AiInputCell(
                    padding: EdgeInsets.zero,
                    titleWidth: titleWidth,
                    width: width,
                    initial: widget.form.sampleLevelObs.value,
                    menus: AICheckoutSampleLevelType.values.map((e) => e.name).toList(),
                    menusValues: AICheckoutSampleLevelType.values.map((e) => e.value).toList(),
                    title: '${S.of(context).ai_test_level}：',
                    hint: '${S.of(context).ai_test_level}：',
                    onChanged: widget.form.changeSampleLevel,
                  ),
                ],
              ),
            ),
          ),
          Obx(() {
            return AiInputCell(
                titleWidth: titleWidth,
                initial: widget.form.sampleNumObs.value,
                width: width,
                readOnly:
                    widget.form.sampleLevelObs.value != AICheckoutSampleLevelType.custom.value,
                title: '${S.of(context).ai_sampling_sample}：',
                hint: '${S.of(context).ai_sampling_sample}',
                onChanged: (value) {
                  widget.form.changeSampleNum(value);
                });
          }),
          ColoredBox(
            color: MColor.white,
            child: Container(
              margin: EdgeInsets.only(top: 5, bottom: 20),
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                      width: titleWidth,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('${S.of(context).ai_additional_information}：'),
                          SizedBox(height: 10),
                          AiTipsButton(
                            richText: widget.form.tips?.values,
                            images: widget.form.orderRequire?.values,
                          ),
                        ],
                      )),
                  Expanded(
                    child: TextField(
                      controller:
                          TextEditingController(text: widget.form.remarks?.values?.firstOrNull),
                      minLines: 3,
                      maxLines: 5,
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(AiConstData.textMaxLength),
                      ],
                      decoration: const InputDecoration(
                          enabledBorder: AiWidgetStyle.enabledBorder,
                          focusedBorder: AiWidgetStyle.focusedBorder,
                          contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 8)),
                      onChanged: widget.form.changeRemark,
                    ),
                  ),
                ],
              ),
            ),
          ),
          GestureDetector(
              onTap: () async {},
              child: Container(
                  padding: EdgeInsets.fromLTRB(20, 0, 20, 10),
                  decoration: BoxDecoration(
                    color: MColor.white,
                  ),
                  child: Row(
                    children: [
                      SizedBox(width: titleWidth, child: Text(S.of(context).ai_inspection_record)),
                      AiImageSelectedButton(manager: widget.form),
                    ],
                  ))),
          AiFormAssetsListView(
            manager: widget.form,
            padding: EdgeInsets.fromLTRB(20, 0, 20, 10),
          ),
          GestureDetector(
            onTap: () async {
              AiDetailDefectInfo? info = await AiInputDefectTile.inputDefectView(
                context,
                AiDetailDefectInfo(),
                addLabel: widget.form.data?.defect?.addLabel,
                changeLabel: widget.form.data?.defect?.changeLabel,
                labels: widget.form.data?.defect?.formLabels.toList() ?? [],
              );
              if (info != null) {
                widget.form.addDefectInfo(info);
              }
              setState(() {});
            },
            child: Container(
                padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
                decoration: BoxDecoration(
                  color: MColor.white,
                ),
                child: Row(
                  children: [
                    SizedBox(width: titleWidth, child: Text(S.of(context).ai_defect_record)),
                    Container(
                      height: 27.0,
                      width: titleWidth,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: MColor.aiMain)),
                      child: Text(
                        S.of(context).ai_add_plus,
                        style: MFont.medium14.copyWith(color: MColor.aiMain),
                      ),
                    )
                  ],
                )),
          ),
          Obx(() {
            if (widget.form.editTimeTitleObs.isEmpty) return SizedBox.shrink();
            return Container(
              alignment: Alignment.centerRight,
              color: MColor.white,
              padding: EdgeInsets.only(right: 20, bottom: 20),
              child: Text(
                widget.form.editTimeTitleObs.value,
                style: MFont.medium12.copyWith(color: MColor.xFF808080),
              ),
            );
          }),

          ///记录的数据
          for (int i = 0; i < (widget.form.defectLength.value); i++)
            AiDefectRecordTile(
                defect: widget.form.data!.defect!.defects![i],
                index: i + 1,
                labels: widget.form.data?.defect?.formLabels.toList() ?? [],
                addLabel: widget.form.data?.defect?.addLabel,
                changeLabel: widget.form.data?.defect?.changeLabel,
                deleteCall: (value) {
                  setState(() {
                    widget.form.removeDefectInfo(value);
                  });
                }),
        ]
      ],
    );
  }
}

///新建测试项
class _AiCheckoutNewItem extends StatefulWidget {
  const _AiCheckoutNewItem({this.fromForm});
  final AiItemForms? fromForm;

  @override
  State<_AiCheckoutNewItem> createState() => _AiCheckoutNewItemState();
}

class _AiCheckoutNewItemState extends State<_AiCheckoutNewItem> {
  late AiItemForms form = widget.fromForm ?? AiItemForms(key: AiItemForms.generatedKey);

  @override
  Widget build(BuildContext context) {
    final titleWidth = 100.0;
    return KeyboardDismissOnTap(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          title: Text(S.of(context).ai_new_test_item),
          centerTitle: true,
        ),
        body: Container(
          width: Get.width,
          padding: EdgeInsets.fromLTRB(20, 20, 20, 0),
          decoration: BoxDecoration(
              color: MColor.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(10),
              )),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AiInputCell(
                title: S.of(context).ai_test_project,
                hint: S.of(context).ai_test_project,
                titleWidth: titleWidth,
                initial: form.name,
                numberOnly: false,
                onChanged: (value) {
                  form.name = value;
                  form.refreshEditTime();
                },
                padding: EdgeInsets.only(bottom: 10),
              ),
              AiInputCell(
                titleWidth: titleWidth,
                padding: EdgeInsets.only(bottom: 10),
                initial: form.sampleLevelObs.value,
                menus: AICheckoutSampleLevelType.values.map((e) => e.name).toList(),
                menusValues: AICheckoutSampleLevelType.values.map((e) => e.value).toList(),
                title: S.of(context).ai_test_level,
                hint: S.of(context).ai_test_level,
                onChanged: form.changeSampleLevel,
              ),
              Obx(() {
                final readOnly =
                    form.sampleLevelObs.value != AICheckoutSampleLevelType.custom.value;
                return AiInputCell(
                    titleWidth: titleWidth,
                    padding: EdgeInsets.only(bottom: 10),
                    initial: readOnly ? form.sampleNumObs.value : form.sampleNum?.values?.first,
                    readOnly: readOnly,
                    title: S.of(context).ai_sampling_sample,
                    hint: S.of(context).ai_sampling_sample,
                    onChanged: (value) {
                      form.changeSampleNumNotObs(value);
                    });
              }),
              if (widget.fromForm == null) bottomButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget bottomButton() {
    return Container(
      padding: EdgeInsets.only(top: 50.0, bottom: 30),
      child: AiModalBottomButton(
        confirm: () {
          if (form.name?.isEmpty ?? true) {
            EasyLoading.showError(
                '${S.of(context).ai_please_fill_in}${S.of(context).ai_test_project}');
            return;
          }
          if (form.sampleNumObs.value.isEmpty) {
            EasyLoading.showError(S.of(context).ai_please_supplement_level_or_sample(
                S.of(context).ai_test_level, S.of(context).ai_sampling_sample));
            return;
          }
          Get.back<AiItemForms>(result: form);
        },
      ),
    );
  }
}
