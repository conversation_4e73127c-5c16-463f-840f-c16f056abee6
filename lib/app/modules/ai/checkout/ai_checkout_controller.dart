import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';

import '../../../../generated/l10n.dart';
import '../../../config/design.dart';
import '../../../tools/tools.dart';
import '../category/entity/ai_category_info.dart';
import '../category/entity/ai_recmend_data.dart';
import '../entiy/ai_detail_checout.dart';
import '../entiy/ai_detail_checout_record.dart';
import '../entiy/ai_detail_checout_single.dart';
import '../entiy/ai_detail_defect_checout_info.dart';
import '../entiy/ai_envent_imgage.dart';

class AiCheckoutController extends GetxController implements AiDataFeature {
  static AiCheckoutController get to => Get.find();

  final aiDetail = AiSummaryDetailData(
          id: 0,
          ruleOrderId: 0,
          orderId: 0,
          productId: 0,
          modelId: 0,
          type: 5,
          status: 0)
      .obs;

  List<AiItemForms>? get inputList =>
      (aiDetail.value.details?.config?.forms ?? []);
  List<AiItemEvent>? get eventList =>
      aiDetail.value.details?.config?.event ?? [];

  AiSummaryResult? get result => aiDetail.value.details?.config?.result;
  late var baseResult = AiBaseEntity();

  final categoryData = AiBaseEntity<List<AiCategoryInfo>>(data: []).obs;
  List<AiCategoryInfo> get categoryList => categoryData.value.data ?? [];

  late int type;

  get all => null;
  @override
  void onInit() {
    type = int.parse(Get.parameters['type']!);
    loadData();
    search('', null);
    super.onInit();

    subscription = eventBus.on<AiEnventImgage>().listen((event) {
      if (isRecord) {
        newAiCheckOutInfo!.recordsList?[clickImgIndex].images?.add(event.url);
      } else {
        editDefectItem!.records![clickImgIndex].images?.add(event.url);
      }
    });
  }

  /*void aiImageChange(String url){
    if (isRecord) {
      newAiCheckOutInfo!.recordsList?[clickImgIndex].images?.add(url);
    } else {
      editDefectItem!.records![clickImgIndex].images?.add(url);
    }
  }*/

  int clickImgIndex = 0;
  bool isRecord = true;
  StreamSubscription? subscription;
  @override
  void onClose() {
    super.onClose();
    if (subscription != null) {
      subscription?.cancel();
    }
  }

  AiItemForms? clickItem; // 首页点击的那一项
  List<AiCheckOutInfo>? itemList; // 详情页大list,包含两个list

  late AiDefectCheckOutModel defectCheckOut;
  late AiCheckOutInfo showDefectItem; // 正在显示的那一大项

  List<AiCheckOutInfoSingle>? showRecordsList; // 正在显示的检测列表全部

  AiCheckOutInfoSingle? newAiCheckOutInfo; // 正在显示的检测单个对象

  AiCheckOutDefectInfo? editDefectItem; // 编辑的缺陷对象
  AiCheckOutRecordInfo? editRecordItem; // 编辑的检测记录对象

  bool showMore = false;
  int showIndex = 0;
  void changeItem(index, AiCheckOutInfoSingle item, setState) {
    newAiCheckOutInfo = item;
    showIndex = index;
    setState(() {});
  }

  int clickIndex = 0;
  void goDetail(index, setState, needGo) {
    clickIndex = index;
    clickItem = inputList?[index];
    clickItem?.data ??= AiFormData.empty();
    if (clickItem?.data!.defectCheckOut == null) {
      clickItem?.data!.defectCheckOut = AiDefectCheckOutModel.empty();
    } else {}

    itemList = clickItem?.data!.defectCheckOut!.values ?? [];
    bool isNew = false;
    if (itemList!.isNotEmpty) {
      showDefectItem = itemList![0];
    } else {
      isNew = true;
      showDefectItem = AiCheckOutInfo.empty();
      itemList?.add(showDefectItem);
    }
    showRecordsList = showDefectItem.recordsList;
    if (showRecordsList!.isNotEmpty) {
      newAiCheckOutInfo = showRecordsList?[0];
    } else {
      newAiCheckOutInfo = AiCheckOutInfoSingle.empty();
    }
    if (needGo) {
      showIndex = 0;
      Get.toNamed(AiRoutes.CHECKOUT_DETAIL, arguments: {'isNew': isNew})
          ?.then((value) {
        if (isNew) {}
        setState(() {});
      });
    } else {
      setState(() {});
    }
  }

  Future<void> goPrevious(setState) async{
    AiBaseEntity entity = await uploadData();
    if(entity.code == 200){
      if (clickIndex == 0) {
        showToast(S.current.ai_no_more);
        return;
      }
      clickIndex--;
      goDetail(clickIndex, setState, false);
    }
  }

  Future<void> goNext(setState) async{
    AiBaseEntity entity = await uploadData();
    if(entity.code == 200){
      if (clickIndex == inputList!.length - 1) {
        showToast(S.current.ai_no_more);
        return;
      }
      clickIndex++;
      goDetail(clickIndex, setState, false);
    }
  }

  AiCheckOutInfoSingle? operateRecords;
  void goInput(item, setState) {
    bool isNew = false;
    if (item != null) {
    } else {
      newAiCheckOutInfo = AiCheckOutInfoSingle.empty();
      isNew = true;
    }
    Get.toNamed(
      AiRoutes.CHECKOUT_ADD_RECORD,
      arguments: {
        'isNew': isNew,
      },
      parameters: Get.parameters as Map<String, String>,
    )?.then((value) async{
      if(value == -2){
        await uploadData();
      }
      setState(() {
        // calCount();
      });
    });
  }

  void addOneRecords(bool isNew) {
    List<AiCheckOutRecordInfo>? records = [];
    for (var item in newAiCheckOutInfo!.recordsList!) {
      if (item.images!.isEmpty && item.label!.isEmpty) {
      } else {
        records.add(item);
      }
    }
    newAiCheckOutInfo?.recordsList = records;

    if (isNew) {
      // if(records.isNotEmpty){
      showRecordsList?.add(newAiCheckOutInfo!);
      // }
    }
    uploadData().then((value) {
      Get.back(result: -1);
    });
  }

  void addOneDefect(bool isNew) {
    print('000000000000');
    if (calcTotal()) {
      List<AiCheckOutDefectInfo>? records = [];
      for (var item in editDefectItem!.records!) {
        if (item.images!.isEmpty && item.label!.isEmpty) {
        } else {
          records.add(item);
        }
      }
      editDefectItem?.records = records;
      // newAiCheckOutInfo?.defectsList= records;
      if (isNew) {
        newAiCheckOutInfo?.defectsList?.add(editDefectItem!);
      }

      Get.back();
    }
  }

  void goEdit(item, setState) {
    print('111111111111');
    clickItem = item;
    Get.toNamed(
      AiRoutes.CHECKOUT_EDIT,
      arguments: {},
    )?.then((value) {
      setState(() {});
      if (value == -1) {
        Get.back(result: -1);
      }
    });
  }

  void goRecmend() {
    Get.toNamed(
      AiRoutes.AT_RECMEND,
      parameters: Get.parameters as Map<String, String>,
      arguments: {'type': type},
    )?.then((value) {
      if (value != null && value['data'] != null) {
        inputList?.addAll(value['data']);
        Get.back(result: -1);
      }
    });
  }

  void goEditDefect(item, setState) {
    print('222222222222222222');
    bool isNew = true;
    if (item != null) {
      isNew = false;
      editDefectItem = item;
    } else {
      editDefectItem = AiCheckOutDefectInfo.empty('');
    }
    Get.toNamed(
      AiRoutes.CHECKOUT_ADD_DEFECT,
      parameters: Get.parameters as Map<String, String>,
      arguments: {'isNew': isNew},
    )?.then((value) {
      isRecord = false;
      setState(() {});
      if (value == -1) {}
    });
  }

  void clickRecItem(item) {
    item.isSelected = !item.isSelected;
    update();
  }

  String searchKeywords = '';
  final recmendData = AiBaseEntity<List<AiRecmendData>>(data: []).obs;
  List<AiRecmendData> get recmendList => recmendData.value.data ?? [];
  void search(keyword, setState) {
    AiProvider().itemSearch(searchKeywords, '1', type.toString()).then((value) {
      if (value.data != null) {
        recmendData.value = value;
      }
      if (setState != null) {
        setState(() {});
      }
    });
  }

  void addRec() {
    List<AiItemForms> aa = [];
    for (var item in recmendList) {
      if (item.isSelected) {
        final now = DateTime.now();
        AiItemForms child =
            AiItemForms(key: 'form${now.day}${now.hour}${now.minute}');
        child.tips = AiModelInfo.empty();
        child.name = item.itemName;
        child.tips!.values = [item.remark ?? ''];
        aa.add(child);
      }
    }
    inputList?.addAll(aa);
    Get.back(result: -1);
  }

  calCount(List<AiCheckOutInfoSingle> countItem) {
    int totalNum1 = 0;
    int totalNum2 = 0;
    int totalNum3 = 0;
    for (var parent in countItem) {
      for (var item in parent.defectsList!) {
        if (item.level == 'aql_1') {
          // 关键
          totalNum1 = totalNum1 +
              (item.totalNum == null ? 0 : int.parse(item.totalNum!));
        } else if (item.level == 'aql_2') {
          // 关键
          totalNum2 = totalNum2 +
              (item.totalNum == null ? 0 : int.parse(item.totalNum!));
        } else if (item.level == 'aql_3') {
          // 关键
          totalNum3 = totalNum3 +
              (item.totalNum == null ? 0 : int.parse(item.totalNum!));
        }
      }
    }
    return '$totalNum1/$totalNum2/$totalNum3';
  }

  bool calcTotal() {
    int tiaochu = editDefectItem!.tiaoChu!.isEmpty
        ? 0
        : int.parse(editDefectItem!.tiaoChu!);
    int tihuan = editDefectItem!.tiHuan!.isEmpty
        ? 0
        : int.parse(editDefectItem!.tiHuan!);
    int fangong = editDefectItem!.fanGong!.isEmpty
        ? 0
        : int.parse(editDefectItem!.fanGong!);
    int totalNum = int.parse(editDefectItem!.totalNum!);
    if (tiaochu > totalNum) {
      showToast(S.current.ai_wrong_tip);
      return false;
    }
    if (tihuan > totalNum) {
      showToast(S.current.ai_wrong_tip);
      return false;
    }
    if (fangong > totalNum) {
      showToast(S.current.ai_wrong_tip);
      return false;
    }
    if (tiaochu + tihuan + fangong > totalNum) {
      showToast(S.current.ai_wrong_tip);
      return false;
    }
    editDefectItem?.refreshEditTime();
    return true;
  }

  void save() {
    uploadData().then((value) {
      Get.back();
    });
  }

  void goBack() {
    Get.back();
  }

  void addInputItem(AiItemForms form) {
    aiDetail.value.details?.config?.addInputItem(form);
  }

  void removeInputItem(AiItemForms form) {
    aiDetail.value.details?.config?.removeInputItem(form);
    aiDetail.refresh();
  }

  void toNext() {
    uploadData().then((value) {
      Get.offAndToNamed(AiRoutes.CHECKOUT_RESULT,
          parameters: Get.parameters as Map<String, String>);
    });
  }

  final simpleReadOnly = true.obs;
  final level = ''.obs;
  final levelNum = ''.obs;
  void showSheet() {
    Get.bottomSheet(Container(
        color: Colors.white,
        height: 300,
        child: ListView.separated(
          itemBuilder: (context, index) => InkWell(
              onTap: () {
                if (AICheckoutSampleLevelType.values[index].name == '自定义') {
                  level.value = '自定义';
                  simpleReadOnly.value = false;
                  update();
                } else {
                  simpleReadOnly.value = true;
                  level.value = AICheckoutSampleLevelType.values[index].name;
                  changeSampleLevel(
                      AICheckoutSampleLevelType.values[index].name, index);
                }
                Get.back();
              },
              child: Container(
                  padding: EdgeInsets.only(top: 10, bottom: 10),
                  child: Text(
                    AICheckoutSampleLevelType.values[index].name,
                    textAlign: TextAlign.center,
                  ))),
          itemCount: AICheckoutSampleLevelType.values.length,
          separatorBuilder: (BuildContext context, int index) => Divider(
            color: MColor.xFFE5E5E5,
            height: 1,
          ),
        )));
  }

  getLevelReal(value) {
    for (final item in AICheckoutSampleLevelType.values) {
      if (value == item.value) {
        return item.name;
      }
    }
    if (value == '无' || value == '') {
      return '/';
    }
    return value;
  }

  void changeSampleLevel(String value, index) {
    AiProvider()
        .getSampleNum(
            level: index.toString(),
            orderNum: AiCategoryController.to.orderNum.toString())
        .then((value) {
      levelNum.value = value.data.toString();
      refresh();
    });
  }

  final numCount = ''.obs;
  final levelCount = ''.obs;

  @override
  void loadData() {
    AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: AiCategoryType.checkout.id,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      baseResult = value;
      if (value.data != null) {
        aiDetail.value = value.data!;
      }
    });
  }

  @override
  Future<AiBaseEntity> uploadData() {
    if (aiDetail.value.details == null) {
      return Future.value(AiBaseEntity(code: 200));
    }
    aiDetail.value.details?.config?.uploadImageIfNeed();
    log('~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~');
    log(aiDetail.value.details!.toJson().toString());
    log('~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~');
    return AiProvider()
        .uploadConfig(id: aiDetail.value.id, details: aiDetail.value.details!);
  }
}
