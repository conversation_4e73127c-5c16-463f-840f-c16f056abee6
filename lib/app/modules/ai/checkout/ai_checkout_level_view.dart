import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/widgets/ai_defect_record_tile_record.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';

import '../widgets/ai_defect_record_tile2.dart';
import 'ai_checkout_controller.dart';

class AiCheckoutLevelView<T extends AiCheckoutController> extends GetView<T> {
  const AiCheckoutLevelView({super.key});


  @override
  Widget build(BuildContext context) {
    Map map = Get.arguments;
    controller.level.value = map['level']??'';
    controller.levelNum.value = map['num']??'';

    return  Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          title: Text(S.current.ai_modify_level),
          centerTitle: true,
          actions: [
            InkWell(
              onTap: (){
                Get.back(result: {
                  'num':controller.levelNum.value,
                  'level': controller.level.value
                });
              },
              child:      Container(
                margin: EdgeInsets.only(right: 20),
                padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
                decoration: BoxDecoration(
                    color: MColor.skin,
                    borderRadius: BorderRadius.circular(5)
                ),
                child: Text(S.current.date_save,style: TextStyle(
                    color: Colors.white,
                    fontSize: 14
                ),) ,
              )
            )

          ],
        ),    body:Obx((){
     return Column(
        children: [
          _requireItem(S.current.ai_sampling_level,controller.level.value,true),
          _requireItem(S.current.ai_simple_num,  controller.levelNum.value,false),
        ],
      );
    }) );}

  Widget _requireItem(title,value,isArrow){
    return Container(
      padding: EdgeInsets.fromLTRB(20, 18, 20, 18),
      margin: EdgeInsets.only(top: 5),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title),
          InkWell(
            onTap: (){
              if(isArrow){
                controller.showSheet();
              }
            },
            child: Row(
              children: [
                if(!isArrow &&controller.level.value=='自定义')
                  Container(
                    width: 80,
              child:  TextField(
                textAlign: TextAlign.right,
                controller: TextEditingController(text:controller.levelNum.value ),
                decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: S.current.apply_enter,
                  isDense: true,
                  contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                ),
                onChanged: (value){
                  controller.levelNum.value = value;
                },
              )
          )

                else if(title==S.current.ai_simple_num)...{
                  Text(value )
                }else...{
                  Text(controller.getLevelReal(value) )
                }
                   ,
                SizedBox(width: 10,),
                isArrow?
                Icon(Icons.keyboard_arrow_down):SizedBox(width: 20,),
              ],
            )
          )
        ],
      ),
    );
  }


}
