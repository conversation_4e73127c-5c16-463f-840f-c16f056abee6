import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/photo_selected/ai_form_assets_list_view.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/assets.dart';
import 'package:inspector/generated/l10n.dart';

import '../entiy/ai_detail_checout_record.dart';
import '../entiy/ai_detail_defect_checout_info.dart';
import '../photo_selected/picker_method.dart';
import '../widgets/ai_judge_result.dart';
import 'ai_checkout_controller.dart';

class AiCheckOutAddView extends GetView<AiCheckoutController> {
  const AiCheckOutAddView({super.key});

  @override
  Widget build(BuildContext context) {
    Map map = Get.arguments;
    bool isNew = map['isNew'];
    return StatefulBuilder(builder: (context, setState) {
      return Scaffold(
        backgroundColor: MColor.backgroundColor,
        appBar: AppBar(
          title: Text(controller.clickItem?.name ?? ''),
          centerTitle: true,
          actions: [
            if (!isNew)
              InkWell(
                onTap: () {
                  showCustomDialog(
                    S.of(context).delete_account_confirm,
                    cancel: true,
                    onConfirm: () {
                      controller.showRecordsList
                          ?.removeAt(controller.showIndex);
                      controller.showIndex = 0;
                      if (controller.showRecordsList!.isNotEmpty) {
                        controller.newAiCheckOutInfo =
                            controller.showRecordsList?[0];
                      } else {
                        controller.newAiCheckOutInfo = null;
                        controller.clickItem?.status = null;
                      }

                      // controller.showRecordsList?.removeAt(controller.showIndex);
                      // controller.newAiCheckOutInfo = null;
                      Get.back(result: -2);
                    },
                  );
                },
                child: Container(
                    margin: EdgeInsets.only(right: 12),
                    child: Text(
                      S.current.ai_delete,
                      style: TextStyle(color: MColor.skin),
                    )),
              )
          ],
        ),
        body: DefaultTextStyle(
          style: MFont.medium14.copyWith(color: MColor.black),
          child: Column(
            children: [
              Expanded(
                  child: AiEmptyView(
                baseResult: controller.baseResult,
                child: SingleChildScrollView(
                    child: Column(
                  children: [
                    _record(),

                    for (int i = 0;
                        i < controller.newAiCheckOutInfo!.recordsList!.length;
                        i++) ...{
                      _item(controller.newAiCheckOutInfo!.recordsList![i],
                          setState, context, i)
                    },
                    // for(var item in controller.newAiCheckOutInfo?.recordsList??[])...{
                    //
                    // },
                    _add(setState),
                    _defect(setState),
                    for (var item
                        in controller.newAiCheckOutInfo?.defectsList ?? []) ...{
                      _defectItem(item, setState),
                    },
                    _judge(),
                  ],
                )),
              )),
              SizedBox(
                height: 30,
              ),
              Container(
                  margin: EdgeInsets.only(left: 20, right: 20),
                  child: AiSubmitButton(
                      onPressed: () {
                        controller.addOneRecords(isNew);
                      },
                      name: S.current.ai_submit,
                      isSave: false)),
            ],
          ),
        ),
      );
    });
  }

  Widget _defect(setState) {
    return Container(
      margin: EdgeInsets.only(top: 10),
      padding: EdgeInsets.fromLTRB(20, 8, 20, 8),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(S.current.ai_find_defect),
          InkWell(
            onTap: () {
              controller.goEditDefect(null, setState);
            },
            child: Container(
              padding: EdgeInsets.fromLTRB(10, 1, 10, 3),
              decoration: BoxDecoration(
                  color: MColor.skin, borderRadius: BorderRadius.circular(5)),
              child: Text(
                S.current.ai_add_plus,
                style: TextStyle(color: Colors.white, fontSize: 14),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _defectItem(AiCheckOutDefectInfo item, setState) {
    return InkWell(
        onTap: () {
          controller.goEditDefect(item, setState);
        },
        child: Container(
          margin: EdgeInsets.only(top: 5),
          padding: EdgeInsets.fromLTRB(20, 8, 20, 20),
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    item.title!,
                    style: MFont.medium16,
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 14,
                    color: MColor.xFF808080,
                  )
                ],
              ),
              SizedBox(
                height: 5,
              ),
              Text(
                item.instructions!,
                style: TextStyle(color: MColor.xFF808080, fontSize: 14),
              )
            ],
          ),
        ));
  }

  Widget _add(setState) {
    return InkWell(
        onTap: () {
          controller.newAiCheckOutInfo?.recordsList
              ?.add(AiCheckOutRecordInfo.empty());
          setState(() {});
        },
        child: Container(
          margin: EdgeInsets.only(top: 15),
          padding: EdgeInsets.fromLTRB(12, 7, 12, 7),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: MColor.skin, width: 1)),
          child: Text(
            S.current.ai_simple_add,
            style: TextStyle(color: MColor.skin),
          ),
        ));
  }

  Widget _record() {
    return Container(
        margin: EdgeInsets.only(left: 20, right: 20, top: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.current.ai_simple_project_record,
              style: MFont.medium18,
            ),
          ],
        ));
  }

  Widget _item(AiCheckOutRecordInfo item, setState, context, index) {
    return Container(
      margin: EdgeInsets.only(top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              Container(
                  margin: EdgeInsets.only(top: 24),
                  padding: EdgeInsets.fromLTRB(24, 8, 12, 8),
                  color: Colors.white,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 5,
                      ),
                      Text(
                        S.current.ai_simple_record,
                        style: MFont.medium16,
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        children: [
                          InkWell(
                            child: Container(
                              width: 62,
                              height: 62,
                              margin: EdgeInsets.only(right: 10),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(color: MColor.xFFC4C4C4)),
                              child: Image.asset(
                                Assets.productPic,
                              ),
                            ),
                            onTap: () async {
                              if (item.label == null || item.label!.isEmpty) {
                                showToast(S.current.remark_toast);
                                return;
                              }
                              final assets = item.assetsIn;
                              final inCount =
                                  item.imageInfoList.length - assets.length;
                              controller.clickImgIndex = index;
                              final list = await PickMethod(
                                maxAssetsCount: max(100 - max(0, inCount), 0),
                              ).forCamera(context);
                              if (list != null && list.isNotEmpty) {
                                item.changeAssetsOnPicker(list);
                              }
                            },
                          ),
                          Expanded(
                            child: AiFormAssetsListView(
                                manager: item,
                                onImageChanged: (res) {
                                  logger.e(
                                      '===============================>>>>>>>>>>>>>>>');
                                  List<String> aa = [];
                                  for (final info in res.imageInfoList.value) {
                                    aa.add(info.url ?? '');
                                  }
                                  item.images = aa;
                                }),
                          ),
                        ],
                      )
                    ],
                  )),
              Positioned(
                right: 20,
                child: IconButton(
                  icon: Icon(Icons.highlight_off),
                  color: MColor.xFFC4C4C4,
                  onPressed: () {
                    print('2222222222');
                    controller.newAiCheckOutInfo!.recordsList?.removeAt(index);
                    // controller.clickItem?.records?.removeAt(index);
                    setState(() {});
                  },
                ),
              ),
            ],
          ),
          SizedBox(
            height: 3,
          ),
          Container(
              padding: EdgeInsets.fromLTRB(24, 8, 12, 8),
              color: Colors.white,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 5,
                  ),
                  Text(
                    S.current.ai_simple_dsec,
                    style: MFont.medium16,
                  ),
                  SizedBox(
                    width: 25,
                  ),
                  Expanded(
                      child: TextField(
                    minLines: 3,
                    maxLines: 18,
                    controller: TextEditingController(text: item.label),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 2, horizontal: 5),
                      fillColor: MColor.white,
                      hintText: S.of(Get.context!).ai_simple_add_desc,
                      hintStyle:
                          MFont.regular13.apply(color: DarkColor.xFF999999),
                      enabledBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: MColor.xFFE5E5E5, width: 1),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: MColor.skin, width: 1),
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                    onChanged: (text) {
                      item.label = text;
                    },
                  ))
                ],
              ))
        ],
      ),
    );
  }

  Widget _judge() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 15,
        ),
        Container(
            padding: EdgeInsets.only(left: 20),
            child: Text(
              S.current.ai_judgment,
              style: MFont.medium18,
              textAlign: TextAlign.start,
            )),
        SizedBox(
          height: 15,
        ),
        AiJudgeView(
          isSame: controller.clickItem?.status,
          noteChange: (value) => controller.result?.remark = value,
          resultChange: (value) => {controller.clickItem?.status = value},
        ),
      ],
    );
  }
}
