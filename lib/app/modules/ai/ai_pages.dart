import 'package:get/get_navigation/get_navigation.dart';
import 'package:inspector/app/modules/ai/attachment/ai_attachment_binding.dart';
import 'package:inspector/app/modules/ai/attachment/ai_attachment_second_view.dart';
import 'package:inspector/app/modules/ai/attachment/ai_attachment_view.dart';
import 'package:inspector/app/modules/ai/category/binding/ai_category_binding.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/checkout/ai_checkout_binding.dart';
import 'package:inspector/app/modules/ai/checkout/ai_checkout_edit_view.dart';
import 'package:inspector/app/modules/ai/checkout/ai_checkout_manage_view.dart';
import 'package:inspector/app/modules/ai/checkout/ai_checkout_view.dart';
import 'package:inspector/app/modules/ai/checkout_result/ai_checkout_result_binding.dart';
import 'package:inspector/app/modules/ai/checkout_result/ai_checkout_result_view.dart';
import 'package:inspector/app/modules/ai/mark/ai_mark_binding.dart';
import 'package:inspector/app/modules/ai/mark/ai_mark_controller.dart';
import 'package:inspector/app/modules/ai/mark/ai_mark_view.dart';
import 'package:inspector/app/modules/ai/measure/ai_measure_binding.dart';
import 'package:inspector/app/modules/ai/measure/ai_measure_standard_view.dart';
import 'package:inspector/app/modules/ai/measure/ai_measure_view.dart';
import 'package:inspector/app/modules/ai/measure_result/ai_measure_result_binding.dart';
import 'package:inspector/app/modules/ai/measure_result/ai_measure_result_view.dart';
import 'package:inspector/app/modules/ai/number/ai_number_binding.dart';
import 'package:inspector/app/modules/ai/number/ai_number_view.dart';
import 'package:inspector/app/modules/ai/number_result/ai_number_result_binding.dart';
import 'package:inspector/app/modules/ai/number_result/ai_number_result_view.dart';
import 'package:inspector/app/modules/ai/package/ai_package_binding.dart';
import 'package:inspector/app/modules/ai/package/ai_package_controller.dart';
import 'package:inspector/app/modules/ai/package/ai_package_manage_view.dart';
import 'package:inspector/app/modules/ai/package/ai_package_result_view.dart';
import 'package:inspector/app/modules/ai/package/ai_package_view.dart';
import 'package:inspector/app/modules/ai/product/ai_product_list_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_binding.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_controller.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_detail_controller.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_edit_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_input_quick_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_input_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_manage_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_quick_binding.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_quick_controller.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_quick_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_quality_view.dart';
import 'package:inspector/app/modules/ai/quality/ai_qulitty_detail_view.dart';
import 'package:inspector/app/modules/ai/quality_result/ai_quality_result_binding.dart';
import 'package:inspector/app/modules/ai/quality_result/ai_quality_result_view.dart';
import 'package:inspector/app/modules/ai/recmend/ai_recmend_binding.dart';
import 'package:inspector/app/modules/ai/recmend/ai_recmend_view.dart';
import 'package:inspector/app/modules/ai/style/ai_style_binding.dart';
import 'package:inspector/app/modules/ai/style/ai_style_controller.dart';
import 'package:inspector/app/modules/ai/style/ai_style_view.dart';

import 'attachment/ai_attachment_signature_view.dart';
import 'checkout/ai_checkout_add_defect_view.dart';
import 'checkout/ai_checkout_add_record_view.dart';
import 'checkout/ai_checkout_detail_view.dart';
import 'checkout/ai_checkout_level_view.dart';
import 'measure/ai_checkout_edit_view.dart';
import 'measure/ai_checkout_manage_view.dart';
import 'measure/ai_measure_detail_view.dart';
import 'measure/ai_measure_record_view.dart';
import 'measure/ai_measure_require_view.dart';
import 'measure/ai_measure_result.dart';
import 'package/ai_help_view.dart';
import 'package/ai_package_detail_view.dart';
import 'package/ai_package_edit_view.dart';
import 'package/ai_package_input_view.dart';

part 'ai_routes.dart';

class AiPages {
  AiPages._();

  static final routes = GetPage(
      name: _Path.CATEGORY,
      page: () => AiCategoryView(),
      binding: AiCategoryBinding(),
      children: [
        GetPage(
            name: _Path.NUMBER,
            page: () => const AiNumberView(),
            binding: AiNumberBinding(),
            children: [
              GetPage(
                  name: _Path.NUMBER_RESULT,
                  page: () => const AiNumberResultView(),
                  binding: AiNumberResultBinding())
            ]),
        GetPage(
          name: _Path.PACKAGE,
          // page: () => const AiPackageView2(),
          page: () => const AiPackageView<AiPackageController>(),
          binding: AiPackageBinding(),
        ),
        GetPage(
          name: _Path.MARK,
          page: () => const AiPackageView<AiPackageController>(),
          binding: AiMarkBinding(),
        ),
        GetPage(
          name: _Path.EDIT,
          page: () => const AiEditView(),
          binding: AiPackageBinding(),
        ),
        GetPage(
          name: _Path.MANAGE,
          page: () => const AiManageView(),
          binding: AiPackageBinding(),
        ),
        GetPage(
          name: _Path.ADD_EDIT,
          page: () => const AiAddEditView<AiPackageController>(),
          binding: AiPackageBinding(),
        ),
        GetPage(
          name: _Path.INPUT,
          page: () => const AiInputView(),
          binding: AiPackageBinding(),
        ),
        GetPage(
          name: _Path.HELP,
          page: () =>  const AiHelpView<AiPackageController>(),
          binding: AiPackageBinding(),
        ),
        GetPage(
          name: _Path.RESULT,
          page: () =>  const AiResultView<AiPackageController>(),
          binding: AiPackageBinding(),
        ),

        GetPage(
          name: _Path.STYLE,
          page: () => const AiPackageView<AiPackageController>(),
          binding: AiStyleBinding(),
        ),
        GetPage(
            name: _Path.CHECKOUT,
            page: () => AiCheckoutView(),
            binding: AiCheckoutBinding(),
            children: [
              GetPage(
                name: _Path.CHECKOUT_RESULT,
                page: () => AiCheckoutResultView(),
                binding: AiCheckoutResultBinding(),
              )
            ]),
        GetPage(
          name: _Path.CHECKOUT_EDIT,
          page: () => const AiCheckoutEditView(),
          binding: AiCheckoutBinding(),
        ),
        GetPage(
          name: _Path.CHECKOUT_MANAGE,
          page: () => const AiCheckoutManageView(),
          binding: AiCheckoutBinding(),
        ),
        GetPage(
          name: _Path.CHECKOUT_DETAIL,
          page: () => const AiCheckoutDetailView(),
          binding: AiCheckoutBinding(),
        ),
        GetPage(
          name: _Path.CHECKOUT_ADD_RECORD,
          page: () => const AiCheckOutAddView(),
          binding: AiCheckoutBinding(),
        ),
        GetPage(
          name: _Path.CHECKOUT_ADD_DEFECT,
          page: () => const AiChecckOutAddDefaultView(),
          binding: AiCheckoutBinding(),
        ),
        GetPage(
          name: _Path.CHECKOUT_LEVEL,
          page: () => const AiCheckoutLevelView(),
          binding: AiCheckoutBinding(),
        ),


        GetPage(
            name: _Path.MEASURE,
            page: () => const AiMeasureView(),
            binding: AiMeasureBinding(),
            children: [
              GetPage(
                name: _Path.MEASURE_RESULT,
                page: () => const AiMeasureResultView(),
                binding: AiMeasureResultBinding(),
              ),
            ]),
        GetPage(
          name: _Path.MEASURE_REQUIRE,
          page: () => const AiMeasureRequireView(),
          binding: AiCheckoutBinding(),
        ),
        GetPage(
          name: _Path.MEASURE_DETAIL,
          page: () => const AiMeasureDetailView(),
          binding: AiCheckoutBinding(),
        ),
        GetPage(
          name: _Path.MEASURE_RECORD,
          page: () => const AiMeasureRecordView(),
          binding: AiCheckoutBinding(),
        ),
        GetPage(
          name: _Path.MEASURE_Standard,
          page: () => const AiMeasureStandardView(),
          binding: AiCheckoutBinding(),
        ),
        GetPage(
          name: _Path.MEASURE_MANAGE,
          page: () => const AiMeasureManageView(),
          binding: AiCheckoutBinding(),
        ),
        GetPage(
          name: _Path.MEASURE_EDIT,
          page: () => const AiMeasureEditView(),
          binding: AiCheckoutBinding(),
        ),

        GetPage(
            name: _Path.QUALITY,
            page: () => const AiQualityView<AiQualityController>(),
            binding: AiQualityBinding(),
            children: [
              GetPage(
                name: _Path.QUALITY_RESULT,
                page: () => const AiQualityResultView(),
                binding: AiQualityResultBinding(),
              ),
            ]),
        GetPage(
          name: _Path.QUALITY_DETAIL,
          page: () => const AiQualityDetailView<AiQualityController>(),
          binding: AiQualityBinding(),
        ),
        GetPage(
          name: _Path.QUALITY_ADD,
          page: () => const AiQualityInputView<AiQualityController>(),
          binding: AiQualityBinding(),
        ),
        GetPage(
          name: _Path.QUALITY_MANAGE,
          page: () => const AiQualityManageView<AiQualityController>(),
          binding: AiQualityBinding(),
        ),
        GetPage(
          name: _Path.QUALITY_EDIT,
          page: () => const AiQualityEditView<AiQualityController>(),
          binding: AiQualityBinding(),
        ),
        GetPage(
          name: _Path.QUALITY_QUICK,
          page: () => const AiQualityQuickView<AiQualityController>(),
          binding: AiQualityQuickBinding(),
        ),
        GetPage(
          name: _Path.QUALITY_QUICK_ADD,
          page: () => const AiQualityInputQuickView<AiQualityQuickController>(),
          binding: AiQualityQuickBinding(),
        ),

        GetPage(
          name: _Path.ATTACHMENT,
          page: () => const AiAttachmentView(),
          binding: AiAttachmentBinding(),
        ),
        GetPage(
          name: _Path.ATTACHMENT_SECOND,
          page: () => const AiAttachmentSecondView(),
          binding: AiAttachmentBinding(),
        ),
        GetPage(
          name: _Path.ATTACHMENT_SIGN,
          page: () => const AiAttachmentSignView(),
          binding: AiAttachmentBinding(),
        ),
        GetPage(
          name: _Path.AT_RECMEND,
          page: () => const ATRecmendView(),
          binding: AiRecmendBinding(),
        ),
      ]);
}
