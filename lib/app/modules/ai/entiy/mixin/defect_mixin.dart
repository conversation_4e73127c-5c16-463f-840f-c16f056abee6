import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';

mixin DefectMixin {
  List<AiDetailDefectInfo>? defects;

  int? _defectOne; //填写后上传时使用
  int? _defectTwo;
  int? _defectThree;

  Map<String, int> get getTotalMap {
    return {
      AiCheckoutDefectLevelType.one.value: defectOne,
      AiCheckoutDefectLevelType.two.value: defectTwo,
      AiCheckoutDefectLevelType.three.value: defectThree,
    };
  }


  int get defectOne {
    if (_defectOne == null) {
      counterDefectLevelNum();
    }
    return _defectOne ?? 0;
  }

  int get defectTwo {
    if (_defectTwo == null) {
      counterDefectLevelNum();
    }
    return _defectTwo ?? 0;
  }

  int get defectThree {
    if (_defectThree == null) {
      counterDefectLevelNum();
    }
    return _defectThree ?? 0;
  }

  Map<String, Map<String, int>?>? _onLabelDefectLevelData;
  Map<String, Map<String, int>?>? get onLabelDefectLevelData {
    if (_onLabelDefectLevelData == null) {
      counterDefectLevelNum();
    }
    return _onLabelDefectLevelData!;
  }

  void counterDefectLevelNum() {
    _onLabelDefectLevelData = {};
    for (AiDetailDefectInfo e in defects ?? []) {
      _defectOne ??= 0;
      _defectTwo ??= 0;
      _defectThree ??= 0;

      final value = e.level ?? '';
      final int count = int.tryParse(e.totalNum ?? '0') ?? 0;
      Map<String, int>? totalMap = _onLabelDefectLevelData![e.label ?? ''];
      totalMap ??= {};

      if (AiCheckoutDefectLevelType.isOne(value)) {
        _defectOne = _defectOne! + count;
        final key = AiCheckoutDefectLevelType.one.value;
        totalMap.addAll({key: count + (totalMap[key] ?? 0)});
      } else if (AiCheckoutDefectLevelType.isTwo(value)) {
        _defectTwo = _defectTwo! + count;
        final key = AiCheckoutDefectLevelType.two.value;
        totalMap.addAll({key: count + (totalMap[key] ?? 0)});
      } else if (AiCheckoutDefectLevelType.isThree(value)) {
        _defectThree = _defectThree! + count;
        final key = AiCheckoutDefectLevelType.three.value;
        totalMap.addAll({key: count + (totalMap[key] ?? 0)});
      }
      _onLabelDefectLevelData![e.label ?? ''] = totalMap;
    }
  }

  late final Set<String> _formLabels = {};

  Set<String> get formLabels {
    Set<String> result = {};
    for (AiDetailDefectInfo e in defects ?? []) {
      if (e.label != null) result.add(e.label!);
    }
    result = _formLabels.union(result);
    return result;
  }

  void addLabel(String value) {
    if (value.isEmpty) return;
    _formLabels.add(value);
  }

  void changeLabel(String oldValue, String newValue) {
    if (oldValue == newValue) return;
    for (AiDetailDefectInfo e in defects ?? []) {
      if (e.label == oldValue) {
        e.label = newValue;
      }
    }
    _formLabels.remove(oldValue);
    _formLabels.add(newValue);
  }

  int index =-1;
  getNum( num){
    if(num=='1'){
      index++;
    }
    AiDetailDefectInfo? info = defects?[index];
    Map aa =info!.toJson();
    String nums = 'totalNum$num';
    return aa?['nums'];
  }


  int? levelOneCount(String label) =>
      onLabelDefectLevelData?[label]?[AiCheckoutDefectLevelType.one.value];
  int? levelTwoCount(String label) =>
      onLabelDefectLevelData?[label]?[AiCheckoutDefectLevelType.two.value];
  int? levelThreeCount(String label) =>
      onLabelDefectLevelData?[label]?[AiCheckoutDefectLevelType.three.value];
}
