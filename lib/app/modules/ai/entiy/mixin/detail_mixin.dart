import 'package:inspector/generated/l10n.dart';

mixin ConfigMixin {
  String? key;

  ///订单数量
  static const orderNumKey = 'orderNum';
  bool get isOrderNum => key == orderNumKey;

  ///已包装数量(成品)
  static const packagedNum = 'packagedNum';
  bool get isPackagedNum => key == packagedNum;

  ///未包装数量(成品)
  static const unpackagedNum = 'unpackagedNum';
  bool get isUnpackagedNum => key == unpackagedNum;

  ///备件数量
  static const backupNumSample = 'backupNumSample';
  bool get isBackupNumSample => key == backupNumSample;

  ///从已包装里抽样
  static const packageNumSample = 'packageNumSample';
  bool get isPackageNumSample => key == packageNumSample;

  ///从未包装里抽样
  static const unpackagedNumSample = 'unpackagedNumSample';
  bool get isUnpackagedNumSample => key == unpackagedNumSample;

  ///抽样包装编号
  static const packageNoSample = 'packageNoSample';
  bool get isPackageNoSample => key == packageNoSample;

  ///装箱完成率
  static const packageRate = 'packageRate';
  bool get isPackageRate => packageRate == key;

  ///未生产完成数量
  static const unproducedNum = 'unproducedNum';
  bool get isUnproducedNum => unproducedNum == key;

  static String keyName(String from) {
    switch (from) {
      case orderNumKey:
        return '订单数量';
      case packagedNum:
        return '已包装数量(成品)';
      case unpackagedNum:
        return '未包装数量(成品)';
      case backupNumSample:
        return '备件数量';
      case packageNumSample:
        return '从已包装里抽样';
      case unpackagedNumSample:
        return '从未包装里抽样';
      case packageNoSample:
        return '抽样包装编号';
      case packageRate:
        return S.current.ai_packing_completion_rate;
      case unproducedNum:
        return S.current.ai_unprocessed_quantity;
    }
    return '';
  }
}
