import 'package:common_utils/common_utils.dart';
import 'package:get/get.dart';
import 'package:inspector/generated/l10n.dart';

mixin EditTimeMixin {
  //
  DateTime? _editDateTime;
  DateTime? get editDateTime => _editDateTime;
  late final editTimeTitleObs = ''.obs;
  bool get hasEditTime => _editDateTime != null;
  void _refreshTimeTitle() {
    if (editDateTime == null) return;
    String timeStr = '';
    if (DateUtil.isToday(editDateTime!.millisecondsSinceEpoch)) {
      timeStr = DateUtil.formatDate(editDateTime, format: DateFormats.h_m);
      timeStr = '${S.current.today} $timeStr';
    } else {
      timeStr = DateUtil.formatDate(editDateTime, format: DateFormats.y_mo_d_h_m);
    }
    timeStr = '$timeStr ${S.current.edit}';
    editTimeTitleObs.value = timeStr;
  }

  void refreshEditTime() {
    _editDateTime = DateTime.now();
    _refreshTimeTitle();
  }

  void parseEditDateTime(dynamic json) {
    final time = json['edit_date_time'];
    if (time != null && time is String && time.isNotEmpty) {
      _editDateTime = DateUtil.getDateTime(time);
      _refreshTimeTitle();
    }
  }

  Map<String, String>? editTimeFormater() {
    if (_editDateTime == null) return null;
    return {'edit_date_time': DateUtil.formatDate(_editDateTime, format: DateFormats.full)};
  }
}
