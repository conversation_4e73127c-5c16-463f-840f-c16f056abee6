import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_checkout_sample_aql_data.dart';
import 'package:inspector/app/modules/ai/entiy/ai_detail_defect_info.dart';
import 'package:inspector/app/modules/ai/entiy/ai_measure_allow_num.dart';
import 'package:inspector/app/modules/ai/entiy/ai_measure_data.dart';
import 'package:inspector/app/modules/ai/entiy/image_info_entity.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/defect_mixin.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/detail_mixin.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/edit_time_mixin.dart';
import 'package:inspector/app/tools/tools.dart';

import 'ai_detail_checout.dart';
import 'ai_detail_checout_record.dart';
import 'ai_detail_checout_single.dart';
import 'ai_detail_defect_checout_info.dart';

class AiSummaryDetailData {
  AiSummaryDetailData({
    required this.id,
    required this.ruleOrderId,
    required this.orderId,
    required this.productId,
    required this.modelId,
    required this.type,
    required this.status,
    this.remark,
    this.details,
    this.rate,
    this.statusText,
  });

  AiSummaryDetailData.fromJson(dynamic json) {
    if (json is! Map) return;
    id = json['id'];
    ruleOrderId = json['rule_order_id'];
    orderId = json['order_id'];
    productId = json['product_id'];
    modelId = json['model_id'];
    type = json['type'];
    if (json['status'] != null) {
      status = int.tryParse(json['status']?.toString() ?? '0') ?? 0;
    }
    remark = json['remark'];
    details = json['details'] != null
        ? AiCategoryDetails.fromJson(json['details'])
        : null;
    rate = json['rate'];
    statusText = json['status_text'];
  }
  int? id;
  int? ruleOrderId;
  int? orderId;
  int? productId;
  int? modelId;
  int? type;
  int? status;
  String? remark;
  AiCategoryDetails? details;
  int? rate;
  String? statusText;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['rule_order_id'] = ruleOrderId;
    map['order_id'] = orderId;
    map['product_id'] = productId;
    map['model_id'] = modelId;
    map['type'] = type;
    map['status'] = status;
    map['status_text'] = statusText;
    if (remark != null) {
      map['remark'] = remark;
    }
    if (rate != null) {
      map['rate'] = rate;
    }
    if (details != null) {
      map['details'] = details?.toJson();
    }
    return map;
  }

  bool get isPassInLast => status == 1;
}

class AiCategoryDetails {
  AiCategoryDetails({
    this.type,
    this.name,
    this.nameEn,
    this.config,
  });

  AiCategoryDetails.fromJson(dynamic json) {
    type = json['type'];
    name = json['name'];
    nameEn = json['name_en'];
    config = json['config'] != null
        ? AiCategoryConfig.fromJson(json['config'])
        : null;
  }
  int? type;
  String? name;
  String? nameEn;
  AiCategoryConfig? config;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['type'] = type;
    map['name'] = name;
    map['name_en'] = nameEn;
    if (config != null) {
      map['config'] = config?.toJson();
    }
    return map;
  }

  Map<String, dynamic> toJson2() {
    final map = <String, dynamic>{};
    map['type'] = type;
    map['name'] = name;
    map['name_en'] = nameEn;
    if (config != null) {
      map['config'] = config?.toJsonNosee();
    }
    return map;
  }
}

class AiCategoryConfig {
  AiCategoryConfig({
    this.forms,
    this.event,
    this.result,
    this.table,
    this.data,
  });

  AiCategoryConfig.fromJson(dynamic json) {
    if (json['forms'] != null) {
      forms = [];
      json['forms'].forEach((v) {
        forms?.add(AiItemForms.fromJson(v));
      });
    }
    final tempTable = json['table'];
    if (tempTable != null) {
      if (tempTable is Map) {
        table = AiCategoryConfig.fromJson(tempTable);
      }
    }

    if (json['event'] != null) {
      event = [];
      json['event'].forEach((v) {
        event?.add(AiItemEvent.fromJson(v));
      });
    }
    data = json['data'] != null ? AiFormData.fromJson(json['data']) : null;
    result = json['result'] != null
        ? AiSummaryResult.fromJson(json['result'])
        : null;
  }

  AiCategoryConfig? table;
  List<AiItemForms>? forms;
  List<AiItemEvent>? event;
  AiSummaryResult? result;
  AiFormData? data;

  //
  void addInputItem(AiItemForms form) {
    form.manual = 1;
    forms ??= [];
    forms!.add(form);
  }

  void removeInputItem(AiItemForms form) {
    forms?.remove(form);
  }

  List<AiDetailDefectInfo>? get defects => data?.defect?.defects;
  set defects(List<AiDetailDefectInfo>? value) {
    data ??= AiFormData();
    data!.defect ??= AiDefectValueModel();
    data!.defect!.values = value;
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (forms != null) {
      map['forms'] = forms?.map((v) => v.toJson()).toList();
    }
    if (table != null) {
      map['table'] = table?.toJson();
    }

    if (event != null) {
      map['event'] = event?.map((v) => v.toJson()).toList();
    }
    if (result != null) {
      map['result'] = result?.toJson();
    }
    if (data != null) {
      map['data'] = data!.toJson();
    }
    // assert(_see);
    return map;
  }

  Map<String, dynamic> toJsonNosee() {
    final map = <String, dynamic>{};
    if (forms != null) {
      map['forms'] = forms?.map((v) => v.toJson()).toList();
    }
    if (table != null) {
      map['table'] = table?.toJson();
    }

    if (event != null) {
      map['event'] = event?.map((v) => v.toJson()).toList();
    }
    if (result != null) {
      map['result'] = result?.toJson();
    }
    if (data != null) {
      map['data'] = data!.toJson();
    }
    return map;
  }

  //
  String get notDoneForm {
    if (forms?.isEmpty ?? true) return '';
    bool result = true;
    for (final form in forms!) {
      if (form.key == 'backupNumSample') {
        result = true;
      } else {
        result = result && form.isDone;
      }

      if (!result && form.key != 'backupNumSample') return form.name!;
    }
    return '';
  }

  //
  bool isFitStandard(AiItemForms form) {
    final level = form.sampleLevel?.values?.first;
    final standard = table?.forms?.firstWhereOrNull(
      (element) => element.sampleLevel?.values?.first == level,
    );
    bool result = true;
    for (AiCheckoutSampleQalInfo o in standard?.sampleQal?.values ?? []) {
      final formAql = form.sampleQal?.values
          ?.firstWhereOrNull((element) => element.key == o.key);
      result = result &&
          (int.tryParse(o.aqlAcNum) ?? 0) >=
              (int.tryParse(formAql?.aqlAcNum ?? '0') ?? 0);
    }
    return result;
  }

  String _aql(int index) {
    return forms?.firstOrNull?.sampleQal?.values?[index].aql?.toString() ?? '';
  }

  String _aqlAcNum(int index) {
    return forms?.firstOrNull?.sampleQal?.values?[index].aqlAcNum?.toString() ??
        '';
  }

  void setAql(int index, String value) {
    forms?.firstOrNull?.sampleQal?.values?[index].aql = value;
  }

  void _setAcNum(int index, int value) {
    forms?.firstOrNull?.sampleQal?.values?[index].aqlAcNum = value;
  }

  Future<AiBaseEntity<int>> getAqlAcNum(
      int index, String aqlValue, String sampleLevel) {
    return AiProvider()
        .getAqlNum(
            level: sampleLevel,
            aql: aqlValue,
            orderNum: AiCategoryController.to.orderNum.toString())
        .then(
      (value) {
        if (value.isGood) {
          setAql(index, aqlValue);
          _setAcNum(index, value.data ?? 0);
        }
        return value;
      },
    );
  }

  //第一个作为参照
  String get aql1 => _aql(0);
  String get aql2 => _aql(1);
  String get aql3 => _aql(2);
  List<String>? get aqlList => [aql1, aql2, aql3];
  //第一个作为参照
  String get aqlAcNum1 => _aqlAcNum(0);
  String get aqlAcNum2 => _aqlAcNum(1);
  String get aqlAcNum3 => _aqlAcNum(2);
  List<String>? get aqlAcNumList => [aqlAcNum1, aqlAcNum2, aqlAcNum3];

  //必要就上传图片
  bool _see = false;
  void uploadImageIfNeed() {
    table?.uploadImageIfNeed();
    for (AiDetailDefectInfo e in (data?.defect?.values ?? [])) {
      e.images = e.uploadImage();
    }
    for (AiCheckOutInfo e in (data?.defectCheckOut?.values ?? [])) {
      for (AiCheckOutInfoSingle child in e.recordsList ?? []) {
        for (AiCheckOutRecordInfo record in child.recordsList ?? []) {
          record.images = e.uploadImage();
        }
        for (AiCheckOutDefectInfo dect in child.defectsList ?? []) {
          dect.images = e.uploadImage();
        }
      }
    }
    for (AiItemForms e in (forms ?? [])) {
      e.image?.type = 'images'; //修正错误数据
      e.image ??= AiModelInfo.image();
      e.image?.values = e.uploadImage();
      // for (AiMeasureInfo e in (e.measureData?.values ?? [])) {
      //   e.images = e.uploadImage();
      // }
      for (AiDetailDefectInfo e in (e.data?.defect?.defects ?? [])) {
        e.images = e.uploadImage();
      }
    }
    _see = true;
  }

  void uploadImageIfNeed2() {
    _see = true;
  }
}

class AiSummaryResult {
  AiSummaryResult({
    this.status,
    this.remark,
  });

  AiSummaryResult.fromJson(dynamic json) {
    if (json['status'] != null) {
      status = int.tryParse(json['status']?.toString() ?? '0') ?? 0;
    }
    remark = json['remark']?.toString();
  }
  int? status;
  String? remark;
  void changeResult(int value) {
    status = value;
  }

  int? get isSame => (status == null || status == 0) ? null : status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['status'] = status ?? 0;
    map['remark'] = remark ?? '';
    return map;
  }
}

class AiItemEvent {
  AiItemEvent({
    this.name,
    this.condion,
  });

  AiItemEvent.fromJson(dynamic json) {
    name = json['name'];
    if (json['condion'] != null) {
      condion = [];
      json['condion'].forEach((v) {
        condion?.add(AiDeatilEventCondion.fromJson(v));
      });
    }
  }
  String? name; //TODO:AI 名称？
  List<AiDeatilEventCondion>? condion;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['name'] = name ?? '';
    if (condion != null) {
      map['condion'] = condion?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class AiDeatilEventCondion {
  AiDeatilEventCondion({
    this.key,
    this.compareType,
    this.compareValue,
    this.compareValueType,
  });

  AiDeatilEventCondion.fromJson(dynamic json) {
    key = json['key'];
    compareType = json['compare_type'];
    compareValue = json['compare_value'].toString();
    compareValueType = json['compare_value_type'];
    if (json['result'] != null) {
      result = AiJudgeResultInfo.fromJson(json['result']);
    }
  }
  String? key;
  String? compareType;
  String? compareValue;
  int? compareValueType; // 1  是数值类型，2是百分比
  AiJudgeResultInfo? result;

  bool compare(String a, String b) {
    num? x = num.tryParse(a);
    num? y = num.tryParse(b);
    if (x == null || y == null) return true;

    switch (compareType) {
      case '<':
        return x < y;
      case '>':
        return x > y;
      case '<=':
        return x <= y;
      case '>=':
        return x >= y;
    }
    return true;
  }

  ///a标准，b结果
  List<String?> titles(String input, [String? name]) {
    logger.d('input->$input name->$name');
    num? a = num.tryParse(input);
    bool hasNum = a != null;
    List<String> list = [
      name ?? '', //判定项
      '$compareType${compareValue?.toString() ?? ''}${isInt ? '' : '%'}', //标准
      '${result?.values?.firstOrNull ?? ''}${isInt ? '' : (hasNum ? '%' : '')}', //结果
      result != null ? (result!.isPassInLast ?? false).title : '',
    ];
    logger.d('result->${result?.toJson()}');
    logger.d(list);
    return list;
  }

  bool get isInt => compareValueType == 1;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['key'] = key;
    map['compare_type'] = compareType;
    map['compare_value'] = compareValue;
    map['compare_value_type'] = compareValueType;
    map['result'] = result?.toJson() ?? AiJudgeResultInfo.empty().toJson();

    return map;
  }
}

class AiItemForms with ConfigMixin, ImageAssetsListManager, EditTimeMixin {
  AiItemForms(
      {required this.key,
      this.type = 'text',
      this.name,
      this.values,
      this.required,
      this.status,
      this.orderRequire,
      this.tips,
      this.remarks,
      this.sampleLevel,
      this.sampleNum,
      this.data,
      this.sampleQal,
      this.measureStandardData,
      this.measureData,
      this.measureAllowNum,
      this.descriptions});
  AiItemForms.manual() : manual = 1;

  AiItemForms.fromJson(dynamic json) {
    key = json['key'];
    type = json['type'];
    name = json['name'];
    if (json['values'] != null) {
      values = [];
      List<dynamic> aa = json['values'];
      for (var item in aa) {
        values?.add(item.toString());
      }
    } else {
      values = [];
    }
    // values = json['values'] != null ? json['values'].cast<String>() : [];
    required = json['required'];
    status = json['status'];
    image = json['image'] != null ? AiModelInfo.fromJson(json['image']) : null;
    if (json['records'] is String) {
      records = [];
    } else {
      records = json['records'] != null ? getRecords(json['records']) : [];
    }

    orderRequire = json['order_require'] != null
        ? AiModelInfo.fromJson(json['order_require'])
        : null;
    tips = json['tips'] != null ? AiModelInfo.fromJson(json['tips']) : null;
    tipsIndustry = json['tips_industry'] != null
        ? AiModelInfo.fromJson(json['tips_industry'])
        : null;
    tipsGuide = json['tips_guide'] != null
        ? AiModelInfo.fromJson(json['tips_guide'])
        : null;
    tips = json['tips'] != null ? AiModelInfo.fromJson(json['tips']) : null;

    remarks =
        json['remarks'] != null ? AiModelInfo.fromJson(json['remarks']) : null;
    sampleLevel = json['sample_level'] != null
        ? AiModelInfo.fromJson(json['sample_level'])
        : null;
    sampleNum = json['sample_num'] != null
        ? AiModelInfo.fromJson(json['sample_num'])
        : null;
    sampleQal = json['sample_aql_data'] != null
        ? AiCheckoutSampleQalData.fromJson(json['sample_aql_data'])
        : null;
    data = json['data'] != null ? AiFormData.fromJson(json['data']) : null;
    measureStandardData = json['measure_standard_data'] != null
        ? AiMeasureData.fromJson(json['measure_standard_data'])
        : null;

    measureData = json['measure_data'] != null
        ? getMeasureData(json['measure_data'])
        : null;
    measureAllowNum = json['allow_error_num'] != null
        ? AiCommonInfo.fromJson(json['allow_error_num'])
        : null;
    productNo = json['product_no'] != null
        ? AiCommonInfo.fromJson(json['product_no'])
        : null;
    manual = json['manual'] ?? 0;
    remark = json['remark']?.toString() ?? '';
    descriptions =
        json['descriptions'] != null ? json['descriptions'].cast<String>() : [];
    parseEditDateTime(json);
    changeUrlListOnGetData(image?.values);
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['key'] = key;
    map['type'] = type ?? '';
    map['name'] = name ?? '';
    map['values'] = values ?? [];
    map['required'] = required ?? 0;
    map['status'] = status ?? 0;
    map['order_require'] =
        orderRequire?.toJson() ?? AiModelInfo.image().toJson();
    map['tips'] = tips?.toJson() ?? AiModelInfo.text().toJson();

    if (image != null) {
      map['image'] = image?.toJson();
    }
    if (records != null) {
      map['records'] = records!.map((e) => e.toJson()).toList();
    }
    if (remarks != null) {
      map['remarks'] = remarks?.toJson();
    }
    if (sampleLevel != null) {
      map['sample_level'] = sampleLevel?.toJson();
    }
    if (sampleNum != null) {
      map['sample_num'] = sampleNum?.toJson();
    }
    if (data != null) {
      map['data'] = data?.toJson();
      sampleQal ??= AiCheckoutSampleQalData();
      sampleQal!.values = [];
      //汇总一下缺陷等级数据
      for (var e in AiCheckoutDefectLevelType.values) {
        int? count;
        if (AiCheckoutDefectLevelType.isOne(e.value)) {
          count = data?.defect?.defectOne;
        } else if (AiCheckoutDefectLevelType.isTwo(e.value)) {
          count = data?.defect?.defectTwo;
        } else if (AiCheckoutDefectLevelType.isThree(e.value)) {
          count = data?.defect?.defectThree;
        }
        sampleQal!.values!.add(AiCheckoutSampleQalInfo(
            key: e.value, aql: '', aqlAcNum: count.toString() ?? '0'));
      }
    }

    if (sampleQal != null) {
      map['sample_aql_data'] = sampleQal!.toJson();
    }
    if (measureStandardData != null) {
      map['measure_standard_data'] = measureStandardData!.toJson();
    }

    if (measureData != null) {
      map['measure_data'] = measureData!.map((e) => e.toJson()).toList();
    }
    if (measureAllowNum != null) {
      map['allow_error_num'] = measureAllowNum!.toJson();
    }
    if (productNo != null) {
      map['product_no'] = productNo!.toJson();
    }
    if (remark != null) {
      map['remark'] = remark;
    }
    map['manual'] = manual;
    final edit = editTimeFormater();
    if (edit != null) {
      map.addAll(edit);
    }
    return map;
  }

  String? key;
  String? type;
  String? name;
  List<String>? values;
  int? required;
  int? status;
  String? remark;
  AiModelInfo? image;
  List<AiRecordsInfo>? records;

  AiModelInfo? orderRequire;
  AiModelInfo? tips;
  AiModelInfo? tipsIndustry;
  AiModelInfo? tipsGuide;

  AiModelInfo? remarks;
  AiModelInfo? sampleLevel;
  AiModelInfo? sampleNum;
  AiFormData? data;
  AiCheckoutSampleQalData? sampleQal;

  AiMeasureData? measureStandardData;
  List<AiMeasureData>? measureData;
  AiCommonInfo? measureAllowNum;
  AiCommonInfo? productNo;
  String get measureNeedNum => sampleNumObs.value;
  //手动添加的form需要该字段区分与后台推荐的 1表示手动的
  int manual = 0;
  bool get fromAdd => manual == 1;
  List<String>? descriptions;

  @override
  void editImageCallback() {
    refreshEditTime();
    super.editImageCallback();
  }

  //
  void changeRemark(String value) {
    remarks ??= AiModelInfo.text();
    final content = value;
    remarks!.values = [content];
    refreshEditTime();
  }

  //
  bool get isRequired => required == 1;
  bool get isDone {
    bool result = values?.isNotEmpty ?? false;
    if (result) {
      for (final o in values!) {
        result = result && o.isNotEmpty;
        if (!result) break;
      }
    }
    if (!result) {
      result = imageInfoList.isNotEmpty;
      if (result) {
        for (final o in imageInfoList) {
          result = result && (o.hasUrl || o.hasAssets);
          if (!result) return false;
        }
      }
    }
    return result;
  }

  static String get generatedKey {
    final now = DateTime.now();
    return 'form${now.day}${now.hour}${now.minute}';
  }

  bool? get isPassInLast =>
      (status == null || status == 0) ? null : status == 1;

  ///包装编号
  List<String> get numberValues =>
      (values?..removeWhere((element) => element.isEmpty))
          ?.firstOrNull
          ?.split('/') ??
      [];
  void changeNumberValues(List<String> value) {
    values = [value.join('/')];
  }

  //操作图片上传的
  late List<String> _imageList = image?.values ?? [];
  List<String> get imageList => _imageList;
  set imageList(List<String> value) {
    _imageList = value;
    image ??= AiModelInfo.image(values: _imageList);
  }

  //
  late final sampleLevelObs = (sampleLevel?.values?.firstOrNull ?? '').obs;
  late final sampleNumObs = (sampleNum?.values?.firstOrNull ?? '').obs;
  void changeSampleLevel(String value, {void Function()? callback}) {
    sampleLevelObs.value = value;
    sampleLevel ??= AiModelInfo.text();
    sampleLevel!.values = [value];
    if (value != AICheckoutSampleLevelType.custom.value) {
      AiProvider()
          .getSampleNum(
              level: sampleLevelObs.value,
              orderNum: AiCategoryController.to.orderNum.toString())
          .then((value) {
        changeSampleNum(value.data ?? '');
        callback?.call();
      });
    }
    refreshEditTime();
  }

  void changeSampleNum(String value) {
    sampleNumObs.value = value;
    changeSampleNumNotObs(value);
  }

  void changeSampleNumNotObs(String value) {
    sampleNum ??= AiModelInfo.text();
    sampleNum!.values = [value];
    refreshEditTime();
  }

  late final defectLength = (data?.defect?.defects?.length ?? 0).obs;

  //

  void addDefectInfo(AiDetailDefectInfo info) {
    data ??= AiFormData();
    data!.defect ??= AiDefectValueModel();
    data!.defect!.defects ??= [];
    data!.defect!.defects!.insert(0, info);
    defectLength.value = data!.defect!.defects!.length;
  }

  void removeDefectInfo(AiDetailDefectInfo info) {
    data!.defect!.defects!.remove(info);
    defectLength.value = data!.defect!.defects!.length;
  }

  //
  // late final measureInfoLength = (measureData?.values?.length ?? 0).obs;

  void addMeasureInfo(AiMeasureInfo info) {
    // info.unitType = measureStandardData?.values?.firstOrNull?.unitType;
    // measureData ??= AiMeasureData();
    // measureData!.values ??= [];
    // measureData!.values!.insert(0, info);
    // measureInfoLength.value = measureData!.values!.length;
  }

  void removeMeasureInfo(AiMeasureInfo info) {
    // measureData?.values?.remove(info);
    // measureInfoLength.value = measureData!.values!.length;
  }

  bool get isMeasurePass {
    final allow = measureAllowNum?.values?.firstOrNull;
    final standard = measureStandardData?.values?.firstOrNull;
    if (allow == null || allow.isEmpty || standard == null) return true;

    bool result = true;
    for (var item in measureData!) {
      for (AiMeasureInfo o in item.values ?? []) {
        result = result && o.allowData(standard: standard, allow: allow);
        if (!result) return false;
      }
    }
    return result;
  }

  //不参与接口的字段
  late bool _joinWrite = false;

  ///临时性的是否在编辑中
  set joinWrite(bool value) {
    _joinWrite = value;
  }

  ///表示是否需要已在（有）编辑的峡类型表单数据
  bool get joinWrite => _joinWrite || canEditImage;

  ///获取的数据中有图片表示可编辑 在包装、唛头、产品样式中有用
  bool get canEditImage => imageList.isNotEmpty;

  void resetEditStatus() {
    joinWrite = false;
    image?.values?.clear();
    imageList.clear();
  }
  // bool get writeDone => !joinWrite || (values != null && values!.isNotEmpty);
}

class AiJudgeResultInfo {
  AiJudgeResultInfo.empty({
    this.status = 0,
    this.values = const [],
  });
  AiJudgeResultInfo.fromJson(dynamic json) {
    if (json['status'] != null) {
      status = int.tryParse(json['status']?.toString() ?? '0') ?? 0;
    }
    final temp = json['values'];
    if (temp != null && temp is List) {
      try {
        values = convertList<String>(temp);
        logger.d('解析values->$values');
      } catch (e) {
        print(e);
      }
    }
  }
  int? status;
  List<String>? values;

  bool? get isPassInLast =>
      (status == null || status == 0) ? null : status == 1;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['status'] = status ?? 0;
    map['values'] = values ?? [];
    return map;
  }
}

class AiRecordsInfo with ImageAssetsListManager {
  // AiRecordsInfo.empty({
  //   this.text = '',
  //   this.images = const [],
  // });
  AiRecordsInfo(this.text, this.images);

  AiRecordsInfo.fromJson(dynamic json) {
    if (json['text'] != null) {
      text = json['text']?.toString();
    }
    final temp = json['images'];
    if (temp != null && temp is List) {
      try {
        images = convertList<String>(json['images']);
      } catch (e) {
        print(e);
      }
    }
    changeUrlListOnGetData(images);
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['text'] = text;
    map['images'] = images ?? [];
    return map;
  }

  String? text;
  List<String>? images = [];
}

List<AiMeasureData> getMeasureData(dynamic json) {
  List<AiMeasureData> bb = [];
  if (json != null) {
    List aa = json;
    for (final item in aa) {
      bb.add(AiMeasureData.fromJson(item));
    }
  }
  return bb;
}

List<AiRecordsInfo> getRecords(dynamic json) {
  List<AiRecordsInfo> bb = [];
  if (json.length == 0) {
    return [];
  }
  if (json != null) {
    // List aa = jsonDecode(json);
    for (final item in json) {
      bb.add(AiRecordsInfo.fromJson(item));
    }
  }
  return bb;
}

class AiModelInfo {
  AiModelInfo.empty() {
    values = [];
    type = '';
  }
  AiModelInfo.text({
    this.values = const [],
  }) : type = 'text';

  AiModelInfo.image({
    this.values = const [],
  }) : type = 'images';

  AiModelInfo.fromJson(dynamic json) {
    type = json['type'];
    final temp = json['values'];
    if (temp != null && temp is List) {
      try {
        values = convertList<String>(json['values']);
      } catch (e) {
        print(e);
      }
    }
  }
  String? type;
  List<String>? values = [];

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['type'] = type;
    map['values'] = values ?? [];
    return map;
  }
}

List<T>? convertList<T>(List<dynamic>? value,
    {T? Function(dynamic value)? parse}) {
  if (value == null) {
    return null;
  }
  try {
    return value.map((dynamic e) => asT<T>(e, parse: parse)!).toList();
  } catch (e) {
    return <T>[];
  }
}

T? asT<T extends Object?>(dynamic value, {T? Function(dynamic value)? parse}) {
  if (value is T) {
    return value;
  }
  String type = T.toString();
  try {
    String valueS = value.toString();
    if (type == 'String') {
      return valueS as T;
    } else if (type == 'int') {
      int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == 'double') {
      return double.parse(valueS) as T;
    } else if (type == 'DateTime') {
      return DateTime.parse(valueS) as T;
    } else if (type == 'bool') {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else {
      return parse?.call(value);
    }
  } catch (e) {
    return null;
  }
}

class AiFormData {
  AiDefectValueModel? defect;
  AiDefectCheckOutModel? defectCheckOut;

  AiFormData({this.defect});
  AiFormData.empty() {
    defectCheckOut = AiDefectCheckOutModel.empty();
  }
  AiFormData.fromJson(Map<String, dynamic> json) {
    defect = json['defect'] != null
        ? AiDefectValueModel.fromJson(json['defect'])
        : null;
    defectCheckOut = json['defectCheckOut'] != null
        ? AiDefectCheckOutModel.fromJson(json['defectCheckOut'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (defect != null) {
      map['defect'] = defect!.toJson();
    }
    if (defectCheckOut != null) {
      map['defectCheckOut'] = defectCheckOut!.toJson();
    }
    return map;
  }
}

class AiDefectCheckOutModel {
  AiDefectCheckOutModel({this.values});
  List<AiCheckOutInfo>? values;
  AiDefectCheckOutModel.empty() {
    values = [];
  }

  AiDefectCheckOutModel.fromJson(Map<String, dynamic> json) {
    values = json['values'] != null
        ? List.from((json['values'] as List).map(
            (e) => AiCheckOutInfo.fromJson(e),
          ))
        : null;
  }
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (values != null) {
      map['values'] = values!.map((e) => e.toJson()).toList();
    }
    return map;
  }
}

class AiDefectValueModel with DefectMixin {
  AiDefectValueModel({this.values});

  List<AiDetailDefectInfo>? values;
  @override
  List<AiDetailDefectInfo>? get defects => values;
  @override
  set defects(List<AiDetailDefectInfo>? value) {
    values = value;
  }

  AiDefectValueModel.fromJson(Map<String, dynamic> json) {
    defects = json['values'] != null
        ? List.from((json['values'] as List).map(
            (e) => AiDetailDefectInfo.fromJson(e),
          ))
        : null;
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (defects != null) {
      map['values'] = defects!.map((e) => e.toJson()).toList();
    }
    return map;
  }
}

extension BoolTitle on bool {
  String get title => this ? 'PASS' : 'FAILED';
}
