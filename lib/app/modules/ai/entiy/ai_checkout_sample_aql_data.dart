import 'package:common_utils/common_utils.dart';

class AiCheckoutSampleQalData {
  AiCheckoutSampleQalData({
    this.type,
    this.values,
  });

  AiCheckoutSampleQalData.fromJson(dynamic json) {
    type = json['type'];
    if (json['values'] != null) {
      values = [];
      json['values'].forEach((v) {
        values?.add(AiCheckoutSampleQalInfo.fromJson(v));
      });
    }
  }
  String? type;
  List<AiCheckoutSampleQalInfo>? values;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['type'] = type;
    if (values != null) {
      map['values'] = values?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class AiCheckoutSampleQalInfo {
  AiCheckoutSampleQalInfo({
    this.key,
    this.aql,
    this.aqlAcNum,
  });

  AiCheckoutSampleQalInfo.fromJson(dynamic json) {
    key = json['key'];
    aql = json['aql'];
    aqlAcNum = json['aql_ac_num'];
  }
  String? key;
  String? aql;
  dynamic aqlAcNum;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    // assert(ObjectUtil.isNotEmpty(key) &&
    //     ObjectUtil.isNotEmpty(aql) &&
    //     ObjectUtil.isNotEmpty(aqlAcNum));
    map['key'] = key;
    map['aql'] = aql;
    map['aql_ac_num'] =  int.tryParse('$aqlAcNum')??0 ;
    return map;
  }
}
