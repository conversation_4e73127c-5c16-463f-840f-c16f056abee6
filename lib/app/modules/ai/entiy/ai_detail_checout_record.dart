import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/entiy/image_info_entity.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/edit_time_mixin.dart';

class AiCheckOutRecordInfo with ImageAssetsListManager, EditTimeMixin {
  AiCheckOutRecordInfo();

  List<String>? images;
  String? label;


  AiCheckOutRecordInfo.empty(){
    images=[];
    label='';
  }
  AiCheckOutRecordInfo.fromJson(Map<String, dynamic> json) {
    images = convertList<String>(json['images']);
    label = json['label']??'';
    parseEditDateTime(json);
    changeUrlListOnGetData(images);
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};

    map['label'] = label;
    map['images'] = images ?? [];

    final edit = editTimeFormater();
    if (edit != null) {
      map.addAll(edit);
    }
    return map;
  }

  @override
  void editImageCallback() {
    refreshEditTime();
    super.editImageCallback();
  }


}

