import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/entiy/image_info_entity.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/edit_time_mixin.dart';

class AiCheckOutDefectInfo with ImageAssetsListManager, EditTimeMixin {
  AiCheckOutDefectInfo();

  List<String>? images;
  String? label;
  String? key;

  String? title='';
  String? instructions='';

  ///aql_1 关键 aql_2重要 aql_3次要
  String? level='';
  String? totalNum='';
  String? actionNum='';

  String? tiaoChu='0';
  String? tiHuan='0';
  String? fanGong='0';

  List<AiCheckOutDefectInfo>? records=[];


  ///action_1挑出 action_2替换 action_3返工
  String? actionType='';

  String get levelName => AiCheckoutDefectLevelType.getName(level ?? '');
  String get actionName =>
      '${AiCheckoutDefectActionType.getName(actionType ?? '')}${actionNum ?? ''}';

  AiCheckOutDefectInfo.empty(mkey){
    key=mkey;
    images=[];
    label='';
    level='';
    totalNum='0';
    tiaoChu='0';
    tiHuan='0';
    fanGong='0';
  }
  AiCheckOutDefectInfo.fromJson(Map<String, dynamic> json) {
    images = convertList<String>(json['images']);

    instructions = json['instructions']??'';
    title = json['title']??'';
    label = json['label']??'';
    key = json['key']??'';
    level = json['level']??'';
    totalNum = json['totalNum']??'0';

    tiaoChu = json['tiaoChu']??'0';
    tiHuan = json['tiHuan']??'0';
    fanGong = json['fanGong']??'0';

    records= json['records'] != null
        ? (json['records'] as List).map((i) => AiCheckOutDefectInfo.fromJson(i)).toList()
        : [];

    actionNum = json['actionNum'];
    actionType = json['actionType'];
    parseEditDateTime(json);
    changeUrlListOnGetData(images);
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['instructions'] = instructions;
    map['title'] = title;

    map['key'] = key;
    map['label'] = label;
    map['level'] = level;
    map['totalNum'] = totalNum;
    map['actionNum'] = actionNum;
    map['actionType'] = actionType;
    map['images'] = images ?? [];
    map['records'] = records?.map((e) => e.toJson()).toList();
    map['tiaoChu'] = tiaoChu;
    map['tiHuan'] = tiHuan;
    map['fanGong'] = fanGong;


    final edit = editTimeFormater();
    if (edit != null) {
      map.addAll(edit);
    }
    return map;
  }

  @override
  void editImageCallback() {
    refreshEditTime();
    super.editImageCallback();
  }

}

/*class AiDetailDefectAction {
  String title;
  int num;

  AiDetailDefectAction({required this.title, this.num = 0});
}*/
