import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/entiy/image_info_entity.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/edit_time_mixin.dart';

import 'ai_detail_checout_record.dart';
import 'ai_detail_checout_single.dart';
import 'ai_detail_defect_checout_info.dart';

class AiCheckOutInfo with ImageAssetsListManager, EditTimeMixin {
  AiCheckOutInfo();

  List<AiCheckOutInfoSingle>? recordsList;
  String? status;


  AiCheckOutInfo.empty(){
    recordsList=[];
    status='';
  }
  AiCheckOutInfo.fromJson(Map<String, dynamic> json) {
    recordsList= json['recordsList'] != null
        ? (json['recordsList'] as List).map((i) => AiCheckOutInfoSingle.fromJson(i)).toList()
        : [];
    // if(json['recordsList'] != null){
    //   json['recordsList'].forEach((v) {
    //     List<AiCheckOutRecordInfo> child = [];
    //     v.forEach((item){
    //       AiCheckOutRecordInfo.fromJson(item);
    //       child.add(item);
    //     });
    //     recordsList?.add(child);
    //     // recordsList?.add(AiCheckOutRecordInfo.fromJson(v)).toList());
    //   });
    // }else{
    //   recordsList = [];
    // }

    status = json['status']??'';
    parseEditDateTime(json);
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};

    map['status'] = status ?? [];
    // map['recordsList'] = recordsList?.map((e) => e.map((child)=>child.toJson())).toList();
    map['recordsList'] = recordsList?.map((v) => v.toJson()).toList();
    final edit = editTimeFormater();
    if (edit != null) {
      map.addAll(edit);
    }
    return map;
  }

  @override
  void editImageCallback() {
    refreshEditTime();
    super.editImageCallback();
  }


}

