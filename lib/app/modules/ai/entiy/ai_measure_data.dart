import 'package:common_utils/common_utils.dart';
import 'package:inspector/app/modules/ai/entiy/image_info_entity.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/edit_time_mixin.dart';

class AiMeasureData {
  AiMeasureData({
    this.type = 'text',
    this.values,
  });
  AiMeasureData.empty(){
    type = 'text';
    values=[];
  }
  AiMeasureData.fromJson(dynamic json) {
    type = json['type'];
    if (json['values'] != null) {
      values = [];
      json['values'].forEach((v) {
        values?.add(AiMeasureInfo.fromJson(v));
      });
    }
  }
  String? type;
  List<AiMeasureInfo>? values;
  String get allValueString {
    String result = '';
    String unit = '';
    result = values
            ?.map((e) {
              unit = e.unitType ?? '';
              return e.measureValueName;
            })
            .toList()
            .join('$unit\n') ??
        '';
    return '${result}$unit';
  }

  String get measureUnit => values?.firstOrNull?.unitType ?? '';
  set measureUnit(String? value) {
    addFirstIfNeed();
    values![0].unitType = value;
  }

  String get measureType => values?.firstOrNull?.type ?? '';
  set measureType(String? value) {
    addFirstIfNeed();
    values![0].type = value;
  }

  void addFirstIfNeed() {
    values ??= [];
    if (values!.elementAtOrNull(0) == null) {
      values!.add(AiMeasureInfo());
    }
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['type'] = type;
    if (values != null) {
      map['values'] = values?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class AiMeasureInfo with ImageAssetsListManager, EditTimeMixin {
  AiMeasureInfo({
    this.l,
    this.w,
    this.h,
    this.type,
    this.name,
    this.images,
    this.unitType,
    this.error,
    this.standard,
    this.measureValue,
  });

  String? l;
  String? w;
  String? h;
  String? type;
  String? unitType;
  String? productNumber;
  List<String>? images;
  String? error;
  String? standard;
  String? measureValue;
  String? name;

  bool get isDimensions => type == 'dimensions';
  bool get isWeight => type == 'weight';
  bool get isLengthWidth => type == 'lengthWidth';

  String get measureValueName {
    String result = '';
    if (isDimensions) {
      result = '${l}x${w}x$h';
    } else if (isLengthWidth) {
      result = '${l}x${w}';
    } else if (isWeight) {
      result = '$l';
    }
    return result;
  }

  bool allowData({required AiMeasureInfo standard, required String allow}) {
    if (standard.isWeight) {
      return _allowRange(from: l ?? '0', standard: standard.l ?? '0', allow: allow);
    } else if (standard.isLengthWidth) {
      final lResult = _allowRange(from: l ?? '0', standard: standard.l ?? '0', allow: allow);
      final wResult = _allowRange(from: w ?? '0', standard: standard.w ?? '0', allow: allow);
      return lResult && wResult;
    } else if (standard.isDimensions) {
      final lResult = _allowRange(from: l ?? '0', standard: standard.l ?? '0', allow: allow);
      final wResult = _allowRange(from: w ?? '0', standard: standard.w ?? '0', allow: allow);
      final hResult = _allowRange(from: h ?? '0', standard: standard.h ?? '0', allow: allow);
      return lResult && wResult && hResult;
    }
    return false;
  }

  bool _allowRange({required String from, required String standard, required String allow}) {
    // print('===========from $from standard $standard allow $allow');

    final a = num.tryParse(from) ?? 0;
    final b = num.tryParse(standard) ?? 0;
    final c = num.tryParse(allow) ?? 0;
    // print('======xxxxx a $a b $b c $c');

    final result = a <= (b + c) && a >= (b - c);

    // print('======result $result');

    return result;
  }

  @override
  void editImageCallback() {
    refreshEditTime();
    super.editImageCallback();
  }
  AiMeasureInfo.empty(){
    type = '';
    error =  '';
    standard =  '';
    name =  '';
    measureValue =  '';
    unitType='';
    productNumber='';
    images=[];
  }
  AiMeasureInfo.fromJson(dynamic json) {
    l = json['l'];
    w = json['w'];
    h = json['h'];
    type = json['type'];
    error = json['error'];
    standard = json['standard'];
    name = json['name'];
    measureValue = json['measureValue'];

    unitType = json['unit_type'];
    productNumber = json['product_number'];
    images = json['images'] != null ? json['images'].cast<String>() : [];
    parseEditDateTime(json);
    changeUrlListOnGetData(images);
  }
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    // assert(ObjectUtil.isNotEmpty(type) && ObjectUtil.isNotEmpty(unitType));
    map['l'] = l ?? '';
    map['w'] = w ?? '';
    map['h'] = h ?? '';
    map['type'] = type ?? '';
    map['error'] = error ?? '';
    map['standard'] = standard ?? '';
    map['unit_type'] = unitType ?? '';
    map['product_number'] = productNumber ?? '';
    map['images'] = images ?? [];
    map['name'] = name ?? '';
    map['measureValue'] = measureValue ?? '';

    final edit = editTimeFormater();
    if (edit != null) {
      map.addAll(edit);
    }
    return map;
  }
}
