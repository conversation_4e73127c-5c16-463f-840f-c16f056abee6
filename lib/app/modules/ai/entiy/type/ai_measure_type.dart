import 'package:inspector/generated/l10n.dart';

enum AiMeasureType {
  dimensions('dimensions'),
  lengthWidth('lengthWidth'),
  weight('weight');

  const AiMeasureType(this.value);
  final String value;
  String get name {
    switch (this) {
      case AiMeasureType.dimensions:
        return S.current.ai_length_width_height;
      case AiMeasureType.lengthWidth:
        return S.current.ai_length_width;
      case AiMeasureType.weight:
        return S.current.ai_other;
    }
  }
}
