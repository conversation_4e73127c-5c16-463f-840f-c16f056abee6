import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/entiy/image_info_entity.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/edit_time_mixin.dart';

class AiDetailDefectInfo with ImageAssetsListManager, EditTimeMixin {
  AiDetailDefectInfo({this.images, this.label, this.level, this.totalNum,this.tiaoChu,this.tiHuan,this.fanGong});
  String? signature;
  List<String>? images;
  String? label;
  String? key;
  List<String>? covers;
  String? title='';
  String? description='';

  ///aql_1 关键 aql_2重要 aql_3次要
  String? level='';
  String? totalNum='';
  String? actionNum='';

  String? tiaoChu='0';
  String? tiHuan='0';
  String? fanGong='0';

  String? totalNum1='';
  String? totalNum2='';
  String? totalNum3='';

  String? tiaoChu1='0';
  String? tiaoChu2='0';
  String? tiaoChu3='0';

  String? tiHuan1='0';
  String? tiHuan2='0';
  String? tiHuan3='0';

  String? fanGong1='0';
  String? fanGong2='0';
  String? fanGong3='0';


  List<AiDetailDefectInfo>? records=[];


  ///action_1挑出 action_2替换 action_3返工
  String? actionType='';

  String get levelName => AiCheckoutDefectLevelType.getName(level ?? '');
  String get actionName =>
      '${AiCheckoutDefectActionType.getName(actionType ?? '')}${actionNum ?? ''}';

  AiDetailDefectInfo.empty(mkey){
    key=mkey;
    images=[];
    covers=[];

    signature='';
    label='';
    level='';
    totalNum='0';
    tiaoChu='0';
    tiHuan='0';
    fanGong='0';
  }
  AiDetailDefectInfo.fromJson(Map<String, dynamic> json) {
    images = convertList<String>(json['images']);
    covers= convertList<String>(json['covers']);
    description = json['description']??'';
    title = json['title']??'';
    label = json['label']??'';
    key = json['key']??'';
    level = json['level']??'';
    totalNum = json['totalNum']??'0';
    signature = json['signature']??'';
    tiaoChu = json['tiaochu']??'0';
    tiHuan = json['tihuan']??'0';
    fanGong = json['fangong']??'0';

    totalNum1 = json['totalNum1']??'0';
    totalNum2 = json['totalNum2']??'0';
    totalNum3 = json['totalNum3']??'0';

    tiaoChu1 = json['tiaochu1']??'0';
    tiaoChu2 = json['tiaoChu2']??'0';
    tiaoChu3 = json['tiaoChu3']??'0';

    tiHuan1 = json['tiHuan1']??'0';
    tiHuan2 = json['tiHuan2']??'0';
    tiHuan3 = json['tiHuan3']??'0';

    fanGong1 = json['fanGong1']??'0';
    fanGong2 = json['fanGong2']??'0';
    fanGong3 = json['fanGong3']??'0';
    records= json['records'] != null
        ? (json['records'] as List).map((i) => AiDetailDefectInfo.fromJson(i)).toList()
        : [];

    actionNum = json['actionNum'];
    actionType = json['actionType'];
    parseEditDateTime(json);
    changeUrlListOnGetData(images);
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    // assert(ObjectUtil.isNotEmpty(label) &&
    //     ObjectUtil.isNotEmpty(level) &&
    //     // ObjectUtil.isNotEmpty(totalNum) &&
    //     // ObjectUtil.isNotEmpty(actionNum) &&
    //     ObjectUtil.isNotEmpty(actionType));
    map['key'] = key;
    map['label'] = label;
    map['signature'] = signature;
    map['level'] = level;
    map['totalNum'] = totalNum;
    map['actionNum'] = actionNum;
    map['actionType'] = actionType;
    map['images'] = images ?? [];
    map['covers'] = covers ?? [];
    map['records'] = records?.map((e) => e.toJson()).toList();
    map['tiaoChu'] = tiaoChu;
    map['tiHuan'] = tiHuan;
    map['fanGong'] = fanGong;

    map['totalNum1'] = totalNum1;
    map['totalNum2'] = totalNum2;
    map['totalNum3'] = totalNum3;

    map['tiaoChu1'] = tiaoChu1;
    map['tiaoChu2'] = tiaoChu2;
    map['tiaoChu3'] = tiaoChu3;

    map['tiHuan1'] = tiHuan1;
    map['tiHuan2'] = tiHuan2;
    map['tiHuan3'] = tiHuan3;

    map['fanGong1'] = fanGong1;
    map['fanGong2'] = fanGong2;
    map['fanGong3'] = fanGong3;


    final edit = editTimeFormater();
    if (edit != null) {
      map.addAll(edit);
    }
    return map;
  }

  @override
  void editImageCallback() {
    refreshEditTime();
    super.editImageCallback();
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AiDetailDefectInfo &&
          runtimeType == other.runtimeType &&
          label == other.label &&
          level == other.level &&
          actionType == other.actionType;

  @override
  int get hashCode => label.hashCode ^ level.hashCode ^ actionType.hashCode;
}

/*class AiDetailDefectAction {
  String title;
  int num;

  AiDetailDefectAction({required this.title, this.num = 0});
}*/
