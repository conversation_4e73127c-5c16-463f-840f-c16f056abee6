import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/photo_selected/action_picture/ai_image_info.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../ai_net/ai_server.dart';

mixin ImageAssetsListManager {
  final imageInfoList = <AiImageInfo>[].obs;
  List<AssetEntity> get assetsIn {
    List<AssetEntity> result = [];
    for (final info in imageInfoList) {
      if (info.hasAssets) {
        result.add(info.assetEntity!);
      }
    }
    return result;
  }

  @protected
  void editImageCallback() {}

  void changeAssetsOnPicker(List<AssetEntity>? assets) {

    if (assets != null) {
      List<AssetEntity> tempAssets = assetsIn;
      assets.sort((a, b) => (a.createDateSecond??0).compareTo(b.createDateSecond??0));

      for (final a in assets) {
        if (!tempAssets.contains(a)) {
          imageInfoList.add(AiImageInfo(assetEntity: a));
        }
      }
    }
    editImageCallback();
  }

  Future<List<String>>  changeAssetsOnPickerNew(List<AssetEntity>? assets) async{
    EasyLoading.show();
    if (assets != null) {
      List<AssetEntity> tempAssets = assets;
      List<String> aa = [];
      for (final a in tempAssets) {
        String? cc= await  startUpload(a,tempAssets.length)  ;
        aa.add(cc!);
      }
      changeUrlListOnGetData(aa);
      EasyLoading.dismiss();
      List<String> back = [];
      for(var item in imageInfoList){
        back.add(item.url!);
      }
      return back;
    }
    return [];
  }
  Future<String?> startUpload(asset,count) async {
    final url = await AiProvider().uploadAsset(
      asset,
      onSendProgress: (count, total) {
      },
    ).then(
          (value) {
        return value;
      },
    );

    return url;
  }

  void changeUrlListOnGetData(List<String>? urlList) {
    imageInfoList.addAll(AiImageInfo.fromList(urlList: urlList));
  }

  void removeImageAtIndex(int index) {
    imageInfoList.removeAt(index);
    editImageCallback();
  }

  List<String> uploadImage() {
    List<String> result = [];
    if (imageInfoList.isEmpty) return result;
    for (var info in imageInfoList) {
      if (info.url != null) {
        result.add(info.url!);
      }
    }
    return result;
  }
}
