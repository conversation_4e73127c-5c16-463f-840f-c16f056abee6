import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/entiy/image_info_entity.dart';
import 'package:inspector/app/modules/ai/entiy/mixin/edit_time_mixin.dart';

import 'ai_detail_checout_record.dart';
import 'ai_detail_defect_checout_info.dart';

class AiCheckOutInfoSingle with ImageAssetsListManager, EditTimeMixin {
  AiCheckOutInfoSingle();

  List<AiCheckOutRecordInfo>? recordsList;
  List<AiCheckOutDefectInfo>? defectsList;
  int? status;

  AiCheckOutInfoSingle.empty(){
    recordsList=[];
    defectsList=[];
    status=0;
  }
  AiCheckOutInfoSingle.fromJson(Map<String, dynamic> json) {
    recordsList= json['recordsList'] != null
        ? (json['recordsList'] as List).map((i) => AiCheckOutRecordInfo.fromJson(i)).toList()
        : [];
    defectsList= json['defectsList'] != null
        ? (json['defectsList'] as List).map((i) => AiCheckOutDefectInfo.fromJson(i)).toList()
        : [];

    status = json['status']??0;
    parseEditDateTime(json);
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};

    map['status'] = status ?? 0;
    // map['recordsList'] = recordsList?.map((e) => e.map((child)=>child.toJson())).toList();
    map['recordsList'] = recordsList?.map((e) => e.toJson()).toList();
    map['defectsList'] = defectsList?.map((e) => e.toJson()).toList();

    final edit = editTimeFormater();
    if (edit != null) {
      map.addAll(edit);
    }
    return map;
  }

  @override
  void editImageCallback() {
    refreshEditTime();
    super.editImageCallback();
  }


}

