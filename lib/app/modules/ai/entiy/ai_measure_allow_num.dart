import 'package:common_utils/common_utils.dart';

class AiCommonInfo {
  AiCommonInfo({
    this.type,
    this.values,
    this.status,
    this.required,
  });

  AiCommonInfo.fromJson(dynamic json) {
    type = json['type'];
    values = json['values'] != null ? json['values'].cast<String>() : [];
    status = json['status'];
    required = json['required'];
  }
  String? type;
  List<String>? values;
  int? status;
  int? required;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    // assert(ObjectUtil.isNotEmpty(type) &&
    //     ObjectUtil.isNotEmpty(values) &&
    //     ObjectUtil.isNotEmpty(status));
    map['type'] = type;
    map['values'] = values;
    map['status'] = status;
    map['required'] = required ?? 0;
    return map;
  }
}
