import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/ai/category/view/ai_category_view.dart';
import 'package:inspector/app/modules/ai/category/empty/ai_empty_view.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/checkout/type/chekout_level_type.dart';
import 'package:inspector/app/modules/ai/checkout_result/ai_checkout_result_controller.dart';
import 'package:inspector/app/modules/ai/entiy/ai_checkout_sample_aql_data.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';
import 'package:inspector/app/modules/ai/extension/table_border_extension.dart';
import 'package:inspector/app/modules/ai/widgets/ai_summary_result.dart';
import 'package:inspector/app/modules/ai/widgets/submit_button.dart';
import 'package:inspector/generated/l10n.dart';

class AiCheckoutResultView extends GetView<AiCheckoutResultController> {
  const AiCheckoutResultView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MColor.backgroundColor,
      appBar: AppBar(
        title: Text(AiCategoryType.checkout.name),
        centerTitle: true,
      ),
      body: DefaultTextStyle(
        style: MFont.medium14.copyWith(color: MColor.black),
        child: KeyboardDismissOnTap(
          child: Column(
            children: [
              AiProductSelectBar(loader: controller),
              Expanded(
                child: Obx(() {
                  return AiEmptyView(
                    baseResult: controller.baseResult,
                    child: ListView(
                      children: [
                        _AiCheckoutJudgeResultView(),
                        SizedBox(
                          height: 10,
                        ),
                        AiSummaryResultView(
                          isSame: controller.result?.isSame,
                          note: controller.result?.remark,
                          noteChange: (value) => controller.result?.remark = value,
                          resultChange: (value) => controller.result?.changeResult(value),
                        )
                      ],
                    ),
                  );
                }),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                      width: Get.width/2 - 20 ,
                    child: AiSubmitButton.save(
                      onPressed: controller.toSave,
                    )
                  )
                  ,
                  Container(
                    width: Get.width/2 - 20 ,
                    child: AiSubmitButton.next(
                      onPressed: controller.toNext,
                    ),
                  )

                ],
              )

            ],
          ),
        ),
      ),
    );
  }
}

class _AiCheckoutJudgeResultView extends GetView<AiCheckoutResultController> {
  const _AiCheckoutJudgeResultView({super.key});

  @override
  Widget build(BuildContext context) {
    final color = MColor.xFFE5E5E5;
    final titleHeight = 57.0;
    final borderAll = TableBorder.all(color: color);
    final rowDecoration = BoxDecoration(color: MColor.xFFFFF3F0);

    final widths = {
      0: FixedColumnWidth(80),
      1: FixedColumnWidth(40),
      2: FixedColumnWidth(40),
      6: FixedColumnWidth(55),
    };
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
      color: MColor.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: Text(
              S.of(context).ai_judgment,
              style: MFont.medium18,
            ),
          ),
          Table(
            columnWidths: {0: FixedColumnWidth(186)},
            border: TableBorderExt.without(color: color, bottom: true),
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            children: [
              TableRow(children: [
                //空合并
                Container(
                  padding: EdgeInsets.only(left: 10),
                  height: 43,
                  decoration: rowDecoration,
                  child: Text(S.of(context).ai_measurement_standard+':ANSl/ASQZ1.4(MIL-STD-105E);'
                      +S.of(context).sampling_plan
                  +':'
                      +S.of(context).single
                  +','
                      +S.of(context).normal
                    ,style: TextStyle(
                    fontSize: 12
                  ),),
                ),
                //等级行列
                Table(
                  border: TableBorderExt.without(color: color, left: true, bottom: true),
                  children: [
                    TableRow(decoration: rowDecoration, children: [
                      for (final type in AiCheckoutDefectLevelType.values)
                        aiResultCell(name: type.name)
                    ]),
                  ],
                ),
              ]),
            ],
          ),
          //值
          Table(
            border: borderAll,
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            columnWidths: {
              0: FixedColumnWidth(67),
              1: FixedColumnWidth(60),
              2: FixedColumnWidth(59),
            },
            children: [
              TableRow(children: [
                aiResultCell(name: S.of(context).ai_sampling_level),
                aiResultCell(name: S.of(context).ai_sample_count),
                aiResultCell(name: 'AQL'),
                aiResultCell(name: controller.table?.aql1 ?? ''),
                aiResultCell(name: controller.table?.aql2 ?? ''),
                aiResultCell(name: controller.table?.aql3 ?? ''),
              ]),
              //动态
              for (AiItemForms form in controller.standardList)
                TableRow(children: [
                  aiResultCell(name: AICheckoutSampleLevelType.getName(form.sampleLevelObs.value)),
                  aiResultCell(name: form.sampleNumObs.value),
                  aiResultCell(name: ' ${S.of(context).ai_maximum_allowable_value}'),
                  for (AiCheckoutSampleQalInfo qal in form.sampleQal?.values ?? [])
                    aiResultCell(name: qal.aqlAcNum?.toString() ?? ''),
                ])
            ],
          ),
          SizedBox(height: 20),
          //标题
          Table(
            border: TableBorderExt.without(color: color, bottom: true),
            columnWidths: {
              0: widths[0]!,
              1: widths[1]!,
              2: widths[2]!,
              4: widths[6]!,
            },
            children: [
              TableRow(decoration: rowDecoration, children: [
                aiResultCell(name: S.of(context).ai_test_item_name, height: titleHeight),
                aiResultCell(name: S.of(context).ai_sampling_level, height: titleHeight),
                aiResultCell(name: S.of(context).ai_sample_count, height: titleHeight),
                Table(
                  children: [
                    TableRow(decoration: rowDecoration, children: [
                      aiResultCell(name: S.of(context).ai_test_result, height: titleHeight / 2)
                    ]),
                    TableRow(decoration: rowDecoration, children: [
                      Table(
                        border: TableBorderExt.only(color: color, top: true),
                        children: [
                          TableRow(children: [
                            aiResultCell(name: S.of(context).ai_critical, height: titleHeight / 2),
                            aiResultCell(name: S.of(context).ai_important, height: titleHeight / 2),
                            aiResultCell(name: S.of(context).ai_minor, height: titleHeight / 2),
                          ])
                        ],
                      )
                    ])
                  ],
                ),
                aiResultCell(name: S.of(context).ai_conclusion, height: titleHeight),
              ]),
            ],
          ),
          //值
          Table(
            border: borderAll,
            columnWidths: widths,
            children: [
              for (int i=0;i<  (controller.inputList??[]).length ;i++)
                // if(controller.inputList?[i].data!=null && controller.inputList?[i].data!.defectCheckOut!=null&&controller.inputList?[i].data!.defectCheckOut!.values!=null
                //     && controller.inputList![i].data!.defectCheckOut!.values!.isNotEmpty
                //     && controller.inputList![i].data!.defectCheckOut!.values!.first.recordsList!.isNotEmpty
                // )

                // if(controller.inputList?[i].data!=null
                // )
                TableRow(children: [
                  aiResultCell(name: controller.inputList![i].name ?? '', pass: controller.isFitStandard(controller.inputList![i])),
                  aiResultCell(
                      name:  controller.getLevelReal(controller.inputList![i].sampleLevel?.values?.firstOrNull??'') ,
                      pass: formIsPass(controller.inputList![i])),
                  aiResultCell(name: controller.inputList![i].sampleNumObs.value, pass: controller.isFitStandard(controller.inputList![i])),
                  if(controller.countList.isNotEmpty)...{
                    aiResultCell(
                        name: controller.countList[i]['aql_1']?? '0', pass: formIsPass(controller.inputList![i])),
                    aiResultCell(
                        name: controller.countList[i]['aql_2'] ?? '0', pass: formIsPass(controller.inputList![i])),
                    aiResultCell(
                        name: controller.countList[i]['aql_3']?? '0',
                        pass: formIsPass(controller.inputList![i])),
                  },
                  aiResultCell2(
                      name: controller.getStatus(controller.inputList![i].status,context) ,
                      last: true,
                      pass: controller.inputList![i].status!)
                  ,
                ]),
            ],
          )
        ],
      ),
    );
  }

  bool formIsPass(AiItemForms form) {
    return form.sampleLevelObs.value == AICheckoutSampleLevelType.custom.value
        ? true
        : controller.isFitStandard(form);
  }
}

Widget aiResultCell(
    {required String name, double? height = 43.0, bool pass = true, bool last = false}) {
  return Container(
    height: height,
    // color: Colors.blue,
    alignment: Alignment.center,
    child: Text(
      name,
      textAlign: TextAlign.center,
      style: TextStyle(
          color: last ? (pass ? MColor.pass : MColor.failed) : (pass ? null : MColor.failed)),
    ),
  );
}

Widget aiResultCell2(
    {required String name, double? height = 43.0, int pass = 1, bool last = false}) {
  return Container(
    height: height,
    // color: Colors.blue,
    alignment: Alignment.center,
    child: Text(
      name,
      textAlign: TextAlign.center,
      style: TextStyle(
          color: (pass==1 ? MColor.pass : pass==2?MColor.skin:MColor.failed ))
    ),
  );
}