import 'dart:convert';

import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_base_entity.dart';
import 'package:inspector/app/modules/ai/ai_net/ai_server.dart';
import 'package:inspector/app/modules/ai/category/controller/ai_category_controller.dart';
import 'package:inspector/app/modules/ai/category/interface/ai_data_feature.dart';
import 'package:inspector/app/modules/ai/category/type/category_type.dart';
import 'package:inspector/app/modules/ai/entiy/ai_summary_detail_data.dart';

import '../../../../generated/l10n.dart';
import '../checkout/type/chekout_level_type.dart';
import '../entiy/ai_detail_checout_single.dart';

class AiCheckoutResultController extends GetxController implements AiDataFeature {
  final AiCategoryType _categoryType = AiCategoryType.checkout;

  final aiDetail = AiSummaryDetailData(
          id: 0, ruleOrderId: 0, orderId: 0, productId: 0, modelId: 0, type: 5, status: 0)
      .obs;

  List<AiItemForms>? get inputList => aiDetail.value.details?.config?.forms ?? [];
  List<AiItemForms> get standardList => aiDetail.value.details?.config?.table?.forms ?? [];
  AiSummaryResult? get result => aiDetail.value.details?.config?.result;
  AiCategoryConfig? get config => aiDetail.value.details?.config;
  AiCategoryConfig? get table => aiDetail.value.details?.config?.table;

  bool isFitStandard(AiItemForms form) => config?.isFitStandard(form) ?? true;
  late var baseResult = AiBaseEntity();

  @override
  void onInit() {
    loadData();
    super.onInit();
  }

  void toNext() {
    uploadData().then(
      (value) {
        Get.offAndToNamed(
          AiCategoryController.to.nextType(fromType: _categoryType.id).location,
          parameters: Get.parameters as Map<String, String>,
        );
      },
    );
  }
  void toSave() {
    uploadData().then(
          (value) {
        Get.back();
      },
    );
  }

  String getStatus(status,context){
    if(status==1){
      return S.of(context).approve;
    }
    if(status==2){
      return S.of(context).ai_default_config_fail;
    }
    if(status==3){
      return S.of(context).ai_wait;
    }
    return '';
  }

  List<Map> countList= [];
  @override
  void loadData() {
    AiProvider()
        .getAiCategoryDetail(
            orderId: AiCategoryController.to.orderId,
            productId: AiCategoryController.to.productId,
            modelId: AiCategoryController.to.modelId,
            type: _categoryType.id,
            orderNum: AiCategoryController.to.orderNum)
        .then((value) {
      baseResult = value;
      if (value.data != null) {
        aiDetail.value = value.data!;
        // if( result?.status==0){
          bool isSucc = true;
          for(AiItemForms item in inputList??[]){
            if(item.status==2 || item.status==3){
              isSucc = false;
            }
            if(item.data==null || item.data!.defectCheckOut==null){
              countList.add({
                'aql_1':'0',
                'aql_2':'0',
                'aql_3':'0',
              });
            }else{
              countList.add(calCount(item.data!.defectCheckOut!.values![0].recordsList!)) ;
            }
          }
          result?.status = isSucc?1:2;
        // }

      }
    });
  }

  getLevelReal(value){
    for(final item in AICheckoutSampleLevelType.values){
      if(value == item.value){
        return item.name;
      }
    }
    if(value=='无' || value==''){
      return '/';
    }
    return value;
  }


  Map calCount( List<AiCheckOutInfoSingle> countItem){
    Map aa = {
      'aql_1':'0',
      'aql_2':'0',
      'aql_3':'0',
    };
    if(countItem==null || countItem.isEmpty){
      return aa;
    }

    int totalNum1=0;
    int totalNum2=0;
    int totalNum3=0;
    for(var parent in countItem){
      for(var item in parent.defectsList!){
        if(item.level=='aql_1'){// 关键
          totalNum1=totalNum1+(item.totalNum==null?0: int.parse(item.totalNum!));
          aa['aql_1'] = totalNum1.toString();
        }else if(item.level=='aql_2'){// 关键
          totalNum2=totalNum2+(item.totalNum==null?0: int.parse(item.totalNum!));
          aa['aql_2'] = totalNum2.toString();
        }else if(item.level=='aql_3'){// 关键
          totalNum3=totalNum3+(item.totalNum==null?0: int.parse(item.totalNum!));
          aa['aql_3'] = totalNum3.toString();
        }
      }
    }
    print(json.encode(aa));
    return aa;
  }

  @override
  Future<AiBaseEntity> uploadData() {
    if (aiDetail.value.details == null) {
      return Future.value(AiBaseEntity(code: 200));
    }
    aiDetail.value.details?.config?.uploadImageIfNeed();
    return AiProvider().uploadConfig(id: aiDetail.value.id, details: aiDetail.value.details!);
  }
}
