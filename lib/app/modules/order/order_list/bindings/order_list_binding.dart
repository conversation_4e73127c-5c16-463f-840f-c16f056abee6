import 'package:get/get.dart';

import 'package:inspector/app/modules/order/order_list/controllers/order_list_controller.dart';
import 'package:inspector/app/tools/tools.dart';

class OrderListBinding extends Bindings {
  final String tag;
  OrderListBinding(this.tag);
  @override
  void dependencies() {
    Get.lazyPut<OrderListController>(() {
      logger.i('create orderlistcontroller');
      return OrderListController();
    }, tag: tag);
  }
}
