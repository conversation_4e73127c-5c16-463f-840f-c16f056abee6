import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_pickers/more_pickers/route/single_picker_route.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/modules/widgets/upload/UploadWidget.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

mixin OrderEditViewMixin {
  Widget addressView(AddressRows? address, {DynamicCallback? callback}) {
    if (address == null) {
      return _emptyAddressView(callback: callback);
    } else {
      return _addressRowsView(address, callback: callback);
    }
  }

  Widget _emptyAddressView({DynamicCallback? callback}) {
    return Container(
        decoration: BoxDecoration(
          color: Get.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6,
          borderRadius: BorderRadius.circular(6),
        ),
        margin: const EdgeInsets.only(top: 14, left: 10, right: 10),
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: SizedBox(
          width: double.infinity,
          height: 60,
          child: Center(
            child: Row(
              children: [
                Expanded(
                    child: GestureDetector(
                        onTap: () {
                          Get.toNamed(Routes.ADDRESS, arguments: true)?.then((value) {
                            if (value == null) {
                              return;
                            }
                            callback?.call(value['addressInfo']);
                          });
                        },
                        child: Text(S.of(Get.context!).pick_address,
                            style: MFont.regular15.apply(color: Get.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666)))),
                GestureDetector(
                  onTap: () {
                    Get.toNamed(Routes.ADDRESS_LIST)?.then((value) {
                      if (value == null) {
                        return;
                      }
                      callback?.call(value['addressInfo']);
                    });
                  },
                  child: Row(
                    children: [
                      Text(
                        S.of(Get.context!).publish_address_book,
                        style: MFont.regular15.apply(color: MColor.skin),
                      ),
                      const SizedBox(width: 4),
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(7.5),
                          border: Border.all(color: MColor.skin),
                        ),
                        width: 15,
                        height: 15,
                        child: const Center(
                          child: Icon(
                            Icons.arrow_forward_ios_sharp,
                            color: MColor.skin,
                            size: 8,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  Widget _addressRowsView(AddressRows address, {DynamicCallback? callback}) {
    var text = '${address.province ?? ''}${address.city ?? ''}${address.area ?? ''}${address.address ?? ''}';
    return Builder(builder: (context) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Get.toNamed(Routes.ADDRESS, arguments: address)?.then((value) {
            if (value == null) {
              return;
            }
            callback?.call(value['addressInfo']);
          });
        },
        child: Container(
          decoration: BoxDecoration(
            color: Get.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6,
            borderRadius: BorderRadius.circular(6),
          ),
          margin: const EdgeInsets.only(top: 14, left: 10, right: 10),
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          width: double.infinity,
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${address.factoryName}',
                      style: MFont.semi_Bold20.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      text,
                      style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '${address.name} ${address.phone}',
                      style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () {
                  Get.toNamed(Routes.ADDRESS_LIST)?.then((value) {
                    if (value == null) {
                      return;
                    }
                    callback?.call(value['addressInfo']);
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(7.5),
                    border: Border.all(color: MColor.skin),
                  ),
                  width: 15,
                  height: 15,
                  child: const Center(
                    child: Icon(
                      Icons.arrow_forward_ios_sharp,
                      color: MColor.skin,
                      size: 8,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      );
    });
  }

  Container orderProductView(TextEditingController productNameEditor, TextEditingController poEditor, TextEditingController remarkEditor,
      List<String> attachments, OnUploaded onUploadCallback, OnRemoved onRemovedCallback) {
    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6,
        borderRadius: BorderRadius.circular(6),
      ),
      margin: const EdgeInsets.only(top: 14, left: 10, right: 10),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Builder(builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  S.of(Get.context!).publish_goods_name,
                  style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.only(top: 5),
                    child: _textField(productNameEditor, TextInputType.text, hint: S.of(Get.context!).publish_name_tips),
                  ),
                ),
              ],
            ),
            Divider(thickness: 1, height: 8, color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6),
            Row(
              children: [
                Text(
                  'P.O',
                  style: MFont.medium16.apply(color: context.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.only(top: 5),
                    child: _textField(poEditor, TextInputType.text, hint: S.of(Get.context!).publish_po_tips),
                  ),
                ),
              ],
            ),
            Divider(thickness: 1, height: 30, color: context.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6),
            Text(
              S.of(Get.context!).publish_file_tips,
              style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            ),
            const SizedBox(height: 10),
            Obx(
              () {
                return UploadWidget.generateUploadWidget(attachments, true, onUploadCallback, onRemovedCallback);
              },
            ),
            const SizedBox(height: 10),
            Text(
              S.of(Get.context!).publish_attention,
              style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            ),
            const SizedBox(height: 10),
            _remarkView(remarkEditor),
          ],
        );
      }),
    );
  }

  TextField _remarkView(TextEditingController tec) {
    return TextField(
      controller: tec,
      style: MFont.regular15.apply(color: Get.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
      keyboardType: TextInputType.multiline,
      minLines: 3,
      maxLines: null,
      decoration: InputDecoration(
        hintText: S.of(Get.context!).publish_attention_tips,
        hintStyle: MFont.regular15.apply(color: Get.isDarkMode ? DarkColor.xFFA2A2A2 : MColor.xFFA2A2A2),
        filled: true,
        isDense: false,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        fillColor: Get.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(color: Get.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(color: Get.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(color: Get.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
        ),
      ),
    );
  }

  Widget _textField(
    TextEditingController? editingController,
    TextInputType type, {
    String? hint,
    TextAlign textAlign = TextAlign.start,
    TextStyle style = MFont.regular16,
    FocusNode? focusNode,
    Function()? onTap,
    Function(String)? onChange,
  }) {
    return Builder(builder: (context) {
      return TextField(
        controller: editingController,
        style: style.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
        textAlign: textAlign,
        onTap: onTap,
        keyboardType: type,
        focusNode: focusNode,
        decoration: InputDecoration(
          filled: true,
          isDense: false,
          hintText: hint,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          hintStyle: style.apply(color: context.isDarkMode ? DarkColor.xFFA2A2A2 : MColor.xFFA2A2A2),
          constraints: const BoxConstraints(maxHeight: 30, minHeight: 20),
          fillColor: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
          border: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7)),
          focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7)),
          enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7)),
        ),
        onChanged: onChange,
      );
    });
  }
}
