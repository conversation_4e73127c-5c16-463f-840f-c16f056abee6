import 'package:common_utils/common_utils.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/modules/auth/mine_provider.dart';
import 'package:inspector/app/modules/order/base/order_edit_controller_mixin.dart';
import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/modules/pulish/publish_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import 'package:intl/intl.dart';

import '../../../../generated/l10n.dart';

class OrderEditController extends GetxController with OrderEditControllerMixin {
  TextEditingController remarkEditor = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController poController = TextEditingController();
  PublishProvider provider = PublishProvider();
  MineProvider mineProvider = MineProvider();

  final dateList = <Map<DateTime, int>>[
    {DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day).add(const Duration(days: 1)): 1}
  ].obs;
  var peopleNum = 1;
  var goodsName = '';
  final docAttachments = RxList<String>([]);
  int? orderId;
  String? orderNo;

  final addressRows = Rxn<AddressRows>();

  @override
  void onInit() {
    super.onInit();
    orderId = Get.arguments['orderId'];
    getOrderDetail();
  }

  void getOrderDetail() {
    var orderProvider = OrderProvider();
    EasyLoading.show();
    orderProvider.takeOrderDetail(orderId!).then((value) async {
      if (value.isSuccess) {
        var detailEntity = value.data;
        var inspTime = detailEntity?.inspectTime;
        var people = 0;
        detailEntity?.timeBeans?.forEach((element) {
          if (element.date == inspTime) {
            people = element.inspectNum ?? 0;
          }
        });
        dateList.value = [
          {DateUtil.getDateTime(inspTime!, isUtc: false)!: people}
        ];
        addressRows.value = detailEntity?.addressInfo;
        orderNo = detailEntity?.orderNo;
        goodsName = detailEntity?.productName ?? '';
        nameController.text = goodsName;

        docAttachments.value = detailEntity?.file ?? [];
        remarkEditor.text = detailEntity?.remark ?? '';
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void fetchRecommend(String email) {
    EasyLoading.show();
    mineProvider.takeRecommend(email).then((value) async {
      if (value.code == 20000) {
        Get.back();
        if (value.message != null && value.message!.isNotEmpty) {
          showCustomDialog(value.message!);
        }
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  void submitAction() {
    if (dateList.isEmpty) {
      showToast(S.of(Get.context!).publish_inspection_time);
      return;
    }
    peopleNum = 0;
    for (var element in dateList) {
      var num = element.values.first;
      peopleNum += num;
    }
    if (peopleNum <= 0) {
      showToast(S.of(Get.context!).publish_inspection_people);
      return;
    }
    // if (addressId == 0 || addressId == null) {
    //   showToast(S.of(Get.context!).publish_factory_tip);
    //   return;
    // }

    List<Map<String, dynamic>> dates = [];
    for (int i = 0; i < dateList.length; i++) {
      final DateFormat formatter = DateFormat('yyyy-MM-dd');
      final date = formatter.format(dateList[i].keys.first);
      Map<String, dynamic> dict = {'date': date};
      dict['inspectNum'] = dateList[i].values.first;
      dates.add(dict);
    }

    EasyLoading.show();
    provider
        .editOrder(
      addressRows.value?.id ?? 0,
      addressRows.value?.userId ?? 0,
      dates,
      docAttachments,
      nameController.text,
      remarkEditor.text,
      orderId,
    )
        .then((value) {
      if (value.isSuccess) {
        Get.back(result: true);
        FocusManager.instance.primaryFocus?.unfocus();
      } else {
        showToast(value.message ?? '');
      }
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  @override
  void onClose() {
    remarkEditor.dispose();
    nameController.dispose();
  }
}
