import 'package:date_format/date_format.dart' as DateFormat;
import 'package:flutter/material.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/modules/order/base/order_edit_view_mixin.dart';
import 'package:inspector/app/modules/order/order_edit/order_edit_controller.dart';
import 'package:inspector/app/routes/app_pages.dart';
import 'package:inspector/app/theme/style.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../generated/l10n.dart';

class OrderEditView extends GetView<OrderEditController> with OrderEditViewMixin {
  const OrderEditView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          S.of(Get.context!).publish_edit_title,
          style: const TextStyle(color: Colors.white),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        actions: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              controller.submitAction();
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              alignment: Alignment.center,
              child: Text(
                S.of(Get.context!).publish_submit,
                style: MFont.medium16.apply(color: Colors.white),
              ),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: const [0, 0.6],
                colors: [MColor.xFFE95332, context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE],
              ),
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: SizedBox(
              height: Get.height,
              child: SafeArea(
                bottom: false,
                child: ListView(
                  children: [
                    Obx(() {
                      AddressRows? address = controller.addressRows.value;
                      return addressView(address, callback: (dynamic addressInfo) {
                        controller.addressRows.value = addressInfo;
                      });
                    }),
                    _topDateView,
                    orderProductView(controller.nameController, controller.poController, controller.remarkEditor, controller.docAttachments, (url) {
                      controller.docAttachments.add(url);
                    }, (position) {
                      controller.docAttachments.removeAt(position);
                      controller.docAttachments.refresh();
                    }),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Container get _topDateView {
    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6,
        borderRadius: BorderRadius.circular(6),
      ),
      margin: const EdgeInsets.only(top: 14, left: 10, right: 10),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Obx(() {
        return Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            if (controller.dateList.isEmpty) ...{
              _dateView(-1),
              Divider(thickness: 1, height: 1, color: Get.isDarkMode ? DarkColor.xFFE6E6E6 : MColor.xFFE6E6E6),
            } else ...{
              for (int i = 0; i < controller.dateList.length; i++) ...{
                _dateView(i),
                const Divider(thickness: 1, height: 1),
              },
            },
          ],
        );
      }),
    );
  }

  Widget _dateView(int index) {
    Map<DateTime, int>? time;
    String text = S.of(Get.context!).publish_inspection_time_selected;
    if (index >= 0) {
      time = controller.dateList[index];
      text = DateFormat.formatDate(time.keys.first, [
        DateFormat.yyyy,
        '-',
        DateFormat.mm,
        ''
            '-',
        DateFormat.dd
      ]);
    }
    TextStyle headerStyle =
        const TextStyle(fontSize: 16, height: 1, fontWeight: FontWeight.w500).apply(color: Get.isDarkMode ? DarkColor.xFF666666 : MColor.xFF666666);
    TextStyle boldStyle = const TextStyle(fontSize: 17, height: 1, fontWeight: FontWeight.w600);
    bool isLast = index == controller.dateList.length - 1;
    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 12),
            Row(
              children: [
                Text(S.of(Get.context!).publish_inspection_time, style: headerStyle),
                const SizedBox(width: 8),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    Get.toNamed(Routes.DATE, arguments: List<Map<DateTime, int>>.from(controller.dateList))?.then((value) {
                      if (value != null) {
                        controller.dateList.value = value;
                        controller.dateList.refresh();
                      }
                    });
                  },
                  child: Text(text, style: boldStyle, textAlign: TextAlign.center),
                ),
              ],
            ),
            const SizedBox(height: 12),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                _pickerSingle(index);
              },
              child: Row(
                children: [
                  Builder(builder: (context) {
                    return Text(
                      S.of(Get.context!).publish_inspection_people,
                      style: headerStyle,
                    );
                  }),
                  const SizedBox(width: 8),
                  Text(
                    '${time?.values.first ?? ''} ',
                    style: boldStyle,
                    textAlign: TextAlign.center,
                  ),
                  Builder(builder: (context) {
                    return Text(
                      S.of(Get.context!).publish_people,
                      style: headerStyle,
                      maxLines: 1,
                      textAlign: TextAlign.center,
                    );
                  }),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],
        ),
      ],
    );
  }

  void _pickerSingle(int index) {
    if (index < 0) {
      showToast(S.of(Get.context!).publish_date_tips);
      return;
    }
    List<int> data = [];
    for (int i = 0; i < 50; i++) {
      data.add(i + 1);
    }
    return Pickers.showSinglePicker(
      Get.context!,
      data: data,
      pickerStyle: PickerStyle(
        commitButton: Padding(padding: const EdgeInsets.only(right: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_ok)),
        cancelButton: Padding(padding: const EdgeInsets.only(left: DefaultStyle.spaceLarge), child: Text(S.of(Get.context!).public_cancel)),
        itemOverlay: Container(
          decoration: BoxDecoration(
            border: Border.symmetric(horizontal: BorderSide(color: Get.isDarkMode ? DarkColor.xFF838383 : MColor.xFF838383)),
          ),
        ),
      ),
      onConfirm: (value, _) {
        var key = controller.dateList[index].keys.first;
        controller.dateList[index][key] = value;
        controller.dateList.refresh();
      },
    );
  }
}
