import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:inspector/app/modules/widgets/upload/UploadWidget.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/order/check/controllers/check_controller.dart';

import '../../../../../generated/l10n.dart';

class CheckView extends GetView<CheckController> {
  const CheckView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(S.of(Get.context!).check_title),
          centerTitle: true,
        ),
        backgroundColor: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7,
        body: Column(
          children: [
            Obx(() {
              var status = controller.status.value;
              if (status <= 0) {
                return Container();
              }
              return Container(
                height: 40,
                width: double.infinity,
                color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
                child: Center(
                  child: Text(
                    status == 1 || status == 5
                        ? S.of(Get.context!).check_checking
                        : status == 2
                            ? S.of(Get.context!).check_check_success
                            : S.of(Get.context!).check_check_failed,
                    style: MFont.semi_Bold15.apply(color: status == 2 ? MColor.xFF25C56F : MColor.skin),
                  ),
                ),
              );
            }),
            Expanded(
              child: ListView(
                children: [
                  Obx(() {
                    return _cardView([
                      Text(
                        S.of(Get.context!).check_picture,
                        style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                      ),
                      const SizedBox(height: 12),
                      UploadWidget.generateUploadWidget(controller.picturesUrls, controller.isInspector ?? false, (url) {
                        controller.picturesUrls.add(url);
                        controller.picturesUrls.refresh();
                      }, (position) {
                        controller.picturesUrls.removeAt(position);
                        controller.picturesUrls.refresh();
                      }, showRemove: controller.status.value != 2),
                    ]);
                  }),
                  Obx(
                    () {
                      return _cardView([
                        Text(
                          S.of(Get.context!).check_draft,
                          style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                        ),
                        const SizedBox(height: 12),
                        UploadWidget.generateUploadWidget(controller.draftUrls, controller.isInspector ?? false, (url) {
                          controller.draftUrls.add(url);
                          controller.draftUrls.refresh();
                        }, (position) {
                          controller.draftUrls.removeAt(position);
                          controller.draftUrls.refresh();
                        }, showRemove: controller.status.value != 2),
                      ]);
                    },
                  ),
                  Obx(() {
                    return _cardView([
                      Text(
                        S.of(Get.context!).check_report,
                        style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                      ),
                      const SizedBox(height: 12),
                      UploadWidget.generateUploadWidget(controller.lianzhengUrls, controller.isInspector ?? false, (url) {
                        controller.lianzhengUrls.add(url);
                        controller.lianzhengUrls.refresh();
                      }, (position) {
                        controller.lianzhengUrls.removeAt(position);
                        controller.lianzhengUrls.refresh();
                      }, showRemove: controller.status.value != 2),
                    ]);
                  }),
                ],
              ),
            ),
            Obx(() {
              var status = controller.status.value;
              if (status == 2 || !(controller.isInspector ?? false)) {
                return Container();
              }
              return _textButton(S.of(Get.context!).check_submit, action: () {
                controller.saveReport();
              });
            }),
            const SizedBox(height: 30),
          ],
        ));
  }

  Widget _cardView(List<Widget> itemViews) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      margin: const EdgeInsets.only(left: 16, right: 16, top: 10),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: itemViews,
      ),
    );
  }

  Widget _textButton(String title, {@required VoidCallback? action}) {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18),
      child: TextButton(
        onPressed: () {
          action!();
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          title,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }
}
