import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/data/report_entity.dart';
import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/tools/tools.dart';

class CheckController extends GetxController {
  OrderProvider provider = OrderProvider();
  final picturesUrls = <String>[].obs;
  final draftUrls = <String>[].obs;
  final lianzhengUrls = <String>[].obs;
  int? orderId;
  bool? isInspector; //是验货员还是下单者
  //0-未上传1-待审核2-通过3-不通过  5上传了
  final status = 0.obs;

  @override
  void onInit() {
    super.onInit();

    orderId = Get.arguments['orderId'];
    isInspector = Get.arguments['isInspector'];
    fetchReportInfo();
  }

  void fetchReportInfo() {
    if (orderId == null) {
      if (!(isInspector ?? false)) {
        Get.back();
      }
      return;
    }
    provider.takeReportInfo(orderId!).then((value) async {
      if (value.isSuccess) {
        var report = value.data ?? ReportEntity();
        status.value = report.status ?? 0;

        List<String> pics = [], drafts = [], lianzheng = [];
        if (isInspector ?? false) {
          report.report?.userReports?.forEach((element) {
            // if (element.userInfo.userId == user)
            pics = element.inspPictures ?? [];
            drafts = element.drafts ?? [];
            lianzheng = element.lianzheng ?? [];
          });
        } else {
          report.report?.formalReports?.forEach((element) {
            // if (element.userInfo.userId == user)
            pics = element.inspPictures ?? [];
            drafts = element.drafts ?? [];
            lianzheng = element.lianzheng ?? [];
          });
        }
        picturesUrls.value = pics;
        draftUrls.value = drafts;
        lianzhengUrls.value = lianzheng;
      } else {
        if (!(isInspector ?? false)) {
          showToast(value.message ?? '');
          Get.back();
        } else {
          logger.e('fetchReportInfo ${value.message}');
        }
      }
    });
  }

  void saveReport() {
    List<Map<String, String>> pictures = [];
    List<Map<String, String>> drafts = [];
    List<Map<String, String>> lianzheng = [];
    for (var url in picturesUrls) {
      pictures.add({'file': url});
    }
    for (var url in draftUrls) {
      drafts.add({'file': url});
    }
    for (var url in lianzhengUrls) {
      lianzheng.add({'file': url});
    }

    // if (drafts.isEmpty || pictures.isEmpty || lianzheng.isEmpty) {
    //   showToast(S.of(Get.context!).check_report);
    //   return;
    // }

    EasyLoading.show();
    provider.takeReportSave(orderId!, pictures, drafts, lianzheng).then((value) async {
      if (value.isSuccess) {
        Future.delayed(const Duration(seconds: 1), () {
          Get.back(result: true);
        });
      }
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  @override
  void onClose() {}
}
