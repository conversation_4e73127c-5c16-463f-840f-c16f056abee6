import 'package:inspector/app/config/api.dart';
import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/data/publish_order_entity.dart';
import 'package:inspector/app/data/report_entity.dart';
import 'package:inspector/app/tools/public_provider.dart';

class OrderProvider {
  //我的验货列表
  Future<BaseModel<PublishOrderEntity>> takeInspections(int? type, int page, int pageSize) {
    var params = {'limit': pageSize, 'page': page};
    if (type != null) {
      params['flag'] = type;
    }
    return PublicProvider.request<PublishOrderEntity>(path: Api.inspectionList, params: params);
  }

  //订单管理列表
  Future<BaseModel<PublishOrderEntity>> takePublish(int? type, int page, int pageSize) {
    var params = {'limit': pageSize, 'page': page};
    if (type != null) {
      params['flag'] = type;
    }
    return PublicProvider.request<PublishOrderEntity>(path: Api.publishList, params: params);
  }

  //订单详情
  Future<BaseModel<OrderDetailEntity>> takeOrderDetail(int id) {
    return PublicProvider.request<OrderDetailEntity>(
        path: '${Api.orderDetail}/$id/desc', isPost: false);
  }

  //开始验货
  Future<BaseModel<dynamic>> takeOrderStart(int id) {
    return PublicProvider.request<dynamic>(path: '${Api.startOrder}/$id', isPost: false);
  }

  //验货员取消订单
  Future<BaseModel<dynamic>> takeOrderCancel(int id, bool ins) {
    if (ins) {
      return PublicProvider.request<dynamic>(path: '${Api.cancelInsOrder}/$id', isDelete: true);
    }
    return PublicProvider.request<dynamic>(path: '${Api.cancelOrder}/$id', isDelete: true);
  }

  //获取报告信息
  Future<BaseModel<ReportEntity>> takeReportInfo(int id) {
    return PublicProvider.request<ReportEntity>(path: '${Api.reportInfo}/$id', isPost: false);
  }

  //提交报告信息
  Future<BaseModel<dynamic>> takeReportSave(int id, List<Map<String, String>> pictures,
      List<Map<String, String>> draft, List<Map<String, String>> lianzheng) {
    return PublicProvider.request<dynamic>(path: Api.reportSave, params: {
      'insp_picture': pictures,
      'lianzheng': lianzheng,
      'draft': draft,
      'orderId': id,
    });
  }

  //提交评价
  Future<BaseModel<dynamic>> takeCommentSave(
      int id, List<String> pictures, String content, int score) {
    return PublicProvider.request<dynamic>(path: Api.orderComment, params: {
      'image': pictures,
      'score': score,
      'content': content,
      'orderId': id,
    });
  }

  //获取群聊信息
  Future<BaseModel<dynamic>> takeImGroup(int id, {int? imUserId}) {
    var params = {'order_id': id, 'to': imUserId};
    return PublicProvider.request<dynamic>(
        path: '${Api.imGroup}/$id', params: params, isPost: false);
  }

  Future<BaseModel<dynamic>> beginInspect(int id) {
    var params = {'orderId': id, 'status': 4};
    return PublicProvider.request(path: Api.beginInspect, params: params);
  }

  Future<BaseModel<dynamic>> confirmOrRefuseOrder(int id, int status) {
    var params = {'orderId': id, 'status': status};
    return PublicProvider.request(path: Api.orderConfirm, params: params);
  }

  ///添加产品
  Future<BaseModel<dynamic>> addProduct(
      {required int orderId,
      int? categoryId,
      String? poNum,
      String? productName,
      String? productNameEn}) {
    final params = {
      'order_id': orderId,
      'category_id': categoryId,
      'po_num': poNum,
      'product_name': productName,
      'product_name_en': productNameEn
    }..removeWhere((key, value) => value == null);
    return PublicProvider.request(path: Api.addProduct, params: params);
  }

  ///修改产品
  Future<BaseModel<dynamic>> editProduct(
      {required int productId,
      int? categoryId,
      String? poNum,
      String? productName,
      String? productNameEn}) {
    final params = {
      'id': productId,
      'category_id': categoryId,
      'po_num': poNum,
      'product_name': productName,
      'product_name_en': productNameEn
    }..removeWhere((key, value) => value == null);
    return PublicProvider.request(path: Api.editProduct, params: params);
  }

  ///删除产品
  Future<BaseModel<dynamic>> deleteProduct(
      {required int productId,
      int? categoryId,
      String? poNum,
      String? productName,
      String? productNameEn}) {
    final params = {'id': productId};
    return PublicProvider.request(path: Api.deleteProduct, params: params);
  }

  ///添加型号
  Future<BaseModel<dynamic>> addModel(
      {required int orderId,
      required int productId,
      required String mark,
      required num amount,
      required String unit,
      required String picUrl,
      String? boxes}) {
    final params = {
      'order_id': orderId,
      'orders_product_id': productId,
      'mark': mark,
      'amount': amount,
      'unit': unit,
      'boxes': boxes,
      'pic': picUrl,
    }..removeWhere((key, value) => value == null);
    return PublicProvider.request(path: Api.addModel, params: params);
  }

  ///修改型号
  Future<BaseModel<dynamic>> editModel(
      {required int modelId,
      required int productId,
      required String unit,
      required String picUrl,
      required String mark,
      required num amount,
      String? boxes}) {
    final params = {
      'id': modelId,
      'mark': mark,
      'amount': amount,
      'unit': unit,
      'boxes': boxes,
      'pic': picUrl,
    }..removeWhere((key, value) => value == null);
    return PublicProvider.request(path: Api.editModel, params: params);
  }

  ///删除型号
  Future<BaseModel<dynamic>> deleteModel({required int modelId}) {
    final params = {'id': modelId};
    return PublicProvider.request(path: Api.deleteModel, params: params);
  }
}
