import 'dart:io';

import 'package:flutter/material.dart';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/order/review/controllers/review_controller.dart';

import '../../../../../generated/l10n.dart';

class ReviewView extends GetView<ReviewController> {
  const ReviewView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).review_title),
        centerTitle: true,
      ),
      backgroundColor: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE,
      body: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                _scoreView,
                _textViewField,
                _cardView([
                  Text(
                    S.of(Get.context!).review_picture,
                    style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
                  ),
                  const SizedBox(height: 12),
                  Obx(() {
                    var paths = controller.picturesPaths;
                    var urls = controller.picturesUrls;

                    return Wrap(
                      runSpacing: 12,
                      spacing: 12,
                      children: [
                        for (int i = 0; i < urls.length; i++) ...{
                          _imageView(i, paths[i], urls[i]),
                        },
                      ],
                    );
                  }),
                ]),
              ],
            ),
          ),
          _textButton(S.of(Get.context!).review_submit, action: () {
            controller.saveComment();
          }),
        ],
      ),
    );
  }

  Container get _scoreView {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Builder(builder: (context) {
            return Text(
              S.of(Get.context!).review_score,
              style: MFont.medium15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
            );
          }),
          const SizedBox(height: 10),
          Row(
            children: [
              _checkView(0),
              const SizedBox(width: 5),
              _checkView(1),
              const SizedBox(width: 5),
              _checkView(2),
              const SizedBox(width: 5),
              _checkView(3),
              const SizedBox(width: 5),
              _checkView(4),
            ],
          ),
        ],
      ),
    );
  }

  Widget _checkView(int index) {
    return Obx(() {
      var selectedIndex = controller.scoreIndex.value;
      var text = S.of(Get.context!).review_score1;
      if (index == 1) {
        text = S.of(Get.context!).review_score2;
      } else if (index == 2) {
        text = S.of(Get.context!).review_score3;
      } else if (index == 3) {
        text = S.of(Get.context!).review_score4;
      } else if (index == 4) {
        text = S.of(Get.context!).review_score5;
      }
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          controller.scoreIndex.value = index;
        },
        child: Row(
          children: [
            Icon(
              selectedIndex == index ? Icons.radio_button_checked : Icons.radio_button_off,
              color: MColor.skin,
              size: 20,
            ),
            // SizedBox(width: 5),
            Builder(builder: (context) {
              return Text(
                text,
                style: MFont.regular13.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
              );
            }),
          ],
        ),
      );
    });
  }

  Widget _cardView(List<Widget> itemViews) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      margin: const EdgeInsets.only(left: 16, right: 16, top: 10),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: itemViews,
      ),
    );
  }

  Widget _imageView(int index, String path, String url) {
    return Stack(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            controller.uploadImage(false, index);
          },
          child: SizedBox(
            width: (Get.width - 72) / 2,
            height: 100,
            child: path.isEmpty
                ? Builder(builder: (context) {
                    return DottedBorder(
                      borderType: BorderType.RRect,
                      color: context.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC,
                      radius: const Radius.circular(6),
                      child: Center(
                        child: Icon(
                          Icons.add,
                          size: 48,
                          color: context.isDarkMode ? DarkColor.xFFABABAC : MColor.xFFABABAC,
                        ),
                      ),
                    );
                  })
                : url.isURL
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: Image.file(
                          File(path),
                          fit: BoxFit.cover,
                        ),
                      )
                    : DottedBorder(
                        borderType: BorderType.RRect,
                        color: Get.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC,
                        radius: const Radius.circular(6),
                        child: const Center(
                          child: SpinKitCircle(
                            color: MColor.skin,
                            size: 40.0,
                          ),
                        ),
                      ),
          ),
        ),
        Visibility(
          visible: url.isEmpty ? false : true,
          child: SizedBox(
            width: (Get.width - 72) / 2,
            child: Align(
              alignment: Alignment.topRight,
              child: GestureDetector(
                onTap: () {
                  controller.deleteImage(index);
                },
                child: Builder(builder: (context) {
                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    width: 30,
                    height: 30,
                    child: Icon(
                      Icons.close,
                      size: 20,
                      color: context.isDarkMode ? DarkColor.xFFCCCCCC : MColor.xFFCCCCCC,
                    ),
                  );
                }),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Container get _textViewField {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15),
      child: Builder(builder: (context) {
        return TextField(
          controller: controller.editingController,
          style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF3D3D3D : MColor.xFF3D3D3D),
          keyboardType: TextInputType.multiline,
          minLines: 5,
          maxLines: null,
          decoration: InputDecoration(
            hintText: S.of(Get.context!).review_tips,
            hintMaxLines: 2,
            hintStyle: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFFA2A2A2 : MColor.xFFA2A2A2),
            filled: true,
            isDense: false,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFF4F5F7 : MColor.xFFF4F5F7),
            ),
          ),
        );
      }),
    );
  }

  Widget _textButton(String title, {@required VoidCallback? action}) {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18, bottom: 30),
      child: TextButton(
        onPressed: () {
          action!();
        },
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(MColor.skin),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          title,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }
}
