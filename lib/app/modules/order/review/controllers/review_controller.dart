import 'package:flutter/cupertino.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/modules/order/order_provider.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../../../generated/l10n.dart';
import '../../../../tools/privacy_helper.dart';

class ReviewController extends GetxController {
  OrderProvider provider = OrderProvider();
  int? orderId;
  final scoreIndex = 0.obs;
  final picturesUrls = <String>[''].obs;
  final picturesPaths = <String>[''].obs;
  TextEditingController editingController = TextEditingController();

  @override
  void onInit() {
    super.onInit();

    orderId = Get.arguments;
  }

  void deleteImage(int index) {
    picturesPaths.removeAt(index);
    picturesUrls.removeAt(index);
  }

  Future<void> uploadImage(bool report, int index) async {
    if (!hasShowCameraTips()) {
      await showCameraTips();
    }
    var value = await FilesPicker.openImage(false);
    if (value.isEmpty) {
      return;
    }

    if (index < picturesPaths.length) {
      picturesPaths[index] = value[0];
    } else {
      picturesPaths.add(value[0]);
    }
    if (index < picturesUrls.length) {
      picturesUrls[index] = '';
    } else {
      picturesUrls.add('');
    }

    var url = await PublicProvider.uploadImages(value[0], UploadType.plain);
    if (url == null || url.isEmpty) {
      showToast(S.of(Get.context!).profile_info_failed);
      picturesPaths[index] = '';
      return;
    }
    picturesUrls[index] = url;
    if (picturesUrls.last.isNotEmpty) {
      picturesPaths.add('');
      picturesUrls.add('');
    }
  }

  void saveComment() {
    EasyLoading.show();
    provider.takeCommentSave(orderId!, picturesUrls, editingController.text, scoreIndex.value + 1).then((value) async {
      if (value.isSuccess) {
        Future.delayed(const Duration(seconds: 1), () {
          Get.back(result: true);
        });
      }
      showToast(value.message ?? '');
    }).whenComplete(() {
      EasyLoading.dismiss();
    });
  }

  @override
  void onClose() {
    editingController.dispose();
  }
}
