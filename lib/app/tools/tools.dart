// ignore_for_file: constant_identifier_names

import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:crypto/crypto.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_pickers/image_pickers.dart';
import 'package:inspector/app/tools/extensions.dart';
import 'package:inspector/generated/l10n.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:map_launcher/map_launcher.dart';

import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/data/china_city.dart';
import 'package:inspector/app/routes/app_navigator.dart';
import 'package:inspector/app/shared/widgets/download.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/translation_service.dart';

import '../modules/store/setting_store.dart';

typedef DynamicCallback = dynamic Function(dynamic data);

void showToast(String message, {bool short = true, ToastGravity gravity = ToastGravity.CENTER}) {
  Fluttertoast.showToast(
    msg: message,
    toastLength: short ? Toast.LENGTH_SHORT : Toast.LENGTH_LONG,
    gravity: gravity,
    timeInSecForIosWeb: 2,
    textColor: Colors.white,
    backgroundColor: MColor.xFFE95332,
  );
}

var logger = Logger(
  printer: SimplePrinter(printTime: true),
);

var loggerNoStack = Logger(
  printer: PrettyPrinter(methodCount: 0),
);

var loadingWidget = const SpinKitCircle(
  color: Colors.white,
  size: 20.0,
);

// 节流函数
const deFaultDurationTime = 1000;
Timer? timer;

const String deFaultThrottleId = 'DeFaultThrottleId';
Map<String, int> startTimeMap = {deFaultThrottleId: 0};
void throttle(Function doSomething, {String throttleId = deFaultThrottleId, durationTime = deFaultDurationTime}) {
  int currentTime = DateTime.now().millisecondsSinceEpoch;
  if (currentTime - (startTimeMap[throttleId] ?? 0) > durationTime) {
    doSomething.call();
    startTimeMap[throttleId] = DateTime.now().millisecondsSinceEpoch;
  }
}

void mzdebounce(Function doSomething, {durationTime = deFaultDurationTime}) {
  timer?.cancel();
  timer = Timer(Duration(milliseconds: durationTime), () {
    doSomething.call();
    timer = null;
  });
}

String formatRecentTime(String dateTime) {
  DateTime? dt = DateTime.tryParse(dateTime);
  if (dt != null) {
    var now = DateTime.now();
    int dtSeconds = (now.millisecondsSinceEpoch - dt.millisecondsSinceEpoch) ~/ 1000;
    if (dtSeconds < 60) {
      //不到1分钟，显示xx秒之前
      return '$dtSeconds ${S.of(Get.context!).public_seconds_ago}';
    }
    int dtMinutes = dtSeconds ~/ 60;
    if (dtMinutes < 60) {
      return '$dtMinutes ${S.of(Get.context!).public_minutes_ago}';
    }
    int dtHours = dtMinutes ~/ 24;
    if (dtHours < 24) {
      return '$dtHours ${S.of(Get.context!).public_hours_ago}';
    }

    if (now.day == dt.day + 1 && now.year == dt.year && now.month == dt.month) return S.of(Get.context!).yesterday;

    if (now.year == dt.year) return '${_z(dt.month)}-${_z(dt.day)}';

    return '${dt.year.toString().substring(2)}-${_z(dt.month)}-${_z(dt.day)}';
  }

  return dateTime;
}

String _z(int i) => i < 10 ? '0$i' : '$i';

class PrecisionLimitFormatter extends TextInputFormatter {
  final int _scale;

  PrecisionLimitFormatter(this._scale);

  RegExp exp = RegExp('[0-9.]');
  static const String POINTER = '.';
  static const String DOUBLE_ZERO = '00';

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.startsWith(POINTER) && newValue.text.length == 1) {
      //第一个不能输入小数点
      return oldValue;
    }

    ///输入完全删除
    if (newValue.text.isEmpty) {
      return const TextEditingValue();
    }

    ///只允许输入小数
    if (!exp.hasMatch(newValue.text)) {
      return oldValue;
    }

    ///包含小数点的情况
    if (newValue.text.contains(POINTER)) {
      ///包含多个小数
      if (newValue.text.indexOf(POINTER) != newValue.text.lastIndexOf(POINTER)) {
        return oldValue;
      }
      String input = newValue.text;
      int index = input.indexOf(POINTER);

      ///小数点后位数
      int lengthAfterPointer = input.substring(index, input.length).length - 1;

      ///小数位大于精度
      if (lengthAfterPointer > _scale) {
        return oldValue;
      }
    } else if (newValue.text.startsWith(POINTER) || newValue.text.startsWith(DOUBLE_ZERO)) {
      ///不包含小数点,不能以“00”开头
      return oldValue;
    }
    return newValue;
  }
}

Widget _textField(TextEditingController controller, String placeHolder, TextInputType? type) {
  return Builder(builder: (context) {
    return TextField(
      controller: controller,
      style: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF565656 : MColor.xFF565656),
      textAlign: TextAlign.center,
      keyboardType: type,
      decoration: InputDecoration(
        hintText: placeHolder,
        hintStyle: MFont.regular15.apply(color: context.isDarkMode ? DarkColor.xFF9A9B9C : MColor.xFF9A9B9C),
        filled: true,
        isDense: false,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        constraints: const BoxConstraints(maxHeight: 40, minHeight: 35),
        fillColor: Colors.white,
        border: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80)),
        focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80)),
        enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: context.isDarkMode ? DarkColor.xFFA4A5A9_80 : MColor.xFFA4A5A9_80)),
      ),
    );
  });
}

Future<void> showCustomDialog(String text,
    {VoidCallback? onConfirm,
    TextEditingController? textController,
    TextInputType? textInputType,
    String? textPlaceHolder,
    bool cancel = false,
    bool dismissWhenConfirm = true,
    String? okTitle,
    VoidCallback? onCancel}) async {
  if (Get.isDialogOpen ?? false) {
    return;
  }
  return Get.generalDialog(
      barrierDismissible: false,
      pageBuilder: (ctx, a, s) {
        return Builder(builder: (context) {
          return WillPopScope(
              onWillPop: () async => false,
              child: CupertinoAlertDialog(
                content: Column(
                  children: [
                    Text(
                      text,
                      style: MFont.semi_Bold17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                    ),
                    if (textController != null) ...{
                      const SizedBox(height: 20),
                      _textField(textController, textPlaceHolder ?? '', textInputType),
                    },
                  ],
                ),
                actions: <Widget>[
                  if (cancel) ...{
                    TextButton(
                      onPressed: () {
                        Get.back();
                        if (onCancel != null) {
                          onCancel();
                        }
                      },
                      child: Text(
                        S.of(Get.context!).public_cancel,
                        style: MFont.regular17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                      ),
                    ),
                  },
                  TextButton(
                    onPressed: () {
                      if (dismissWhenConfirm) {
                        Get.back();
                      }
                      if (onConfirm != null) {
                        onConfirm();
                      }
                    },
                    child: Text(
                      okTitle ?? S.of(Get.context!).public_ok,
                      style: MFont.semi_Bold17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                    ),
                  ),
                ],
              ));
        });
      });
}

extension Ratio on double {
  double get pixRatio {
    return this * (Get.width / 375.0);
  }
}

class FilesPicker {
  static Future<String?> openCamera() async {
    try {
      XFile? file = await ImagePicker().pickImage(
        source: ImageSource.camera,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 100,
      );
      if (file != null) {
        var name = file.path;
        if (name.contains('.png') || name.contains('.jpg') || name.contains('.jpeg')) {
          return name;
        } else {
          showToast(S.of(Get.context!).image_format);
        }
      }
    } catch (error) {
      logger.e('open camera ${error.toString()}');
    }
    return null;
  }

  static Future<List<String>> openImage(bool isCamera, {enableCrop = true, int cropHeight = 1, int cropWidth = 1, int selectCount = 1}) async {
    try {
      var files = await ImagePickers.pickerPaths(
        uiConfig: UIConfig(uiThemeColor: Get.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
        selectCount: selectCount,
        showCamera: true,
        showGif: false,
        cropConfig: CropConfig(enableCrop: enableCrop, height: cropHeight, width: cropWidth),
      );

      // final file = await ImagePicker().pickImage(
      //   source: isCamera ? ImageSource.camera : ImageSource.gallery,
      //   maxWidth: 800,
      //   maxHeight: 800,
      //   imageQuality: 100,
      // );
      if (files.isNotEmpty) {
        List<String> selected = [];
        for (var element in files) {
          var name = element.path ?? '';
          if (name.contains('.png') || name.contains('.jpg') || name.contains('.jpeg')) {
            selected.add(name);
          }
        }
        return selected;
      }
      return List.empty();
    } catch (error) {
      logger.e('open image ${error.toString()}');
      return List.empty();
    }
  }

  static void showSheet() {
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
        ),
        height: 100,
        child: ListView.builder(
          itemCount: 3,
          itemBuilder: (ctx, index) {
            if (index == 2) {
              return GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  Get.back();
                },
                child: SizedBox(
                  height: 50,
                  child: Center(
                    child: Builder(builder: (context) {
                      return Text(
                        S.of(Get.context!).order_cancel,
                        style: MFont.semi_Bold17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                      );
                    }),
                  ),
                ),
              );
            }

            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                Get.back();
                // if (index == 0) {
                //   ImagePickers.openCamera().then((value) {});
                // } else {
                //   ImagePickers.pickerPaths().then((value) {});
                // }
              },
              child: Builder(builder: (context) {
                return Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE, width: 1)),
                  ),
                  child: Center(
                    child: Builder(builder: (context) {
                      return Text(
                        index == 0 ? S.of(Get.context!).camera : S.of(Get.context!).photo_album,
                        style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                      );
                    }),
                  ),
                );
              }),
            );
          },
        ),
      ),
    );
  }

  static Future<List<String>> openFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['png', 'jpg', 'gif', 'jpeg', 'pdf', 'docx', 'txt', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'zip', 'rar'],
        allowMultiple: true);

    if (result != null) {
      return result.files.where((e) => e.path != null).map((e) => e.path!).toList();
      // return result.files.path;
    } else {
      logger.e('open file error');
      return List.empty();
    }
  }
}

/// 防止文字自动换行
extension FixAutoLines on String {
  String fixAutoLines() {
    return Characters(this).join('\u{200B}');
  }

  String get mdx5 {
    Uint8List content = const Utf8Encoder().convert(this);
    Digest digest = md5.convert(content);

    return digest.toString();
  }
}

class Location {
  static Location share = Location();
  Map<String, Object>? locationResult;

  Position? position;

  // Future<void> requestLocation() async {
  //   try {
  //     bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
  //     if (!serviceEnabled) {
  //       showToast(S.of(Get.context!).turn_on_location_service);
  //       return;
  //     }

  //     LocationPermission permission = await Geolocator.checkPermission();
  //     if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
  //       permission = await Geolocator.requestPermission();
  //       if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
  //         showToast(S.of(Get.context!).enable_location_service);
  //         return;
  //       }
  //     }
  //     position = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.best, timeLimit: const Duration(seconds: 30));
  //     unawaited(GlobalConst.sharedPreferences?.setDouble(Constant.lat, position!.latitude));
  //     unawaited(GlobalConst.sharedPreferences?.setDouble(Constant.lon, position!.longitude));
  //     unawaited(PublicProvider.userGps(position!.latitude, position!.longitude));
  //   } catch (ex) {
  //     showToast(S.of(Get.context!).failed_location_service);
  //   }
  // }

  Future<void> toNavigation(double? lat, double? lon, String? address) async {
    if (lat == null || lon == null || address == null) {
      return;
    }
    if (lat <= 0 || lon <= 0) {
      return;
    }

    var availableMaps = await MapLauncher.installedMaps;
    if (availableMaps.isEmpty) {
      showToast(S.of(Get.context!).no_install_map);
      return;
    }
    await Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
        ),
        height: 60 + (62.0 * availableMaps.length > 200 ? 260 : (62.0 * availableMaps.length)),
        child: ListView.builder(
          itemCount: availableMaps.length + 1,
          itemBuilder: (ctx, index) {
            if (index == availableMaps.length) {
              return GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  Get.back();
                },
                child: SizedBox(
                  height: 50,
                  child: Center(
                    child: Builder(builder: (context) {
                      return Text(
                        S.of(Get.context!).order_cancel,
                        style: MFont.semi_Bold17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                      );
                    }),
                  ),
                ),
              );
            }
            var map = availableMaps[index];
            var name = '';
            if (map.mapType == MapType.apple) {
              name = S.of(Get.context!).apple_map;
            } else if (map.mapType == MapType.amap) {
              name = S.of(Get.context!).amap;
            } else if (map.mapType == MapType.baidu) {
              name = S.of(Get.context!).baidu_map;
            } else if (map.mapType == MapType.google) {
              name = S.of(Get.context!).google_map;
            } else if (map.mapType == MapType.googleGo) {
              name = S.of(Get.context!).google_map;
            } else if (map.mapType == MapType.tencent) {
              name = S.of(Get.context!).tencent_map;
            }

            return GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                Get.back();

                map.showDirections(destination: Coords(lat, lon), destinationTitle: address);
              },
              child: Builder(builder: (context) {
                return Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: context.isDarkMode ? DarkColor.xFFEEEEEE : MColor.xFFEEEEEE, width: 1)),
                  ),
                  child: Center(
                    child: Builder(builder: (context) {
                      return Text(
                        name,
                        style: MFont.semi_Bold15.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                      );
                    }),
                  ),
                );
              }),
            );
          },
        ),
      ),
    );
  }
}

class Helper {
  Helper._();

  static String parseOrderType(int? type) {
    switch (type) {
      case 1:
        return S.of(Get.context!).publish_sampling;
      case 2:
        return S.of(Get.context!).publish_all;
      case 3:
        return S.of(Get.context!).publish_online;
      case 4:
        return S.of(Get.context!).publish_factory;
      case 5:
        return S.of(Get.context!).publish_watch;
      case 6:
        return S.of(Get.context!).publish_watch_inspection;
      default:
        return '-';
    }
  }

  static String parseSampleType(int type) {
    switch (type) {
      case 1:
        return '未提供样品';
      case 2:
        return '送往或已在工厂';
      case 3:
        return '送往环球验货';
      default:
        return '-';
    }
  }

  static String parseApplyStatus(int status) {
    switch (status) {
      case 0:
        return S.of(Get.context!).grabbing;
      case 1:
        return S.of(Get.context!).order_applying;
      case 2:
        return S.of(Get.context!).order_apply_dispatched;
      default:
        return S.of(Get.context!).order_apply_expired;
    }
  }

  static String parseChinaArea(String address) {
    if (SettingStore.to.getCurrentLocale().languageCode == 'zh') return address;
    List<ChinaArea> chinaAreas = GlobalConst.chinaAreas;
    for (var province in chinaAreas) {
      if (address.startsWith(province.areaName)) {
        for (var city in province.cities) {
          if (province.areaName + city.areaName == address) return 'China ${province.pinyin} ${city.pinyin}';
        }
        return 'China ${province.pinyin}';
      }
    }
    return address;
  }

  static String parseBillType(String type) {
    if (type.startsWith('订单支付')) {
      return type.replaceAll('订单支付', S.of(Get.context!).order_payment);
    } else if (type.startsWith('订单退回')) {
      return type.replaceAll('订单退回', S.of(Get.context!).order_refund);
    } else if (type.startsWith('支出-提现')) {
      return type.replaceAll('支出-提现', S.of(Get.context!).expend_withdrawal);
    } else if (type.startsWith('收入-订单退款')) {
      return type.replaceAll('收入-订单退款', S.of(Get.context!).incoming_refund);
    } else if (type.startsWith('收入-充值')) {
      return type.replaceAll('收入-充值', S.of(Get.context!).incoming_recharge);
    } else {
      return type;
    }
  }

  static String parseMonth(int month) {
    if (SettingStore.to.getCurrentLocale().languageCode == 'zh') return month.toString();
    switch (month) {
      case 1:
        return 'Jan';
      case 2:
        return 'Feb';
      case 3:
        return 'Mar';
      case 4:
        return 'Apr';
      case 5:
        return 'May';
      case 6:
        return 'Jun';
      case 7:
        return 'Jul';
      case 8:
        return 'Aug';
      case 9:
        return 'Sep';
      case 10:
        return 'Oct';
      case 11:
        return 'Nov';
      case 12:
        return 'Dec';
      default:
        return month.toString();
    }
  }

  static Future<void> downloadFile(String url) async {
    var result = await showGeneralDialog(
        context: Get.context!,
        barrierLabel: 'Inspector',
        barrierDismissible: false,
        pageBuilder: (context, animation, secondaryAnimation) {
          return DownloadPage(url);
        });
    if (result == null || result == '') return;

    await AppNavigator.downloadFiles();
  }

  static String parseMsgTime(String msgTime) {
    var now = DateTime.now();
    var time = DateTime.parse(msgTime);
    if (time.difference(now).inDays == 0) {
      return DateFormat('HH:mm a').format(time);
    } else if (time.difference(now).inDays == -1) {
      return '昨天 ${DateFormat('HH:mm a').format(time)}';
    } else if (time.difference(now).inDays >= -7 && time.difference(now).inDays < -1) {
      return DateFormat('EEEE').format(time);
    } else {
      return DateFormat('EEE, M/d/y').format(time);
    }
  }
}

class CustomFloatingActionButtonLocation extends FloatingActionButtonLocation {
  FloatingActionButtonLocation location;
  double offsetX; // X方向的偏移量
  double offsetY; // Y方向的偏移量
  CustomFloatingActionButtonLocation(this.location, this.offsetX, this.offsetY);

  @override
  Offset getOffset(ScaffoldPrelayoutGeometry scaffoldGeometry) {
    Offset offset = location.getOffset(scaffoldGeometry);
    return Offset(offset.dx + offsetX, offset.dy + offsetY);
  }
}
