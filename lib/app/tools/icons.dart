/// Flutter icons MyFlutterApp
/// Copyright (C) 2024 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  MyFlutterApp
///      fonts:
///       - asset: fonts/MyFlutterApp.ttf
///
///
///
import 'package:flutter/widgets.dart';

class AppIcons {
  AppIcons._();

  static const _kFontFam = 'AppIcons';
  static const String? _kFontPkg = null;

  static const IconData google = IconData(0xeb8d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData apple = IconData(0xea08, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData location = IconData(0xe622, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData edit = IconData(0xe623, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData shopping_cart = IconData(0xe70b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData home = IconData(0xe620, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData menu_manage = IconData(0xe628, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData message = IconData(0xe61f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData mine = IconData(0xe61c, fontFamily: _kFontFam, fontPackage: _kFontPkg);

  static Icon icon(
    codePoint, {
    double? size,
    double? fill,
    double? weight,
    double? grade,
    double? opticalSize,
    Color? color,
    List<Shadow>? shadows,
    String? semanticLabel,
    TextDirection? textDirection,
  }) {
    return Icon(
      IconData(codePoint, fontFamily: _kFontFam, fontPackage: _kFontPkg),
      size: size,
      fill: fill,
      weight: weight,
      grade: grade,
      opticalSize: opticalSize,
      color: color,
      shadows: shadows,
      semanticLabel: semanticLabel,
      textDirection: textDirection,
    );
  }
}
