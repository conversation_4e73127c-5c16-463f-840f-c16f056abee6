import 'dart:convert';
import 'dart:developer';

import 'package:flutter/foundation.dart';

import 'package:dio/dio.dart';

import 'package:inspector/app/config/api.dart';
import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/tools/device.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/app/tools/translation_service.dart';
import 'package:logger/logger.dart';

import '../modules/auth/account_service.dart';
import '../modules/store/setting_store.dart';
import 'storage_util.dart';

import '../modules/auth/account_service.dart';
import '../modules/store/setting_store.dart';
import 'storage_util.dart';

class Server {
  static bool env = false;
  static String get domain {
    if (env) {
      return 'https://app.globalinsp.com/';
    } else {
      return 'https://ceshi.f-qc.com/';
    }
  }

  static String get web {
    if (env) {
      return 'https://app.globalinsp.com/';
    } else {
      return 'https://ceshi.f-qc.com/';
    }
  }

  static String get socket {
    if (env) {
      return 'wss://chat.globalinsp.com';
    } else {
      return 'wss://ceshi.f-qc.com:2347';
    }
  }
}

class ContentType {
  static String formData = 'multipart/form-data';
  static String json = 'application/json';
  static String formValue = 'application/x-www-form-urlencoded';
  static String text = 'application/text';
}

class AppCommonHeaderInterceptor extends Interceptor {
  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    Map<String, dynamic> headers = {};
    headers['versionName'] = GlobalConst.versionName;
    headers['versionCode'] = GlobalConst.versionCode;
    headers['osType'] = DeviceUtils.osType;
    headers['osVersion'] = DeviceUtils.sysVersion;
    headers['deviceType'] = DeviceUtils.deviceName;
    headers['deviceId'] = DeviceUtils.deviceId;
    headers['timestamp'] = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    headers['timeZone'] = DateTime.now().timeZoneOffset.inSeconds;
    headers['token'] = AccountService.instance.getToken();

    headers['language'] = SettingStore.to.getCurrentLocale().toString();
    options.headers.addAll(headers);
    super.onRequest(options, handler);
  }
}

class PublicProvider {
  factory PublicProvider() => _getInstance() ?? PublicProvider();

  static PublicProvider? get instance => _getInstance();

  // 静态变量_instance，存储唯一对象
  static PublicProvider? _instance;
  final Dio _dio = Dio();

  // 获取对象
  static PublicProvider? _getInstance() {
    _instance ??= PublicProvider._internal();

    return _instance;
  }

  PublicProvider._internal() {
    _dio.interceptors.add(AppCommonHeaderInterceptor());
    addLoggerForDio(_dio);
  }

  static void _config() {
    instance!._dio.options.baseUrl = Server.domain;
    instance!._dio.options.method = 'POST';
    instance!._dio.options.contentType = ContentType.json;
    instance!._dio.options.connectTimeout = const Duration(seconds: 30);
    instance!._dio.options.sendTimeout = const Duration(seconds: 30);
    instance!._dio.options.receiveTimeout = const Duration(seconds: 30);
    instance!._dio.options.responseType = ResponseType.json;
  }

  //mydev
  static Future<BaseModel<T>> request<T>({
    required String path,
    dynamic params,
    String? type,
    //mydev
    bool? chat,
    bool isAll = false,
    bool isPost = true,
    bool isDelete = false,
  }) async {
    _config();

    var url = !isAll ? instance!._dio.options.baseUrl + path : path;
    //mydev
    // if (chat == true) {
    //   url = 'http://wchat.inspector.ltd/api/$path';
    // }

    var response = await instance?._dio.fetch(
      RequestOptions(
          path: url,
          method: isDelete
              ? 'DELETE'
              : isPost
                  ? 'POST'
                  : 'GET',
          data: params,
          contentType: type),
    );

    return BaseModel.fromJson(response);
  }

  static Future<dynamic> oriRequest({
    required String path,
    dynamic params,
    String? type,
    bool isPost = true,
  }) async {
    _config();
    var response = await instance?._dio.fetch(RequestOptions(path: path, method: isPost ? 'POST' : 'GET', data: params, contentType: type));
    return response?.data;
  }

  static Future<String?> uploadImages(String filePath, UploadType type) async {
    return PublicProvider.request<dynamic>(
            path: Api.uploadImage,
            isPost: true,
            params: FormData.fromMap({'type': type.name, 'picfile': await MultipartFile.fromFile(filePath, filename: filePath)}),
            type: 'multipart/form-data')
        .then((value) async {
      if (value.isSuccess && value.data != null) {
        return value.data?.values.first;
      }
      return null;
    });
  }

  static Future<String?> userGps(num? lat, num? lon) async {
    if (lat == null || lon == null) {
      return Future.value();
    }

    return PublicProvider.request<String>(path: Api.userGps, params: {'lat': lat, 'lon': lon}).then((value) {
      if (value.isSuccess && value.data != null) {}
      return null;
    });
  }

  static Future<void> downloadFile(String urlPath, String savePath, Function(int, int)? onReceiveProgress, {CancelToken? cancelToken}) async {
    await instance?._dio.download(
      urlPath,
      savePath,
      onReceiveProgress: onReceiveProgress,
      cancelToken: cancelToken,
      options: Options(responseType: ResponseType.bytes, method: 'GET'),
    );
  }
}

void addLoggerForDio(Dio dio) {
  if (!Server.env) {
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        //打印请求地址
        //logger.d('request: ${options.method} ${options.uri} headers :${options.headers} data:${options.data}');
        return handler.next(options); //continue
      },
      onResponse: (response, handler) {
        if(!'${response.requestOptions.uri}'.contains('api/com/area')){
          log('------------------------- \n 请求地址: ${response.requestOptions.uri} \n 请求参数：${response.requestOptions.data.toString()} \n 响应结果：${response.data?.toString()} \n-------------------------');
        }
        return handler.next(response); // continue
      },
      onError: (e, handler) {
        logger.e(e.toString());
        // logger.d('onError: ${response.requestOptions.uri} ${response.requestOptions.data.toString()} ${response.data?.toString()}');
        // ErrorEntity eInfo = createErrorEntity(e);
        // onError(eInfo);
        return handler.next(e); //continue
      },
    ));
  }
}

class FileInfo {
  String? url;
  String? name;

  FileInfo(this.url, this.name);
}

enum UploadType { plain, iden, resume }
