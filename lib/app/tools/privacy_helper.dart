import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/modules/auth/third_login_store.dart';
import 'package:inspector/app/modules/store/privacy_store.dart';
import 'package:inspector/app/tools/global_const.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/l10n.dart';

import 'package:shared_preferences/shared_preferences.dart';

import 'package:inspector/app/routes/app_pages.dart';

import 'storage_util.dart';

bool hasShowCameraTips() {
  return StorageUtil.getBool('camera_tips');
}

bool hasShowStorageTips() {
  return StorageUtil.getBool('storage_tips');
}

void setStorageTips() {
  StorageUtil.setBool('storage_tips', true);
}

void setCameraTips() {
  StorageUtil.setBool('camera_tips', true);
}

Future<void> showCameraTips() async {
  setCameraTips();
  return showCustomDialog(S.of(Get.context!).camera_permission_tips, okTitle: S.of(Get.context!).public_continue);
}

void showPrivacySheet({VoidCallback? onConfirm}) {
  Get.bottomSheet(
      persistent: false,
      isScrollControlled: true,
      ignoreSafeArea: false,
      SafeArea(
        bottom: false,
        child: Builder(builder: (context) {
          return Container(
            padding: const EdgeInsets.fromLTRB(15, 20, 15, 20),
            decoration: BoxDecoration(color: MColor.xFFFFFFFF, borderRadius: BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  S.of(Get.context!).setting_policy_title,
                  style: MFont.semi_Bold17.apply(color: MColor.xFF333333),
                ),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                  child: RichText(
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                      text: TextSpan(
                          text: S.of(Get.context!).setting_policy_sub_title,
                          style: MFont.regular14.apply(color: MColor.xFF333333),
                          //手势监听
                          // recognizer: ,
                          children: [
                            TextSpan(
                                text: ' ${S.of(Get.context!).setting_user_agreement} ',
                                style: MFont.regular14.apply(color: MColor.skin),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    Get.toNamed(
                                      Routes.WEB,
                                      parameters: {'url': '${Server.web}/privacy/user.html', 'title': S.of(Get.context!).setting_user_agreement},
                                    );
                                  }),
                            TextSpan(
                              text: S.of(Get.context!).public_and,
                              style: MFont.regular14.apply(color: MColor.xFF333333),
                            ),
                            TextSpan(
                                text: ' ${S.of(Get.context!).setting_privacy_policy}',
                                style: MFont.regular14.apply(color: MColor.skin),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    Get.toNamed(
                                      Routes.WEB,
                                      parameters: {'url': '${Server.web}/privacy/registerpolicy.html', 'title': S.of(Get.context!).setting_privacy_policy},
                                    );
                                  }),
                          ])),
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all(const StadiumBorder()),
                        minimumSize: MaterialStateProperty.all(const Size(100, 28)),
                      ),
                      onPressed: () {
                        Get.back();
                      },
                      child: Text(
                        S.of(Get.context!).public_deny.capitalize!,
                        style: MFont.regular17.apply(color: MColor.xFF333333),
                      ),
                    ),
                    const SizedBox(width: 20),
                    TextButton(
                      style: ButtonStyle(
                        shape: MaterialStateProperty.all(const StadiumBorder()),
                        backgroundColor: MaterialStateProperty.all(MColor.skin),
                        minimumSize: MaterialStateProperty.all(const Size(200, 28)),
                      ),
                      onPressed: () {
                        Get.back();
                        PrivacyStore.to.setPrivacyAgreed();
                        Future.delayed(const Duration(milliseconds: 500), () {
                          GlobalConst.initDeviceInfo();
                        });

                        if (onConfirm != null) {
                          onConfirm();
                        }
                      },
                      child: Text(
                        S.of(Get.context!).public_ok,
                        style: MFont.regular17.apply(color: Colors.white),
                      ),
                    )
                  ],
                ),
              ],
            ),
          );
        }),
      ),
      isDismissible: false,
      enterBottomSheetDuration: const Duration(milliseconds: 200),
      exitBottomSheetDuration: const Duration(milliseconds: 200));
}

void showPrivacyDialog() {
  Get.generalDialog(pageBuilder: (ctx, a, s) {
    return Builder(builder: (context) {
      return WillPopScope(
          onWillPop: () async => false,
          child: CupertinoAlertDialog(
            title: Column(
              children: [
                Text(
                  '${S.of(Get.context!).setting_user_agreement} ${S.of(Get.context!).public_and} ${S.of(Get.context!).setting_privacy_policy}',
                  style: MFont.semi_Bold17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
              ],
            ),
            content: buildContent(context),
            actions: <Widget>[
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  S.of(Get.context!).public_deny.capitalize!,
                  style: MFont.regular17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
              ),
              TextButton(
                onPressed: () {
                  Get.back();
                  PrivacyStore.to.setPrivacyAgreed();
                  Future.delayed(const Duration(milliseconds: 500), () async {
                    await GlobalConst.initDeviceInfo();
                    await ThirdLoginStore.to.initSdk();
                  });
                },
                child: Text(
                  S.of(Get.context!).public_ok,
                  style: MFont.regular17.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
              ),
            ],
          ));
    });
  });
}

Widget buildContent(BuildContext context) {
  return SizedBox(
    height: 300,
    //ListView可滑动
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Expanded(
          child: ListView(
            shrinkWrap: true,
            children: [
              Text(
                textAlign: TextAlign.start,
                S.of(Get.context!).setting_privacy_content,
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 8,
        ),
        RichText(
          text: TextSpan(
              style: MFont.regular14.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
              //手势监听
              // recognizer: ,
              children: [
                TextSpan(
                  text: S.of(Get.context!).setting_policy_tips2,
                  style: MFont.regular14.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
                TextSpan(
                    text: S.of(Get.context!).setting_user_agreement,
                    style: MFont.regular14.apply(color: context.isDarkMode ? DarkColor.skin : MColor.skin),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Get.toNamed(
                          Routes.WEB,
                          parameters: {'url': '${Server.web}/privacy/user.html', 'title': S.of(Get.context!).setting_user_agreement},
                        );
                      }),
                TextSpan(
                  text: S.of(Get.context!).public_and,
                  style: MFont.regular14.apply(color: context.isDarkMode ? DarkColor.xFF333333 : MColor.xFF333333),
                ),
                TextSpan(
                    text: S.of(Get.context!).setting_privacy_policy,
                    style: MFont.regular14.apply(color: context.isDarkMode ? DarkColor.skin : MColor.skin),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Get.toNamed(
                          Routes.WEB,
                          parameters: {'url': '${Server.web}/privacy/registerpolicy.html', 'title': S.of(Get.context!).setting_privacy_policy},
                        );
                      }),
              ]),
        )
      ],
    ),
  );
}
