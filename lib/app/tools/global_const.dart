import 'dart:async';
import 'dart:convert';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:inspector/app/data/china_city.dart';
import 'package:inspector/app/modules/auth/account_service.dart';
import 'package:inspector/app/modules/store/privacy_store.dart';
import 'package:inspector/app/tools/storage_util.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:inspector/app/config/constant.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/shared/services/screen.dart';
import 'package:inspector/app/tools/device.dart';
import 'package:inspector/generated/json/base/json_convert_content.dart';

const MIDDLE_AVATAR_SIZE = 40.0;

class GlobalConst {
  static final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  static PackageInfo? packageInfo;
  static final Connectivity connectivity = Connectivity();

  static String versionName = '';
  static String versionCode = '';

  static Future<bool> asyns() async {
    var packageInfo = await PackageInfo.fromPlatform();
    versionCode = packageInfo.buildNumber;
    versionName = packageInfo.version;

    if (PrivacyStore.to.isPrivacyAgreed()) {
      await initDeviceInfo();
    }
    return Future.value(true);
  }

  static Future<bool> initDeviceInfo() async {
    packageInfo = await PackageInfo.fromPlatform();
    unawaited(DeviceUtils.init());
    return Future.value(true);
  }

  static double screenWidth = Screen.width;
  static double screenHeight = Screen.height;
  static UserInfoEntity? tempModel;
  static UserInfoEntity? get userModel {
    if (tempModel == null) {
      var jsonStr = StorageUtil.getString(Constant.kUser);
      if (jsonStr.isEmpty) {
        return null;
      }
      var map = json.decode(jsonStr);
      var model = JsonConvert.fromJsonAsT<UserInfoEntity>(map);
      tempModel = model;
    }

    return tempModel;
  }

  static Future<void> saveUser(UserInfoEntity entity) async {
    String info = json.encode(entity.toJson());
    await StorageUtil.setString(Constant.kUser, info);
  }

  static List<ChinaArea> chinaAreas = [];
}
