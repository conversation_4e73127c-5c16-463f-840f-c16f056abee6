import 'package:shared_preferences/shared_preferences.dart';

class StorageUtil {
  static late SharedPreferences _prefs;

  static Future init() async => _prefs = await SharedPreferences.getInstance();

  static getBool(String key, {bool defValue = false}) => _prefs.getBool(key) ?? defValue;
  static setBool(String key, bool value) => _prefs.setBool(key, value);
  static getInt(String key, {int defValue = 0}) => _prefs.getInt(key) ?? defValue;
  static setInt(String key, int value) => _prefs.setInt(key, value);
  static getDouble(String key, {double defValue = 0.0}) => _prefs.getDouble(key) ?? defValue;
  static setDouble(String key, double value) => _prefs.setDouble(key, value);
  static String getString(String key, {String defValue = ''}) => _prefs.getString(key) ?? defValue;
  static setString(String key, String value) => _prefs.setString(key, value);
  static getStringList(String key, {List<String> defValue = const []}) => _prefs.getStringList(key) ?? defValue;
  static setStringList(String key, List<String> value) => _prefs.setStringList(key, value);
  static containsKey(String key) => _prefs.containsKey(key);

  static remove(String key) => _prefs.remove(key);

  /// 存储对象
  static Future<bool> setObject<T>(String key, T value, String Function(T) encode, T Function(String) decode) {
    return setString(key, encode(value));
  }

  /// 获取对象
  static T? getObject<T>(String key, T Function(String) decode) {
    final value = getString(key);
    if (value is String) {
      return decode(value);
    }
    return null;
  }

  /// 存储对象列表
  static Future<bool> setObjectList<T>(String key, List<T> value, String Function(T) encode, T Function(String) decode) {
    return setStringList(key, value.map((e) => encode(e)).toList());
  }

  /// 获取对象列表
  static List<T>? getObjectList<T>(String key, T Function(String) decode) {
    final value = getStringList(key);
    if (value is List<String>) {
      return value.map((e) => decode(e)).toList();
    }
    return null;
  }
}

class StorageKey {
  /// 最后一次登录的邮箱
  static const String LAST_LOGIN_EMAIL = "last_login_email";

  /// token
  static const String TOKEN = "token";
  static const String LOGIN_EMAIL = 'login_email';
  static const String LOGIN_PASSWORD = 'login_password';
  static const String USER_LOGIN = "user_login";
  static const String USER_INFO = "user_info";

  static const String GOODS_CART = "goods_cart";
  static const String ADDRESS_INFO = "address_info";

  /// app setting
  static const String kLocale = 'key_locale';
  static const String kCurrency = 'key_currency';
  static const String kThemeMode = 'key_theme_mode';
  static const String kGreenRise = 'key_green_rise';
}
