// import 'package:flutter/material.dart';

// import 'package:get/get.dart';

// import 'package:inspector/app/config/constant.dart';
// import 'package:inspector/app/tools/global_const.dart';
// import 'package:inspector/generated/id_ID.dart';
// import 'package:inspector/generated/ja_JP.dart';
// 
// import 'package:inspector/generated/zh_TW.dart';

// class TranslationService extends Translations {
//   static const List<Locale> locales = [
//     Locale('zh', 'CN'),
//     Locale('zh', 'TW'),
//     Locale('en', 'US'),
//     Locale('id', 'ID'),
//     Locale('ja', 'JP'),
//   ];

//   static Locale get locale {
//     int? index = GlobalConst.sharedPreferences?.getInt(Constant.kLanguage);
//     if (index != null && index < locales.length) {
//       return locales[index];
//     } else {
//       if (Get.deviceLocale?.languageCode ==
//           const Locale('zh', 'CN').languageCode) {
//         GlobalConst.sharedPreferences?.setInt(Constant.kLanguage, 0);
//         return const Locale('zh', 'CN');
//       } else {
//         GlobalConst.sharedPreferences?.setInt(Constant.kLanguage, 2);
//         return const Locale('en', 'US');
//       }
//     }
//   }

//   static final fallbackLocale = locales[2];

//   static String getLocaleName(int index) {
//     switch (index) {
//       case 0:
//         return '多语言';
//       case 1:
//         return '多語言';
//       case 2:
//         return 'Languages';
//       case 3:
//         return 'Berbahasa';
//       case 4:
//         return '多言語';
//       default:
//         return 'Languages';
//     }
//   }

//   static String getLocaleValue(int index) {
//     switch (index) {
//       case 0:
//         return '简体中文';
//       case 1:
//         return '繁体中文';
//       case 2:
//         return 'English';
//       case 3:
//         return 'Indonesia';
//       case 4:
//         return '日本語';
//       default:
//         return 'English';
//     }
//   }

//   @override
//   Map<String, Map<String, String>> get keys => {
//         'zh_CN': Locales.zh_CN,
//         'zh_TW': zh_TW,
//         'en_US': Locales.en_US,
//         'id_ID': id_ID,
//         'ja_JP': ja_JP,
//       };
// }
