// ignore_for_file: constant_identifier_names

part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();

  static const HOME = _Paths.HOME;
  static const LAUNCH = _Paths.LAUNCH;
  static const TABBAR = _Paths.TABBAR;
  static const AREALIST = _Paths.AREALIST;
  static const AUTH_LOGIN = _Paths.AUTH_LOGIN;
  static const Register = _Paths.Register;
  static const NewRegister = _Paths.NEW_REGISTER;
  static const ACCOUNT_SWITCH = _Paths.ACCOUNT_SWITCH;
  static const REGISTER_TWO = _Paths.REGISTER_TWO;
  static const MESSAGE = _Paths.MESSAGE;
  static const MINE = _Paths.MINE;
  static const PURCHASE_LIST = _Paths.PURCHASE_LIST;
  static const PURCHASE_PUBLISH = _Paths.PURCHASE_PUBLISH;
  static const PURCHASE_SEARCH = _Paths.PURCHASE_SEARCH;
  static const PURCHASE_MY_POSTS = _Paths.PURCHASE_MY_POSTS;
  static const PURCHASE_MY_REPLIES = _Paths.PURCHASE_MY_REPLIES;
  static const PURCHASE_MY_APPEALS = _Paths.PURCHASE_MY_APPEALS;
  static const PURCHASE_PAY = _Paths.PURCHASE_PAY;
  static const LIST_DETAIL = _Paths.LIST_DETAIL;
  static const ADD_MODEL = _Paths.ADD_MODEL;
  static const ADD_PRODUCT = _Paths.ADD_PRODUCT;
  static const ADD_PRODUCT_INFO = _Paths.ADD_PRODUCT_INFO;
  static const ASSIGN_INSPECTOR = _Paths.ASSIGN_INSPECTOR;
  static const ASSIGN_HISOTRY = _Paths.ASSIGN_HISTORY;
  static const PROFILE = _Paths.MINE + _Paths.PROFILE;
  static const THIRD_LOGIN_MANAGE = _Paths.MINE + _Paths.THIRD_LOGIN_MANAGE;
  static const USER_PROFILE = _Paths.USER_PROFILE;
  static const APPLY = _Paths.MINE + _Paths.APPLY;
  static const SETTING = _Paths.MINE + _Paths.SETTING;
  static const ORDER_EDIT = _Paths.ORDER_EDIT;
  static const PUBLISH = _Paths.PUBLISH;
  static const INSPECTION_INFO = _Paths.PUBLISH + _Paths.INSPECTION_INFO;
  static const DATE = _Paths.PUBLISH + _Paths.DATE;
  static const ADDRESS = _Paths.MINE + _Paths.ADDRESS;
  static const ADDRESS_LIST = _Paths.MINE + _Paths.ADDRESS_LIST;
  static const PAY = _Paths.PUBLISH + _Paths.PAY;
  static const WEB = _Paths.WEB;
  static const CHECK = _Paths.ORDER + _Paths.CHECK;
  static const REVIEW = _Paths.ORDER + _Paths.REVIEW;
  static const RECORD = _Paths.RECORD;
  static const WALLET = _Paths.MINE + _Paths.WALLET;
  static const BIN_PAY = _Paths.MINE + _Paths.BIN_PAY;
  static const BIND_BANK = _Paths.MINE + _Paths.BIND_BANK;
  static const ADD_BANK = _Paths.MINE + _Paths.ADD_BANK;
  static const CHARGE = _Paths.MINE + _Paths.CHARGE;
  static const CHARGE_HISTORY = _Paths.MINE + _Paths.CHARGE_HISTORY;
  static const CASH = _Paths.MINE + _Paths.CASH;
  static const WITHDRAW_LIST = _Paths.MINE + _Paths.WITHDRAW_LIST;
  static const BILL_LIST = _Paths.MINE + _Paths.BILL_LIST;
  static const CHAT = _Paths.MESSAGE + _Paths.CHAT;
  static const REPORT = _Paths.ORDER + _Paths.REPORT;
  static const MEMEBER = _Paths.MESSAGE + _Paths.MEMEBER;

  static const FORUM = _Paths.FORUM;
  static const INSP_LIST = _Paths.INSP_LIST;
  static const INSP_MY_INSP = _Paths.INSP_MY_INSP;
  static const INSP_ORDER_MANAGE = _Paths.INSP_ORDER_MANAGE;

  static const chatSettings = '/chat/settings';
  static const downloadFiles = _Paths.MINE + _Paths.DOWNLOAD_FILES;
}

abstract class _Paths {
  static const HOME = '/home';
  static const HOME_NEW = '/newhome';
  static const TABBAR = '/tabbar';
  static const AUTH_LOGIN = '/auth/login';
  static const AREALIST = '/area';
  static const LAUNCH = '/launch';
  static const Register = '/register';
  static const NEW_REGISTER = '/register-new';
  static const REGISTER_TWO = '/register-two';
  static const ORDER = '/order';
  static const MESSAGE = '/message';
  static const MINE = '/mine';
  static const LIST_DETAIL = '/list-detail';
  static const ADD_PRODUCT = '/add_product';
  static const ADD_PRODUCT_INFO = '/add_product_info';
  static const ADD_MODEL = '/add_model';
  static const PROFILE = '/profile';
  static const THIRD_LOGIN_MANAGE = '/third-login-mgr';
  static const APPLY = '/apply';
  static const ORDER_EDIT = '/order-edit';
  static const PUBLISH = '/pulish';
  static const PURCHASE_LIST = '/purchase';
  static const PURCHASE_SEARCH = '/purchase-search';
  static const PURCHASE_MY_POSTS = '/purchase/myPosts';
  static const PURCHASE_MY_REPLIES = '/purchase/myReplies';
  static const PURCHASE_MY_APPEALS = '/purchase/myAppeals';
  static const PURCHASE_PAY = '/purchase-pay';
  static const PURCHASE_PUBLISH = '/purchase/add';
  static const INSPECTION_INFO = '/inspection-info';
  static const DATE = '/pulish/date';
  static const ADDRESS = '/address';
  static const ADDRESS_LIST = '/address-list';
  static const PAY = '/pay';
  static const SETTING = '/setting';
  static const WEB = '/web';
  static const CHECK = '/check';
  static const REVIEW = '/review';
  static const RECORD = '/insp/applyRecod';
  static const WALLET = '/wallet';
  static const BIN_PAY = '/bin-pay';
  static const BIND_BANK = '/bind-bank';
  static const ADD_BANK = '/add-bank';
  static const CHARGE = '/charge';
  static const CHARGE_HISTORY = '/charge_history';
  static const CASH = '/cash';
  static const BILL_LIST = '/bill-list';
  static const WITHDRAW_LIST = '/withdraw-list';
  static const CHAT = '/chat';
  static const REPORT = '/report';
  static const MEMEBER = '/memeber';
  static const DOWNLOAD_FILES = '/download-files';
  static const USER_PROFILE = '/user-profile';
  static const ASSIGN_INSPECTOR = '/assign-inspector';
  static const ASSIGN_HISTORY = '/assign-history';
  static const FORUM = '/forum';
  static const ACCOUNT_SWITCH = '/account-switch';

  static const INSP_LIST = '/index/inlist';
  static const INSP_MY_INSP = '/insp/myInsp';
  static const INSP_ORDER_MANAGE = '/insp/orderManage';
}

class _RouterRegister {
  _RouterRegister._();

  static final routerMap = {};
  /**
   * /index/index	首页
/index/inlist	验货广场
/index/login	登录
/index/register	注册
/user/index	用户中心
/index/wantOrder	验货-下单页
/index/checkOrder	验货-下单确认页
/user/Mysuppliers	验货-地址簿
/user/orderManage	验货-我的订单
/user/accountManage	个人信息
/insp/sindex	我的验货
/insp/accountManage	验货员信息（含修改）
/insp/sindex	验货广场
/user/fundManage	钱包
/insp/applyRecod	验货-申请记录
/groupchat	在线聊天
/insp/promotion	推广管理
/shop	商城首页
/shop/detail	商城详情页
/shop/edit	商品新增和编辑页
/blog	论坛首页
/blog/detail	论坛详情页
/blog/edit	帖子新增和编辑页
   */
}

class _PageBuilder {
  final String path;

  final GetPageBuilder builder;

  _PageBuilder(this.path, this.builder);
}

class RouterJump {
  static void jump(String url) {
    logger.i('uri $url');
    // if (_RouterRegister.routerMap.containsKey(uri.path)) {
    // _PageBuilder builder = _RouterRegister.routerMap[uri.path]!;
    // Get.toNamed('/shop/order_list');
    if (url.isNotEmpty) {
      Get.toNamed(url);
    }
    // Get.toNamed('/shop/detail?sku_id=1');
    // Get.to(() => PurchaseEditView(tag: controller.purchaseId.toString()),
    //             preventDuplicates: true,
    //             arguments: {'purchase_id': controller.purchaseId},
    //             binding: PurchaseEditBinding(tag: controller.purchaseId.toString()))
    // } else {}
  }
}
