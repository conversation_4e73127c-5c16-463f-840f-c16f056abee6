// ignore_for_file: non_constant_identifier_names

import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/modules/ai/ai_pages.dart';
import 'package:inspector/app/modules/auth/account_switch/account_switch_page.dart';
import 'package:inspector/app/modules/auth/login/controllers/auth_login_controller.dart';
import 'package:inspector/app/modules/auth/login/views/auth_login_view.dart';
import 'package:inspector/app/modules/auth/new_register/register_step_two.dart';
import 'package:inspector/app/modules/auth/register/views/auth_register_view.dart';
import 'package:inspector/app/modules/auth/views/area_code_view.dart';
import 'package:inspector/app/modules/home/<USER>/action_model_binding.dart';
import 'package:inspector/app/modules/home/<USER>/action_model_view.dart';
import 'package:inspector/app/modules/home/<USER>/action_product_binding.dart';
import 'package:inspector/app/modules/home/<USER>/action_product_view.dart';
import 'package:inspector/app/modules/home/<USER>/home_list_bindings.dart';
import 'package:inspector/app/modules/home/<USER>/bindings/order_detail_binding.dart';
import 'package:inspector/app/modules/home/<USER>/views/order_detail_view.dart';
import 'package:inspector/app/modules/home/<USER>/bindings/record_binding.dart';
import 'package:inspector/app/modules/home/<USER>/views/record_view.dart';
import 'package:inspector/app/modules/home/<USER>/HomeListView.dart';
import 'package:inspector/app/modules/init/bindings/init_binding.dart';
import 'package:inspector/app/modules/init/web/bindings/web_binding.dart';
import 'package:inspector/app/modules/init/web/views/web_view.dart';
import 'package:inspector/app/modules/inspect/my_insp_list/my_insp_list_page.dart';
import 'package:inspector/app/modules/inspect/my_publish/my_publish_page.dart';
import 'package:inspector/app/modules/mall/mall_pages.dart';
import 'package:inspector/app/modules/message/memeber/bindings/memeber_binding.dart';
import 'package:inspector/app/modules/message/memeber/views/memeber_view.dart';
import 'package:inspector/app/modules/message/views/message_view.dart';
import 'package:inspector/app/modules/mine/add_bank/bindings/add_bank_binding.dart';
import 'package:inspector/app/modules/mine/add_bank/views/add_bank_view.dart';
import 'package:inspector/app/modules/mine/address/address_view.dart';
import 'package:inspector/app/modules/mine/address/binding.dart';
import 'package:inspector/app/modules/mine/address_list/address_list_binding.dart';
import 'package:inspector/app/modules/mine/address_list/address_list_view.dart';
import 'package:inspector/app/modules/mine/apply/ApplyInspectorBinding.dart';
import 'package:inspector/app/modules/mine/apply/ApplyInspectorView.dart';
import 'package:inspector/app/modules/mine/bill_list/bindings/bill_list_binding.dart';
import 'package:inspector/app/modules/mine/bill_list/views/bill_list_view.dart';
import 'package:inspector/app/modules/mine/bin_pay/bindings/bin_pay_binding.dart';
import 'package:inspector/app/modules/mine/bin_pay/views/bin_pay_view.dart';
import 'package:inspector/app/modules/mine/bind_bank/bindings/bind_bank_binding.dart';
import 'package:inspector/app/modules/mine/bind_bank/views/bind_bank_view.dart';
import 'package:inspector/app/modules/mine/bindings/mine_binding.dart';
import 'package:inspector/app/modules/mine/cash/bindings/cash_binding.dart';
import 'package:inspector/app/modules/mine/cash/views/cash_view.dart';
import 'package:inspector/app/modules/mine/charge/bindings/charge_binding.dart';
import 'package:inspector/app/modules/mine/charge/views/charge_view.dart';
import 'package:inspector/app/modules/mine/charge_history/bindings/charge_history_binding.dart';
import 'package:inspector/app/modules/mine/charge_history/views/charge_history_view.dart';
import 'package:inspector/app/modules/mine/profile/bindings/profile_binding.dart';
import 'package:inspector/app/modules/mine/profile/views/profile_view.dart';
import 'package:inspector/app/modules/mine/setting/bindings/setting_binding.dart';
import 'package:inspector/app/modules/mine/setting/views/setting_view.dart';
import 'package:inspector/app/modules/mine/third_login_mgr/third_login_mgr_binding.dart';
import 'package:inspector/app/modules/mine/third_login_mgr/third_login_mgr_view.dart';
import 'package:inspector/app/modules/mine/views/mine_view.dart';
import 'package:inspector/app/modules/mine/wallet/bindings/wallet_binding.dart';
import 'package:inspector/app/modules/mine/wallet/views/wallet_view.dart';
import 'package:inspector/app/modules/mine/withdraw/bindings/withdraw_history_binding.dart';
import 'package:inspector/app/modules/mine/withdraw/views/withdraw_history_view.dart';
import 'package:inspector/app/modules/new_home/home_new_view.dart';
import 'package:inspector/app/modules/order/check/bindings/check_binding.dart';
import 'package:inspector/app/modules/order/check/views/check_view.dart';
import 'package:inspector/app/modules/order/order_edit/order_edit_binding.dart';
import 'package:inspector/app/modules/order/order_edit/order_edit_view.dart';
import 'package:inspector/app/modules/order/review/bindings/review_binding.dart';
import 'package:inspector/app/modules/order/review/views/review_view.dart';
import 'package:inspector/app/modules/pulish/bindings/pulish_binding.dart';
import 'package:inspector/app/modules/pulish/date/bindings/date_binding.dart';
import 'package:inspector/app/modules/pulish/date/views/date_view.dart';
import 'package:inspector/app/modules/pulish/inspection_info/bindings/order_publish_binding.dart';
import 'package:inspector/app/modules/pulish/inspection_info/views/order_publish_view.dart';
import 'package:inspector/app/modules/pulish/pay/bindings/pay_binding.dart';
import 'package:inspector/app/modules/pulish/pay/views/pay_view.dart';
import 'package:inspector/app/modules/pulish/views/publish_view.dart';
import 'package:inspector/app/modules/purchase/purchase_list/purchase_list_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_list/purchase_list_view.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_appeals/purchase_my_appeals_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_appeals/purchase_my_appeals_view.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_posts/purchase_my_posts_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_posts/purchase_my_posts_view.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_replies/purchase_my_replies_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_my/purchase_my_replies/purchase_my_replies_view.dart';
import 'package:inspector/app/modules/purchase/purchase_pay/purchase_pay_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_pay/purchase_pay_view.dart';
import 'package:inspector/app/modules/purchase/purchase_publish/purchase_publish_view.dart';
import 'package:inspector/app/modules/purchase/purchase_search/purchase_search_binding.dart';
import 'package:inspector/app/modules/purchase/purchase_search/purchase_search_view.dart';
import 'package:inspector/app/modules/tabbar/views/tabbar_view.dart';
import 'package:inspector/app/tools/tools.dart';

import '../modules/auth/account_service.dart';
import '../modules/auth/new_register/new_register_binding.dart';
import '../modules/auth/new_register/new_register_view.dart';
import '../modules/home/<USER>/action_info_binding.dart';
import '../modules/home/<USER>/action_info_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static Future<String> INITIAL([bool init = false]) async {
    String? page = Routes.AUTH_LOGIN;
    if (AccountService.instance.getToken().isNotEmpty == true) {
      page = Routes.TABBAR;
      // unawaited(Location.share.requestLocation());
    } else {
      Get.lazyPut<AuthLoginController>(() => AuthLoginController());
    }

    if (!init) {
      unawaited(Get.offNamedUntil(page, (route) => false));
    }

    return Future.value(page);
  }

  static final routes = [
    // GetPage(
    //   name: '/',
    //   unknownRoute:
    // ),
    GetPage(
      name: _Paths.AUTH_LOGIN,
      page: () => const AuthLoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.TABBAR,
      page: () => CustomTabbarView(),
      binding: TabbarBinding(),
    ),
    GetPage(
      name: _Paths.AREALIST,
      page: () => AreaCodeView(),
      binding: TabbarBinding(),
    ),
    GetPage(
      name: _Paths.Register,
      page: () => const AuthRegisterView(),
      binding: RegisterBinding(),
    ),
    GetPage(name: _Paths.NEW_REGISTER, page: () => const NewRegisterView(), binding: NewRegisterBinding()),
    GetPage(name: _Paths.REGISTER_TWO, page: () => const RegisterStepTwoView(), binding: RegisterStepTwoBinding()),
    GetPage(
      name: _Paths.HOME_NEW,
      page: () => const HomeNewView(),
    ),
    GetPage(
      name: _Paths.INSP_LIST,
      page: () => const HomeListView(),
      binding: HomeListBindings(),
      children: [],
    ),
    GetPage(
      name: _Paths.LIST_DETAIL,
      page: () => const OrderDetailView(),
      binding: OrderDetailBinding(),
    ),
    GetPage(
      name: _Paths.ADD_MODEL,
      page: () => const ActionModelView(),
      binding: ActionModelBinding(),
    ),
    GetPage(
      name: _Paths.ADD_PRODUCT,
      page: () => const ActionProductView(),
      binding: ActionProductBinding(),
    ),
    GetPage(
      name: _Paths.ADD_PRODUCT_INFO,
      page: () => const ActionInfoView(),
      binding: ActionInfoBinding(),
    ),

    GetPage(
      name: _Paths.RECORD,
      page: () => const RecordView(),
      binding: RecordBinding(),
    ),
    // GetPage(
    //   name: _Paths.ASSIGN_INSPECTOR,
    //   page: () => const AssignInspectorView(),
    //   binding: AssignInspectorBinding(),
    // ),
    // GetPage(name: _Paths.ASSIGN_HISTORY, page: () => const AssignHistoryView(), binding: AssignHistoryBinding()),
    GetPage(
      name: _Paths.ORDER,
      page: () => const SizedBox(),
      children: [
        GetPage(
          name: _Paths.CHECK,
          page: () => const CheckView(),
          binding: CheckBinding(),
        ),
        GetPage(
          name: _Paths.REVIEW,
          page: () => const ReviewView(),
          binding: ReviewBinding(),
        ),
      ],
    ),
    GetPage(
      name: _Paths.ORDER_EDIT,
      page: () => const OrderEditView(),
      binding: OrderEditBindings(),
    ),
    GetPage(
      name: _Paths.INSP_MY_INSP,
      page: () => MyInspListPage(),
    ),
    GetPage(
      name: _Paths.INSP_ORDER_MANAGE,
      page: () => MyPublishPage(),
    ),
    // GetPage(
    //   name: _Paths.USER_ORDERS,
    //   page: () => OrderListView(0, 'order-0', show: true),
    //   binding: OrderListBinding('order-0'),
    // ),
    // GetPage(
    //   name: _Paths.ORDER_LIST2,
    //   page: () => OrderListView(1, 'order-1', show: true),
    //   binding: OrderListBinding('order-1'),
    // ),
    // GetPage(
    //   name: _Paths.INSP_SINDEX,
    //   page: () => OrderListView(1, 'order-1', show: true),
    //   binding: OrderListBinding('order-1'),
    // ),
    GetPage(
      name: _Paths.MESSAGE,
      page: () => const IMessageView(),
      children: [
        // GetPage(
        //   name: _Paths.CHAT,
        //   page: () => const ChatView(),
        //   binding: ChatBinding(),
        // ),
        GetPage(
          name: _Paths.MEMEBER,
          page: () => const MemeberView(),
          binding: MemeberBinding(),
        ),
      ],
    ),
    GetPage(name: _Paths.PURCHASE_LIST, page: () => const PurchaseListView(), binding: PurchaseListBinding()),
    GetPage(
      name: _Paths.PURCHASE_PUBLISH,
      page: () {
        return PurchasePublishView();
      },
    ),
    // binding: PurchasePublishBinding()),
    GetPage(name: _Paths.PURCHASE_MY_POSTS, page: () => const PurchaseMyPostsView(), binding: PurchaseMyPostsBinding()),
    GetPage(name: _Paths.PURCHASE_MY_REPLIES, page: () => const PurchaseMyRepliesView(), binding: PurchaseMyRepliesBinding()),
    GetPage(name: _Paths.PURCHASE_MY_APPEALS, page: () => const PurchaseMyAppealsView(), binding: PurchaseMyAppealsBinding()),
    GetPage(name: _Paths.PURCHASE_PAY, page: () => const PurchasePayView(), binding: PurchasePayBinding()),
    GetPage(name: _Paths.PURCHASE_SEARCH, page: () => const PurchaseSearchView(), binding: PurchaseSearchBinding()),
    GetPage(
      name: _Paths.MINE,
      page: () => const MineView(),
      binding: MineBinding(),
      children: [
        GetPage(
          name: _Paths.PROFILE,
          page: () => const ProfileView(),
          binding: ProfileBinding(),
        ),
        GetPage(name: _Paths.THIRD_LOGIN_MANAGE, page: () => const ThirdLoginMgrView(), binding: ThirdLoginMgrBinding()),
        GetPage(name: _Paths.APPLY, page: () => ApplyInspectorView(), binding: ApplyInspectorBinding()),
        GetPage(
          name: _Paths.ADDRESS,
          page: () => const AddressView(),
          binding: AddressBinding(),
        ),
        GetPage(
          name: _Paths.ADDRESS_LIST,
          page: () => const AddressListView(),
          binding: AddressListBinding(),
        ),
        GetPage(
          name: _Paths.SETTING,
          page: () => const SettingPage(),
          binding: SettingBinding(),
        ),
        GetPage(
          name: _Paths.WALLET,
          page: () => const WalletView(),
          binding: WalletBinding(),
        ),
        GetPage(
          name: _Paths.BIN_PAY,
          page: () => const BinPayView(),
          binding: BinPayBinding(),
        ),
        GetPage(
          name: _Paths.BIND_BANK,
          page: () => const BindBankView(),
          binding: BindBankBinding(),
        ),
        GetPage(
          name: _Paths.ADD_BANK,
          page: () => const AddBankView(),
          binding: AddBankBinding(),
        ),
        GetPage(
          name: _Paths.CHARGE,
          page: () => const ChargeView(),
          binding: ChargeBinding(),
        ),
        GetPage(
          name: _Paths.CHARGE_HISTORY,
          page: () => const ChargeHistoryView(),
          binding: ChargeHistoryBinding(),
        ),
        GetPage(
          name: _Paths.CASH,
          page: () => const CashView(),
          binding: CashBinding(),
        ),
        GetPage(
          name: _Paths.BILL_LIST,
          page: () => const BillListView(),
          binding: BillListBinding(),
        ),
        GetPage(
          name: _Paths.WITHDRAW_LIST,
          page: () => const WithdrawHistoryView(),
          binding: WithdrawHistoryBinding(),
        )
      ],
    ),
    GetPage(
      name: _Paths.ACCOUNT_SWITCH,
      page: () => const AccountSwitchPage(),
    ),
    GetPage(
      name: _Paths.PUBLISH,
      page: () => const PublishView(),
      binding: PulishBinding(),
      children: [
        GetPage(
          name: _Paths.INSPECTION_INFO,
          page: () => OrderPublishView(),
          binding: OrderPublishBinding(),
        ),
        GetPage(
          name: _Paths.DATE,
          page: () => const DateView(),
          binding: DateBinding(),
        ),
        GetPage(
          name: _Paths.PAY,
          page: () => const PayView(),
          binding: PayBinding(),
        ),
      ],
    ),
    GetPage(
      name: _Paths.WEB,
      page: () {
        String? url = Get.parameters['url'];
        return WebView();
      },
      binding: WebBinding(),
    ),
    GetPage(name: _Paths.FORUM, page: () => const PurchaseSearchView(), binding: PurchaseSearchBinding()),
    MallPages.routes,
    AiPages.routes,
    // GetPage(name: Routes.chatSettings, page: () => const ChatSettingsPage(), binding: ChatSettingsPageBinding()),
  ];
}
