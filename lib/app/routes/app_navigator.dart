import 'dart:async';

import 'package:flutter/material.dart';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import 'package:inspector/app/config/api_service.dart';
import 'package:inspector/app/modules/message/chat_provider.dart';
import 'package:inspector/app/modules/message/settings/pages/update_group_name.dart';
import 'package:inspector/app/modules/mine/setting/views/locale.dart';
import 'package:inspector/app/modules/mine/setting/views/theme.dart';
import 'package:inspector/app/routes/app_pages.dart';

class AppNavigator {
  AppNavigator._();

  ///关闭页面的层级数量
  static void close(int times) {
    Get.close(times);
  }

  ///默认方式打开一个页面
  static Future<T?>? open<T>(Widget page, {bool? opaque, Transition? transition, Bindings? binding, dynamic arguments}) {
    return Get.to<T>(
      () => page,
      arguments: arguments,
      opaque: opaque,
      transition: transition,
      binding: binding,
    );
  }

  ///后退
  static void back<T>({T? result}) => Get.back<T>(result: result);

  ///订单详情
  static Future<void> oderDetail(int orderId) async {
    await Get.toNamed(Routes.LIST_DETAIL, arguments: orderId, parameters: {'isApply': '1'});
  }

  ///聊天详情页面
  static Future<void> chatDetail(int id, String title) async {
    await Get.toNamed(Routes.CHAT, arguments: {'id': id, 'title': title});
  }

  ///更新群聊名称页面
  static Future<void> updateGroupName() async => await open(const UpdateGroupNamePage());

  ///创建群聊
  static Future<void> openChatGroup(int orderId) async {
    // unawaited(EasyLoading.show());
    //
    // int conversationId = await ApiService.getConversationId(orderId);
    // var result = await ChatProvider().takeChatMsg(conversationId, 1, 1);
    //
    // unawaited(EasyLoading.dismiss());
    //
    // await chatDetail(conversationId, result.data['conversation']['object_info']['title']);
  }

  ///多语言选择
  static Future<void> locale() async {
    await open(const LocalePage());
  }

  ///主题选择
  static Future<void> theme() async {
    await open(const ThemePage());
  }

  static Future<void> downloadFiles() async {
    await Get.toNamed(Routes.downloadFiles);
  }
}
