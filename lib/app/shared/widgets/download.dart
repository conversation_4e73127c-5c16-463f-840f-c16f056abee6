// ignore_for_file: depend_on_referenced_packages

import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/app/tools/tools.dart';

import '../../../generated/l10n.dart';

class DownloadPage extends StatefulWidget {
  const DownloadPage(this.url, {Key? key}) : super(key: key);

  final String url;

  @override
  State<DownloadPage> createState() => _DownloadPageState();
}

class _DownloadPageState extends State<DownloadPage> {
  String savedPath = '';
  double percentage = 0.0;
  CancelToken cancelToken = CancelToken();

  @override
  void initState() {
    _download();
    super.initState();
  }

  Future<void> _download() async {
    String fileName = widget.url.substring(widget.url.lastIndexOf('/') + 1);

    Directory dir = await getTemporaryDirectory();
    Directory folder = Directory(path.join(dir.path, 'download'));
    if (!folder.existsSync()) await folder.create(recursive: true);
    savedPath = path.join(folder.path, fileName);

    try {
      await PublicProvider.downloadFile(widget.url, savedPath, calcDownloadPercentage, cancelToken: cancelToken);
    } catch (error) {
      cancelToken.cancel();
      Navigator.of(Get.context!).pop();
      showToast('文件下载失败');
    }
  }

  void calcDownloadPercentage(int received, int total) {
    setState(() {
      percentage = received / total;
      if (percentage == 1.0) Navigator.of(context).pop(savedPath);
    });
  }

  @override
  Widget build(BuildContext context) {
    Widget title = Text(S.of(Get.context!).downloading);
    Widget cancel = GestureDetector(
      onTap: () {
        cancelToken.cancel();
        Navigator.of(context).pop();
      },
      child: Text(S.of(Get.context!).order_cancel),
    );
    Widget indicator = CircularProgressIndicator(value: percentage);
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 156,
          height: 156,
          child: Material(
            borderRadius: BorderRadius.circular(6),
            child: Column(
              children: [
                const SizedBox(height: 16),
                title,
                const SizedBox(height: 24),
                indicator,
                const SizedBox(height: 24),
                cancel,
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
