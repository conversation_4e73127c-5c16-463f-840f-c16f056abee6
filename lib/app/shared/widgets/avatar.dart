import 'package:flutter/material.dart';

import 'package:cached_network_image/cached_network_image.dart';

class Avatar extends StatelessWidget {
  const Avatar({
    this.url,
    this.displayName,
    this.size = 40.0,
    this.radius = 999.0,
    Key? key,
  }) : super(key: key);

  final String? url;
  final String? displayName;
  final double size;
  final double radius;

  @override
  Widget build(BuildContext context) {
    Widget current = url != null && url!.isNotEmpty ? _image : _noImage;

    return ClipRRect(
      borderRadius: BorderRadius.circular(radius),
      child: current,
    );
  }

  Widget get _noImage {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(color: getColor(displayName)),
      child: const Icon(Icons.person),
    );
  }

  Widget get _image {
    return CachedNetworkImage(
      imageUrl: url!,
      fit: BoxFit.cover,
      width: size,
      height: size,
      placeholder: (c, s) => _noImage,
      errorWidget: (c, s, d) => _noImage,
    );
  }

  Color getColor(String? source) {
    if (source == null) return Colors.transparent;

    int hash = source.hashCode & 0xffff;
    double hue = (hash * 360.0 / (1 << 15)) % 360.0;
    return HSVColor.fromAHSV(1.0, hue, 0.4, 0.9).toColor();
  }
}
