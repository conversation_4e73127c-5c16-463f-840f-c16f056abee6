import 'package:flutter/material.dart';

extension StringExtension on String {
  ///是否为正整数字符串
  bool isPositiveInteger() {
    RegExp regex = RegExp(r'^\d+$');
    return regex.hasMatch(this);
  }

  ///根据字符串哈希值转化为颜色
  Color toColor() {
    String source = this + (length > 0 ? substring(0, 1) : '');
    int hash = source.hashCode & 0xffff;
    double hue = (hash * 360.0 / (1 << 15)) % 360.0;
    return HSVColor.fromAHSV(1.0, hue, 0.4, 0.9).toColor();
  }

  ///删除字符串中头部指定的字符
  String trimStart(String chars) {
    var value = this;

    while (value.startsWith(chars)) {
      value = value.substring(chars.length);
    }

    return value;
  }

  ///删除字符串中尾部指定的字符
  String trimEnd(String chars) {
    var value = this;

    while (value.endsWith(chars)) {
      value = value.substring(0, value.length - chars.length);
    }

    return value;
  }

  ///从指定位置开始截取指定长度的字符串
  String subString({int start = 0, int? length}) {
    String source = this;

    length ??= source.length;

    int sourceLength = source.length;

    if (sourceLength - start <= length) return source.substring(start);

    return source.substring(start, length + start);
  }

  ///删除html标签
  String clearHtml() {
    return replaceAll(RegExp(r'<\/?.+?\/?>'), '');
  }
}
