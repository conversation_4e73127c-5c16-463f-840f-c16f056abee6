// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:inspector/app/shared/components/navigation/back_leading.dart';
import 'package:inspector/app/shared/services/utils.dart';

//页面右上角图标距离页面的间距
const _ACTION_RIGHT_SPACE = 16.0;

class PageHelper {
  PageHelper._();

  static List<Widget>? _getPageActions({
    String? text,
    IconData? iconData,
    Widget? actionWidget,
    Function()? onClick,
    List<Widget>? children,
  }) {
    Widget? current = actionWidget;

    if (!Utils.isNullOrEmpty(text)) current = Text(text!);
    if (!Utils.isNullOrEmpty(iconData)) current = Icon(iconData);

    if (current != null) {
      current = GestureDetector(
        onTap: onClick?.call,
        child: Container(
          color: Colors.transparent,
          alignment: Alignment.center,
          child: current,
        ),
      );
    }
    List<Widget>? actions = children ?? (current == null ? null : [current]);

    if (actions == null) return null;

    return actions..add(const SizedBox(width: _ACTION_RIGHT_SPACE));
  }

  static AppBar getAppBar({
    bool autoLeading = true,
    Widget? leading,
    Color? leadingColor,
    Function()? leadingClick,
    String? title,
    Color? titleColor,
    Widget? titleWidget,
    String? actionText,
    IconData? actionIcon,
    Widget? actionWidget,
    double titleSpacing = 0.0,
    double? elevation,
    Function()? actionClick,
    List<Widget>? actions,
    Color? backgroundColor,
    SystemUiOverlayStyle? systemOverlayStyle,
  }) {
    return AppBar(
      automaticallyImplyLeading: autoLeading,
      leading: !autoLeading ? null : leading ?? BackLeading(color: leadingColor, onClick: leadingClick),
      title: titleWidget ?? Text(title ?? '', style: TextStyle(color: titleColor)),
      titleSpacing: titleSpacing,
      elevation: elevation,
      toolbarOpacity: 0.75,
      systemOverlayStyle: systemOverlayStyle,
      backgroundColor: backgroundColor,
      actions: _getPageActions(text: actionText, iconData: actionIcon, actionWidget: actionWidget, onClick: actionClick, children: actions),
    );
  }
}
