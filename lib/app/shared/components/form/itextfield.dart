import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:inspector/app/theme/style.dart';

enum TextInputModes {
  single,
  textarea,
  password,
}

class ITextField extends StatefulWidget {
  const ITextField({
    Key? key,
    this.keyboardType,
    this.focusNode,
    this.textInputAction = TextInputAction.done,
    this.placeholder,
    this.controller,
    this.maxLength = 999,
    this.disabled = false,
    this.readonly = false,
    this.clearable = true,
    this.autofocus = false,
    this.inputFormatters,
    this.type = TextInputModes.single,
    this.rows = 2,
    this.showWordLimit = false,
    this.inputAlign = TextAlign.start,
    this.contextMenuBuilder,
    this.onChange,
    this.onEditingComplete,
    this.onSubmitted,
    this.onFocused,
    this.style,
    this.padding = EdgeInsets.zero,
    this.contentPadding = EdgeInsets.zero,
    this.crossAxisAlignment = CrossAxisAlignment.center,
  }) : super(key: key);

  ///控制器属性
  final TextEditingController? controller;

  ///键盘显示类型
  final TextInputType? keyboardType;

  ///焦点控制
  final FocusNode? focusNode;

  ///键盘右下角的按钮类型
  final TextInputAction textInputAction;

  ///输入的最大字符数
  final int maxLength;

  ///占位提示文字
  final String? placeholder;

  ///是否禁用输入框
  final bool disabled;

  ///是否只读
  final bool readonly;

  ///是否启用清除小组件
  final bool clearable;

  ///自动聚焦
  final bool autofocus;

  ///检验输入框正则
  final List<TextInputFormatter>? inputFormatters;

  ///输入框类型
  final TextInputModes type;

  ///输入框行数，当type=textarea可用
  final int rows;

  ///显示字数统计
  final bool showWordLimit;

  ///输入框内容对齐方式
  final TextAlign inputAlign;

  ///工具栏定制
  final EditableTextContextMenuBuilder? contextMenuBuilder;

  ///输入框内容变化时触发
  final Function(String val)? onChange;

  ///输入框内容编辑结束时触发
  final Function()? onEditingComplete;

  ///输入框获得焦点时触发
  final Function()? onFocused;

  ///内容提交(按回车)的回调
  final Function(String val)? onSubmitted;

  ///文本样式
  final TextStyle? style;

  ///
  final EdgeInsetsGeometry padding;

  ///
  final EdgeInsetsGeometry contentPadding;

  final CrossAxisAlignment crossAxisAlignment;
  @override
  State<ITextField> createState() => _ITextField();
}

class _ITextField extends State<ITextField> {
  bool _isShowPwd = false;
  late bool _isShowDelete;
  TextEditingController? _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();

    /// 获取初始化值
    _isShowDelete = _controller!.text.isNotEmpty;

    /// 监听输入改变
    _controller!.addListener(() {
      if (mounted) setState(() => _isShowDelete = _controller!.text.isNotEmpty);
    });
  }

  Widget buildTextField() {
    return Expanded(
      child: TextFormField(
        controller: _controller,
        focusNode: widget.focusNode,
        keyboardType: widget.keyboardType,
        textInputAction: widget.type == TextInputModes.textarea ? TextInputAction.newline : widget.textInputAction,
        textAlign: widget.inputAlign,
        autofocus: widget.autofocus,
        readOnly: widget.readonly,
        enabled: !widget.disabled,
        maxLines: widget.type == TextInputModes.textarea ? widget.rows : 1,
        style: widget.style ?? const TextStyle(fontSize: DefaultStyle.fontSizeNormal),
        inputFormatters: widget.inputFormatters,
        obscureText: widget.type == TextInputModes.password ? !_isShowPwd : false,
        decoration: InputDecoration(
          isDense: true,
          contentPadding: widget.contentPadding,
          hintText: widget.placeholder,
          hintStyle: const TextStyle(fontSize: DefaultStyle.fontSizeNormal, color: DefaultStyle.gray4),
          counterText: !widget.showWordLimit ? '' : null,
          border: InputBorder.none,
        ),
        maxLength: widget.maxLength,
        contextMenuBuilder: widget.contextMenuBuilder,
        cursorWidth: 1.0,
        onEditingComplete: () => widget.onEditingComplete?.call(),
        onChanged: (val) => widget.onChange?.call(val),
        onTap: () => widget.onFocused?.call(),
        onFieldSubmitted: (val) => widget.onSubmitted?.call(val),
      ),
    );
  }

  Widget buildTrailing(BuildContext context) {
    const double iconSpacing = 10.0;
    Color color = DefaultStyle.gray4;

    return Row(
      children: <Widget>[
        widget.type == TextInputModes.password ? const SizedBox(width: iconSpacing) : const SizedBox(),
        widget.type == TextInputModes.password
            ? GestureDetector(
                child: Icon(_isShowPwd ? Icons.visibility : Icons.visibility_off, size: 16.0, color: color),
                onTap: () => setState(() => _isShowPwd = !_isShowPwd),
              )
            : const SizedBox(),
        widget.clearable && _isShowDelete ? const SizedBox(width: iconSpacing) : const SizedBox(),
        widget.clearable && _isShowDelete
            ? GestureDetector(
                child: Icon(Icons.cancel, size: 16.0, color: color),
                onTap: () {
                  _controller!.text = '';
                  if (widget.onChange != null) widget.onChange?.call('');
                },
              )
            : const SizedBox(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: widget.crossAxisAlignment,
      children: [buildTextField(), buildTrailing(context)],
    );
  }
}
