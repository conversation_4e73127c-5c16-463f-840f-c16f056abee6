// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

const _COLOR_DARK = Color(0xFFEEEEEE);
const _COLOR_LIGHT = Color(0xFF212121);

///页面后退导航图标
class BackLeading extends StatelessWidget {
  const BackLeading({
    Key? key,
    this.onClick,
    this.color,
    this.iosShape = true,
    this.iconData,
    this.background,
  }) : super(key: key);

  final VoidCallback? onClick;
  final Color? color;
  final bool iosShape;
  final IconData? iconData;
  final Color? background;

  IconData get _icon => iconData ?? (!iosShape ? Icons.arrow_back : Icons.arrow_back_ios);

  @override
  Widget build(BuildContext context) {
    Widget current = Icon(_icon, color: color ?? (Theme.of(context).brightness == Brightness.dark ? _COLOR_DARK : _COLOR_LIGHT));

    if (background != null) {
      current = Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(color: background, shape: BoxShape.circle),
        padding: const EdgeInsets.fromLTRB(2.0, 2.0, 2.0, 2.0),
        child: current,
      );
    }

    return GestureDetector(onTap: () => onClick == null ? Navigator.of(context).pop() : onClick?.call(), child: current);
  }
}
