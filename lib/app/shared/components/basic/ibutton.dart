import 'dart:async';

import 'package:flutter/material.dart';

import 'package:inspector/app/config/design.dart';

class IButton extends StatefulWidget {
  const IButton({required this.title, this.backgroundColor, this.onPressed, Key? key}) : super(key: key);

  final String title;

  ///自定义按钮背景色
  final Color? backgroundColor;

  final FutureOr Function()? onPressed;

  @override
  State<IButton> createState() => _IButtonState();
}

class _IButtonState extends State<IButton> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 22, right: 22, top: 18),
      child: TextButton(
        onPressed: widget.onPressed,
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(widget.backgroundColor ?? MColor.skin),
          shape: MaterialStateProperty.all(const StadiumBorder()),
          minimumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
          visualDensity: VisualDensity.compact,
          maximumSize: MaterialStateProperty.all(const Size(double.infinity, 49)),
        ),
        child: Text(
          widget.title,
          style: MFont.medium18.apply(color: Colors.white),
        ),
      ),
    );
  }
}
