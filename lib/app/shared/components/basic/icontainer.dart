import 'package:flutter/material.dart';

import 'package:inspector/app/theme/style.dart';

///仅对SDK中的Container进行简单的封装
///自动响应Theme Mode的变化
class IContainer extends StatelessWidget {
  const IContainer({
    Key? key,
    this.alignment,
    this.padding,
    this.color,
    this.decoration,
    this.foregroundDecoration,
    this.width,
    this.height,
    this.margin,
    this.transform,
    this.transformAlignment,
    this.child,
    this.clipBehavior = Clip.none,
    this.constraints,
  }) : super(key: key);

  final Widget? child;
  final AlignmentGeometry? alignment;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  final BoxDecoration? decoration;
  final Decoration? foregroundDecoration;
  final BoxConstraints? constraints;
  final EdgeInsetsGeometry? margin;
  final Matrix4? transform;
  final AlignmentGeometry? transformAlignment;
  final Clip clipBehavior;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    Color background = color ?? (Theme.of(context).brightness == Brightness.dark ? DefaultStyle.gray9 : DefaultStyle.white);

    return Container(
      alignment: alignment,
      padding: padding,
      decoration: (decoration ?? const BoxDecoration()).copyWith(color: background),
      foregroundDecoration: foregroundDecoration,
      width: width,
      height: height,
      margin: margin,
      transform: transform,
      transformAlignment: transformAlignment,
      clipBehavior: clipBehavior,
      constraints: constraints,
      child: child,
    );
  }
}
