import 'package:flutter/material.dart';

class DefaultStyle {
  DefaultStyle._();

  static const Color transparent = Color(0x00000000);
  static const Color primary = Color(0xFFC42407);
  static const Color black = Color(0xFF000000);
  static const Color white = Color(0xFFFFFFFF);

  static const Color red = Color(0xFFFF0045);
  static const Color green = Color(0xFF67C23A);
  static const Color blue = Color(0xFF409EFF);
  static const Color yellow = Color(0xFFFFC107);
  static const Color indigo = Color(0xFF6610F2);
  static const Color purple = Color(0xFF6F42C1);
  static const Color pink = Color(0xFFE83E8C);
  static const Color orange = Color(0xFFFD7E14);
  static const Color teal = Color(0xFF20C997);
  static const Color cyan = Color(0xFF17A2B8);

  static const Color gray1 = Color(0xFFF5F5F5);
  static const Color gray2 = Color(0xFFEEEEEE);
  static const Color gray3 = Color(0xFFE0E0E0);
  static const Color gray4 = Color(0xFFBDBDBD);
  static const Color gray5 = Color(0xFF9E9E9E);
  static const Color gray6 = Color(0xFF757575);
  static const Color gray7 = Color(0xFF616161);
  static const Color gray8 = Color(0xFF424242);
  static const Color gray9 = Color(0xFF212121);

  //页面默认背景颜色
  static const pageBackground = Color(0xFFF4F5F7);

  ///20.0
  static const fontSizeXLarge = 20.0;

  ///16.0
  static const fontSizeLarge = 16.0;

  ///14.0
  static const fontSizeNormal = 14.0;

  ///12.0
  static const fontSizeXSmall = 12.0;

  ///10.0
  static const fontSizeXXSmall = 10.0;

  ///8.0
  static const fontSizeXXXSmall = 8.0;

  ///16.0
  static const spaceLarge = 16.0;

  ///10.0
  static const spaceNormal = 10.0;

  ///4.0
  static const spaceSmall = 4.0;

  ///999.0
  static const radiusMax = 999.0;

  ///16.0
  static const radiusXLarge = 18.0;

  ///10.0
  static const radiusLarge = 10.0;

  ///6.0
  static const radiusNormal = 6.0;

  ///4.0
  static const radiusSmall = 4.0;

  ///2.0
  static const radiusXSmall = 2.0;
}
