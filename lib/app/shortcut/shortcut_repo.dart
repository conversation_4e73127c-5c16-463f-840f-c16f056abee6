import 'package:inspector/app/data/base_entity.dart';

import '../config/api.dart';
import '../data/public_model.dart';
import '../tools/http.dart';
import '../tools/public_provider.dart';
import 'shortcut_entity.dart';

class ShortcutRepo {
  static Future<BaseEntity<List<ShortcutCategory>>> getShortcutList() async {
    var response = await HttpUtil().get(Api.shortcutList);
    return BaseEntity.fromJson(response, (json) {
      final list = <ShortcutCategory>[];
      if (json != null) {
        for (var element in (json as List<dynamic>)) {
          if (element != null) {
            list.add(ShortcutCategory.fromJson(element));
          }
        }
      }
      return list;
    });
  }
}
