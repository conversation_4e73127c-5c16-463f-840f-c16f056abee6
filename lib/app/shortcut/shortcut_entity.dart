import 'package:json_annotation/json_annotation.dart';

part 'shortcut_entity.g.dart';

@JsonSerializable()
class ShortcutData extends Object {
  @J<PERSON><PERSON><PERSON>(name: 'id')
  int id;

  @J<PERSON><PERSON><PERSON>(name: 'pid')
  int pid;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'name')
  String name;

  @J<PERSON><PERSON><PERSON>(name: 'route')
  String route;

  @Json<PERSON><PERSON>(name: 'icon')
  String icon;

  @<PERSON>son<PERSON>ey(name: 'status')
  int status;

  @<PERSON>son<PERSON><PERSON>(name: 'sort')
  int sort;

  ShortcutData(
    this.id,
    this.pid,
    this.name,
    this.route,
    this.icon,
    this.status,
    this.sort,
  );

  factory ShortcutData.fromJson(Map<String, dynamic> srcJson) => _$ShortcutDataFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ShortcutDataToJson(this);
}

@JsonSerializable()
class ShortcutCategory extends Object {
  @Json<PERSON>ey(name: 'id')
  int id;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'pid')
  int pid;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'name')
  String name;

  @J<PERSON><PERSON><PERSON>(name: 'route')
  String route;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'icon')
  String icon;

  @J<PERSON><PERSON><PERSON>(name: 'status')
  int status;

  @Json<PERSON>ey(name: 'sort')
  int sort;

  @JsonKey(name: 'children')
  List<ShortcutData> children;

  ShortcutCategory(
    this.id,
    this.pid,
    this.name,
    this.route,
    this.icon,
    this.status,
    this.sort,
    this.children,
  );

  factory ShortcutCategory.fromJson(Map<String, dynamic> srcJson) => _$ShortcutCategoryFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ShortcutCategoryToJson(this);
}
