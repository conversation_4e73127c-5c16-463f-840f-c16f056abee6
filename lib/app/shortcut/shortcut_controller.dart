import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:inspector/app/shortcut/shortcut_repo.dart';

import '../tools/tools.dart';
import 'shortcut_entity.dart';

const int ITEM_ROW_COUNT = 3;

class ShortcutController extends GetxController {
  GlobalKey<RefreshIndicatorState> indicatorKey = GlobalKey<RefreshIndicatorState>();
  final TextEditingController searchController = TextEditingController();
  final ScrollController leftListController = ScrollController(initialScrollOffset: 0);
  final ScrollController rightListController = ScrollController(initialScrollOffset: 0);
  final shortcutCategories = <ShortcutCategory>[].obs;

  final indexLeft = 0.obs;

  final rightOffsetStepList = <double>[];
  @override
  void onInit() {
    super.onInit();

    //左侧Listview的监听，目前没有用到
    leftListController.addListener(() {});

    //右侧ListView的监听
    rightListController.addListener(() {
      //滚动超过右侧Listview的项目长度时，将左侧选中最后一项，这一个判断不添加的话，左侧ListView选择不到最后一项
      if (rightOffsetStepList.last <= rightListController.offset) {
        indexLeft.value = shortcutCategories.length - 1;
      } else {
        for (int i = 0; i < rightOffsetStepList.length; i++) {
          if (rightOffsetStepList[i] > rightListController.offset + 0.1) {
            indexLeft.value = (i - 1 < 0 ? 0 : (i - 1));
            break;
          }
        }
      }
    });
    getShortcuts();
  }

  Future<void> getShortcuts() async {
    try {
      var resp = await ShortcutRepo.getShortcutList();
      if (resp.isSuccess) {
        shortcutCategories.clear();
        rightOffsetStepList.clear();
        if (resp.data != null) {
          var offset = 0.0;
          shortcutCategories.addAll(resp.data!);
          for (var category in shortcutCategories) {
            rightOffsetStepList.add(offset);
            var lines = (category.children.length / ITEM_ROW_COUNT).ceil();
            offset += (lines * 76 + (lines - 1) * 10) + 80;
          }
        }
      }
    } catch (e) {
      logger.e('getShortcuts $e');
    } finally {}
  }

  @override
  void onClose() {
    leftListController.dispose();
    rightListController.dispose();
    super.onClose();
  }

  void onLeftClicked(int index) {
    rightListController.animateTo(rightOffsetStepList[index], duration: const Duration(milliseconds: 500), curve: Curves.easeIn);
  }
}
