import 'dart:async';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../generated/l10n.dart';
import '../config/design.dart';
import '../routes/app_pages.dart';
import '../tools/icons.dart';
import 'shortcut_controller.dart';

class ShortcutView extends GetView<ShortcutController> {
  const ShortcutView({super.key});

  @override
  ShortcutController get controller {
    return GetInstance().putOrFind(() => ShortcutController(), tag: tag);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(Get.context!).shortcut_tab_name, style: TextStyle(color: MColor.xFF000000, fontSize: 22, fontWeight: FontWeight.w500)),
        centerTitle: true,
        scrolledUnderElevation: 0,
      ),
      backgroundColor: MColor.xFFFFFFFF,
      body: SafeArea(child: Obx(() {
        if (controller.shortcutCategories.isEmpty) {
          return const SizedBox();
        }
        return RefreshIndicator(
          key: controller.indicatorKey,
          onRefresh: () => controller.getShortcuts(),
          child: controller.shortcutCategories.isEmpty ? const SizedBox() : _LinkageRollingPageState(),
          // Column(
          //     mainAxisSize: MainAxisSize.min,
          //     children: [
          //       // _searchView,
          //       // const SizedBox(
          //       //   height: 8,
          //       // ),
          //       Expanded(child: _LinkageRollingPageState()),
          //     ],
          //   )
        );
      })),
    );
  }

  Widget get _searchView {
    return Builder(builder: (context) {
      return Container(
        height: 34,
        margin: const EdgeInsets.fromLTRB(15, 0, 15, 0),
        padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(17), color: MColor.xFFF6F6F6),
        child: Row(
          children: [
            AppIcons.icon(0xe60d, size: 18, color: MColor.xFFB8B8B8),
            const SizedBox(
              width: 10,
            ),
            Text(S.of(Get.context!).search, style: TextStyle(color: MColor.xFFA6A6A6, fontSize: 14))
          ],
        ),
      );
    });
  }
}

//修改这里的数据会更改一页高度
const double leftItemHeight = 66; // 左边一个item的高度
const double rightItemTitleHeight = 38; // 右边一个标题的高度
const double leftWidgetWidth = 88; // 左边组件占据空间的比例
const int rightWidgetFlex = 9; // 右边组件占据空间的比例
const int rightGridViewCrossAxisCount = 3; //右侧GirdView一排数量，这里测试时为3

class _LinkageRollingPageState extends GetView<ShortcutController> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: leftWidgetWidth,
          child: _leftListViewWidget,
        ),
        Expanded(
          child: _rightListViewWidget(),
        ),
      ],
    );
  }

  //左侧分类栏
  Widget get _leftListViewWidget {
    return Container(
      alignment: Alignment.topCenter,
      margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
      color: Colors.white,
      child: ListView.builder(
        controller: controller.leftListController,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () {
              controller.onLeftClicked(index);
            },
            behavior: HitTestBehavior.opaque,
            child: Builder(builder: (context) {
              var topRightRadius = index == 0 ? Radius.circular(10) : Radius.zero;
              var bottomRightRadius = index == controller.shortcutCategories.length - 1 ? Radius.circular(10) : Radius.zero;
              return Obx(() {
                return Container(
                  height: leftItemHeight,
                  padding: const EdgeInsets.fromLTRB(1, 0, 0, 0),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      color: controller.indexLeft.value != index ? MColor.xFFF6F6F6 : MColor.xFFFFFFFF,
                      borderRadius: BorderRadius.only(topRight: topRightRadius, bottomRight: bottomRightRadius)),
                  child: Text(
                    controller.shortcutCategories[index].name,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
                  ),
                );
              });
            }),
          );
        },
        itemCount: controller.shortcutCategories.length,
      ),
    );
  }

  //右侧项目栏 - ListView 版本
  Widget _rightListViewWidget() {
    return LayoutBuilder(builder: (context, constraints) {
      return Container(
        alignment: Alignment.topCenter,
        padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
        color: Colors.transparent,
        child: ListView.separated(
          separatorBuilder: (context, index) {
            return Container(
              child: Divider(
                height: 80,
                thickness: 1,
                color: MColor.xFFF6F6F6,
              ),
            );
          },
          itemBuilder: (context, index) {
            double width = (constraints.maxWidth - 20 - 20) / 3;
            double ratio = width / 76;
            double remainHeight = 0;
            if (index == controller.shortcutCategories.length - 1) {
              int length = controller.shortcutCategories[index].children.length;
              int lines = length ~/ 3 + (length % 3 == 0 ? 0 : 1);
              remainHeight = constraints.maxHeight - lines * 53 - (lines - 1) * 20 - 40;
            }
            return Column(
              children: [
                GridView.builder(
                  padding: const EdgeInsets.all(0),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: controller.shortcutCategories[index].children.length,
                  itemBuilder: (context, childIdx) {
                    var entrance = controller.shortcutCategories[index].children[childIdx];
                    return GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        // RouterJump.jump('${Routes.INSP_ORDER_MANAGE}?index=2');
                        RouterJump.jump(entrance.route);
                      },
                      child: Container(
                        height: 53,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Builder(builder: (context) {
                              return AppIcons.icon(int.tryParse(entrance.icon, radix: 16), size: 24, color: MColor.xFFEA6A46);
                            }),
                            const SizedBox(
                              height: 12,
                            ),
                            Text(
                              entrance.name,
                              style: const TextStyle(fontSize: 14, color: MColor.xFF2A2A2A, height: 1.5),
                              maxLines: 1,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: ITEM_ROW_COUNT, mainAxisSpacing: 20, childAspectRatio: ratio, crossAxisSpacing: 10),
                ),
                if (index == controller.shortcutCategories.length - 1) ...{
                  SizedBox(
                    height: remainHeight,
                  )
                }
              ],
            );
          },
          itemCount: controller.shortcutCategories.length,
          controller: controller.rightListController,
        ),
      );
    });
  }
}
