import 'dart:convert';

import 'package:inspector/app/data/chat_message.dart';
import 'package:inspector/app/data/conversation_entity.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/receive_message.g.dart';

@JsonSerializable()
class ReceiveMessage extends ChatMessage {
  ReceiveMessage();

  ChatUserEntity? sender;
  int? recordId;

  factory ReceiveMessage.fromJson(Map<String, dynamic> json) => $ReceiveMessageFromJson(json);

  Map<String, dynamic> toJson() => $ReceiveMessageToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
