import 'dart:convert';

import 'package:get/get.dart';
import 'package:inspector/app/data/group_entity.dart';
import 'package:inspector/app/data/receive_message.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/tools/public_provider.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/conversation_entity.g.dart';
import 'package:inspector/generated/l10n.dart';

@JsonSerializable()
class PublishConvEntity {
  int? total;
  int? totalUnread;
  List<PublishConvRows>? rows;
  List<NoticeConvRows>? notices;
  int? noticeTotal;
  int? noticeUnread;

  PublishConvEntity();

  factory PublishConvEntity.fromJson(Map<String, dynamic> json) => $PublishConvEntityFromJson(json);

  Map<String, dynamic> toJson() => $PublishConvEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

abstract class ConvRows {
  int? id;
  int? type; //1用户，2组群
  String? updatedAt;
  String? createdAt;
  int? unread;

  String getConvAvatar();
  String getConvName();
  String getMsgDesc();
}

@JsonSerializable()
class NoticeConvRows extends ConvRows {
  String? title;
  String? msg;
  int? status;

  int? urlType;
  String? url;

  NoticeConvRows();

  factory NoticeConvRows.fromJson(Map<String, dynamic> json) => $NoticeConvRowsFromJson(json);

  Map<String, dynamic> toJson() => $NoticeConvRowsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  @override
  String getConvAvatar() {
    if (type == 2) {
      return '${Server.domain}static/img/c_email.png';
    } else if (type == 3) {
      return '${Server.domain}static/img/c_order.png';
    } else if (type == 4) {
      return '${Server.domain}static/img/c_wallet.png';
    } else if (type == 5) {
      return '${Server.domain}static/img/c_uinfo.png';
    } else {
      return '';
    }
  }

  @override
  String getMsgDesc() {
    return msg ?? '';
  }

  @override
  String getConvName() {
    if (type == 2) {
      return S.of(Get.context!).message_email;
    } else if (type == 3) {
      return S.of(Get.context!).message_order;
    } else if (type == 4) {
      return S.of(Get.context!).message_wallet;
    } else if (type == 5) {
      return S.of(Get.context!).message_user;
    } else {
      return '';
    }
  }
}

@JsonSerializable()
class PublishConvRows extends ConvRows {
  String? avatar;
  String? name;
  GroupInfoEntity? groupInfo;
  ReceiveMessage? lastMsg;
  int? orderId;
  int? id;
  int? status;
  String? datetime;

  PublishConvRows();

  factory PublishConvRows.fromJson(Map<String, dynamic> json) => $PublishConvRowsFromJson(json);

  Map<String, dynamic> toJson() => $PublishConvRowsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  @override
  String getConvAvatar() {
    return avatar ?? '';
  }

  @override
  String getMsgDesc() {
    if (lastMsg != null) {
      if (lastMsg!.type == 5) {
        return '[订单消息]';
      } else if (lastMsg!.type == 2) {
        return '[文件消息]';
      } else if (lastMsg!.type == 3) {
        return '[图片消息]';
      } else {
        return lastMsg!.msg ?? '';
      }
    } else {
      return '';
    }
  }

  @override
  String getConvName() {
    return name ?? '';
  }
}

@JsonSerializable()
class ChatMessageEntity {
  String? msg;
  int? msgId;
  String? datetime;
  UserInfoEntity? sender;

  ChatMessageEntity();
  factory ChatMessageEntity.fromJson(Map<String, dynamic> json) => $ChatMessageFromJson(json);

  Map<String, dynamic> toJson() => $ChatMessageToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AdminListEntity {
  int? total;
  List<ChatUserEntity>? admins;

  AdminListEntity();
  factory AdminListEntity.fromJson(Map<String, dynamic> json) => $AdminListFromJson(json);

  Map<String, dynamic> toJson() => $AdminListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ChatUserEntity {
  ChatUserEntity();

  int? userId;
  String? name;
  String? head;
  int? imUserId;
  UserRole? role;

  factory ChatUserEntity.fromJson(Map<String, dynamic> json) {
    ChatUserEntity entity = $ChatUserFromJson(json);
    if (json['role'] != null) entity.role = UserRole.values.firstWhere((e) => e.key == json['role']);
    return entity;
  }

  Map<String, dynamic> toJson() => $ChatUserToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
/*@JsonSerializable()
class RecordRows {
  int? id;
  String? brief;
  RecordRows();

  factory RecordRows.fromJson(Map<String, dynamic> json) =>
      $RecordRowsFromJson(json);

  Map<String, dynamic> toJson() => $RecordRowsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}*/
