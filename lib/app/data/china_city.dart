class AreaModel {
  late int areaId;
  late String areaName;
  late int parentId;
  late String pinyin;
  List<AreaModel>? areas;

  AreaModel();

  static AreaModel fromJson(Map<String, dynamic> json) {
    AreaModel model = AreaModel();

    model.areaId = json['areaId'];
    model.areaName = json['areaName'];
    model.parentId = json['parentId'];
    model.pinyin = json['pinyin'];
    if (json.containsKey('child')) {
      model.areas = AreaModel.fromMapList(json['child']);
    }

    return model;
  }

  static List<AreaModel> fromMapList(dynamic mapList) {
    List<AreaModel> models = [];
    for (int i = 0; i < mapList.length; i++) {
      models.add(fromJson(mapList[i]));
    }

    return models;
  }
}

class ChinaArea {
  late int areaId;
  late String areaName;
  late int parentId;
  late String pinyin;

  late List<AreaModel> cities;

  ChinaArea();

  static ChinaArea fromJson(Map<String, dynamic> json) {
    ChinaArea model = ChinaArea();

    model.areaId = json['areaId'];
    model.areaName = json['areaName'];
    model.parentId = json['parentId'];
    model.pinyin = json['pinyin'];
    model.cities = AreaModel.fromMapList(json['child']);

    return model;
  }

  static List<ChinaArea> fromMapList(dynamic mapList) {
    List<ChinaArea> models = [];
    for (int i = 0; i < mapList.length; i++) {
      models.add(fromJson(mapList[i]));
    }

    return models;
  }
}
