import 'dart:convert';

import 'package:get/get.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/order_list_entity.g.dart';

import '../../generated/l10n.dart';

@JsonSerializable()
class OrderListEntity {
  int? total;
  List<OrderListRows>? rows;

  OrderListEntity();

  factory OrderListEntity.fromJson(Map<String, dynamic> json) => $OrderListEntityFromJson(json);

  Map<String, dynamic> toJson() => $OrderListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AdminNoteResp {
  int total = 0;
  List<AdminNote> list = [];
  AdminNoteResp();

  factory AdminNoteResp.fromJson(Map<String, dynamic> json) {
    var model = $AdminNoteRespFromJson(json);
    return model;
  }

  Map<String, dynamic> toJson() => $AdminNoteRespToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AdminNote {
  AdminNote();
  int id = 0;
  int pid = 0;
  int userId = 0;
  int userType = 0;
  int orderId = 0;
  String msg = '';
  String createdAt = '';
  UserInfoEntity? admin;

  factory AdminNote.fromJson(Map<String, dynamic> json) {
    var model = $AdminNoteFromJson(json);
    return model;
  }

  Map<String, dynamic> toJson() => $AdminNoteToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderListRows {
  int? id;
  String? provinceCity;
  String? distance;
  String? award;
  String? date;
  String? address;
  String? inspectionDate;
  int? type;
  int? inspNumber;
  int? inspDay;
  String? reportType;
  String? productName;
  int? applyStatus; //0抢单中，1已申请，2已派单，4已过期
  int? applyNum;
  double? lon;
  double? lat;
  int? multipleDate;
  bool? isSelf;
  List<OrderListRows>? children;
  bool connectLast = false;
  bool connectNext = false;
  String? orderNumber;
  String? salesman;
  List<AdminNote> adminNote = [];

  int? flag;
  int? orderStatus;
  int? refundStatus;
  int? payStatus;

  OrderListRows();

  factory OrderListRows.fromJson(Map<String, dynamic> json) {
    var model = $OrderListRowsFromJson(json);
    model.orderNumber = json['ordersNum'];
    return model;
  }

  Map<String, dynamic> toJson() => $OrderListRowsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  OrderStatus calculateStatus() {
    if (flag == 1) {
      return OrderStatus.WaitPay;
    } else if (flag == 6) {
      return OrderStatus.Cancelled; // 已取消
    } else if (flag == 5) {
      if ((orderStatus ?? 0) < 0 && refundStatus == 1) {
        return OrderStatus.CancelledAndRefundPending; // 已取消 退款待审核
      } else if ((orderStatus ?? 0) < 0) {
        return OrderStatus.Cancelled; // 已取消
      } else if ((orderStatus ?? 0) > 0) {
        if (refundStatus == 1) {
          return OrderStatus.RefundPending; //退款待审核
        } else if (refundStatus == 2 && payStatus == 3) {
          return OrderStatus.RefundPartial; //部分退款
        } else if (refundStatus == 3) {
          return OrderStatus.RefundDenied; //已拒绝退款
        }
      }
    } else if (refundStatus == 1) {
      return OrderStatus.RefundPending; //退款待审核
    } else if (flag == 2 && payStatus == 1) {
      return OrderStatus.WaitPay; //待支付
    } else if (flag == 2 && payStatus == 2) {
      return OrderStatus.WaitDispatch; //待派单
    } else if (flag == 3 && (payStatus == 2 || payStatus == 3)) {
      return OrderStatus.Inspecting; //验货中
    } else if (flag == 4) {
      return OrderStatus.Finished; //已完成
    }
    return OrderStatus.Unknown;
  }
}

enum OrderStatus {
  Unknown(0, 'order_status_unknown'),
  WaitPay(1, 'order_wait_pay'),
  Cancelled(2, 'order_cancelled'),
  CancelledAndRefundPending(3, 'order_cancelled_refund_pending'),
  RefundPending(4, 'order_refund_pending'),
  RefundPartial(5, 'order_refund_partial'),
  RefundDenied(6, 'order_refund_denied'),
  WaitDispatch(7, 'order_wait_dispatch'),
  Inspecting(8, 'order_doing'),
  Finished(9, 'order_finished');

  final int status;
  final String value;
  const OrderStatus(this.status, this.value);
}
