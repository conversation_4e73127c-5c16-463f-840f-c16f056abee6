import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/group_entity.g.dart';

@JsonSerializable()
class GroupEntity {
  GroupInfoEntity? groupInfo;
  List<GroupMemberEntity>? members;

  GroupEntity();

  factory GroupEntity.fromJson(Map<String, dynamic> json) => $GroupEntityFromJson(json);

  Map<String, dynamic> toJson() => $GroupEntityToJson(this);
}

@JsonSerializable()
class GroupInfoEntity {
  int? id;
  String? name;
  String? avatar;
  int? orderId;
  int? founder;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  List<GroupMemberEntity>? members;

  GroupInfoEntity();

  factory GroupInfoEntity.fromJson(Map<String, dynamic> json) => $GroupInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => $GroupInfoEntityToJson(this);
}

@JsonSerializable()
class GroupMemberEntity {
  int? imUserId; //对应后端返回的user_id字段，实际后端是取的im_user_id
  int? userId; //对应后端返回的uid字段，实际后端是取的user_id
  String? nick;
  String? head;
  int? role; //1-用户 2-验货员 3-工厂 4管理员 5系统
  GroupMemberEntity();

  factory GroupMemberEntity.fromJson(Map<String, dynamic> json) => $GroupMemberEntityFromJson(json);

  Map<String, dynamic> toJson() => $GroupMemberEntityToJson(this);
}
