import 'dart:convert';

import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/order_info_entity.g.dart';
import 'package:inspector/generated/l10n.dart';

@JsonSerializable()
class OrderInfoEntity {
  int? orderId;
  int? userId;
  int? userAccount;
  int? type;
  String? inspectTime;
  String? country;
  String? city;
  String? province;
  List<OrderProduct>? orderProducts;
  String? ordersNum;

  OrderInfoEntity();

  factory OrderInfoEntity.fromJson(Map<String, dynamic> json) {
    var model = $OrderInfoEntityFromJson(json);

    return model;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderProduct {
  int? id;
  int? orderId;
  int? categoryId;
  String? productName;
  String? productNameEn;
  List<ProductModel>? productModel;

  OrderProduct();

  factory OrderProduct.fromJson(Map<String, dynamic> json) {
    var model = $OrderProductEntityFromJson(json);

    return model;
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> map = <String, dynamic>{};
    map['id'] = id;
    map['order_id'] = orderId;
    map['category_id'] = categoryId;
    map['product_name'] = productName;
    map['product_name_en'] = productNameEn;
    map['model'] = productModel?.map((e) => e.toJson()).toList();
    return map;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ProductModel {
  int? id;
  int? orderId;
  int? orderProductId;
  String? mark;
  String? unit;
  int? amount;
  String? pic;

  ProductModel();

  String get lostName {
    if (mark?.isEmpty ?? true) {
      return S.current.ai_add_product_model_full;
    }
    if (unit?.isEmpty ?? true) {
      return S.current.ai_add_product_unit;
    }
    if (amount == null || (amount ?? 0) <= 0) {
      return S.current.ai_add_product_num;
    }
    if (pic?.isEmpty ?? true) {
      return S.current.ai_inspection_image;
    }
    return '';
  }

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    var model = $ProductModelEntityFromJson(json);

    return model;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> map = <String, dynamic>{};
    map['id'] = id;
    map['order_id'] = orderId;
    map['orders_product_id'] = orderProductId;
    map['mark'] = mark;
    map['unit'] = unit;
    map['amount'] = amount;
    map['pic'] = pic;
    return map;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
