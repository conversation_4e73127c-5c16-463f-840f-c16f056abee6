import 'dart:convert';

import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/user_profile_resp.g.dart';

@JsonSerializable()
class UserProfileResp {

  UserInfoEntity? userInfo;

  InspectorProfile? inspectorProfile;

  UserProfileResp();

  factory UserProfileResp.fromJson(Map<String, dynamic> json) {
    var entity = $UserProfileRespFromJson(json);
    return entity;
  }

  Map<String, dynamic> toJson() {
    var map = $UserProfileRespToJson(this);
    return map;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class InspectorProfile {
  InspectorProfile();

  String? initialPrice;//人民币验货费用
  String? initialMoney;//美金验货费用
  UserAccountType? accountType;

  factory InspectorProfile.fromJson(Map<String, dynamic> json) {
    var entity = $InspectorProfileFromJson(json);
    return entity;
  }

  Map<String, dynamic> toJson() {
    var map = $InspectorProfileToJson(this);
    return map;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}