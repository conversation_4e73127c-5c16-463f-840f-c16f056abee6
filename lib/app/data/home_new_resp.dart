import 'package:inspector/app/data/mall_entity.dart';
import 'package:inspector/app/data/order_list_entity.dart';
import 'package:inspector/app/data/purchase_entity.dart';
import 'package:inspector/app/modules/widgets/banner_view.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/home_new_resp.g.dart';

@JsonSerializable()
class HomeNewResp {
  List<HomeEntrance> entrances = [];
  List<HomeBanner> banners = [];
  List<HomeNotice> notices = [];

  HomeInspect? homeInspect;
  HomePurchase? homePurchase;
  HomeMall? homeMall;

  HomeNewResp();

  factory HomeNewResp.fromJson(Map<String, dynamic> json) => $HomeNewRespFromJson(json);

  Map<String, dynamic> toJson() => $HomeNewRespToJson(this);
}

@JsonSerializable()
class HomeEntrance {
  String name = '';
  String icon = '';
  String image = '';
  String url = '';

  HomeEntrance();

  factory HomeEntrance.fromJson(Map<String, dynamic> json) => $HomeEntranceFromJson(json);

  Map<String, dynamic> toJson() => $HomeEntranceToJson(this);
}

@JsonSerializable()
class HomeBanner {
  String image = '';
  String url = '';
  HomeBanner();

  factory HomeBanner.fromJson(Map<String, dynamic> json) => $HomeBannerFromJson(json);

  Map<String, dynamic> toJson() => $HomeBannerToJson(this);

  @override
  String toString() {
    return image;
  }
}

@JsonSerializable()
class HomeNotice {
  String title = '';
  String message = '';
  String url = '';
  int urlType = 0; //0内部 1外部
  HomeNotice();

  factory HomeNotice.fromJson(Map<String, dynamic> json) => $HomeNoticeFromJson(json);

  Map<String, dynamic> toJson() => $HomeNoticeToJson(this);
}

@JsonSerializable()
class HomeInspect {
  String name = '';
  List<OrderListRows> inspects = [];
  HomeInspect();

  factory HomeInspect.fromJson(Map<String, dynamic> json) => $HomeInspectFromJson(json);

  Map<String, dynamic> toJson() => <String, dynamic>{};
}

@JsonSerializable()
class HomeMall {
  String name = '';
  HomeMall();

  List<GoodsDetailEntity> list = [];

  factory HomeMall.fromJson(Map<String, dynamic> json) => $HomeMallFromJson(json);

  Map<String, dynamic> toJson() => <String, dynamic>{};
}

@JsonSerializable()
class HomePurchase {
  String name = '';
  List<PurchaseItemEntity> purchases = [];
  HomePurchase();

  factory HomePurchase.fromJson(Map<String, dynamic> json) => $HomePurchaseFromJson(json);

  Map<String, dynamic> toJson() => <String, dynamic>{};
}
