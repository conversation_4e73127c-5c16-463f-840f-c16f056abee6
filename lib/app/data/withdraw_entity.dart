import 'dart:convert';

import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/withdraw_entity.g.dart';

@JsonSerializable()
class WithdrawEntity {
  int? type; //1-人民币 2-美元
  double? samount; //实付金额
  double? amount; //提现金额
  double? balance; //账户余额
  int? status; //状态 1-审核中 2-审核通过 3-审核不通过
  String? remark; //备注
  int? makeStatus; //"打款状态 1-未打款 2-已打款  3-驳回"
  int? cashMethod; //提现方式 1-系统充值 2-PayPai 3-Veem 4-微信 5-支付宝 6银行卡  7:其他
  int? auditor;
  String? paymentCode;
  String? bankId;
  String? refuseTip;
  int? payAuditor;
  String? accNum;
  String? skName;
  String? fileUrl;
  String? createAt;
  String? updateAt;
  int? payTime;
  int? checkTime;
  int? payAuto;

  WithdrawEntity();

  factory WithdrawEntity.fromJson(Map<String, dynamic> json) => $WithdrawEntityFromJson(json);

  Map<String, dynamic> toJson() => $WithdrawEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
