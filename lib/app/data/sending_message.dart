import 'dart:convert';

import 'package:inspector/app/data/chat_message.dart';
import 'package:inspector/app/data/conversation_entity.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/sending_message.g.dart';

@JsonSerializable()
class SendingMessage extends ChatMessage {
  late String scene; //normal or group;
  late int type; //消息类型

  bool isTimeout = false;

  Map<String, dynamic> toJson() => $SendingMessageToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
