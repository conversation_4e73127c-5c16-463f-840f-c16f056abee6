import 'dart:convert';

import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/user_account_entity.g.dart';

@JsonSerializable()
class UserAccountEntity {
  UserAccountEntity();
  @JSONField(name: 'user_id')
  int? userId;
  @JSONField(name: 'amount_type')
  int? amountType;
  @JSONField(name: 'amount')
  String? amount;
  @JSONField(name: 'frozen_amount')
  String? frozenAmount;
  @JSONField(name: 'us_amount')
  String? amountUs;
  @JSONField(name: 'frozen_us_amount')
  String? frozenAmountUs;
  @J<PERSON>NField(name: 'integral')
  int? integral;
  @JSONField(name: 'inspection_fee')
  String? inspectionFee;
  @JSONField(name: 'us_inspection_fee')
  String? inspectionFeeUs;
  @JSONField(name: 'inspection_remark')
  String? inspectionRemark;
  @JSONField(name: 'ali_img')
  String? aliImg;
  @JSONField(name: 'wx_img')
  String? wxImg;
  @JSONField(name: 'open_bank')
  String? openBank;
  @JSONField(name: 'bank_name')
  String? bankName;
  @JSONField(name: 'bank_card')
  String? bankCard;
  @JSONField(name: 'last_amount')
  String? lastAmount;
  @JSONField(name: 'paypal')
  String? paypal;
  @JSONField(name: 'veem')
  String? veem;
  @JSONField(name: 'username')
  String? username;

  factory UserAccountEntity.fromJson(Map<String, dynamic> json) {
    return $UserAccountEntityFromJson(json);
  }
}
