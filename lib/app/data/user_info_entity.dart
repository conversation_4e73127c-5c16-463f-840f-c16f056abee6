// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/user_info_entity.g.dart';

@JsonSerializable()
class UserInfoEntity {
  String? head;
  String? name;
  String? nick;
  dynamic position;
  String? phone;
  String? email;
  String? wechatNum;
  //mydev
  String? im_token;
  //0: 未提交 1:未审核 2：已审核  3:审核被拒
  int? checkStatus;
  int? uid;
  bool get emailHold {
    if (email == null || email!.isEmpty || email!.endsWith('@phone.com')) {
      return false;
    }
    return true;
  }

  UserRole? role;

  int? imUserId;
  int? grade;
  int? is_auth;

  int? quotaId;
  double? quotaUsd;
  double? quotaRmb;

  String? country;
  String? city;
  String? province;

  UserInfoEntity();

  factory UserInfoEntity.fromJson(Map<String, dynamic> json) {
    var entity = $UserInfoEntityFromJson(json);
    entity.imUserId = json['im_user_id'];
    if (json['role'] != null) entity.role = UserRole.values.firstWhere((e) => e.key == json['role']);
    return entity;
  }

  Map<String, dynamic> toJson() {
    var map = $UserInfoEntityToJson(this);
    map['im_user_id'] = imUserId;
    if (role != null) map['role'] = role!.key;
    return map;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
