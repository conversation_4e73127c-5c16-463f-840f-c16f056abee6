import 'dart:convert';

import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/report_entity.g.dart';

@JsonSerializable()
class ReportEntity {
  ReportModel? report;

  dynamic auditor;

  //报告状态：0-未上传1-待审核2-通过3-不通过  5上传了
  int? status;

  ReportEntity();

  factory ReportEntity.fromJson(Map<String, dynamic> json) => $ReportEntityFromJson(json);

  Map<String, dynamic> toJson() => $ReportEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ReportModel {
  List<InspectorReport>? userReports;
  List<InspectorReport>? formalReports;

  ReportModel();

  factory ReportModel.fromJson(Map<String, dynamic> json) => $ReportModelFromJson(json);
}

@JsonSerializable()
class InspectorReport {
  ReportUserInfo? userInfo;
  List<String>? inspPictures;
  List<String>? lianzheng;
  List<String>? drafts;

  InspectorReport();

  factory InspectorReport.fromJson(Map<String, dynamic> json) => $InspectorReportFromJson(json);
}

@JsonSerializable()
class ReportUserInfo {
  int? userId;
  String? nick;
  String? head;

  ReportUserInfo();

  factory ReportUserInfo.fromJson(Map<String, dynamic> json) => $ReportUserInfoFromJson(json);
}
