import 'dart:convert';

import 'package:inspector/app/data/order_detail_entity.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/data/user_account.dart';
import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/models/inspector.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/assign_info_entity.g.dart';

@JsonSerializable()
class AssignResp {
  int? total;
  int? assignCount;
  List<AssignInfoEntity>? assignInfos;
  OrderInfoEntity? orderInfo;

  AssignResp();

  factory AssignResp.fromJson(Map<String, dynamic> json) {
    var entity = $AssignRespFromJson(json);
    return entity;
  }
}

@JsonSerializable()
class AssignInfoEntity {
  UserInfoEntity? inspector;

  double? distance;
  ApplyRecord? applyRecord;
  OrderInspector? orderInspector;
  UserAccountEntity? userAccount;

  int? orderCount; //下单次数
  int? inspectCount; //验货次数
  int? socialSecurity;
  int? account;
  String? cost;

  bool hasAssigned = false;

  AssignInfoEntity();

  factory AssignInfoEntity.fromJson(Map<String, dynamic> json) {
    var entity = $AssignInfoEntityFromJson(json);
    return entity;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ApplyRecord {
  ApplyRecord();

  @JSONField(name: 'id')
  int? id;
  @JSONField(name: 'orders_id')
  int? orderId;
  @JSONField(name: 'inspector')
  int? inspector;
  @JSONField(name: 'scene')
  int? scene;
  @JSONField(name: 'app_fee')
  String? appFee;
  @JSONField(name: 'actual_fee')
  String? actualFee;
  @JSONField(name: 'account')
  int? account;
  @JSONField(name: 'status')
  int? status;
  @JSONField(name: 'admin_id')
  int? adminId;
  @JSONField(name: 'admin_name')
  String? adminName;
  @JSONField(name: 'admin_time')
  String? adminTime;
  @JSONField(name: 'mark')
  String? mark;
  @JSONField(name: 'create_at')
  String? createAt;
  @JSONField(name: 'update_at')
  String? updateAt;

  factory ApplyRecord.fromJson(Map<String, dynamic> json) {
    var record = $ApplyRecordFromJson(json);
    return record;
  }
}

@JsonSerializable()
class OrderInspector {
  OrderInspector();

  int? id;
  int? ordersId;
  int? userId;
  String? cost;
  int? account;
  int? status;
  String? addCost;
  int? inspectionStatus;
  String? createdAt;
  String? updatedAt;
  int? isFrozen;
  int? frozenTime;
  int? unfrozenTime;
  String? subCost;
  String? remark;

  factory OrderInspector.fromJson(Map<String, dynamic> json) {
    var entity = $OrderInspectorFromJson(json);
    return entity;
  }
}

@JsonSerializable()
class AssignOrders {
  int total = 0;
  List<OrderDetailEntity> data = [];
}
