import 'dart:convert';

import 'package:inspector/app/data/user_info_entity.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/purchase_entity.g.dart';

@JsonSerializable()
class PurchaseClassEntity {
  int id = 0;
  int? pid;
  String name = '';
  int? status;
  List<PurchaseClassEntity> children = [];

  PurchaseClassEntity();

  factory PurchaseClassEntity.fromJson(Map<String, dynamic> json) => $PurchaseClassEntityFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseClassEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class PurchaseResp {
  PurchaseResp();

  factory PurchaseResp.fromJson(Map<String, dynamic> json) => $PurchaseRespFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseRespToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  List<PurchaseItemEntity> list = [];
  List<PurchaseClassEntity> classList = [];
  int total = 0;
}

@JsonSerializable()
class PurchaseTag {
  PurchaseTag();

  factory PurchaseTag.fromJson(Map<String, dynamic> json) => $PurchaseTagFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseTagToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  String? key;
  String? val;
}

@JsonSerializable()
class PurchaseItemEntity {
  PurchaseItemEntity();

  factory PurchaseItemEntity.fromJson(Map<String, dynamic> json) => $PurchaseItemEntityFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseItemEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  int? id;
  int? userId;
  int? classId;
  String? title;
  String? product;
  int? replyId;
  List<PurchaseTag> tags = [];
  List<String> pic = [];
  String? country;
  int? count;
  String? price;
  int? priceAccount;
  UserAccountType account = UserAccountType.CNY;
  String bounty = '';
  String? paid;
  int? payStatus;
  int? status; //-1 已删除 0未发布 1 已发布 2 已完结 3 已下架
  int? isSheild;
  int? verify; //0审核中 1 已通过 2未通过
  int reading = 0;
  int reply = 0;
  UserInfoEntity? provider;
  String updatedAt = '';
  String? pastTime;
  String? contentPrev;
  String? addTime;
  UserInfoEntity? user;
}

@JsonSerializable()
class PurchaseDetailResp {
  PurchaseDetailResp();

  factory PurchaseDetailResp.fromJson(Map<String, dynamic> json) => $PurchaseDetailRespFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseDetailRespToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  PurchaseDetailEntity? detail;
  PurchaseReplyResp? replys;
  int purAuthType = 0;
}

@JsonSerializable()
class PurchaseReplyResp {
  PurchaseReplyResp();

  factory PurchaseReplyResp.fromJson(Map<String, dynamic> json) => $PurchaseReplyRespFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseReplyRespToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  List<PurchaseReplyEntity> list = [];
  int page = 0;
  int pageSize = 0;
  int total = 0;
}

@JsonSerializable()
class PurchaseDetailEntity {
  PurchaseDetailEntity();

  factory PurchaseDetailEntity.fromJson(Map<String, dynamic> json) => $PurchaseDetailEntityFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseDetailEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  int id = 0;
  int classId = 0;
  String product = '';
  String title = '';
  String createdAt = '';
  String updatedAt = '';
  List<String> pic = [];
  double bounty = 0.0;
  int? justLandlord;
  String? deadline;
  String? province;
  String? city;
  String? count;
  UserAccountType account = UserAccountType.CNY;
  UserInfoEntity? user;
  PurchaseReplyEntity? replyInfo;
}

@JsonSerializable()
class PurchaseReplyEntity {
  PurchaseReplyEntity();

  factory PurchaseReplyEntity.fromJson(Map<String, dynamic> json) => $PurchaseReplyEntityFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseReplyEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  int id = 0;
  int? pid;
  int? userId;
  int? purchase_id;
  int type = 1; //1 无paidcontent 2 有
  int? reply_user_id;
  String content = '';

  PurchasePaidContentEntity? paidContent;
  PurchasePayInfoEntity? payInfo;
  String price = '';
  UserAccountType account = UserAccountType.CNY;
  String ip = '';
  String ipAddress = '';
  int? status;
  int? verify;
  String? verify_at;
  int? verify_admin;
  String? admin_remark;
  String createdAt = '';
  String updatedAt = '';
  List<PurchaseReplyEntity> reply = [];
  int? reply_count;
  int appealStatus = 0; //0未申诉 1已申诉 2已通过 3已拒绝 4已取消
  int evaluateStatus = 0;
  int evaluateCount = 0;
  double evaluate = 0;
  PurchaseMyEvaluateEntity? myEvaluate;
  int zan = 0; //赞的个数
  int zanStatus = 0; //自己是否有赞过

  UserInfoEntity? replyTo;
  UserInfoEntity? user;
}

@JsonSerializable()
class PurchaseMyEvaluateEntity {
  double? score;
  String? note;
  PurchaseMyEvaluateEntity();

  factory PurchaseMyEvaluateEntity.fromJson(Map<String, dynamic> json) => $PurchaseMyEvaluateEntityFromJson(json);

  Map<String, dynamic> toJson() => {};

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class PurchasePayInfoEntity {
  int payStatus = 0;
  PurchasePayInfoEntity();

  factory PurchasePayInfoEntity.fromJson(Map<String, dynamic> json) => $PurchasePayInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => {};

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class PurchaseCommentResp {
  PurchaseCommentResp();

  factory PurchaseCommentResp.fromJson(Map<String, dynamic> json) => $PurchaseCommentRespFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseCommentRespToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  int? maxCount;
  PurchaseReplyEntity? reply;
}

@JsonSerializable()
class PurchaseMyReplyResp {
  PurchaseMyReplyResp();

  factory PurchaseMyReplyResp.fromJson(Map<String, dynamic> json) => $PurchaseMyReplyRespFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseMyReplyRespToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  int total = 0;
  List<PurchaseMyReplyEntity> replies = [];
}

@JsonSerializable()
class PurchaseMyReplyEntity {
  PurchaseMyReplyEntity();

  factory PurchaseMyReplyEntity.fromJson(Map<String, dynamic> json) => $PurchaseMyReplyEntityFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseMyReplyEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  PurchaseReplyEntity? reply;
  PurchaseDetailEntity? topic;
}

@JsonSerializable()
class PurchasePaidContentEntity {
  PurchasePaidContentEntity();
  factory PurchasePaidContentEntity.fromJson(Map<String, dynamic> json) => $PurchasePaidContentEntityFromJson(json);

  Map<String, dynamic> toJson() => $PurchasePaidContentEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  String supplier = '';
  String contact = '';
  String phone = '';
  String email = '';
  String addr = '';
  String other = '';
  String lowPrice = '';
}

@JsonSerializable()
class PurchaseMyAppealResp {
  PurchaseMyAppealResp();
  factory PurchaseMyAppealResp.fromJson(Map<String, dynamic> json) => $PurchaseMyAppealRespFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseMyAppealRespToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  int total = 0;
  List<PurchaseAppealEntity> list = [];
}

@JsonSerializable()
class PurchaseAppealEntity {
  PurchaseAppealEntity();
  factory PurchaseAppealEntity.fromJson(Map<String, dynamic> json) => $PurchaseAppealEntityFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseAppealEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  int id = 0;
  int replyId = 0;
  String price = '';
  String note = '';
  int admin_id = 0;
  String admin_remark = '';
  String admin_note = '';
  String refund = '';
  String completed_time = ''; //管理员通过或者拒绝的时间
  int status = 0; //0未申诉 1已申诉 2已通过 3已拒绝 4已取消
  String created_at = ''; //创建申诉的时间
  String updated_at = ''; //取消申诉的时间
  PurchaseReplyEntity? replyInfo;
}
