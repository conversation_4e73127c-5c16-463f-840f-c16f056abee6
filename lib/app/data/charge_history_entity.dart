import 'dart:convert';

import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/charge_history_entity.g.dart';

@JsonSerializable()
class ChargeHistoryEntity {
  int? type; //1-人民币 2-美元
  int? rechargeMode; //
  int? rechargeNum; //
  double? rechargeAmount; //
  double? preBalance; //
  double? afterBalance; //
  int? status; //状态
  int? toStatus; //状态
  String? remark; //备注
  int? adminUserId; //
  int? auditor;
  String? voucher;
  String? createAt;
  String? updateAt;
  int? increase;

  ChargeHistoryEntity();

  factory ChargeHistoryEntity.fromJson(Map<String, dynamic> json) => $ChargeHistoryEntityFromJson(json);

  Map<String, dynamic> toJson() => $ChargeHistoryEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
