import 'dart:convert';
import 'dart:ui';

import 'package:get/get.dart';
import 'package:inspector/app/config/design.dart';
import 'package:inspector/app/tools/tools.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/bill_entity.g.dart';

import '../../generated/l10n.dart';
import 'order_detail_entity.dart';

@JsonSerializable()
class BillEntity {
  double? rmbExpenditureAccount;
  double? usaExpenditureAccount;
  double? rmbIncomeAccount;
  double? usaIncomeAccount;
  List<BillCapitalModelList>? capitalModelList;

  BillEntity();

  factory BillEntity.fromJson(Map<String, dynamic> json) => $BillEntityFromJson(json);

  Map<String, dynamic> toJson() => $BillEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class BillCapitalModelList {
  String? title;
  int? type; // 1-收入，2-支出，3-冻结，4-解冻，5-提现
  String? time;
  double? account;
  int? userAccount;
  double? balance;
  double? frozenBalance;
  int? orderId;
  OrderDetailEntity? orderInfo;

  BillCapitalModelList();

  factory BillCapitalModelList.fromJson(Map<String, dynamic> json) => $BillCapitalModelListFromJson(json);

  Map<String, dynamic> toJson() => $BillCapitalModelListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  String getTypeDesc() {
    logger.i('$type $title');
    return type == 1
        ? S.of(Get.context!).bill_income
        : (type == 2
            ? S.of(Get.context!).bill_outcome
            : (type == 3
                ? S.of(Get.context!).bill_freeze
                : (type == 4 ? S.of(Get.context!).bill_unfreeze : (type == 5 ? S.of(Get.context!).bill_withdraw : ''))));
  }

  Color getTypeDescColor() {
    return type == 1 ? MColor.skin : (type == 2 ? MColor.xFF40A900 : MColor.xFF565656);
  }
}
