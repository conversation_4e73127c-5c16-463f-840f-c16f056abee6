import 'dart:convert';

import 'package:inspector/app/data/bank_entity.dart';
import 'package:inspector/app/data/bind_pay_entity.dart';
import 'package:inspector/app/data/wallet_entity.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/wallet_info_entity.g.dart';

@JsonSerializable()
class WalletInfoEntity {
  WalletEntity? wallet;
  List<BankEntity>? bankList;
  List<BindPayEntity>? payList;

  WalletInfoEntity();

  factory WalletInfoEntity.fromJson(Map<String, dynamic> json) => $WalletInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => $WalletInfoEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
