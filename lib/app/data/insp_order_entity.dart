import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/insp_order_entity.g.dart';

@JsonSerializable()
class InspOrderResp {
  InspOrderResp();

  factory InspOrderResp.fromJson(Map<String, dynamic> json) => $InspOrderRespFromJson(json);

  Map<String, dynamic> toJson() => $InspOrderRespToJson(this);

  int total = 0;
  List<InspOrderEntity> list = [];
}

@JsonSerializable()
class InspOrderEntity {
  InspOrderEntity();

  factory InspOrderEntity.fromJson(Map<String, dynamic> json) => $InspOrderEntityFromJson(json);

  Map<String, dynamic> toJson() => $InspOrderEntityToJson(this);

  int orderId = 0;
  int type = 0;
  String ordersNum = '';
  String inspectTime = '';
  int sample = 0;
  String standard = '';
  String price = '';
  String pPrice = '';
  String inspAddress = '';
  String country = '';
  String province = '';
  String city = '';
  String area = '';
  List<String> productName = [];
}
