import 'dart:convert';

import 'package:inspector/app/data/user_account.dart';
import 'package:inspector/app/enums/account_type.dart';
import 'package:inspector/app/modules/purchase/purchase_pay/purchase_pay_controller.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/mall_entity.g.dart';

@JsonSerializable()
class GoodsDetailResp {
  GoodsDetailResp();

  factory GoodsDetailResp.fromJson(Map<String, dynamic> json) => $GoodsDetailRespFromJson(json);

  Map<String, dynamic> toJson() => {};

  GoodsDetailEntity? detail;
  ShopInfoEntity? shopInfo;
  List<SkuEntity> skus = [];
  List<SpecAttrEntity> specAttrs = [];
  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ShopInfoEntity {
  ShopInfoEntity();

  int id = 0;
  int userId = 0;
  String name = '';
  String evaluate = '';
  String createdAt = '';
  factory ShopInfoEntity.fromJson(Map<String, dynamic> json) => $ShopInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => {};
  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GoodsDetailEntity {
  GoodsDetailEntity();

  factory GoodsDetailEntity.fromJson(Map<String, dynamic> json) => $GoodsDetailEntityFromJson(json);

  Map<String, dynamic> toJson() => {};

  String title = '';
  String id = '';
  String skuId = '';
  int shopId = 0;
  int classId = 0;
  List<String> pic = [];
  String content = '';
  String product = '';
  String producing_area = '';
  String short_address = '';
  List<String> content_pics = [];
  int sales = 0;
  int stock = 0;

  String price = '';
  String unit = '';
  UserAccountType accountType = UserAccountType.CNY;

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SpecEntity {
  String id = '';
  String name = '';

  SpecEntity();

  factory SpecEntity.fromJson(Map<String, dynamic> json) => $SpecEntityFromJson(json);

  Map<String, dynamic> toJson() => {};

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SpecAttrEntity {
  SpecAttrEntity();
  String id = '';
  String name = '';
  int classId = 0;
  List<SpecEntity> specs = [];

  factory SpecAttrEntity.fromJson(Map<String, dynamic> json) => $SpecAttrEntityFromJson(json);

  Map<String, dynamic> toJson() => {};

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SkuEntity {
  SkuEntity();

  String id = '';
  String goodsId = '';
  UserAccountType accountType = UserAccountType.CNY;
  String money = '';

  List<SkuSpecKV> specs = [];
  List<String> pic = [];
  String unit = '';
  int sales = 0;
  int quantity = 0;
  int min = 0;
  int max = 0;
  String finalPrice = '';

  factory SkuEntity.fromJson(Map<String, dynamic> json) => $SkuEntityFromJson(json);

  Map<String, dynamic> toJson() => {};

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SkuSpecKV {
  String id = '';
  String vId = '';

  SkuSpecKV();
  factory SkuSpecKV.fromJson(Map<String, dynamic> json) => $SkuSpecKVFromJson(json);

  Map<String, dynamic> toJson() => {};

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class MallAddressEntity {
  MallAddressEntity();
  int id = 0;
  String note = '';
  String name = '';
  String phone = '';
  String country = '';
  String province = '';
  String city = '';
  String area = '';
  String address = '';
  bool isDefault = false;
  factory MallAddressEntity.fromJson(Map<String, dynamic> json) => $MallAddressEntityFromJson(json);

  Map<String, dynamic> toJson() => {};

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class MallAddressListResp {
  MallAddressListResp();
  int total = 0;
  List<MallAddressEntity> list = [];
  factory MallAddressListResp.fromJson(Map<String, dynamic> json) => $MallAddressListRespFromJson(json);

  Map<String, dynamic> toJson() => {};

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderSubmitEntity {
  OrderSubmitEntity();
  String skuId = '';
  int count = 0;
  String remark = '';

  factory OrderSubmitEntity.fromJson(Map<String, dynamic> json) => $OrderSubmitEntityFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    data['sku_id'] = skuId;
    data['count'] = count;
    data['remark'] = remark;
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderPayEntity {
  OrderPayEntity();

  factory OrderPayEntity.fromJson(Map<String, dynamic> json) => $OrderPayEntityFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

  int id = 0;
  String sn = '';
  UserAccountType account = UserAccountType.CNY;
  String money = '';
  String express_fee = '';
  int pay_status = 0;
  String pay_time = '';
  int pay_type = 0;
  String pay_order_no = '';
  String created_at = '';
  String updated_at = '';
  String total_goods_price = '';

  //pay_type 支付方式 1-余额支付 2-PayPai 3-Veem 4-微信 5-支付宝

  String getPayType() {
    switch (pay_type) {
      case 1:
        return '余额支付';
      case 2:
        return 'Paypal';
      case 3:
        return 'Veem';
      case 4:
        return '微信支付';
      case 5:
        return '支付宝';
    }
    return '';
  }
}

@JsonSerializable()
class OrderExpressEntity {
  OrderExpressEntity();

  // "sn": "s2404290204527425",
  // "shop_id": 1,
  // "express_fee_id": 3,
  // "area_id": 217,
  // "money": "8.00",
  // "status": 0,
  // "created_at": "2024-04-29 02:04:52",
  // "updated_at": "2024-04-29 02:04:52"
  factory OrderExpressEntity.fromJson(Map<String, dynamic> json) => $OrderExpressEntityFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderGoodsSnapshotEntity {
  int id = 0;
  String sn = '';
  String title = '';
  UserAccountType account = UserAccountType.CNY;
  String order_num = '';
  String product = '';
  int count = 0;
  String total_money = '';
  String pay_money = '';
  String sku_id = '';
  int shop_id = 0;
  String shop_name = '';
  String remark = '';
  String ship_address = '';
  int express_fee_id = 0;
  int express_status = 0;
  String express_code = '';
  String express_name = '';
  String express_url = '';
  List<String> pic = [];

  String delivery_address = '';
  String delivery_name = '';
  String delivery_phone = '';

  String created_at = '';
  String updated_at = '';
  List<OrderGoodsSkuEntity> sku = [];

  int status = 0;
  int o_status = 0;

  String getStatusDesc() {
    switch (o_status) {
      case 0:
        return '待支付';
      case 1:
        return '待发货';
      case 2:
        return '已发货';
      case 3:
        return '已退回';
      case 4:
        return '已签收';
      case 5:
        return '已完成';
      case 6:
        return '已取消';
      case 7:
        return '已关闭';
    }
    return '';
  }

  OrderGoodsSnapshotEntity();
  factory OrderGoodsSnapshotEntity.fromJson(Map<String, dynamic> json) => $OrderGoodsSnapshotEntityFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderGoodsEntity {
  OrderGoodsEntity();

  factory OrderGoodsEntity.fromJson(Map<String, dynamic> json) => $OrderGoodsEntityFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

  String sn = '';
  String order_num = '';
  List<String> pic = [];
  String title = '';
  String product = '';
  int count = 0;
  UserAccountType account = UserAccountType.CNY;
  String money = '';
  String total_money = '';
  String pay_money = '';
  List<OrderGoodsSkuEntity> sku = [];
  String sku_id = '';
  int shop_id = 0;
  String shop_name = '';
  String remark = '';
  String ship_address = '';
}

@JsonSerializable()
class OrderGoodsSkuEntity {
  OrderGoodsSkuEntity();

  factory OrderGoodsSkuEntity.fromJson(Map<String, dynamic> json) => $OrderGoodsSkuEntityFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

  int id = 0;
  String val = '';
  String name = '';
}

@JsonSerializable()
class MallOrderDetailEntity {
  OrderPayEntity? payInfo;
  List<OrderGoodsSnapshotEntity> goodsList = [];

  MallOrderDetailEntity();

  factory MallOrderDetailEntity.fromJson(Map<String, dynamic> json) => $MallOrderDetailEntityFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class MallOrderListResp {
  MallOrderListResp();

  int total = 0;
  List<MallOrderDetailEntity> list = [];

  factory MallOrderListResp.fromJson(Map<String, dynamic> json) => $MallOrderListRespFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class PaymentResp {
  PaymentResp();

  List<PaymentMethod> methods = [];
  String price = '';
  UserAccountType account = UserAccountType.CNY;
  UserAccountEntity? userAccount;
  factory PaymentResp.fromJson(Map<String, dynamic> json) => $PaymentRespFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class PaymentMethod {
  PaymentMethod();

  String name = '';
  int type = 0;
  String icon = '';
  String color = '';
  String image = '';
  String desc = '';

  factory PaymentMethod.fromJson(Map<String, dynamic> json) => $PaymentMethodFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class MallHomeResp {
  List<MallHomeTab> tabList = [];
  List<GoodsDetailEntity> wellChosen = [];
  List<GoodsDetailEntity> goodsList = [];
  MallHomeResp();

  factory MallHomeResp.fromJson(Map<String, dynamic> json) => $MallHomeRespFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class MallHomeTab {
  MallHomeTab();

  String name = '';
  List<MallHomeTabIcon> list = [];

  factory MallHomeTab.fromJson(Map<String, dynamic> json) => $MallHomeTabFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class MallHomeTabIcon {
  String name = '';
  String icon = '';
  String image = '';
  String url = '';
  MallHomeTabIcon();

  factory MallHomeTabIcon.fromJson(Map<String, dynamic> json) => $MallHomeTabIconFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    return data;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
