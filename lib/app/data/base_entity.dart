import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';

part 'base_entity.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class BaseEntity<T> {
  @Json<PERSON>ey(name: 'code')
  int? code;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message')
  String? message;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'flag', defaultValue: false)
  bool flag = false;

  @JsonKey(name: 'data')
  T? data;

  @JsonKey(includeFromJson: false)
  late bool isSuccess;

  factory BaseEntity.fromJson(Map<String, dynamic> json, T Function(dynamic json) func) {
    var entity = _$BaseEntityFromJson<T>(json, func);
    entity.isSuccess = entity.code == 20000;
    return entity;
  }

  BaseEntity({this.code, this.message, this.flag = false, this.data});
}
