import 'dart:convert';

import 'package:inspector/app/data/address_entity.dart';
import 'package:inspector/app/data/order_info_entity.dart';
import 'package:inspector/app/models/inspector.dart';
import 'package:inspector/app/models/supervisor.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/order_detail_entity.g.dart';

@JsonSerializable()
class OrderDetailEntity {
  int? orderId;
  String? orderNo;
  double? price;
  String? inspectTime;
  int? type;
  String? provinceCity;
  String? factoryName;
  String? name;
  String? phone;
  String? address;
  int? addressId;
  int? factoryUserId;
  String? email;
  List<OrderDateEntity>? timeBeans;
  String? remark;
  String? productName;
  int? poNum;
  List<String> file = [];
  List<String> template = [];
  String? city;
  int? inspectType;
  int? priceType;
  bool? isLookInspection;
  bool? isApply;
  String? createdTime;
  double? lon;
  double? lat;
  List<InspectorModel>? inspectors;
  SupervisorModel? supervisor;
  dynamic salesmanInfo;

  AddressRows? addressInfo;

  int? userAccount; //货币类型

  ///是否是我发布的订单
  bool? isSelf;

  ///1-草稿 2-待接单 3-待验货 4-验货完成 5-取消 6-验货中
  int? status;

  int? insStatus;

  int? statusType;
  int? commentStatus; //comment_status 1-未评价 2-已评价 3-回复待审核

  ///支付方式1-平台 2-paypal 3-支付宝 4-其他
  int? payType;
  //报告状态：0-未上传1-待审核2-通过3-不通过  5上传了,"
  int? reportStatus;

  bool? canEdit;

  int? sample;
  String? standard;
  int? critical;
  String? major;
  String? minor;
  int? inspectNum;
  List<OrderProduct>? orderProducts;

  double? allPrice; //母订单总价格
  String? reportPoNums;
  String? reportTotalAmout;
  String? successTime;
  String? firstArrivalTime;
  String? inspectorNames;
  OrderDetailEntity();

  factory OrderDetailEntity.fromJson(Map<String, dynamic> json) {
    var model = $OrderDetailEntityFromJson(json);
    model.lon =
        json['lon'] == null ? 0.0 : double.parse(json['lon'].toString());
    model.lat =
        json['lat'] == null ? 0.0 : double.parse(json['lat'].toString());
    model.inspectors = InspectorModel.fromMapList(json['inspector']);

    if (json['salesman_info'] != null) {
      if (json['salesman_info'] is List) {
        var list = SupervisorModel.fromMapList(json['salesman_info']);
        if (list.isNotEmpty) model.supervisor = list.first;
      } else {
        model.supervisor = SupervisorModel.fromJson(json['salesman_info']);
      }
    }

    model.insStatus = json['insStatus'];
    model.statusType = json['statusType'];
    model.commentStatus = json['commentStatus'];
    model.salesmanInfo = json['salesman_info'];
    return model;
  }

  Map<String, dynamic> toJson() => $OrderDetailEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderDateEntity {
  String? date;
  int? inspectNum;

  OrderDateEntity();

  factory OrderDateEntity.fromJson(Map<String, dynamic> json) =>
      $OrderDateEntityFromJson(json);

  Map<String, dynamic> toJson() => $OrderDateEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AddressInfo {
  int? id;
  int? factoryUserId;
  String? factoryName;
  String? name;
  String? phone;
  String? email;
  String? province;
  String? city;
  String? area;
  double? lon;
  double? lat;
  String? address;
  String? created;

  AddressInfo();
  factory AddressInfo.fromJson(Map<String, dynamic> json) =>
      $AddressInfoFromJson(json);
}
