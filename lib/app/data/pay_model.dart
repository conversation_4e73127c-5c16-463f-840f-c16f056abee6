import 'dart:convert';

import 'package:inspector/app/data/user_account.dart';
import 'package:inspector/generated/json/base/json_field.dart';
import 'package:inspector/generated/json/pay_model.g.dart';

@JsonSerializable()
class PayInfoResp {
  String price = '';
  int account = 1;
  UserAccountEntity? userAccount;

  PayInfoResp();

  factory PayInfoResp.fromJson(Map<String, dynamic> json) => $PayInfoRespFromJson(json);

  Map<String, dynamic> toJson() => $PayInfoRespToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
