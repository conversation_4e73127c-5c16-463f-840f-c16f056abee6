class SupervisorModel {
  SupervisorModel();

  int? userId;
  String? name;
  int? imUserId;

  static SupervisorModel fromJson(Map<String, dynamic> json) {
    var model = SupervisorModel();

    model.userId = json['id'];
    model.name = json['name'];
    model.imUserId = json['im_user_id'];

    return model;
  }

  static List<SupervisorModel> fromMapList(dynamic mapList) {
    if (mapList == null) return [];

    List<SupervisorModel> models = [];

    for (int i = 0; i < mapList.length; i++) {
      models.add(fromJson(mapList[i]));
    }

    return models;
  }
}
