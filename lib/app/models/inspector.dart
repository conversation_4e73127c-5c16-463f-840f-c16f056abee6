import 'package:inspector/app/enums/account_type.dart';

class InspectorModel {
  InspectorModel();

  int? userId;
  UserAccountType? accountType;
  String? nick;
  double? cost; //验货费用

  static InspectorModel fromJson(Map<String, dynamic> json) {
    var model = InspectorModel();

    model.userId = json['user_id'];
    model.nick = json['nick'];
    model.accountType = json['account'] == 1
        ? UserAccountType.CNY
        : json['account'] == 2
            ? UserAccountType.USD
            : null;
    model.cost = json['cost'] == null || json['cost'].trim() == '' ? null : double.parse(json['cost'].toString());

    return model;
  }

  static List<InspectorModel> fromMapList(dynamic mapList) {
    if (mapList == null) return [];

    List<InspectorModel> models = [];

    for (int i = 0; i < mapList.length; i++) {
      models.add(fromJson(mapList[i]));
    }

    return models;
  }
}
