import 'package:get/get_rx/get_rx.dart';
import 'package:inspector/app/data/wallet_entity.dart';

class WalletProvider {
  // 静态变量_instance，存储唯一对象
  static WalletProvider? _instance;
  WalletProvider._internal() {
    _instance = this;
  }

  static WalletProvider get instance => _instance ?? WalletProvider._internal();

  Rx<WalletEntity> walletModel = WalletEntity().obs;

  double getExchangeRate() {
    return walletModel.value.rate ?? 7.0;
  }
}
