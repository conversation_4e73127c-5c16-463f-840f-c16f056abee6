import 'package:dio/dio.dart';

import 'package:inspector/app/data/china_city.dart';
import 'package:inspector/app/data/group_entity.dart';
import 'package:inspector/app/data/public_model.dart';
import 'package:inspector/app/modules/message/models/member.dart';
import 'package:inspector/app/tools/public_provider.dart';

class ApiService {
  ApiService._();

  ///获取群成员列表
  static Future<List<GroupMemberEntity>> getChatGroupMembers({required int groupId, int pageIndex = 1, int limit = 30}) async {
    BaseModel<List<GroupMemberEntity>> response = await PublicProvider.request<List<GroupMemberEntity>>(
      path: 'group/members?group_id=$groupId&page=$pageIndex&limit=$limit',
      isPost: true,
      chat: true,
    );

    return response.data!;
  }

  ///更新群聊名称
  static Future<void> updateChatGroupName(int groupId, String groupName) async {
    await PublicProvider.request<dynamic>(
      path: 'chat/group/rename',
      params: {'group_id': groupId, 'name': groupName},
      isPost: true,
      chat: true,
    );
  }

  //退出群聊
  static Future<dynamic> exitChatGroup(int groupId) async {
    BaseModel<dynamic> response = await PublicProvider.request<dynamic>(
      path: 'group/exit',
      params: {'group_id': groupId},
      isPost: true,
      chat: true,
    );

    return response;
  }

  ///客户端根据订单ID获取会话ID
  static Future<int> getConversationId(int orderId) async {
    BaseModel<dynamic> response = await PublicProvider.request<dynamic>(
      path: 'grab/order/getConversationId',
      params: {'order_id': '$orderId'},
      isPost: true,
      chat: false,
      isAll: false,
    );

    return response.data['id'];
  }

  ///上传聊天文件
  static Future<String?> uploadFile(String filePath) async {
    FormData formData = FormData();
    formData.files.add(MapEntry('source', await MultipartFile.fromFile(filePath, filename: filePath.substring(filePath.lastIndexOf('/') + 1))));

    var result = await PublicProvider.request<dynamic>(
      path: 'chat/upload',
      isPost: true,
      chat: true,
      params: formData,
      type: 'multipart/form-data',
    );

    if (result.code == 20000 && result.data != null) {
      return result.data['path'];
    }
    return null;
  }

  static Future<List<ChinaArea>> getChinaCities() async {
    BaseModel<dynamic> response = await PublicProvider.request<dynamic>(
      path: '${Server.domain}api/com/area',
      isPost: false,
      chat: false,
      isAll: true,
    );
    return ChinaArea.fromMapList(response.data);
  }
}
