// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

///颜色
class MColor {
  MColor._();

  ///主题色
  static const Color skin = Color(0xFFF2591D);
  static const Color skin_05 = Color(0x0DF2591D);
  static const Color skin_80 = Color(0xFFF2591D);
  static Color skin_50 = skin.withOpacity(0.5);
  static Color skin_20 = skin.withOpacity(0.2);

  ///黑色
  static const Color xFF333333 = Color(0xFF333333);
  static const Color xFF3D3D3D = Color(0xFF3D3D3D);
  static Color xFF333333_60 = const Color(0xFF333333).withAlpha(60);
  static Color xFF333333_50 = const Color(0xFF333333).withAlpha(50);
  static const Color xFF838383 = Color(0xFF838383);
  static const Color xFF303535 = Color(0xFF303535);

  static const Color backgroundColor = Color(0xFFF2F6FA);
  static const Color white = xFFFFFFFF;
  static const Color xFFF2F6FA = Color(0xFFF2F6FA);
  static const Color xFFE95332 = Color(0xFFE95332);
  static const Color xFFEEEEEE = Color(0xFFEEEEEE);
  static const Color xFFFFFFFF = Color(0xFFFFFFFF);
  static const Color xFFEBEBEB = Color(0xFFEEEEEE);
  static const Color xFFF4F5F7 = Color(0xFFF4F5F7);
  static const Color xFFF7F8F9 = Color(0xFFF7F8F9);
  static Color xFFA4A5A9_80 = const Color(0xFFA4A5A9).withAlpha(80);
  static const Color xFFD7D9DD = Color(0xFFD7D9DD);
  static const Color xFFD7A17C = Color(0xFFD7A17C);
  static const Color xFF9A9B9C = Color(0xFF9A9B9C);
  static const Color x80E3E3E3 = Color(0x80E3E3E3);
  static const Color xFFDDDDDD = Color(0xFFDDDDDD);
  static const Color xFF565656 = Color(0xFF565656);
  static const Color xFF999999 = Color(0xFF999999);
  static const Color xFF666666 = Color(0xFF666666);
  static const Color xFFCCCCCC = Color(0xFFCCCCCC);
  static const Color xFFCFCFCF = Color(0xFFCFCFCF);
  static Color xFFCFCFCF_50 = const Color(0xFFCFCFCF).withAlpha(50);
  static const Color xFFFCFCFC = Color(0xFFFCFCFC);
  static const Color black = xFF000000;
  static const Color xFF000000 = Color(0xFF000000);
  static const Color xFF797979 = Color(0xFF797979);
  static const Color xFF25C56F = Color(0xFF25C56F);
  static const Color xFF40A900 = Color(0xFF40A900);
  static const Color xFFA2A2A2 = Color(0xFFA2A2A2);
  static const Color xFFABABAC = Color(0xFFABABAC);
  static const Color xFFFB6668 = Color(0xFFFB6668);
  static const Color xFFEEB697 = Color(0xFFEEB697);
  static const Color xFFFEE0D0 = Color(0xFFFEE0D0);
  static const Color xFFEEB595 = Color(0xFFEEB595);
  static const Color xFF5C3F2B = Color(0xFF5C3F2B);
  static const Color xFFB6906E = Color(0xFFB6906E);
  static const Color xFFBBBBBB = Color(0xFFBBBBBB);
  static const Color xFFBFBFBF = Color(0xFFBFBFBF);
  static const Color xFFF4F6F9 = Color(0xFFF4F6F9);
  static const Color xFFE6E6E6 = Color(0xFFE6E6E6);
  static const Color xFFD9A179 = Color(0xFFD9A179);
  static const Color xFFDF8D14 = Color(0xFFDF8D14);
  static const Color xFF1BA12B = Color(0xFF1BA12B);
  static const Color xFF0081E7 = Color(0xFF0081E7);
  static const Color xFFEA6A46 = Color(0xFFEA6A46);
  static const Color xFFD95F66 = Color(0xFFD95F66);
  static const Color xFFF6F6F6 = Color(0xFFF6F6F6);
  static const Color xFF2A2A2A = Color(0xFF2A2A2A);
  static const Color xFF777777 = Color(0xFF777777);
  static const Color xFFE64724 = Color(0xFFE64724);
  static const Color xFFE5E5E5 = Color(0xFFE5E5E5);
  static const Color xFFC4C4C4 = Color(0xFFC4C4C4);
  static const Color xFFF2591D = Color(0xFFF2591D);
  static const Color aiMain = xFFF2591D;
  static const Color xFFA6A6A6 = Color(0xFFA6A6A6);
  static const Color xFFD6D6D6 = Color(0xFFD6D6D6);
  static const Color xFFF0F0F0 = Color(0xFFF0F0F0);
  static const Color xFF808080 = Color(0xFF808080);
  static const Color xFF383838 = Color(0xFF383838);
  static const Color xFF858585 = Color(0xFF858585);
  static const Color xFFF2F2F2 = Color(0xFFF2F2F2);
  static const Color xFFFFF3F0 = Color(0xFFFFF3F0);
  static const Color xFF616161 = Color(0xFF616161);
  static const Color xFFEDEDED = Color(0xFFEDEDED);
  static const Color xFF2A82E4 = Color(0xFF2A82E4);
  static const Color xFFFAFAFA = Color(0xFFFAFAFA);
  static const Color xFFB8B8B8 = Color(0xFFB8B8B8);
  static const Color xFFE8E8E8 = Color(0xFFE8E8E8);
  static const Color xFFFF1111 = Color(0xFFFF1111);
  static const Color xFF92D050 = Color(0xFF92D050);


  static const Color failed = xFFFF1111;
  static const Color pass = xFF92D050;
  static const Color xFFE8040A = Color(0xFFE8040A);
  static Color x37FFE9E3 = Color(0xFFFFE9E3).withOpacity(0.55);
}

class DarkColor {
  DarkColor._();

  ///主题色
  static const Color skin = Color(0xFFC42407);
  static const Color skin_05 = Color(0x0DC42407);
  static const Color skin_80 = Color(0xFFC42407);
  static Color skin_50 = const Color(0xFFC42407).withOpacity(0.5);

  ///黑色
  static const Color xFF333333 = Color(0xFFCCCCCC);
  static const Color xFF3D3D3D = Color(0xFFC2C2C2);
  static Color xFF333333_60 = const Color(0xFFCCCCCC).withAlpha(60);
  static Color xFF333333_50 = const Color(0xFFCCCCCC).withAlpha(50);
  static const Color xFF838383 = Color(0xFF7C7C7C);
  static const Color xFF303535 = Color(0xFFCFCACA);

  static const Color xFFE95332 = Color(0xFFE95332);
  static const Color xFFEEEEEE = Color(0xFF111111);
  static const Color xFFFFFFFF = Color(0xFF000000);
  static const Color xFFEBEBEB = Color(0xFF111111);
  static const Color xFFF4F5F7 = Color(0xFF0B0A09);
  static const Color xFFF7F8F9 = Color(0xFF080706);
  static Color xFFA4A5A9_80 = const Color(0xFF5B5A56).withAlpha(80);
  static const Color xFFD7D9DD = Color(0xFF282622);
  static const Color xFFD7A17C = Color(0xFFD7A17C);
  static const Color xFF9A9B9C = Color(0xFF656463);
  static const Color x80E3E3E3 = Color(0x801C1C1C);
  static const Color xFFDDDDDD = Color(0xFF222222);
  static const Color xFF565656 = Color(0xFFA9A9A9);
  static const Color xFF999999 = Color(0xFF666666);
  static const Color xFF666666 = Color(0xFF999999);
  static const Color xFFCCCCCC = Color(0xFF333333);
  static const Color xFFCFCFCF = Color(0xFF303030);
  static const Color xFF000000 = Color(0xFFFFFFFF);
  static const Color xFF797979 = Color(0xFF868686);
  static const Color xFF25C56F = Color(0xFF25C56F);
  static const Color xFF40A900 = Color(0xFF40A900);
  static const Color xFFA2A2A2 = Color(0xFF5D5D5D);
  static const Color xFF2A2A2A = Color(0xFF2A2A2A);
  static const Color xFFABABAC = Color(0xFF545453);
  static const Color xFFFB6668 = Color(0xFFFB6668);
  static const Color xFFEEB697 = Color(0xFFEEB697);
  static const Color xFFFEE0D0 = Color(0xFFFEE0D0);
  static const Color xFFEEB595 = Color(0xFFEEB595);
  static const Color xFF5C3F2B = Color(0xFF5C3F2B);
  static const Color xFFB6906E = Color(0xFFB6906E);
  static const Color xFFBBBBBB = Color(0xFF444444);
  static const Color xFFBFBFBF = Color(0xFF404040);
  static const Color xFFF4F6F9 = Color(0xFF0B0906);
  static const Color xFFE6E6E6 = Color(0xFF191919);
  static const Color xFFD9A179 = Color(0xFFD9A179);
  static const Color xFFDF8D14 = Color(0xFFDF8D14);
  static const Color xFF1BA12B = Color(0xFF1BA12B);
  static const Color xFF0081E7 = Color(0xFF0081E7);
  static const Color xFFEA6A46 = Color(0xFFEA6A46);
  static const Color xFFD95F66 = Color(0xFFD95F66);
  static const Color xFFF6F6F6 = Color(0xFFF6F6F6);
  static const Color xFF777777 = Color(0xFF777777);
}

class MyColors {
  MyColors._();

  static const lightThemeColors = {
    'skin': Color(0xFFC42407),
    'skin_05': Color(0x0DC42407),
    'skin_80': Color(0xFFC42407),
    'skin_50': Color(0x99C42407),
    'xFF333333': Color(0xFF333333),
    'xFF3D3D3D': Color(0xFF3D3D3D),
    'xFF333333_60': Color(0x99333333),
    'xFF333333_50': Color(0x80333333),
    'xFF838383': Color(0xFF838383),
    'xFF303535': Color(0xFF303535),
    'xFFE95332': Color(0xFFE95332),
    'xFFEEEEEE': Color(0xFFEEEEEE),
    'xFFFFFFFF': Color(0xFFFFFFFF),
    'xFFEBEBEB': Color(0xFFEEEEEE),
    'xFFF4F5F7': Color(0xFFF4F5F7),
    'xFFF7F8F9': Color(0xFFF7F8F9),
    'xFFA4A5A9_80': Color(0x80A4A5A9),
    'xFFD7D9DD': Color(0xFFD7D9DD),
    'xFFD7A17C': Color(0xFFD7A17C),
    'xFF9A9B9C': Color(0xFF9A9B9C),
    'x80E3E3E3': Color(0x80E3E3E3),
    'xFFDDDDDD': Color(0xFFDDDDDD),
    'xFF565656': Color(0xFF565656),
    'xFF999999': Color(0xFF999999),
    'xFF666666': Color(0xFF666666),
    'xFFCCCCCC': Color(0xFFCCCCCC),
    'xFFCFCFCF': Color(0xFFCFCFCF),
    'xFF000000': Color(0xFF000000),
    'xFF797979': Color(0xFF797979),
    'xFF25C56F': Color(0xFF25C56F),
    'xFF40A900': Color(0xFF40A900),
    'xFFA2A2A2': Color(0xFFA2A2A2),
    'xFFABABAC': Color(0xFFABABAC),
    'xFFFB6668': Color(0xFFFB6668),
    'xFFEEB697': Color(0xFFEEB697),
    'xFFFEE0D0': Color(0xFFFEE0D0),
    'xFFEEB595': Color(0xFFEEB595),
    'xFF5C3F2B': Color(0xFF5C3F2B),
    'xFFB6906E': Color(0xFFB6906E),
    'xFFBBBBBB': Color(0xFFBBBBBB),
    'xFFBFBFBF': Color(0xFFBFBFBF),
    'xFFF4F6F9': Color(0xFFF4F6F9),
    'FFE6E6E6': Color(0xFFE6E6E6),
    'FFD9A179': Color(0xFFD9A179),
    'FFDF8D14': Color(0xFFDF8D14),
    'FF1BA12B': Color(0xFF1BA12B),
    'FF0081E7': Color(0xFF0081E7),
    'FFEA6A46': Color(0xFFEA6A46),
    'FFD95F66': Color(0xFFD95F66)
  };

  static const darkThemeColors = {
    'skin': Color(0xFFC42407),
    'skin_05': Color(0x0DC42407),
    'skin_80': Color(0xFFC42407),
    'skin_50': Color(0x99C42407),
    //下面的颜色均为黑色主题下的颜色，与亮色主题的颜色互为反色
    'xFF333333': Color(0xFFCCCCCC),
    'xFF3D3D3D': Color(0xFFC2C2C2),
    'xFF333333_60': Color(0x99666666),
    'xFF333333_50': Color(0x80CCCCCC),
    'xFF838383': Color(0xFF7C7C7C),
    'xFF303535': Color(0xFFCFCACA),
    'xFFE95332': Color(0xFF16ACCD),
    'xFFEEEEEE': Color(0xFF111111),
    'xFFFFFFFF': Color(0xFF000000),
    'xFFEBEBEB': Color(0xFF111111),
    'xFFF4F5F7': Color(0xFF0B0A08),
    'xFFF7F8F9': Color(0xFF080706),
    'xFFA4A5A9_80': Color(0x805A5A56),
    'xFFD7D9DD': Color(0xFF282622),
    'xFFD7A17C': Color(0xFF285E83),
    'xFF9A9B9C': Color(0xFF656463),
    'x80E3E3E3': Color(0x801C1C1C),
    'xFFDDDDDD': Color(0xFF222222),
    'xFF565656': Color(0xFFA9A9A9),
    'xFF999999': Color(0xFF666666),
    'xFF666666': Color(0xFF999999),
    'xFFCCCCCC': Color(0xFF333333),
    'xFFCFCFCF': Color(0xFF303030),
    'FF000000': Color(0xFFFFFFFF),
    'FF797979': Color(0xFF868686),
    'FF25C56F': Color(0xFFDA3A90),
    'FF40A900': Color(0xFFBF56FF),
    'FFA2A2A2': Color(0xFF5D5D5D),
    'FFABABAC': Color(0xFF545453),
    'FFFB6668': Color(0xFFF49997),
    'FFEEB697': Color(0xFFF14968),
    'xFFFEE0D0': Color(0xFFF11F2F),
    'FFEEB595': Color(0xFFF14A6A),
    'FF5C3F2B': Color(0xFFFAC0D4),
    'xFFB6906E': Color(0xFF496F91),
    'xFFBBBBBB': Color(0xFF444444),
    'xFFBFBFBF': Color(0xFF404040),
    'xFFF4F6F9': Color(0xFF0B0906),
    'FFE6E6E6': Color(0xFF191919),
    'FFD9A179': Color(0xFF265E86),
    'FFDF8D14': Color(0xFF2072EB),
    'FF1BA12B': Color(0xFFE45ED4),
    'FF0081E7': Color(0xFFFF7E18),
    'FFEA6A46': Color(0xFF1595B9),
    'FFD95F66': Color(0xFF26A099)
  };
}

class MFont {
  static const bold30 = TextStyle(fontSize: 30, fontWeight: FontWeight.w700);
  static const bold24 = TextStyle(fontSize: 24, fontWeight: FontWeight.w700);
  static const bold22 = TextStyle(fontSize: 22, fontWeight: FontWeight.w700);
  static const bold20 = TextStyle(fontSize: 20, fontWeight: FontWeight.w700);
  static const bold16 = TextStyle(fontSize: 16, fontWeight: FontWeight.w700);
  static const bold18 = TextStyle(fontSize: 18, fontWeight: FontWeight.w700);

  static const semi_Bold24 = TextStyle(fontSize: 24, fontWeight: FontWeight.w600);
  static const semi_Bold22 = TextStyle(fontSize: 22, fontWeight: FontWeight.w600);
  static const semi_Bold20 = TextStyle(fontSize: 20, fontWeight: FontWeight.w600);
  static const semi_Bold17 = TextStyle(fontSize: 17, fontWeight: FontWeight.w600);
  static const semi_Bold16 = TextStyle(fontSize: 16, fontWeight: FontWeight.w600);
  static const semi_Bold15 = TextStyle(fontSize: 15, fontWeight: FontWeight.w600);
  static const semi_Bold14 = TextStyle(fontSize: 14, fontWeight: FontWeight.w600);
  static const semi_Bold13 = TextStyle(fontSize: 13, fontWeight: FontWeight.w600);
  static const semi_Bold12 = TextStyle(fontSize: 12, fontWeight: FontWeight.w600);
  static const semi_Bold11 = TextStyle(fontSize: 11, fontWeight: FontWeight.w600);

  ///medium
  static const medium36 = TextStyle(fontSize: 36, fontWeight: FontWeight.w500);
  static const medium30 = TextStyle(fontSize: 30, fontWeight: FontWeight.w500);
  static const medium26 = TextStyle(fontSize: 26, fontWeight: FontWeight.w500);
  static const medium24 = TextStyle(fontSize: 24, fontWeight: FontWeight.w500);
  static const medium20 = TextStyle(fontSize: 20, fontWeight: FontWeight.w500);
  static const medium18 = TextStyle(fontSize: 18, fontWeight: FontWeight.w500);
  static const medium17 = TextStyle(fontSize: 17, fontWeight: FontWeight.w500);
  static const medium16 = TextStyle(fontSize: 16, fontWeight: FontWeight.w500);
  static const medium15 = TextStyle(fontSize: 15, fontWeight: FontWeight.w500);
  static const medium14 = TextStyle(fontSize: 14, fontWeight: FontWeight.w500);
  static const medium13 = TextStyle(fontSize: 13, fontWeight: FontWeight.w500);
  static const medium12 = TextStyle(fontSize: 12, fontWeight: FontWeight.w500);
  static const medium11 = TextStyle(fontSize: 11, fontWeight: FontWeight.w500);
  static const medium10 = TextStyle(fontSize: 10, fontWeight: FontWeight.w500);

  ///regular
  static const regular30 = TextStyle(fontSize: 30, fontWeight: FontWeight.w400);
  static const regular24 = TextStyle(fontSize: 24, fontWeight: FontWeight.w400);
  static const regular20 = TextStyle(fontSize: 20, fontWeight: FontWeight.w400);
  static const regular19 = TextStyle(fontSize: 19, fontWeight: FontWeight.w400);
  static const regular18 = TextStyle(fontSize: 18, fontWeight: FontWeight.w400);
  static const regular17 = TextStyle(fontSize: 17, fontWeight: FontWeight.w400);
  static const regular16 = TextStyle(fontSize: 16, fontWeight: FontWeight.w400);
  static const regular15 = TextStyle(fontSize: 15, fontWeight: FontWeight.w400);
  static const regular14 = TextStyle(fontSize: 14, fontWeight: FontWeight.w400);
  static const regular13 = TextStyle(fontSize: 13, fontWeight: FontWeight.w400);
  static const regular12 = TextStyle(fontSize: 12, fontWeight: FontWeight.w400);
  static const regular11 = TextStyle(fontSize: 11, fontWeight: FontWeight.w400);
  static const regular10 = TextStyle(fontSize: 10, fontWeight: FontWeight.w400);
  static const regular6 = TextStyle(fontSize: 6, fontWeight: FontWeight.w400);
}

class MGradient {
  MGradient._();

  ///垂直渐变色
  static LinearGradient verticalGradient(List<Color> colors) {
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: colors,
    );
  }

  ///左右渐变色
  static LinearGradient horizontalGradient(List<Color> colors) {
    return LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: colors,
    );
  }
}
