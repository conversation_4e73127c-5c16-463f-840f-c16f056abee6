class Api {
  //login
  static const takeSmsCode = 'code/sms';
  static const takeEmailCode = 'code/email';
  static const bindMobile = 'login/bind/phone';
  static const emailLogin = 'login/email/login';
  static const mobileLogin = 'login/mobile/login';
  static const resetPassword = 'v2/user/resetpwd';
  static const areaList = 'config/area/list';
  static const userGps = 'user/gps';
  static const newUserLogin = 'v2/user/login';
  static const registerBindThird = 'v2/user/register';
  static const bindThird = 'v2/user/bind3th';

  //个人信息
  static const mineInfo = 'user/info';
  //个人资料
  static const userProfile = 'v2/user/profile';
  //更新信息
  static const editInfo = 'user/editInfo';
  //审核信息提交
  static const apply = 'user/inspector/apply';
  //审核信息
  static const applyIno = 'user/inspectorInfo';
  //钱包信息
  static const wallet = 'user/wallet';
  //钱包信息（包括支付绑定信息）
  static const walletInfo = 'user/walletinfo';
  //支付绑定信息
  static const bindPay = 'user/bing/pay';
  //设置默认账户
  static const defaultAccount = '/user/setmoneytype';

  static const userParsing = 'v2/user/parsing';

  //银行卡列表
  static const bankList = 'user/bank/list';
  //添加银行卡
  static const addBank = 'user/addBank';
  //删除银行卡
  static const deleteBank = 'user/bank/delById';
  //推荐
  static const recommend = 'introduced/introduced';

  //验货列表
  static const orderList = 'grab/order/searchList';
  //验货标准价格
  static const standPrice = 'grab/order/price';
  //申请记录
  static const applyList = 'grab/order/applyList';
  //抢单
  static const orderApply = 'grab/order/apply';
  //一口价
  static const onlyPrice = 'welfare/autoPrice/search';
  //vip价格
  static const vipPrice = 'grab/order/calculation';
  //提交订单
  static const submitOrder = 'grab/order/submit';
  //支付订单
  static const payOrder = 'pay/payOrder';
  //充值支付
  static const payCharge = 'pay/recharge/payOrder';
  //充值
  static const chargeOrder = 'wallet/recharge';
  //提现
  static const cash = 'wallet/apply';
  //账单列表
  static const billList = 'wallet/capital/list';
  //提现列表
  static const cashList = 'wallet/list';
  //充值列表
  static const chargeList = 'wallet/recharge/list';
  //订单客服备注列表
  static const orderNotes = 'v2/ad/order/note';
  //新增订单备注
  static const addAdminNote = 'v2/ad/order/addnote';

  //我发布的单
  static const publishList = 'grab/order/push/list';
  //我验货的订单
  static const inspectionList = 'grab/order/inspection/list';
  //订单详情
  static const orderDetail = 'grab/order';
  //开始验货
  static const startOrder = 'grab/order/inspection/start';
  //开始验货
  static const beginInspect = 'grab/order/status/update';

  //确认派单
  static const orderConfirm = 'grab/order/confirm';
  //客户取消订单
  static const cancelOrder = 'grab/order';
  //验货员取消订单
  static const cancelInsOrder = 'grab/order/inspection';
  //报告查询
  static const reportInfo = 'inspection/search';
  //提交报告报告
  static const reportSave = 'inspection/submit';
  //编辑订单
  static const orderEdit = 'grab/order/edit';
  //服务评价
  static const orderComment = 'comment/orderCommentAdd';
  //发起群聊
  static const imGroup = 'group/create';
  //获取会话信息
  static const imGroupInfo = 'group/info';
  //获取会话详情
  static const chatInfo = 'chat/conversation/info';
  //获取聊天记录
  static const chatRecords = 'chat/records';
  // 获取会话列表
  static const conversationList = 'chat/conversation/list';
  // 发起一个会话
  static const createConv = 'chat/conversation/add';
  // 批量发消息
  static const batchTemplateMsg = 'chat/batch_send';

  // 拉取通知
  static const noticeList = 'notice/list';

  // 刷新token
  static const refreshToken = 'v2/user/token';

  //保存地址
  static const addressSave = 'address/save';
  //地址列表
  static const addressList = 'address/list';
  //地址删除
  static const addressDelete = 'address/delete';
  //地址编辑
  static const addressEdit = 'address/edit';
  //地址识别
  static const addressRecognize = 'address/recognize';
  //验证身份证
  static const authIDCard = 'sdk/ocrIdCard';
  //更新app
  static const updateApp = 'config/splash/app';
  //获取百度token
  static const bdToken =
      'https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=gUvKmoPQVlCECodlXz8WGHPZ&client_secret=IwKG8AhVUiCkobillxRpCAK0lK2uEnvl';
  //百度地址解析
  static const bdAddress = 'https://aip.baidubce.com/rpc/2.0/nlp/v1/address';
  // //高德获取经纬度
  // static const gdLatx = 'http://restapi.amap.com/v3/geocode/geo?key=e6cbde4afde7707f3da9e5af69ea5c68';

  //图片上传
  static const uploadImage = 'upd/files';

  // 客服聊天
  static const contactService = 'group/contact_service';

  // 指派验货员列表
  static const assignInspectorList = 'v2/ad/insp/list';
  // 指派验货员
  static const assignInspector = 'v2/ad/insp/assign';
  // 取消指派
  static const cancelAssign = 'v2/ad/insp/cancel';
  // 获取验货列表
  static const inspOrder = 'v2/ad/insp/orders';

  static const purchaseClassList = 'v2/purchase/class';
  static const purchaseItemList = 'v2/purchase/list';
  static const purchaseDetails = 'v2/purchase/detail';
  static const purchaseAdd = 'v2/purchase/add';
  static const purchaseAddReply = 'v2/purchase/reply';
  static const purchaseAddLike = 'v2/purchase/zan';
  static const purchaseTaglist = 'v2/purchase/tag/list';
  static const payPurchase = 'v2/pay';
  static const payInfo = 'v2/pay_info';
  static const purchaseMyReplies = 'v2/purchase/myreply';
  static const purchaseMyAppeals = 'v2/purchase/myappeal';
  static const purchaseSubmitReview = 'v2/purchase/evaluate/set';
  static const purchaseAppeal = 'v2/purchase/appeal';
  static const purchaseAppealDetail = 'v2/purchase/appeal/detail';
  static const purchaseCancelAppeal = 'v2/purchase/cancel_appeal';
  static const purchaseDraft = 'v2/purchase/draft';
  static const purchaseComplaint = 'v2/purchase/report';
  static const chatComplaint = 'chat/report';
  static const userComplaint = 'v2/user/report';

  static const homeNewList = 'v2/index/list';

  static const goodsDetail = 'v2/shop/detail';
  static const goodsCalcPrice = 'v2/shop/calc';
  static const orderCreate = 'v2/shop/createorder';
  static const mallDefaultAddress = 'v2/express/default';
  static const mallSaveAddress = 'v2/express/save';
  static const mallAddressInfo = 'v2/express/detail';
  static const mallAddressList = 'v2/express';
  static const mallDeleteAddress = 'v2/express/delete';
  static const mallSetDefault = 'v2/express/setdefault';
  static const mallOrderDetail = 'v2/shop/order';
  static const mallOrderList = 'v2/shop/orders';
  static const mallOrderCancel = 'v2/shop/order/cancel';
  static const mallPaymentInfo = 'v2/pay_info';
  static const mallExpressFee = 'v2/expressfee/getfee';
  static const mallPayOrder = 'v2/pay';
  static const mallPayAlipayTest = 'v2/shop/order/alipaytest';
  static const mallHome = 'v2/shop/indexdata';
  static const mallOrderReceived = 'v2/shop/order/received';

  static const shortcutList = 'v2/app/classlist';

  static String checkBindAvailable = 'v2/user/checkbind';
  static String unbindThird = 'v2/user/unbind';
  static const addProduct = 'v2/product/add';
  static const editProduct = 'v2/product/save';
  static const deleteProduct = 'v2/product/del';
  static const addModel = 'v2/model/add';
  static const editModel = 'v2/model/save';
  static const deleteModel = 'v2/model/del';
}
