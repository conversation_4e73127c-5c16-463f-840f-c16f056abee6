# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at
  # https://dart-lang.github.io/linter/lints/index.html.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    avoid_print: true
    prefer_single_quotes: true
    always_declare_return_types: true
    always_put_required_named_parameters_first: true
    always_use_package_imports: true
    avoid_returning_null_for_future: true
    avoid_returning_null: true
    avoid_slow_async_io: true
    unawaited_futures: true
    unnecessary_await_in_return: true
    avoid_void_async: true
    unnecessary_final: false
    unnecessary_null_checks: true
    prefer_foreach: true
    cancel_subscriptions: true
    avoid_classes_with_only_static_members: false
    constant_identifier_names: false
    prefer_const_constructors: false
    prefer_const_declarations: false




# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options


dart_code_metrics:
  anti-patterns:
    - long-method: 300
    - long-parameter-list:: 20
  metrics:
    cyclomatic-complexity: 25
    maximum-nesting-level: 6
    number-of-parameters: 20
    source-lines-of-code: 200
  metrics-exclude:
    - test/**
    - lib/graphql/_generated/**
  rules:
    # common
    - avoid-banned-imports: false
    - avoid-collection-methods-with-unrelated-types: false
    - avoid-duplicate-exports: true
    - avoid-dynamic: false
    - avoid-global-state: false
    - avoid-ignoring-return-values: false
    - avoid-late-keyword: false
    - avoid-missing-enum-constant-in-map: true
    - avoid-nested-conditional-expressions:
        acceptable-level: 2
    - avoid-non-ascii-symbols: false
    - avoid-non-null-assertion: false
    - avoid-passing-async-when-sync-expected: false
    - avoid-redundant-async: true
    - avoid-throw-in-catch-block: false
    - avoid-top-level-members-in-tests: false
    - avoid-unnecessary-type-assertions: true
    - avoid-unnecessary-type-casts: true
    - avoid-unrelated-type-assertions: false
    - avoid-unused-parameters: false
    - ban-name: false
    - binary-expression-operand-order: true
    - double-literal-format: true
    - format-comment: false
    - member-ordering-extended: false
        # order:
        #   - public-fields
        #   - private-fields
        #   - constructors
        #   - init-state-method
        #   - build-method
        #   - did-change-dependencies-method
        #   - did-update-widget-method
        #   - dispose-method
    - newline-before-return: false
    - no-boolean-literal-compare: true
    - no-empty-block: false
    - no-equal-arguments: false
    - no-equal-then-else: true
    - no-magic-number: false
    - no-object-declaration: false
    - prefer-async-await: false
    - prefer-commenting-analyzer-ignores: false
    - prefer-conditional-expressions: true
    - prefer-correct-identifier-length: false
    - prefer-correct-test-file-name: false
    - prefer-correct-type-name:
        excluded: []
        min-length: 3
        max-length: 40
    - prefer-enums-by-name: true
    - prefer-first: true
    - prefer-immediate-return: true
    - prefer-iterable-of: false
    - prefer-last: true
    - prefer-match-file-name: false
    - prefer-moving-to-variable: false
    - prefer-trailing-comma: false
    - tag-name: false

    # flutter specific
    - always-remove-listener: true
    - avoid-border-all: false
    - avoid-returning-widgets: false
    - avoid-shrink-wrap-in-lists: false
    - avoid-unnecessary-setstate: false
    - avoid-use-expanded-as-spacer: false
    - avoid-wrapping-in-padding: false
    - check-for-equals-in-render-object-setters: false
    - consistent-update-render-object: false
    - prefer-const-border-radius: false
    - prefer-correct-edge-insets-constructor: false
    - prefer-extracting-callbacks: false
    - prefer-single-widget-per-file: false
