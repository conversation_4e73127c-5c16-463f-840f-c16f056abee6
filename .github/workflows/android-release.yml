name: android-release

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build_android:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout the code
        uses: actions/checkout@v4

      - name: Setup Java to compile Android project
        uses: actions/setup-java@v4
        with:
          distribution: 'oracle'
          java-version: '17'

      - name: Install and set Flutter version
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.13.9'

      - name: Create the Keystore
        env:
          KEYSTORE_BASE64: ${{ secrets.KEYSTORE_BASE64 }}
          KEY_PROPERTIES: ${{ secrets.KEY_PROPERTIES }}
        run: |
          # import keystore from secrets
          ls -all
          echo $KEYSTORE_BASE64 | base64 -di > android/key.jks
          echo $KEY_PROPERTIES | base64 -di > android/key.properties

      - name: Restore packages
        run: flutter pub get

      - name: Build Android Apk
        run: flutter build apk --release --flavor prod -t lib/main_prod.dart

    #   - name: Upload PGYER File
    #     id: pgyer
    #     uses: xtayga/upload-pgyer-action@master
    #     with:
    #         url: https://www.pgyer.com/apiv2/app/upload
    #         forms: '{"_api_key":"${{ secrets.PGY_KEY }}","buildName":"RSSAndroid"}'
    #         fileForms: '{"file":"build/app/outputs/apk/release/app-release.apk"}'

      - name: Publish Android Artefacts
        uses: actions/upload-artifact@v1
        with:
          name: app-prod-release.apk
          path: build/app/outputs/flutter-apk/app-prod-release.apk


      - name: Replace dependencies for google play compile
        run: |
          sed -i "/r_upgrade/d" pubspec.yaml
          sed -i "/UpgradeHelper/d" lib/app/modules/mine/setting/views/setting_view.dart
          rm lib/UpgradeHelper.dart


      - name: Restore packages
        run: flutter pub get

      - name: Build Google Play apk
        run: flutter build apk --release --flavor gp -t lib/main_gp.dart --no-tree-shake-icons

      - name: Build Google Play aab
        run: flutter build appbundle --release --flavor gp -t lib/main_gp.dart --no-tree-shake-icons

      #   - name: Upload PGYER File
      #     id: pgyer
      #     uses: xtayga/upload-pgyer-action@master
      #     with:
      #         url: https://www.pgyer.com/apiv2/app/upload
      #         forms: '{"_api_key":"${{ secrets.PGY_KEY }}","buildName":"RSSAndroid"}'
      #         fileForms: '{"file":"build/app/outputs/apk/release/app-release.apk"}'

      - name: Publish Android Apk
        uses: actions/upload-artifact@v1
        with:
          name: app-gp-release.apk
          path: build/app/outputs/flutter-apk/app-gp-release.apk

      - name: Publish Android aab
        uses: actions/upload-artifact@v1
        with:
          name: app-gp-release.aab
          path: build/app/outputs/bundle/gpRelease/app-gp-release.aab